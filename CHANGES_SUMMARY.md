# Tóm Tắt Thay Đổi: Thêm Trường ID Cho API Audience Custom Fields

## Mô Tả
Cập nhật API `GET /api/v1/admin/marketing/audience-custom-fields` để trả về trường `id` trong response.

## Các Thay Đổi Đã Thực Hiện

### 1. Entity (AdminAudienceCustomFieldDefinition)
**File:** `src/modules/marketing/admin/entities/admin-audience-custom-field-definition.entity.ts`

- Thêm trường `id` tự động tăng làm primary key
- Chuyển `fieldKey` và `createdBy` từ composite primary key thành unique constraint
- Import thêm `PrimaryGeneratedColumn` từ TypeORM

```typescript
@PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
id: number;
```

### 2. DTO Response
**File:** `src/modules/marketing/admin/dto/audience-custom-field-definition/audience-custom-field-definition-response.dto.ts`

- Thêm trường `id` vào DTO response
- Cậ<PERSON> nhật Swagger documentation

```typescript
@ApiProperty({
  description: 'ID tự động tăng của trường tùy chỉnh',
  example: 1,
})
id: number;
```

### 3. Service
**File:** `src/modules/marketing/admin/services/admin-audience-custom-field-definition.service.ts`

- Cập nhật phương thức `mapToResponseDto()` để bao gồm trường `id`
- Thêm phương thức `findById()` để tìm kiếm theo ID

```typescript
private mapToResponseDto(customField: AdminAudienceCustomFieldDefinition): AudienceCustomFieldDefinitionResponseDto {
  return {
    id: customField.id,
    fieldKey: customField.fieldKey,
    // ... các trường khác
  };
}
```

### 4. Controller
**File:** `src/modules/marketing/admin/controllers/admin-audience-custom-field-definition.controller.ts`

- Thêm endpoint `GET id/:id` để tìm kiếm theo ID
- Import thêm `ParseIntPipe`
- Cập nhật documentation cho endpoint hiện tại

```typescript
@Get('id/:id')
async findById(@Param('id', ParseIntPipe) id: number): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>>
```

### 5. Database Migration
**File:** `migrations/add-id-to-audience-admin-custom-fields.sql`

- Script SQL để thêm cột `id` tự động tăng
- Xóa primary key constraint cũ
- Tạo primary key mới cho cột `id`
- Tạo unique constraint cho `field_key + created_by`

## Kết Quả

Sau khi áp dụng các thay đổi này:

1. API `GET /api/v1/admin/marketing/audience-custom-fields` sẽ trả về trường `id` trong response
2. Có thêm endpoint mới `GET /api/v1/admin/marketing/audience-custom-fields/id/:id` để tìm kiếm theo ID
3. Endpoint cũ `GET /api/v1/admin/marketing/audience-custom-fields/:fieldKey` vẫn hoạt động bình thường

## Cách Triển Khai

1. Chạy migration script để cập nhật database:
   ```sql
   -- Chạy file migrations/add-id-to-audience-admin-custom-fields.sql
   ```

2. Restart ứng dụng để áp dụng các thay đổi code

## Lưu Ý

- Các bản ghi hiện có trong database sẽ tự động được gán ID khi chạy migration
- Unique constraint cho `field_key + created_by` đảm bảo tính duy nhất của dữ liệu
- API response giờ đây sẽ bao gồm cả `id` và `fieldKey` để hỗ trợ cả hai cách tìm kiếm
