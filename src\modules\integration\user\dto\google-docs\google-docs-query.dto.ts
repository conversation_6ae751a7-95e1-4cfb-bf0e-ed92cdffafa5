import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, Min, Max, IsEnum } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho query danh sách Google Docs integrations
 */
export class GoogleDocsIntegrationQueryDto extends QueryDto {
  /**
   * Tìm kiếm theo tên integration
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên integration',
    example: 'My Google Docs',
  })
  @IsOptional()
  @IsString({ message: 'search phải là chuỗi' })
  declare search?: string;

  /**
   * Lọc theo trạng thái kết nối
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái kết nối',
    enum: ['connected', 'disconnected', 'expired'],
    example: 'connected',
  })
  @IsOptional()
  @IsEnum(['connected', 'disconnected', 'expired'], {
    message: 'status phải là một trong: connected, disconnected, expired',
  })
  status?: 'connected' | 'disconnected' | 'expired';
}

/**
 * DTO cho query danh sách documents
 */
export class GoogleDocumentsListQueryDto {
  /**
   * Tìm kiếm theo tên document
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên document',
    example: 'My Document',
  })
  @IsOptional()
  @IsString({ message: 'search phải là chuỗi' })
  search?: string;

  /**
   * Số lượng documents trả về (tối đa 100)
   */
  @ApiPropertyOptional({
    description: 'Số lượng documents trả về (tối đa 100)',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'pageSize phải là số' })
  @Min(1, { message: 'pageSize phải lớn hơn 0' })
  @Max(100, { message: 'pageSize không được vượt quá 100' })
  pageSize?: number = 10;

  /**
   * Token để lấy trang tiếp theo
   */
  @ApiPropertyOptional({
    description: 'Token để lấy trang tiếp theo',
    example: 'next-page-token',
  })
  @IsOptional()
  @IsString({ message: 'pageToken phải là chuỗi' })
  pageToken?: string;

  /**
   * Sắp xếp theo (createdTime, modifiedTime, name)
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo',
    enum: ['createdTime', 'modifiedTime', 'name'],
    example: 'modifiedTime',
  })
  @IsOptional()
  @IsEnum(['createdTime', 'modifiedTime', 'name'], {
    message: 'orderBy phải là một trong: createdTime, modifiedTime, name',
  })
  orderBy?: 'createdTime' | 'modifiedTime' | 'name' = 'modifiedTime';

  /**
   * Thứ tự sắp xếp (asc, desc)
   */
  @ApiPropertyOptional({
    description: 'Thứ tự sắp xếp',
    enum: ['asc', 'desc'],
    example: 'desc',
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'], {
    message: 'sortOrder phải là một trong: asc, desc',
  })
  sortOrder?: 'asc' | 'desc' = 'desc';
}

/**
 * DTO cho query thông tin document
 */
export class GoogleDocumentInfoQueryDto {
  /**
   * ID của document
   */
  @ApiPropertyOptional({
    description: 'ID của document',
    example: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
  })
  @IsString({ message: 'documentId phải là chuỗi' })
  documentId: string;

  /**
   * Có bao gồm nội dung document không
   */
  @ApiPropertyOptional({
    description: 'Có bao gồm nội dung document không',
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return Boolean(value);
  })
  includeContent?: boolean = false;
}

/**
 * DTO cho việc đọc nội dung document
 */
export class GoogleDocumentReadQueryDto {
  /**
   * ID của document
   */
  @ApiPropertyOptional({
    description: 'ID của document',
    example: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
  })
  @IsString({ message: 'documentId phải là chuỗi' })
  documentId: string;

  /**
   * Định dạng nội dung trả về
   */
  @ApiPropertyOptional({
    description: 'Định dạng nội dung trả về',
    enum: ['text', 'html', 'markdown'],
    example: 'text',
  })
  @IsOptional()
  @IsEnum(['text', 'html', 'markdown'], {
    message: 'format phải là một trong: text, html, markdown',
  })
  format?: 'text' | 'html' | 'markdown' = 'text';

  /**
   * Có bao gồm thông tin formatting không
   */
  @ApiPropertyOptional({
    description: 'Có bao gồm thông tin formatting không',
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return Boolean(value);
  })
  includeFormatting?: boolean = false;
}

/**
 * DTO cho việc tạo document
 */
export class GoogleDocumentCreateDto {
  /**
   * Tiêu đề của document
   */
  @ApiPropertyOptional({
    description: 'Tiêu đề của document',
    example: 'New Document',
  })
  @IsString({ message: 'title phải là chuỗi' })
  title: string;

  /**
   * Nội dung ban đầu của document (tùy chọn)
   */
  @ApiPropertyOptional({
    description: 'Nội dung ban đầu của document',
    example: 'This is the initial content of the document.',
  })
  @IsOptional()
  @IsString({ message: 'content phải là chuỗi' })
  content?: string;

  /**
   * ID của folder chứa document (tùy chọn)
   */
  @ApiPropertyOptional({
    description: 'ID của folder chứa document',
    example: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
  })
  @IsOptional()
  @IsString({ message: 'folderId phải là chuỗi' })
  folderId?: string;
}

/**
 * DTO cho việc cập nhật nội dung document
 */
export class GoogleDocumentUpdateDto {
  /**
   * ID của document
   */
  @ApiPropertyOptional({
    description: 'ID của document',
    example: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
  })
  @IsString({ message: 'documentId phải là chuỗi' })
  documentId: string;

  /**
   * Nội dung mới của document
   */
  @ApiPropertyOptional({
    description: 'Nội dung mới của document',
    example: 'This is the updated content.',
  })
  @IsString({ message: 'content phải là chuỗi' })
  content: string;

  /**
   * Vị trí bắt đầu để chèn nội dung (index)
   */
  @ApiPropertyOptional({
    description: 'Vị trí bắt đầu để chèn nội dung (index)',
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'insertIndex phải là số' })
  @Min(1, { message: 'insertIndex phải lớn hơn 0' })
  insertIndex?: number = 1;

  /**
   * Có thay thế toàn bộ nội dung không
   */
  @ApiPropertyOptional({
    description: 'Có thay thế toàn bộ nội dung không',
    example: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return Boolean(value);
  })
  replaceAll?: boolean = false;
}
