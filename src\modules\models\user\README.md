# Models User Module

Module quản lý models và fine-tuning cho user.

## 🎯 Tính năng chính

### 1. User Data Fine-Tune Management
- Tạo, cập nhật, xóa dataset fine-tune
- Upload file train và validation lên S3
- **Validate dataset theo định dạng provider** ⭐ (Tính năng mới)

### 2. User Models Management
- L<PERSON>y danh sách models theo provider
- Hỗ trợ các loại model: SYSTEM, USER, FINE_TUNE, ALL

## 🔧 API Endpoints

### User Data Fine-Tune
```
GET    /user/data-fine-tune           # Lấy danh sách dataset
POST   /user/data-fine-tune           # Tạo dataset mới
GET    /user/data-fine-tune/:id       # Lấy chi tiết dataset
PUT    /user/data-fine-tune/:id       # Cập nhật dataset
DELETE /user/data-fine-tune/:id       # Xóa dataset
POST   /user/data-fine-tune/:id/upload-url  # Tạo URL upload
POST   /user/data-fine-tune/:id/validate    # Validate dataset ⭐ (Mới)
```

### User Models
```
GET    /user/models                   # Lấy danh sách models
```

## ⭐ Tính năng Validate Dataset (Mới)

### Mô tả
API validate dataset cho phép kiểm tra tính hợp lệ của file train và validation theo định dạng của từng AI provider.

### Endpoint
```
POST /user/data-fine-tune/{id}/validate
```

### Request Body
```json
{
  "modelId": "uuid-of-model"
}
```

### Response
```json
{
  "success": true,
  "data": {
    "trainResult": {
      "isValid": true,
      "totalTokens": 1000,
      "totalLines": 15,
      "fileSizeBytes": 5000
    },
    "validResult": {
      "isValid": true,
      "totalTokens": 500,
      "totalLines": 8,
      "fileSizeBytes": 2500
    },
    "totalTokens": 1500,
    "provider": "OPENAI",
    "modelUsed": "gpt-3.5-turbo",
    "message": "Validation thành công cho cả file train và validation"
  }
}
```

### Validation Rules

#### OpenAI
- **Định dạng**: Messages format với role (system/user/assistant) và content
- **Số dòng**: Tối thiểu 10 dòng
- **Token**: Tối đa 50,000 token mỗi dòng
- **File size**: Tối đa 250MB

#### Gemini
- **Định dạng**: Contents format với role (user/model), parts, và systemInstruction tùy chọn
- **Số dòng**: 20-5,000 dòng
- **Token**: Không giới hạn
- **File size**: Không giới hạn

### Tính năng nổi bật
- ✅ **Schema validation** chính xác cho từng provider
- ✅ **Token counting** sử dụng TokenCounterService
- ✅ **Stream processing** cho file lớn
- ✅ **Auto API key retrieval** cho Gemini từ integration
- ✅ **Detailed error reporting** với thông tin dòng cụ thể
- ✅ **File size validation** theo giới hạn provider

## 🏗️ Kiến trúc

### Services
- `UserDataFineTuneService` - Quản lý dataset fine-tune
- `UserModelsService` - Quản lý models cho user
- `JsonlFileValidatorService` - Validate file JSONL ⭐ (Mới)
- `TokenCounterService` - Đếm token chính xác

### Controllers
- `UserDataFineTuneController` - API endpoints cho dataset
- `UserModelsController` - API endpoints cho models

### Repositories
- `UserDataFineTuneRepository` - Database operations cho dataset
- `ModelsRepository` - Database operations cho models
- `ModelRegistryRepository` - Database operations cho model registry

### DTOs
- `ValidateDataFineTuneDto` - Request DTO cho validate API ⭐ (Mới)
- `ValidationResultDto` - Response DTO cho validate API ⭐ (Mới)
- Các DTO khác cho CRUD operations

## 🔐 Security & Integration

### Authentication
- Sử dụng `JwtUserGuard` cho tất cả endpoints
- User ID được lấy từ JWT token

### API Key Management
- **OpenAI**: Sử dụng system API key hoặc user API key
- **Gemini**: Tự động lấy API key từ model integration và giải mã

### File Storage
- Upload file lên S3/CloudFlare R2
- Tạo signed URL cho upload an toàn
- Stream processing để xử lý file lớn

## 📝 Logging & Error Handling

### Logging
- Chi tiết log cho mỗi bước validation
- Performance monitoring cho file processing
- Error tracking với stack trace

### Error Handling
- `AppException` với error codes cụ thể
- Detailed error messages cho validation failures
- Graceful fallback cho API key issues

## 🧪 Testing

### Unit Tests
- `UserDataFineTuneController.spec.ts` - Controller tests
- `JsonlFileValidatorService.spec.ts` - Service tests
- Coverage cho tất cả validation scenarios

### Integration Tests
- End-to-end testing cho validate API
- Mock S3 và external services
- Test với real JSONL files

## 📚 Documentation

### API Documentation
- `docs/validate-dataset-api.md` - Chi tiết API validate
- Swagger/OpenAPI annotations
- Postman collection examples

### Code Documentation
- JSDoc comments cho tất cả methods
- TypeScript interfaces cho type safety
- README files cho từng component

## 🚀 Deployment & Monitoring

### Environment Variables
```env
# S3/CloudFlare R2
CF_R2_REGION=auto
CF_R2_ACCESS_KEY=your_access_key
CF_R2_SECRET_KEY=your_secret_key
CF_R2_ENDPOINT=your_endpoint
CF_BUCKET_NAME=your_bucket

# Encryption
KEY_PAIR_PRIVATE_KEY=your_private_key
ENCRYPTION_SECRET_KEY=your_encryption_key

# AI Providers (optional)
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key
```

### Performance Monitoring
- Token counting performance metrics
- File processing time tracking
- API response time monitoring
- Error rate tracking

## 🔄 Future Enhancements

### Planned Features
- [ ] Batch validation cho multiple datasets
- [ ] Real-time validation progress tracking
- [ ] Advanced error recovery mechanisms
- [ ] Support cho thêm AI providers (Claude, etc.)
- [ ] Caching cho validation results
- [ ] Webhook notifications cho validation completion

### Technical Improvements
- [ ] Parallel processing cho large files
- [ ] Advanced schema validation rules
- [ ] Custom validation rules per user
- [ ] Integration với fine-tuning job creation
- [ ] Advanced analytics cho validation metrics
