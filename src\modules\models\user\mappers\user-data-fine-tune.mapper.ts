import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@/shared/utils';
import { UserDataFineTune } from '../../entities/user-data-fine-tune.entity';
import { UserDataFineTuneResponseDto, UserDataFineTuneDetialResponseDto } from '../dto/user-data-fine-tune/user-data-fine-tune-response.dto';

/**
 * Mapper cho User Data Fine Tune
 */
export class UserDataFineTuneMapper {
  /**
   * Chuyển đổi UserDataFineTune entity sang UserDataFineTuneResponseDto
   * @param entity UserDataFineTune entity
   * @param cdnService CDN service để generate URL
   * @returns UserDataFineTuneResponseDto
   */
  static toResponseDto(entity: UserDataFineTune, cdnService: CdnService): UserDataFineTuneResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      createdAt: entity.createdAt,
    };
  }

  /**
   * Chuyển đổi UserDataFineTune entity sang UserDataFineTuneDetialResponseDto
   * @param entity UserDataFineTune entity
   * @param cdnService CDN service để generate URL
   * @returns UserDataFineTuneDetialResponseDto
   */
  static toDetailResponseDto(entity: UserDataFineTune, cdnService: CdnService): UserDataFineTuneDetialResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      createdAt: entity.createdAt,
      trainDatasetUrl: cdnService.generateUrlView(entity.trainDataset, TimeIntervalEnum.ONE_DAY) || '',
      validDatasetUrl: entity.validDataset 
        ? cdnService.generateUrlView(entity.validDataset, TimeIntervalEnum.ONE_DAY)
        : null,
    };
  }

  /**
   * Chuyển đổi mảng UserDataFineTune entities sang mảng UserDataFineTuneResponseDto
   * @param entities Mảng UserDataFineTune entities
   * @param cdnService CDN service để generate URL
   * @returns Mảng UserDataFineTuneResponseDto
   */
  static toResponseDtoList(entities: UserDataFineTune[], cdnService: CdnService): UserDataFineTuneResponseDto[] {
    return entities.map(entity => this.toResponseDto(entity, cdnService));
  }

  /**
   * Chuyển đổi mảng UserDataFineTune entities sang mảng UserDataFineTuneDetialResponseDto
   * @param entities Mảng UserDataFineTune entities
   * @param cdnService CDN service để generate URL
   * @returns Mảng UserDataFineTuneDetialResponseDto
   */
  static toDetailResponseDtoList(entities: UserDataFineTune[], cdnService: CdnService): UserDataFineTuneDetialResponseDto[] {
    return entities.map(entity => this.toDetailResponseDto(entity, cdnService));
  }
}
