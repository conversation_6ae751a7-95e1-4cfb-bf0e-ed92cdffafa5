# Zalo Pending Members API Documentation

## Tổng quan

API này cho phép lấy danh sách thành viên chờ duyệt của một nhóm chat Zalo GMF.

## Điều kiện sử dụng

- **Quyền cần thiết**: OA phải được cấp quyền quản lý thông tin nhóm
- **Thành viên**: OA phải là thành viên của nhóm
- **Trạng thái nhóm**: Nhóm phải đang hoạt động

## Endpoint

### L<PERSON>y danh sách thành viên chờ duyệt

```
GET /v1/zalo-group-management/{integrationId}/{groupId}/pending-members
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| integrationId | string | Yes | ID của Integration Zalo OA (UUID format) |
| groupId | string | Yes | ID của nhóm chat Zalo |

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| offset | integer | No | 0 | Offset muốn query (≥ 0) |
| count | integer | No | 5 | Số lượng mong muốn query (1-100) |

#### Headers

```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

#### Response

**Success Response (200 OK):**

```json
{
  "success": true,
  "message": "Lấy danh sách thành viên chờ duyệt thành công",
  "data": {
    "offset": 0,
    "count": 5,
    "total": 1,
    "member_count": 1,
    "members": [
      {
        "user_id": "8756287263669629130",
        "name": "Hoàng Trường Phước"
      }
    ]
  }
}
```

**Error Response (400 Bad Request):**

```json
{
  "success": false,
  "message": "Group ID không được để trống",
  "error": "VALIDATION_ERROR"
}
```

**Error Response (403 Forbidden):**

```json
{
  "success": false,
  "message": "OA không có quyền quản lý thông tin nhóm",
  "error": "PERMISSION_DENIED"
}
```

**Error Response (404 Not Found):**

```json
{
  "success": false,
  "message": "Nhóm không tồn tại hoặc OA không phải thành viên",
  "error": "GROUP_NOT_FOUND"
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| offset | integer | Offset của query |
| count | integer | Số lượng muốn query |
| total | integer | Tổng số lượng thành viên chờ duyệt |
| member_count | integer | Số lượng thành viên chờ duyệt được trả về |
| members | Array | Danh sách thành viên chờ duyệt |
| members[].user_id | string | ID của thành viên chờ duyệt |
| members[].name | string | Tên hiển thị của thành viên chờ duyệt |

## Sử dụng

### 1. Lấy danh sách thành viên chờ duyệt (mặc định)

```bash
curl -X GET \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4/pending-members' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

### 2. Lấy với phân trang

```bash
curl -X GET \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4/pending-members?offset=10&count=20' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

### 3. Lấy tất cả thành viên chờ duyệt

```bash
curl -X GET \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4/pending-members?offset=0&count=100' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

## Error Handling

### Lỗi thường gặp

1. **Không có quyền quản lý nhóm**
   - **Nguyên nhân**: OA chưa được cấp quyền quản lý thông tin nhóm
   - **Giải pháp**: Liên hệ admin để cấp quyền

2. **Nhóm không tồn tại**
   - **Nguyên nhân**: Group ID không đúng hoặc nhóm đã bị xóa
   - **Giải pháp**: Kiểm tra lại Group ID

3. **OA không phải thành viên**
   - **Nguyên nhân**: OA chưa tham gia nhóm
   - **Giải pháp**: Thêm OA vào nhóm trước

4. **Validation Error**
   - **Nguyên nhân**: Tham số không hợp lệ (offset < 0, count > 100)
   - **Giải pháp**: Kiểm tra lại tham số

## Code Example (JavaScript)

```javascript
class ZaloPendingMembersManager {
  constructor(apiBaseUrl, authToken) {
    this.apiBaseUrl = apiBaseUrl;
    this.authToken = authToken;
  }

  async getPendingMembers(integrationId, groupId, options = {}) {
    const { offset = 0, count = 5 } = options;
    
    const url = new URL(
      `${this.apiBaseUrl}/v1/zalo-group-management/${integrationId}/${groupId}/pending-members`
    );
    
    url.searchParams.append('offset', offset.toString());
    url.searchParams.append('count', count.toString());

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
      },
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data;
  }

  async getAllPendingMembers(integrationId, groupId) {
    const allMembers = [];
    let offset = 0;
    const count = 50; // Lấy tối đa mỗi lần
    
    while (true) {
      const result = await this.getPendingMembers(integrationId, groupId, {
        offset,
        count,
      });
      
      allMembers.push(...result.members);
      
      // Nếu đã lấy hết hoặc không còn dữ liệu
      if (result.member_count < count || allMembers.length >= result.total) {
        break;
      }
      
      offset += count;
    }
    
    return allMembers;
  }

  async hasPendingMembers(integrationId, groupId) {
    const result = await this.getPendingMembers(integrationId, groupId, {
      offset: 0,
      count: 1,
    });
    
    return result.total > 0;
  }
}

// Sử dụng
const pendingManager = new ZaloPendingMembersManager(
  'https://api.example.com',
  'your-jwt-token'
);

// Lấy danh sách thành viên chờ duyệt
pendingManager.getPendingMembers('integration-id', 'group-id')
  .then(result => {
    console.log(`Có ${result.total} thành viên chờ duyệt`);
    result.members.forEach(member => {
      console.log(`- ${member.name} (${member.user_id})`);
    });
  })
  .catch(error => {
    console.error('Lỗi:', error.message);
  });

// Kiểm tra có thành viên chờ duyệt không
pendingManager.hasPendingMembers('integration-id', 'group-id')
  .then(hasPending => {
    if (hasPending) {
      console.log('Có thành viên chờ duyệt');
    } else {
      console.log('Không có thành viên chờ duyệt');
    }
  });
```

## Best Practices

1. **Phân trang hợp lý**: Sử dụng count từ 5-20 cho UI, tối đa 50-100 cho xử lý batch
2. **Cache kết quả**: Cache trong thời gian ngắn (1-2 phút) để tránh gọi API liên tục
3. **Error handling**: Xử lý các lỗi phổ biến như không có quyền, nhóm không tồn tại
4. **Polling**: Nếu cần real-time, polling mỗi 30-60 giây
5. **Validation**: Kiểm tra tham số trước khi gọi API

## Tích hợp với workflow

API này thường được sử dụng cùng với:
- **Duyệt thành viên**: API approve/reject pending members
- **Thông báo**: Gửi thông báo khi có thành viên mới chờ duyệt
- **Dashboard**: Hiển thị số lượng thành viên chờ duyệt trong admin panel
