import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  MaxLength,
  ValidateNested
} from 'class-validator';
import { SUPPORTED_KNOWLEDGE_FILE_MIME_TYPES } from '../../user/dto/batch-create-files.dto';

class FileDto {
  @ApiProperty({
    description: 'Tên file',
    example: 'Tài liệu hướng dẫn.pdf',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Tên file không được để trống' })
  @IsString({ message: 'Tên file phải là chuỗi' })
  @MaxLength(255, { message: 'Tên file không được vượt quá 255 ký tự' })
  name: string;

  @ApiProperty({
    description: 'Loại file (MIME type)',
    example: 'application/pdf',
    maxLength: 100,
    enum: SUPPORTED_KNOWLEDGE_FILE_MIME_TYPES
  })
  @IsNotEmpty({ message: 'Loại file không được để trống' })
  @IsString({ message: 'Loại file phải là chuỗi' })
  @MaxLength(100, { message: 'Loại file không được vượt quá 100 ký tự' })
  @IsIn(SUPPORTED_KNOWLEDGE_FILE_MIME_TYPES, {
    message: 'Loại file không được hỗ trợ. Chỉ hỗ trợ: PDF, DOC, DOCX, PPTX, JSON, HTML, TXT'
  })
  mime: string;

  @ApiProperty({
    description: 'Dung lượng file (bytes)',
    example: 1024,
  })
  @IsNotEmpty({ message: 'Dung lượng file không được để trống' })
  @IsNumber({}, { message: 'Dung lượng file phải là số' })
  @IsPositive({ message: 'Dung lượng file phải là số dương' })
  storage: number;
}

export class BatchCreateFilesDto {
  @ApiProperty({
    description: 'Danh sách các file cần tạo (tối đa 5 file)',
    type: [FileDto],
    example: [
      {
        name: 'Tài liệu hướng dẫn của sơn sói.pdf',
        mime: 'application/pdf',
        storage: 1024000
      },
      {
        name: 'Báo cáo tháng.docx',
        mime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        storage: 512000
      }
    ]
  })
  @IsArray({ message: 'Files phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một file' })
  @ArrayMaxSize(5, { message: 'Chỉ được tải lên tối đa 5 file cùng lúc' })
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  files: FileDto[];
}
