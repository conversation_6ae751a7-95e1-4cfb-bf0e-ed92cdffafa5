# Triển Khai Mã Hóa Ảnh Cho Rule Contract

## 📋 Tổng Quan

Đã triển khai thành công cơ chế mã hóa ảnh cho luồng Rule Contract với các thay đổi tối thiểu và tương thích ngược.

## 🔧 Các File Đã Được Cập <PERSON>h<PERSON>t

### 1. Controller Layer
**File**: `src/modules/rule-contract/state-machine/rule-contract-xstate.controller.ts`

**Thay đổi**:
- ✅ Inject `RuleContractStateEncryptedRepository` và `EncryptionMigrationService`
- ✅ Thêm method `processContextDataForResponse()` để giải mã ảnh trước khi trả về frontend
- ✅ Cập nhật tất cả API endpoints để sử dụng `processContextDataForResponse()`
- ✅ Thêm API `/encryption-status` để debug trạng thái mã hóa
- ✅ Cập nhật API `/reset` để reset cả encrypted repository

**Các API đã được cập nhật**:
- `GET /status` - Lấy trạng thái hợp đồng
- `POST /select-contract-type` - Chọn loại hợp đồng
- `POST /accept-terms` - Chấp nhận điều khoản
- `POST /submit-individual-info` - Gửi thông tin cá nhân
- `POST /submit-business-info` - Gửi thông tin doanh nghiệp
- `POST /proceed-to-signature` - Chuyển tới bước ký
- `POST /signature-completed` - Hoàn thành chữ ký
- `POST /verify-otp` - Xác thực OTP
- `POST /resend-otp` - Gửi lại OTP
- `POST /upload-file` - Upload file
- `POST /go-back` - Quay lại bước trước
- `POST /reset` - Reset dữ liệu
- `GET /encryption-status` - Kiểm tra trạng thái mã hóa (mới)

### 2. Service Layer
**File**: `src/modules/rule-contract/state-machine/rule-contract-xstate.service.ts`

**Thay đổi**:
- ✅ Inject `RuleContractStateEncryptedRepository` và `EncryptionMigrationService`
- ✅ Thêm method `getStateRepository()` để chọn repository phù hợp
- ✅ Cập nhật `saveState()` để sử dụng encrypted repository khi cần
- ✅ Cập nhật `loadStateFromDatabase()` để load từ encrypted repository
- ✅ Cập nhật `resetUserState()` để reset cả encrypted repository

### 3. Module Configuration
**File**: `src/modules/rule-contract/state-machine/rule-contract-xstate.module.ts`

**Thay đổi**:
- ✅ Import `RuleContractStateEncryptedRepository`
- ✅ Import `EncryptionMigrationService`
- ✅ Thêm vào providers array

## 🔐 Các Trường Được Mã Hóa

### Trong `contextData` JSON:
1. **Individual Contract**:
   - `citizenIdFrontUrl` → `citizenIdFrontUrl_public_key`
   - `citizenIdBackUrl` → `citizenIdBackUrl_public_key`

2. **Business Contract**:
   - `businessLicenseUrl` → `businessLicenseUrl_public_key`
   - `representativeIdFrontUrl` → `representativeIdFrontUrl_public_key`
   - `representativeIdBackUrl` → `representativeIdBackUrl_public_key`

3. **Signature & Contract**:
   - `signatureImageUrl` → `signatureImageUrl_public_key`
   - `contractUrl` → `contractUrl_public_key`
   - `signedContractUrl` → `signedContractUrl_public_key`

## 🚀 Cách Hoạt Động

### 1. Khi Lưu Dữ Liệu (Save)
```typescript
// Tự động chọn repository
const repository = this.getStateRepository();

if (repository === this.encryptedStateRepository) {
  // Ảnh sẽ được mã hóa tự động trong contextData
  await this.encryptedStateRepository.saveState(userId, contractId, state, context);
} else {
  // Lưu bình thường
  await this.stateRepository.saveState(userId, contractId, state, context);
}
```

### 2. Khi Trả Về Frontend (Response)
```typescript
// Giải mã ảnh trước khi trả về
const processedContext = await this.processContextDataForResponse(
  currentState.context,
  user.id
);

return ApiResponseDto.success({
  state: currentState.value,
  context: processedContext, // Context đã được giải mã
  availableEvents,
});
```

## 🔧 Cấu Hình

### Environment Variables
```env
# Bật/tắt mã hóa
ENCRYPTION_ENABLED=true

# Key mã hóa (đã có sẵn)
KEY_PAIR_PRIVATE_KEY=your_32_character_private_key_here
```

### Database
- Không cần thêm trường mới vào `rule_contract_states`
- Public keys được lưu trong `contextData` JSON với suffix `_public_key`

## 🧪 Testing & Debug

### 1. Kiểm Tra Trạng Thái Mã Hóa
```bash
GET /api/rule-contract/xstate/encryption-status
```

**Response**:
```json
{
  "code": 200,
  "message": "Lấy trạng thái mã hóa thành công",
  "result": {
    "encryptionEnabled": true,
    "configValid": true,
    "configErrors": [],
    "imageUrls": {
      "citizenIdFrontUrl": "https://cdn.example.com/decrypted-image.jpg",
      "citizenIdBackUrl": "https://cdn.example.com/decrypted-image2.jpg",
      "signatureImageUrl": null,
      "businessLicenseUrl": null
    }
  }
}
```

### 2. Test Flow
1. **Disable Encryption**: Set `ENCRYPTION_ENABLED=false` → URLs lưu trực tiếp
2. **Enable Encryption**: Set `ENCRYPTION_ENABLED=true` → URLs được mã hóa tự động
3. **Check Response**: Tất cả API response đều có URLs đã giải mã

## ✅ Kiểm Tra Hoàn Thành

### Các Phần Đã Mã Hóa:
- ✅ **citizenIdFrontUrl** trong contextData
- ✅ **citizenIdBackUrl** trong contextData  
- ✅ **signatureImageUrl** trong contextData
- ✅ **businessLicenseUrl** trong contextData
- ✅ **representativeIdFrontUrl** trong contextData
- ✅ **representativeIdBackUrl** trong contextData
- ✅ **contractUrl** trong contextData
- ✅ **signedContractUrl** trong contextData

### Các API Đã Cập Nhật:
- ✅ Tất cả 12 API endpoints đã được cập nhật
- ✅ Tự động giải mã ảnh trước khi trả về frontend
- ✅ Tự động mã hóa ảnh khi lưu vào database
- ✅ Fallback mechanism khi có lỗi mã hóa/giải mã

### Tính Năng Bổ Sung:
- ✅ Debug API để kiểm tra trạng thái mã hóa
- ✅ Migration service để chuyển đổi dần dần
- ✅ Logging chi tiết cho debugging
- ✅ Error handling và fallback

## 🎯 Kết Quả

**Trước khi triển khai**:
- URLs ảnh lưu trực tiếp trong contextData
- Không có bảo mật cho ảnh nhạy cảm

**Sau khi triển khai**:
- URLs ảnh được mã hóa tự động khi lưu
- URLs ảnh được giải mã tự động khi trả về
- Hoàn toàn transparent với frontend
- Có thể bật/tắt mã hóa qua environment variable
- Tương thích ngược 100%

## 🔄 Next Steps

1. **Testing**: Test kỹ tất cả flow với encryption enabled/disabled
2. **Performance**: Monitor performance impact của mã hóa/giải mã
3. **Monitoring**: Setup monitoring cho encryption operations
4. **Documentation**: Update API documentation với encryption info
