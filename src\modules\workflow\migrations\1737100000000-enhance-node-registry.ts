import { MigrationInterface, QueryRunner } from 'typeorm';

export class EnhanceNodeRegistry1737100000000 implements MigrationInterface {
  name = 'EnhanceNodeRegistry1737100000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update CHECK constraint to include all 192 node categories
    await queryRunner.query(`
      ALTER TABLE "node_definitions" 
      DROP CONSTRAINT IF EXISTS "CHK_node_definitions_category"
    `);

    await queryRunner.query(`
      ALTER TABLE "node_definitions" 
      ADD CONSTRAINT "CHK_node_definitions_category" CHECK (
        category IN (
          'system',
          'google_sheets', 'google_docs', 'google_gmail', 'google_drive', 
          'google_calendar', 'google_ads', 'google_analytics',
          'facebook_pages', 'facebook_ads', 'facebook_business', 
          'facebook_messenger', 'facebook_insights', 'instagram',
          'zalo_oa', 'zalo_zns', 'zalo_social', 'zalo_agent', 'zalo_webhook',
          'ai_openai', 'ai_anthropic', 'ai_google', 'ai_deepseek', 'ai_meta'
        )
      )
    `);

    // Add indexes for better performance on common queries
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_node_definitions_category" 
      ON "node_definitions" ("category")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_node_definitions_tags" 
      ON "node_definitions" USING GIN ("tags")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_node_definitions_deprecated" 
      ON "node_definitions" ("is_deprecated")
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_node_definitions_search" 
      ON "node_definitions" USING GIN (
        to_tsvector('english', COALESCE("name", '') || ' ' || COALESCE("description", ''))
      )
    `);

    // Add composite index for common filter combinations
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_node_definitions_category_deprecated" 
      ON "node_definitions" ("category", "is_deprecated")
    `);

    // Insert system node definitions if they don't exist
    await queryRunner.query(`
      INSERT INTO "node_definitions" (
        "type", "name", "description", "category", "input_schema", "output_schema", 
        "version", "documentation", "examples", "tags", "icon_url", "color_scheme"
      ) VALUES 
      (
        'system.webhook.trigger',
        'Webhook Trigger',
        'Webhook trigger node (data passthrough from BE App)',
        'system',
        '{"type":"object","properties":{"webhookUrl":{"type":"string","format":"uri"},"method":{"type":"string","enum":["POST","GET","PUT","DELETE"]},"headers":{"type":"object"},"authentication":{"type":"object"}},"required":["webhookUrl","method"]}',
        '{"type":"object","properties":{"data":{"type":"object"},"headers":{"type":"object"},"timestamp":{"type":"string","format":"date-time"}}}',
        '1.0.0',
        'Receives webhook data from external services. Data processing is handled by BE App.',
        '{"facebook":{"webhookUrl":"https://api.facebook.com/webhook","method":"POST"},"zalo":{"webhookUrl":"https://api.zalo.me/webhook","method":"POST"}}',
        '{trigger,webhook,external}',
        '/icons/nodes/system/webhook.svg',
        '#4CAF50'
      ),
      (
        'system.manual.trigger',
        'Manual Trigger',
        'Manual workflow start trigger',
        'system',
        '{"type":"object","properties":{"triggerData":{"type":"object"},"userId":{"type":"number"},"metadata":{"type":"object"}}}',
        '{"type":"object","properties":{"data":{"type":"object"},"userId":{"type":"number"},"timestamp":{"type":"string","format":"date-time"}}}',
        '1.0.0',
        'Manually triggered workflow execution with custom input data.',
        '{"basic":{"triggerData":{"message":"Hello World"},"userId":123}}',
        '{trigger,manual,start}',
        '/icons/nodes/system/manual.svg',
        '#2196F3'
      ),
      (
        'system.http.request',
        'HTTP Request',
        'Make HTTP API calls to external services',
        'system',
        '{"type":"object","properties":{"url":{"type":"string","format":"uri"},"method":{"type":"string","enum":["GET","POST","PUT","DELETE","PATCH"]},"headers":{"type":"object"},"body":{"type":"object"},"timeout":{"type":"number","minimum":1000,"maximum":30000}},"required":["url","method"]}',
        '{"type":"object","properties":{"status":{"type":"number"},"data":{"type":"object"},"headers":{"type":"object"},"duration":{"type":"number"}}}',
        '1.0.0',
        'Execute HTTP requests to external APIs with full configuration support.',
        '{"get":{"url":"https://api.example.com/data","method":"GET"},"post":{"url":"https://api.example.com/create","method":"POST","body":{"name":"test"}}}',
        '{http,api,request}',
        '/icons/nodes/system/http.svg',
        '#FF9800'
      )
      ON CONFLICT ("type") DO NOTHING
    `);

    // Create function to validate JSON schemas
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION validate_node_schema()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Basic validation that input_schema and output_schema have required structure
        IF NOT (NEW.input_schema ? 'type' AND NEW.input_schema->>'type' = 'object') THEN
          RAISE EXCEPTION 'input_schema must be a valid JSON schema with type object';
        END IF;
        
        IF NOT (NEW.output_schema ? 'type' AND NEW.output_schema->>'type' = 'object') THEN
          RAISE EXCEPTION 'output_schema must be a valid JSON schema with type object';
        END IF;
        
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create trigger for schema validation
    await queryRunner.query(`
      CREATE TRIGGER validate_node_schema_trigger
        BEFORE INSERT OR UPDATE ON "node_definitions"
        FOR EACH ROW EXECUTE FUNCTION validate_node_schema();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop trigger and function
    await queryRunner.query(`DROP TRIGGER IF EXISTS validate_node_schema_trigger ON "node_definitions"`);
    await queryRunner.query(`DROP FUNCTION IF EXISTS validate_node_schema()`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definitions_category"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definitions_tags"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definitions_deprecated"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definitions_search"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definitions_category_deprecated"`);

    // Revert CHECK constraint to original
    await queryRunner.query(`
      ALTER TABLE "node_definitions" 
      DROP CONSTRAINT IF EXISTS "CHK_node_definitions_category"
    `);

    await queryRunner.query(`
      ALTER TABLE "node_definitions" 
      ADD CONSTRAINT "CHK_node_definitions_category" CHECK (
        category IN (
          'system', 'google_sheet', 'google_docs', 'google_gmail', 
          'google_ads', 'google_drive', 'google_calendar', 'facebook_page', 
          'facebook_ads', 'facebook_messenger', 'instagram', 'zalo_oa', 
          'zalo_zns', 'zalo'
        )
      )
    `);

    // Remove system nodes that were added
    await queryRunner.query(`
      DELETE FROM "node_definitions" 
      WHERE "type" IN (
        'system.webhook.trigger',
        'system.manual.trigger', 
        'system.http.request'
      )
    `);
  }
}
