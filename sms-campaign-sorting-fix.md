# Sửa Lỗi Sorting Cho SMS Campaign API

## Vấn Đề
API `GET /marketing/sms-campaigns` không hỗ trợ sorting động với `sortBy` và `sortDirection` parameters. Mặc dù `SmsCampaignQueryDto` đã kế thừa từ `QueryDto`, nhưng repository và service không sử dụng các tham số này.

## Nguyên Nhân
1. **Repository**: Method `findByUserIdWithPagination` có sorting cố định `orderBy('campaign.createdAt', 'DESC')`
2. **Service**: Method `getCampaigns` không truyền `sortBy` và `sortDirection` vào repository
3. **DTO**: Không có validation cụ thể cho các trường sortBy hợp lệ

## Giải Pháp Đã Thực Hiện

### 1. Cập Nhật Repository (`sms-campaign-user.repository.ts`)

#### Thêm Parameters
```typescript
async findByUserIdWithPagination(
  userId: number,
  page: number = 1,
  limit: number = 20,
  search?: string,
  status?: SmsCampaignStatus,
  sortBy?: string,                    // ✅ ADDED
  sortDirection?: 'ASC' | 'DESC'      // ✅ ADDED
): Promise<{ campaigns: SmsCampaignUser[]; total: number }>
```

#### Dynamic Sorting Logic
```typescript
// Xử lý sắp xếp
const validSortFields = ['createdAt', 'updatedAt', 'name', 'status', 'scheduledAt', 'totalRecipients'];
const finalSortBy = sortBy && validSortFields.includes(sortBy) ? sortBy : 'createdAt';
const finalSortDirection = sortDirection === 'ASC' ? 'ASC' : 'DESC';

const campaigns = await queryBuilder
  .orderBy(`campaign.${finalSortBy}`, finalSortDirection)
  .skip((page - 1) * limit)
  .take(limit)
  .getMany();
```

### 2. Cập Nhật Service (`sms-campaign.service.ts`)

#### Truyền Sorting Parameters
```typescript
const { campaigns, total } = await this.smsCampaignRepository.findByUserIdWithPagination(
  userId,
  page,
  limit,
  queryDto.search,
  queryDto.status,
  queryDto.sortBy,        // ✅ ADDED
  queryDto.sortDirection  // ✅ ADDED
);
```

### 3. Cập Nhật DTO (`sms-campaign-response.dto.ts`)

#### Override sortBy với Validation
```typescript
/**
 * Trường cần sắp xếp (override từ QueryDto để có options cụ thể)
 * @example "createdAt"
 */
@ApiProperty({
  description: 'Trường cần sắp xếp',
  example: 'createdAt',
  enum: ['createdAt', 'updatedAt', 'name', 'status', 'scheduledAt', 'totalRecipients'],
  required: false,
})
@IsOptional()
@IsIn(['createdAt', 'updatedAt', 'name', 'status', 'scheduledAt', 'totalRecipients'], {
  message: 'sortBy phải là một trong: createdAt, updatedAt, name, status, scheduledAt, totalRecipients'
})
declare sortBy?: string;
```

## Các Trường Sorting Hỗ Trợ

| Trường | Mô Tả | Ví Dụ |
|--------|-------|-------|
| `createdAt` | Ngày tạo campaign | Mặc định |
| `updatedAt` | Ngày cập nhật cuối | Theo thời gian sửa đổi |
| `name` | Tên campaign | Sắp xếp theo alphabet |
| `status` | Trạng thái campaign | DRAFT, SCHEDULED, SENDING, SENT, FAILED |
| `scheduledAt` | Thời gian lên lịch | Theo thời gian gửi |
| `totalRecipients` | Số lượng người nhận | Theo số lượng |

## Cách Sử Dụng

### Basic Sorting
```
GET /marketing/sms-campaigns?sortBy=name&sortDirection=ASC
```

### Kết Hợp Với Filter
```
GET /marketing/sms-campaigns?sortBy=totalRecipients&sortDirection=DESC&status=SENT
```

### Kết Hợp Với Search và Pagination
```
GET /marketing/sms-campaigns?sortBy=createdAt&sortDirection=ASC&search=khuyến mãi&page=2&limit=10
```

## Fallback Behavior

1. **Invalid sortBy**: Fallback về `createdAt`
2. **Invalid sortDirection**: Fallback về `DESC`
3. **Missing parameters**: Sử dụng default từ QueryDto

## Testing

Sử dụng file `test-sms-campaign-sorting.http` để test các scenarios:
- ✅ Sorting theo từng trường
- ✅ Kết hợp với filter và search
- ✅ Pagination với sorting
- ✅ Invalid parameters handling

## Kết Quả

- ✅ API `GET /marketing/sms-campaigns` giờ đây hỗ trợ sorting động
- ✅ Validation đúng cho các trường sortBy
- ✅ Fallback behavior an toàn
- ✅ Tương thích ngược với existing code
- ✅ Performance tốt với database indexing

## Lưu Ý

- Đảm bảo database có index cho các trường được sort thường xuyên
- Các trường sortBy được validate ở cả DTO level và repository level
- TypeScript error đã được fix với `declare` keyword
