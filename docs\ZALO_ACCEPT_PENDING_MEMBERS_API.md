# Zalo Accept Pending Members API Documentation

## Tổng quan

API này cho phép duyệt các thành viên chờ duyệt vào nhóm chat Zalo GMF.

## Đi<PERSON><PERSON> kiện sử dụng

- **Quyền cần thiết**: OA phải được cấp quyền quản lý thông tin nhóm
- **Vai trò**: OA phải là admin của nhóm
- **Trạng thái**: Các user ID phải đang trong danh sách chờ duyệt
- **Giới hạn**: Tối đa 100 thành viên mỗi lần duyệt

## Endpoint

### Duyệt thành viên chờ duyệt

```
POST /v1/zalo-group-management/{integrationId}/{groupId}/pending-members/accept
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| integrationId | string | Yes | ID của Integration Zalo OA (UUID format) |
| groupId | string | Yes | ID của nhóm chat Zalo |

#### Request Body

```json
{
  "memberUserIds": [
    "8756287263669629130",
    "1234567890123456789"
  ]
}
```

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| memberUserIds | string[] | Yes | Danh sách user ID được duyệt (1-100 items) |

#### Headers

```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

#### Response

**Success Response (200 OK):**

```json
{
  "success": true,
  "message": "Duyệt thành viên chờ duyệt thành công",
  "data": {
    "error": 0,
    "message": "Success",
    "acceptedCount": 2
  }
}
```

**Error Response (400 Bad Request):**

```json
{
  "success": false,
  "message": "Danh sách member user IDs không được để trống",
  "error": "VALIDATION_ERROR"
}
```

**Error Response (403 Forbidden):**

```json
{
  "success": false,
  "message": "OA không có quyền quản lý thông tin nhóm",
  "error": "PERMISSION_DENIED"
}
```

**Error Response (404 Not Found):**

```json
{
  "success": false,
  "message": "Nhóm không tồn tại hoặc OA không phải admin",
  "error": "GROUP_NOT_FOUND"
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| error | integer | Mã lỗi từ Zalo API (0 = thành công) |
| message | string | Thông báo kết quả từ Zalo API |
| acceptedCount | integer | Số lượng thành viên đã gửi request duyệt |

## Sử dụng

### 1. Duyệt một thành viên

```bash
curl -X POST \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4/pending-members/accept' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "memberUserIds": ["8756287263669629130"]
  }'
```

### 2. Duyệt nhiều thành viên

```bash
curl -X POST \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4/pending-members/accept' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "memberUserIds": [
      "8756287263669629130",
      "1234567890123456789",
      "9876543210987654321"
    ]
  }'
```

## Error Handling

### Lỗi thường gặp

1. **Không có quyền admin**
```json
{
  "success": false,
  "message": "OA không có quyền quản lý thông tin nhóm",
  "error": "PERMISSION_DENIED"
}
```
**Giải pháp**: Đảm bảo OA là admin của nhóm

2. **User không trong danh sách chờ duyệt**
```json
{
  "success": false,
  "message": "Một số user ID không có trong danh sách chờ duyệt",
  "error": "INVALID_USER_IDS"
}
```
**Giải pháp**: Kiểm tra danh sách pending members trước khi duyệt

3. **Vượt quá giới hạn**
```json
{
  "success": false,
  "message": "Không thể duyệt quá 100 thành viên cùng lúc",
  "error": "VALIDATION_ERROR"
}
```
**Giải pháp**: Chia nhỏ danh sách thành các batch < 100 items

4. **Nhóm đã đầy**
```json
{
  "success": false,
  "message": "Nhóm đã đạt số lượng thành viên tối đa",
  "error": "GROUP_FULL"
}
```
**Giải pháp**: Kiểm tra số lượng thành viên hiện tại

## Code Example (JavaScript)

```javascript
class ZaloPendingMembersManager {
  constructor(apiBaseUrl, authToken) {
    this.apiBaseUrl = apiBaseUrl;
    this.authToken = authToken;
  }

  async acceptPendingMembers(integrationId, groupId, memberUserIds) {
    const response = await fetch(
      `${this.apiBaseUrl}/v1/zalo-group-management/${integrationId}/${groupId}/pending-members/accept`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          memberUserIds: memberUserIds,
        }),
      }
    );
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data;
  }

  async acceptAllPendingMembers(integrationId, groupId) {
    // Lấy danh sách thành viên chờ duyệt
    const pendingResponse = await fetch(
      `${this.apiBaseUrl}/v1/zalo-group-management/${integrationId}/${groupId}/pending-members`,
      {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      }
    );
    
    const pendingData = await pendingResponse.json();
    
    if (!pendingData.success || pendingData.data.members.length === 0) {
      return { acceptedCount: 0, message: 'Không có thành viên chờ duyệt' };
    }
    
    // Duyệt tất cả thành viên
    const memberUserIds = pendingData.data.members.map(member => member.user_id);
    
    // Chia nhỏ nếu quá 100 thành viên
    const batchSize = 100;
    let totalAccepted = 0;
    
    for (let i = 0; i < memberUserIds.length; i += batchSize) {
      const batch = memberUserIds.slice(i, i + batchSize);
      const result = await this.acceptPendingMembers(integrationId, groupId, batch);
      totalAccepted += result.acceptedCount;
    }
    
    return {
      acceptedCount: totalAccepted,
      message: `Đã duyệt ${totalAccepted} thành viên`,
    };
  }
}

// Sử dụng
const pendingManager = new ZaloPendingMembersManager(
  'https://api.example.com',
  'your-jwt-token'
);

// Duyệt thành viên cụ thể
pendingManager.acceptPendingMembers(
  'integration-id',
  'group-id',
  ['8756287263669629130', '1234567890123456789']
).then(result => {
  console.log(`Đã duyệt ${result.acceptedCount} thành viên`);
}).catch(error => {
  console.error('Lỗi:', error.message);
});

// Duyệt tất cả thành viên chờ duyệt
pendingManager.acceptAllPendingMembers('integration-id', 'group-id')
  .then(result => {
    console.log(result.message);
  });
```

## Workflow tích hợp

### 1. Workflow cơ bản
```
1. Lấy danh sách pending members
   GET /pending-members

2. Chọn thành viên cần duyệt
   (User selection hoặc auto-approve)

3. Duyệt thành viên
   POST /pending-members/accept

4. Kiểm tra kết quả
   (Success/Error handling)
```

### 2. Auto-approve workflow
```javascript
async function autoApprovePendingMembers(integrationId, groupId) {
  try {
    // Lấy danh sách chờ duyệt
    const pending = await getPendingMembers(integrationId, groupId);
    
    if (pending.total === 0) {
      return { message: 'Không có thành viên chờ duyệt' };
    }
    
    // Duyệt tất cả
    const userIds = pending.members.map(m => m.user_id);
    const result = await acceptPendingMembers(integrationId, groupId, userIds);
    
    return {
      message: `Đã tự động duyệt ${result.acceptedCount}/${pending.total} thành viên`,
      success: true,
    };
  } catch (error) {
    return {
      message: `Lỗi auto-approve: ${error.message}`,
      success: false,
    };
  }
}
```

## Best Practices

1. **Kiểm tra trước khi duyệt**: Luôn lấy danh sách pending members trước
2. **Batch processing**: Chia nhỏ nếu > 100 thành viên
3. **Error handling**: Xử lý các lỗi phổ biến
4. **Logging**: Log các hoạt động duyệt thành viên
5. **Notification**: Thông báo cho admin khi có lỗi
6. **Rate limiting**: Tránh gọi API quá nhanh
