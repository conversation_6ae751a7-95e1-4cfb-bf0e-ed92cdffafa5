import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { ModelIntegration } from '../entities/model-integration.entity';
import { Integration } from '@modules/integration/entities/integration.entity';

/**
 * Repository cho ModelIntegration entity
 */
@Injectable()
export class ModelIntegrationRepository extends Repository<ModelIntegration> {
  private readonly logger = new Logger(ModelIntegrationRepository.name);

  constructor(private dataSource: DataSource) {
    super(ModelIntegration, dataSource.createEntityManager());
  }

  /**
   * Tạo base query với các select c<PERSON> bản
   * @returns QueryBuilder
   */
  private createBaseQuery() {
    return this.createQueryBuilder('modelIntegration')
      .select([
        'modelIntegration.modelId',
        'modelIntegration.integrationId'
      ]);
  }

  /**
   * Tìm integration theo model ID
   * @param modelId ID của model
   * @returns ModelIntegration hoặc null
   */
  async findByModelId(modelId: string): Promise<ModelIntegration | null> {
    try {
      this.logger.log(`Tìm integration cho model ${modelId}`);

      return this.createBaseQuery()
        .where('modelIntegration.modelId = :modelId', { modelId })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm integration cho model ${modelId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm integration với thông tin chi tiết theo model ID và user ID
   * @param modelId ID của model
   * @param userId ID của user
   * @returns Integration với thông tin chi tiết hoặc null
   */
  async findIntegrationByModelAndUser(
    modelId: string, 
    userId: number
  ): Promise<{
    integration_id: string;
    integration_user_id: number;
    integration_encrypted_config: string;
    integration_secret_key: string;
  } | null> {
    try {
      this.logger.log(`Tìm integration cho model ${modelId} và user ${userId}`);

      const result = await this.createQueryBuilder('modelIntegration')
        .leftJoin(Integration, 'integration', 'integration.id = modelIntegration.integration_id')
        .select([
          'integration.id',
          'integration.user_id',
          'integration.encrypted_config',
          'integration.secret_key'
        ])
        .where('modelIntegration.model_id = :modelId', { modelId })
        .andWhere('integration.user_id = :userId', { userId })
        .andWhere('integration.encrypted_config IS NOT NULL')
        .andWhere('integration.secret_key IS NOT NULL')
        .getRawOne();

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm integration cho model ${modelId} và user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm tất cả model integrations theo integration ID
   * @param integrationId ID của integration
   * @returns Danh sách ModelIntegration
   */
  async findByIntegrationId(integrationId: string): Promise<ModelIntegration[]> {
    try {
      this.logger.log(`Tìm model integrations cho integration ${integrationId}`);

      return this.createBaseQuery()
        .where('modelIntegration.integrationId = :integrationId', { integrationId })
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm model integrations cho integration ${integrationId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo model integration mới
   * @param modelId ID của model
   * @param integrationId ID của integration
   * @returns ModelIntegration đã tạo
   */
  async createModelIntegration(modelId: string, integrationId: string): Promise<ModelIntegration> {
    try {
      this.logger.log(`Tạo model integration cho model ${modelId} và integration ${integrationId}`);

      const modelIntegration = this.create({
        modelId,
        integrationId
      });

      return this.save(modelIntegration);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo model integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa model integration
   * @param modelId ID của model
   * @param integrationId ID của integration
   * @returns Số lượng record đã xóa
   */
  async deleteModelIntegration(modelId: string, integrationId: string): Promise<number> {
    try {
      this.logger.log(`Xóa model integration cho model ${modelId} và integration ${integrationId}`);

      const result = await this.createQueryBuilder()
        .delete()
        .from(ModelIntegration)
        .where('modelId = :modelId', { modelId })
        .andWhere('integrationId = :integrationId', { integrationId })
        .execute();

      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa model integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra xem model integration có tồn tại không
   * @param modelId ID của model
   * @param integrationId ID của integration
   * @returns true nếu tồn tại, false nếu không
   */
  async checkExists(modelId: string, integrationId: string): Promise<boolean> {
    try {
      const count = await this.createQueryBuilder('modelIntegration')
        .where('modelIntegration.modelId = :modelId', { modelId })
        .andWhere('modelIntegration.integrationId = :integrationId', { integrationId })
        .getCount();

      return count > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra model integration: ${error.message}`, error.stack);
      throw error;
    }
  }
}
