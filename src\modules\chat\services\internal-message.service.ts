import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions';

// Import entities
import { InternalConversationMessage } from '../entities/internal-conversation-message.entity';
import { InternalConversationThread } from '../entities/internal-conversation-thread.entity';
import { InternalConversationMessagesAttachment } from '../entities/internal-conversation-messages-attachment.entity';

// Import DTOs
import { QueryMessagesDto } from '../dto';

// Import error codes
import { CHAT_ERROR_CODES } from '../exceptions';
import { CdnService } from '@/shared/services';
import { TimeIntervalEnum } from '@/shared/utils';
import {
  MessageAttachmentResponseDto,
  ThreadMessageResponseDto,
} from '../dto/thread-message-response.dto';

/**
 * Service for managing internal conversation messages
 */
@Injectable()
export class InternalMessageService {
  private readonly logger = new Logger(InternalMessageService.name);

  constructor(
    @InjectRepository(InternalConversationMessage)
    private readonly messageRepository: Repository<InternalConversationMessage>,
    @InjectRepository(InternalConversationThread)
    private readonly threadRepository: Repository<InternalConversationThread>,
    @InjectRepository(InternalConversationMessagesAttachment)
    private readonly attachmentRepository: Repository<InternalConversationMessagesAttachment>,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Get messages in a thread for a user with pagination
   */
  async getUserThreadMessages(
    userId: number,
    threadId: string,
    queryDto: QueryMessagesDto,
  ): Promise<PaginatedResult<ThreadMessageResponseDto>> {
    // First verify thread exists and user has access
    await this.verifyThreadAccess(threadId, userId, undefined);

    return this.getThreadMessages(threadId, queryDto);
  }

  /**
   * Get messages in a thread for an employee with pagination
   */
  async getEmployeeThreadMessages(
    employeeId: number,
    threadId: string,
    queryDto: QueryMessagesDto,
  ): Promise<PaginatedResult<ThreadMessageResponseDto>> {
    // First verify thread exists and employee has access
    await this.verifyThreadAccess(threadId, undefined, employeeId);

    return this.getThreadMessages(threadId, queryDto);
  }

  /**
   * Private method to get messages with pagination using two-query approach
   */
  private async getThreadMessages(
    threadId: string,
    queryDto: QueryMessagesDto,
  ): Promise<PaginatedResult<ThreadMessageResponseDto>> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortDirection = 'ASC', // Default to ASC for chronological order
    } = queryDto;
    const skip = (page - 1) * limit;

    // Query 1: Get main message data with pagination
    const messageQuery = this.messageRepository
      .createQueryBuilder('icm')
      .select([
        'icm.id as message_id',
        'icm.text as message_text',
        'icm.created_at as message_created_at',
        'icm.has_attachments as has_attachments',
        'icm.role as role',
        'icm.is_tool_call_confirm as is_tool_call_confirm',
        'a.avatar as avatar',
      ])
      .where('icm.thread_id = :threadId', { threadId })
      .leftJoin('agents', 'a', 'icm.agent_id = a.id')
      .orderBy(`icm.${sortBy}`, sortDirection as 'ASC' | 'DESC')
      .offset(skip)
      .limit(limit);

    const [messages, totalItems] = await Promise.all([
      messageQuery.getRawMany(),
      this.messageRepository.count({ where: { threadId } }),
    ]);

    // Extract message IDs for messages that have attachments
    const messageIdsWithAttachments = messages
      .filter((msg) => msg.has_attachments)
      .map((msg) => msg.message_id);

    // Query 2: Get attachments for messages that have them
    const attachmentsMap: Map<string, MessageAttachmentResponseDto[]> = new Map();

    if (messageIdsWithAttachments.length > 0) {
      const attachmentQuery = this.attachmentRepository
        .createQueryBuilder('icma')
        .leftJoin('media_data', 'md', 'icma.attachment_id = md.id')
        .leftJoin('knowledge_files', 'kf', 'icma.attachment_id = kf.id')
        .select([
          'icma.message_id as message_id',
          'icma.attachment_id as attachment_id',
          'icma.media_type as attachment_type',
          'md.storage_key as media_storage_key',
          'kf.storage_key as knowledge_storage_key',
        ])
        .where('icma.message_id IN (:...messageIds)', {
          messageIds: messageIdsWithAttachments,
        });

      const attachments = await attachmentQuery.getRawMany();

      // Group attachments by message ID
      attachments.forEach((att) => {
        const messageId = att.message_id;
        if (!attachmentsMap.has(messageId)) {
          attachmentsMap.set(messageId, []);
        }
        const storageKey = att.media_storage_key || att.knowledge_storage_key;
        const viewUrl =
          this.cdnService.generateUrlView(
            storageKey,
            TimeIntervalEnum.FIVE_MINUTES,
          ) ||
          '';
        const attachment: MessageAttachmentResponseDto = {
          attachmentId: att.attachment_id,
          attachmentType: att.attachment_type,
          viewUrl,
        };

        attachmentsMap.get(messageId)!.push(attachment);
      });
    }

    // Build final response DTOs
    const items: ThreadMessageResponseDto[] = messages.map((message) => {
      return {
        avatar: this.cdnService.generateUrlView(
          message.avatar,
          TimeIntervalEnum.ONE_DAY,
        ) || undefined,
        messageId: message.message_id,
        messageText: message.message_text,
        messageCreatedAt: parseInt(message.message_created_at),
        hasAttachments: message.has_attachments,
        role: message.role,
        isToolCallConfirm: message.is_tool_call_confirm,
        attachments:
          message.has_attachments && attachmentsMap.has(message.message_id)
            ? attachmentsMap.get(message.message_id)
            : undefined,
      };
    });

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Verify thread access for user or employee
   */
  private async verifyThreadAccess(
    threadId: string,
    userId?: number,
    employeeId?: number,
  ): Promise<void> {
    const thread = await this.threadRepository.findOne({
      where: {
        id: threadId,
        deletedAt: IsNull(), // Only find non-deleted threads
      },
    });

    if (!thread) {
      throw new AppException(CHAT_ERROR_CODES.THREAD_NOT_FOUND);
    }

    // Check ownership
    const isUserOwner = userId && thread.userId === userId;
    const isEmployeeOwner = employeeId && thread.employeeId === employeeId;

    if (!isUserOwner && !isEmployeeOwner) {
      throw new AppException(CHAT_ERROR_CODES.THREAD_ACCESS_DENIED);
    }
  }
}
