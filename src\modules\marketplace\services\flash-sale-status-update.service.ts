import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { FlashSaleRepository } from '../repositories/flash-sale.repository';
import { FlashSaleStatus } from '../enums/flash-sale-status.enum';
import { Transactional } from 'typeorm-transactional';

/**
 * Service tự động cập nhật trạng thái flash sale dựa trên thời gian
 */
@Injectable()
export class FlashSaleStatusUpdateService {
  private readonly logger = new Logger(FlashSaleStatusUpdateService.name);

  constructor(
    private readonly flashSaleRepository: FlashSaleRepository
  ) {}

  /**
   * ✅ Cron job chạy mỗi phút để cập nhật trạng thái flash sale
   * 🎯 Mục đích: Tự động chuyển SCHEDULED → ACTIVE → EXPIRED theo thời gian
   * 🔧 Đã fix duplicate service registration - chỉ chạy 1 instance
   */
  @Cron(CronExpression.EVERY_MINUTE)
  @Transactional()
  async updateFlashSaleStatuses(): Promise<void> {
    try {
      // Tìm flash sales cần cập nhật trạng thái
      const flashSalesToUpdate = await this.flashSaleRepository.findFlashSalesForStatusTransition();

      // ✅ Chỉ log khi có flash sales cần update
      if (flashSalesToUpdate.length === 0) {
        return; // Không log gì khi không có gì cần update
      }

      const startTime = Date.now();

      const now = Date.now();
      let scheduledToActiveCount = 0;
      let activeToExpiredCount = 0;

      for (const flashSale of flashSalesToUpdate) {
        let newStatus: FlashSaleStatus | null = null;
        let transitionType = '';

        // SCHEDULED → ACTIVE (khi đến startTime)
        if (flashSale.status === FlashSaleStatus.SCHEDULED && flashSale.startTime <= now) {
          newStatus = FlashSaleStatus.ACTIVE;
          transitionType = 'SCHEDULED → ACTIVE';
          scheduledToActiveCount++;
        }
        // ACTIVE → EXPIRED (khi qua endTime)
        else if (flashSale.status === FlashSaleStatus.ACTIVE && flashSale.endTime <= now) {
          newStatus = FlashSaleStatus.EXPIRED;
          transitionType = 'ACTIVE → EXPIRED';
          activeToExpiredCount++;
        }

        if (newStatus) {
          await this.flashSaleRepository.update(flashSale.id, {
            status: newStatus,
            updatedAt: now
          });

          const startDate = new Date(Number(flashSale.startTime)).toLocaleString('vi-VN');
          const endDate = new Date(Number(flashSale.endTime)).toLocaleString('vi-VN');

          this.logger.log(
            `🚀 ${transitionType}: Flash Sale #${flashSale.id} ` +
            `(Product: ${flashSale.productId}, Owner: ${flashSale.userId || `Admin-${flashSale.employeeId}`}) ` +
            `[${startDate} - ${endDate}]`
          );
        }
      }

      const totalUpdated = scheduledToActiveCount + activeToExpiredCount;
      const executionTime = Date.now() - startTime;

      this.logger.log(
        `✅ Flash sale status update completed in ${executionTime}ms: ` +
        `${scheduledToActiveCount} started, ${activeToExpiredCount} expired (${totalUpdated} total)`
      );

    } catch (error) {
      this.logger.error(`❌ Failed to update flash sale statuses: ${error.message}`, error.stack);
    }
  }

  /**
   * ✅ Manual trigger cho testing (optional)
   */
  async triggerStatusUpdate(): Promise<{ updated: number; message: string }> {
    try {
      this.logger.log('Manual flash sale status update triggered');
      
      const flashSalesToUpdate = await this.flashSaleRepository.findFlashSalesForStatusTransition();
      const now = Date.now();
      let updatedCount = 0;

      for (const flashSale of flashSalesToUpdate) {
        let newStatus: FlashSaleStatus | null = null;

        if (flashSale.status === FlashSaleStatus.SCHEDULED && flashSale.startTime <= now) {
          newStatus = FlashSaleStatus.ACTIVE;
        } else if (flashSale.status === FlashSaleStatus.ACTIVE && flashSale.endTime <= now) {
          newStatus = FlashSaleStatus.EXPIRED;
        }

        if (newStatus) {
          await this.flashSaleRepository.update(flashSale.id, {
            status: newStatus,
            updatedAt: now
          });
          updatedCount++;
        }
      }

      return {
        updated: updatedCount,
        message: `Manual status update completed: ${updatedCount} flash sales updated`
      };
    } catch (error) {
      throw new Error(`Manual status update failed: ${error.message}`);
    }
  }

  /**
   * ✅ Get statistics về flash sales cần update (for monitoring)
   */
  async getUpdateStatistics(): Promise<{
    scheduledToActive: number;
    activeToExpired: number;
    total: number;
  }> {
    try {
      const flashSalesToUpdate = await this.flashSaleRepository.findFlashSalesForStatusTransition();
      const now = Date.now();

      let scheduledToActive = 0;
      let activeToExpired = 0;

      for (const flashSale of flashSalesToUpdate) {
        if (flashSale.status === FlashSaleStatus.SCHEDULED && flashSale.startTime <= now) {
          scheduledToActive++;
        } else if (flashSale.status === FlashSaleStatus.ACTIVE && flashSale.endTime <= now) {
          activeToExpired++;
        }
      }

      return {
        scheduledToActive,
        activeToExpired,
        total: scheduledToActive + activeToExpired
      };
    } catch (error) {
      this.logger.error(`Failed to get update statistics: ${error.message}`, error.stack);
      return { scheduledToActive: 0, activeToExpired: 0, total: 0 };
    }
  }
}
