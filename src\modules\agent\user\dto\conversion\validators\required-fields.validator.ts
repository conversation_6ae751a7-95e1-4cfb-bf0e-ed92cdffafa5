import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';
import { ConvertConfigItemDto } from '../convert-config-item.dto';

/**
 * Validator constraint để kiểm tra có đủ field bắt buộc (email và phone)
 */
@ValidatorConstraint({ name: 'hasRequiredFields', async: false })
export class RequiredFieldsConstraint implements ValidatorConstraintInterface {
  validate(convertConfigItems: ConvertConfigItemDto[], args: ValidationArguments) {
    if (!Array.isArray(convertConfigItems)) {
      return false;
    }

    const emailItem = convertConfigItems.find(item =>
      item && item.name && item.name.toLowerCase() === 'customer_email'
    );
    const phoneItem = convertConfigItems.find(item =>
      item && item.name && item.name.toLowerCase() === 'customer_phone'
    );

    // Phải có cả email và phone
    if (!emailItem || !phoneItem) {
      return false;
    }

    // Email và phone phải có deletable = false (không được xóa)
    if (emailItem.deletable !== false || phoneItem.deletable !== false) {
      return false;
    }

    // Email và phone phải required = true
    if (emailItem.required !== true || phoneItem.required !== true) {
      return false;
    }

    return true;
  }

  defaultMessage(args: ValidationArguments) {
    const convertConfigItems = args.value as ConvertConfigItemDto[];

    if (!Array.isArray(convertConfigItems)) {
      return 'Danh sách cấu hình conversion phải là một array';
    }

    const emailItem = convertConfigItems.find(item =>
      item && item.name && item.name.toLowerCase() === 'customer_email'
    );
    const phoneItem = convertConfigItems.find(item =>
      item && item.name && item.name.toLowerCase() === 'customer_phone'
    );

    const errors: string[] = [];

    // Kiểm tra thiếu fields
    if (!emailItem) {
      errors.push('Thiếu field bắt buộc: customer_email');
    }
    if (!phoneItem) {
      errors.push('Thiếu field bắt buộc: customer_phone');
    }

    // Kiểm tra deletable
    if (emailItem && emailItem.deletable !== false) {
      errors.push('Field customer_email không được phép xóa (deletable phải là false)');
    }
    if (phoneItem && phoneItem.deletable !== false) {
      errors.push('Field customer_phone không được phép xóa (deletable phải là false)');
    }

    // Kiểm tra required
    if (emailItem && emailItem.required !== true) {
      errors.push('Field customer_email phải là bắt buộc (required phải là true)');
    }
    if (phoneItem && phoneItem.required !== true) {
      errors.push('Field customer_phone phải là bắt buộc (required phải là true)');
    }

    if (errors.length > 0) {
      return errors.join('; ');
    }

    return 'Field customer_email và customer_phone phải có đầy đủ và không được xóa';
  }
}

/**
 * Decorator để validate có đủ field bắt buộc
 * @param validationOptions Tùy chọn validation
 */
export function HasRequiredFields(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: RequiredFieldsConstraint,
    });
  };
}
