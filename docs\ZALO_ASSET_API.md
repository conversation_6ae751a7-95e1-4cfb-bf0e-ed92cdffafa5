# Zalo Asset ID API Documentation

## Tổng quan

API này cho phép lấy danh sách Asset ID có thể sử dụng để tạo nhóm GMF (Group Management Feature) từ Zalo Official Account.

## Endpoint

### L<PERSON><PERSON> danh sách Asset ID

```
GET /v1/zalo-group-management/{integrationId}/assets
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| integrationId | string | Yes | ID của Integration Zalo OA (UUID format) |

#### Headers

```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

#### Response

**Success Response (200 OK):**

```json
{
  "success": true,
  "message": "L<PERSON>y danh sách Asset ID thành công",
  "data": {
    "assets": [
      {
        "asset_id": "asset_123456789",
        "product_type": "gmf10",
        "quota_type": "sub_quota",
        "valid_through": "10/10/2024",
        "auto_renew": false,
        "status": "available",
        "used_id": null
      },
      {
        "asset_id": "asset_987654321",
        "product_type": "gmf50",
        "quota_type": "purchase_quota",
        "valid_through": "30/11/2024",
        "auto_renew": true,
        "status": "used",
        "used_id": "group_123456"
      }
    ],
    "total": 2
  }
}
```

**Error Response (400 Bad Request):**

```json
{
  "success": false,
  "message": "Integration không tồn tại hoặc không có quyền truy cập",
  "error": "INTEGRATION_NOT_FOUND"
}
```

**Error Response (500 Internal Server Error):**

```json
{
  "success": false,
  "message": "Lỗi khi lấy danh sách Asset ID",
  "error": "INTERNAL_SERVER_ERROR"
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| assets | Array | Danh sách thông tin asset |
| assets[].asset_id | string | ID của asset |
| assets[].product_type | string | Loại sản phẩm (gmf10/gmf50/gmf100) |
| assets[].quota_type | string | Loại quota (sub_quota/purchase_quota/reward_quota) |
| assets[].valid_through | string | Ngày hết hạn của asset_id (format: dd/mm/yyyy) |
| assets[].auto_renew | boolean | Có bật tự động gia hạn hay không |
| assets[].status | string | Trạng thái asset (available/used) |
| assets[].used_id | string\|null | ID của đối tượng sử dụng gói (group_id nếu đã sử dụng) |
| total | number | Tổng số asset có sẵn |

## Sử dụng

### 1. Lấy danh sách Asset ID

```bash
curl -X GET \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-************/assets' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json'
```

### 2. Sử dụng Asset ID để tạo nhóm

Sau khi lấy được danh sách Asset ID, bạn có thể chọn một asset_id có `status = "available"` để sử dụng khi tạo nhóm GMF.

```bash
curl -X POST \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-************' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "groupName": "Nhóm khách hàng VIP",
    "description": "Nhóm dành cho các khách hàng VIP",
    "assetId": "asset_123456789",
    "memberUids": ["user123", "user456"],
    "metadata": {
      "source": "manual",
      "campaign_id": "camp123"
    }
  }'
```

## Lưu ý

1. **Điều kiện sử dụng:**
   - OA phải có gói GMF hoạt động
   - Asset_id được sử dụng để làm dịch vụ hoạt động của nhóm

2. **Chọn Asset ID:**
   - Chỉ sử dụng asset có `status = "available"`
   - Kiểm tra `valid_through` để đảm bảo asset chưa hết hạn
   - Các loại sản phẩm GMF:
     - `gmf10`: Nhóm chat tối đa 10 thành viên
     - `gmf50`: Nhóm chat tối đa 50 thành viên
     - `gmf100`: Nhóm chat tối đa 100 thành viên

3. **Loại Quota:**
   - `sub_quota`: Hạn mức từ gói OA (Gói Nâng cao/Gói Premium)
   - `purchase_quota`: Hạn mức từ việc mua lẻ tính năng
   - `reward_quota`: Hạn mức được tặng

4. **Error Handling:**
   - Nếu không có asset nào khả dụng, API sẽ trả về danh sách rỗng
   - Kiểm tra quyền truy cập Integration trước khi gọi API
   - API gọi trực tiếp đến Zalo endpoint với POST method

## Ví dụ Integration

```typescript
// Service để lấy Asset ID
async getAvailableAssets(integrationId: string): Promise<ZaloAssetListDto> {
  const response = await this.httpService.get(
    `/v1/zalo-group-management/${integrationId}/assets`,
    {
      headers: {
        Authorization: `Bearer ${this.jwtToken}`,
      },
    }
  );
  
  return response.data.data;
}

// Chọn asset có sẵn để sử dụng
function selectBestAsset(assets: ZaloAssetDto[]): string | null {
  const availableAssets = assets.filter(asset => asset.status === 'available');

  if (availableAssets.length === 0) {
    return null;
  }

  // Chọn asset đầu tiên có sẵn
  return availableAssets[0].asset_id;
}
```
