# Zalo Upload System - Tổ<PERSON> quan hệ thống

## 🎯 Mục tiêu

Xây dựng hệ thống upload file cho Zalo Official Account với:
- ✅ Validation nghiêm ngặt theo yêu cầu Zalo API
- ✅ Queue system cho xử lý bất đồng bộ
- ✅ Monitoring và logging chi tiết
- ✅ Error handling và user experience tốt

## 🏗️ Kiến trúc hệ thống

### **1. Upload Flow**
```
Client → Validation → Queue → Worker → Zalo API → Database → Response
```

### **2. Components**

#### **A. Validation Layer**
- **ZaloUploadValidationPipe**: Validate file format, size, name
- **ZaloFileValidatorUtil**: Utility functions cho validation
- **ZaloUploadLoggerMiddleware**: Log validation attempts

#### **B. Queue System**
- **QueueService**: Manage job creation
- **ZaloUploadWorker**: Process upload jobs
- **Job Types**: UPLOAD_IMAGE, UPLOAD_FILE, UPLOAD_GIF

#### **C. API Layer**
- **ZaloUploadController**: Handle HTTP requests
- **Sync/Async endpoints**: Direct upload vs Queue processing

## 📋 File Format Support

### **Zalo API Requirements**
| Format | Extension | MIME Type | Max Size |
|--------|-----------|-----------|----------|
| PDF | .pdf | application/pdf | 5MB |
| Word 97-2003 | .doc | application/msword | 5MB |
| Word 2007+ | .docx | application/vnd.openxmlformats-officedocument.wordprocessingml.document | 5MB |
| CSV | .csv | text/csv, application/csv | 5MB |

### **Validation Rules**
1. **Dual Check**: Both MIME type and file extension
2. **Size Limit**: Maximum 5MB per file
3. **Filename**: Must be valid and not empty
4. **Error Messages**: Clear, actionable feedback

## 🔧 API Endpoints

### **1. Upload File (Sync)**
```
POST /v1/marketing/zalo/upload/{integrationId}/upload/file
```

### **2. Upload GIF (Async via Queue)**
```
POST /v1/marketing/zalo/upload/{integrationId}/upload/gif
```

### **3. Check Job Status**
```
GET /v1/marketing/zalo/upload/job/{jobId}/status
```

### **4. Upload History**
```
GET /v1/marketing/zalo/upload/{integrationId}/uploads
```

## 🚦 Error Handling

### **Validation Errors**
```json
{
  "success": false,
  "code": 9999,
  "message": "File phải có định dạng PDF, DOC, DOCX hoặc CSV (theo yêu cầu Zalo API). Hãy thử xuất Excel thành CSV"
}
```

### **Zalo API Errors**
```json
{
  "success": false,
  "code": 9999,
  "message": "Lỗi từ Zalo API: file is invalid. We only support pdf, doc, csv"
}
```

## 📊 Monitoring & Logging

### **Upload Logger Middleware**
- Log file details (name, size, MIME type)
- Validation results
- Response times
- Success/failure rates

### **Queue Monitoring**
- Job states (waiting, active, completed, failed)
- Processing times
- Retry attempts
- Error tracking

## 🧪 Testing

### **Validation Test Script**
```bash
node scripts/test-zalo-file-validation.js
```

### **Test Cases**
- ✅ Valid formats: PDF, DOC, DOCX, CSV
- ❌ Invalid formats: XLSX, PPTX, TXT, JPG
- ❌ Size limits: Files > 5MB
- ❌ Extension mismatches

## 📁 File Structure

```
src/modules/marketing/user/
├── controllers/
│   └── zalo-upload.controller.ts
├── pipes/
│   └── zalo-upload-validation.pipe.ts
├── middleware/
│   └── zalo-upload-logger.middleware.ts
├── utils/
│   └── zalo-file-validator.util.ts
├── entities/
│   └── zalo-upload.entity.ts
└── repositories/
    └── zalo-upload.repository.ts

src/shared/queue/
├── workers/
│   └── zalo-upload.worker.ts
├── queue.service.ts
├── queue.types.ts
└── queue.constants.ts

docs/
├── ZALO_FILE_VALIDATION.md
├── ZALO_UPLOAD_QUEUE.md
└── ZALO_UPLOAD_SYSTEM_SUMMARY.md

scripts/
└── test-zalo-file-validation.js
```

## 🔄 Migration Path

### **Phase 1: Validation Fix** ✅
- Updated validation to match Zalo requirements
- Added detailed error messages
- Implemented logging middleware

### **Phase 2: Queue System** ✅
- Added async processing for GIF uploads
- Implemented job status tracking
- Added worker for background processing

### **Phase 3: Monitoring** ✅
- Added comprehensive logging
- Created test scripts
- Documentation and utilities

## 🚀 Deployment Checklist

### **Environment Setup**
- [ ] Redis server running
- [ ] Queue workers started
- [ ] Environment variables configured

### **Validation**
- [ ] Test all supported file formats
- [ ] Verify error messages
- [ ] Check file size limits

### **Queue System**
- [ ] Test job creation
- [ ] Verify worker processing
- [ ] Monitor queue health

### **Monitoring**
- [ ] Check logs for validation attempts
- [ ] Monitor upload success rates
- [ ] Set up alerts for failures

## 📈 Performance Metrics

### **Success Metrics**
- Upload success rate > 95%
- Validation accuracy 100%
- Queue processing time < 30s
- Error rate < 5%

### **Monitoring Points**
- File format distribution
- Upload volume trends
- Error pattern analysis
- Queue performance metrics

## 🔮 Future Enhancements

### **Planned Features**
- [ ] Batch file upload
- [ ] File compression optimization
- [ ] Advanced queue prioritization
- [ ] Real-time upload progress

### **Optimization Opportunities**
- [ ] Caching validation results
- [ ] Parallel processing
- [ ] Smart retry strategies
- [ ] Predictive error handling

## 📞 Support & Troubleshooting

### **Common Issues**
1. **File format rejected**: Check Zalo API requirements
2. **Queue stuck**: Restart workers
3. **Upload timeout**: Check file size and network
4. **Validation false positive**: Review MIME type mapping

### **Debug Commands**
```bash
# Check queue status
curl GET /v1/marketing/zalo/upload/job/{jobId}/status

# View logs
tail -f logs/upload.log

# Test validation
node scripts/test-zalo-file-validation.js --format pdf
```

Hệ thống upload Zalo đã sẵn sàng production với validation chính xác và queue processing hiệu quả! 🎉
