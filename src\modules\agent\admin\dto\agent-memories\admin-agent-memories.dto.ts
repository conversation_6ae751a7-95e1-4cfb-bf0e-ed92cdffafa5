import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  IsString, 
  IsOptional, 
  IsObject, 
  ValidateNested, 
  IsUUID,
  IsArray,
  IsNumber,
  <PERSON>,
  Max
} from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto';
import { StructuredContentInterface } from '@modules/agent/interfaces/agent-memory.interface';

/**
 * DTO cho structured content của admin agent memory
 */
export class AdminAgentMemoryStructuredContentDto implements StructuredContentInterface {
  @ApiPropertyOptional({
    description: 'Tiêu đề của memory',
    example: 'JavaScript Programming Advanced',
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Lý do ghi nhớ thông tin này',
    example: 'Để hỗ trợ lập trình web hiệu quả hơn',
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiProperty({
    description: 'Nội dung chính của memory',
    example: '<PERSON><PERSON><PERSON> thức về JavaScript ES6+, async/await, và modern frameworks',
  })
  @IsString()
  content: string;
}

/**
 * DTO để tạo admin agent memory mới
 */
export class CreateAdminAgentMemoryDto {
  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: AdminAgentMemoryStructuredContentDto,
    example: {
      title: 'JavaScript Programming Advanced',
      content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
      reason: 'Để hỗ trợ lập trình web hiệu quả hơn'
    }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => AdminAgentMemoryStructuredContentDto)
  structuredContent: AdminAgentMemoryStructuredContentDto;
}

/**
 * DTO để cập nhật admin agent memory
 */
export class UpdateAdminAgentMemoryDto {
  @ApiPropertyOptional({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: AdminAgentMemoryStructuredContentDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AdminAgentMemoryStructuredContentDto)
  structuredContent?: AdminAgentMemoryStructuredContentDto;
}

/**
 * DTO cho query danh sách admin agent memories
 */
export class QueryAdminAgentMemoryDto extends QueryDto {
}

/**
 * DTO response cho admin agent memory
 */
export class AdminAgentMemoryResponseDto {
  @ApiProperty({
    description: 'UUID của memory',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'UUID của agent',
    example: '456e7890-e89b-12d3-a456-************',
  })
  agentId: string;

  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: AdminAgentMemoryStructuredContentDto,
  })
  structuredContent: AdminAgentMemoryStructuredContentDto;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1703120000000,
  })
  createdAt: number;
}
