import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  Index,
  BeforeUpdate 
} from 'typeorm';

/**
 * Entity đại diện cho bảng workflow_nodes trong cơ sở dữ liệu
 * Lưu trữ các node instances trong một workflow cụ thể
 */
@Entity('workflow_nodes')
@Index('idx_workflow_nodes_workflow_id', ['workflowId'])
@Index('idx_workflow_nodes_node_type', ['nodeType'])
export class WorkflowNode {
  /**
   * ID định danh duy nhất cho một workflow node
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Khóa ngoại, liên kết đến workflow chứa node này
   */
  @Column({ name: 'workflow_id', type: 'uuid', nullable: false })
  workflowId: string;

  /**
   * ID của node trong workflow (unique trong một workflow)
   */
  @Column({ name: 'node_id', type: 'varchar', length: 255, nullable: false })
  nodeId: string;

  /**
   * Loại node, tham chiếu đến node_definitions.type
   */
  @Column({ name: 'node_type', type: 'varchar', length: 100, nullable: false })
  nodeType: string;

  /**
   * Tên hiển thị của node instance
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * Mô tả của node instance
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Cấu hình của node instance (input parameters, settings)
   */
  @Column({ name: 'config', type: 'jsonb', default: () => "'{}'::jsonb", nullable: false })
  config: Record<string, any>;

  /**
   * Vị trí của node trên canvas (x, y coordinates)
   */
  @Column({ name: 'position', type: 'jsonb', default: () => "'{\"x\": 0, \"y\": 0}'::jsonb", nullable: false })
  position: { x: number; y: number };

  /**
   * Metadata bổ sung cho node (UI state, annotations, etc.)
   */
  @Column({ name: 'metadata', type: 'jsonb', default: () => "'{}'::jsonb", nullable: false })
  metadata: Record<string, any>;

  /**
   * Thời gian tạo node, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật node lần cuối, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = Date.now();
  }
}
