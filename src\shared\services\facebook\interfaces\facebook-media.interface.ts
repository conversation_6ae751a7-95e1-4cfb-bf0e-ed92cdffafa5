/**
 * Interface cho Ad Image
 */
export interface AdImage {
  hash: string;
  url: string;
  url_128: string;
  width: number;
  height: number;
  original_width: number;
  original_height: number;
  permalink_url: string;
  name?: string;
  status?: string;
  account_id?: string;
  created_time?: string;
  updated_time?: string;
  creatives?: string[];
  filename?: string;
  is_associated_creatives_in_adgroups?: boolean;
  zipbytes?: number;
}

/**
 * Interface cho Ad Video
 */
export interface AdVideo {
  id: string;
  title?: string;
  description?: string;
  length: number;
  created_time: string;
  updated_time?: string;
  status: VideoStatus;
  source: string;
  picture: string;
  permalink_url: string;
  thumbnails?: VideoThumbnail[];
  format?: VideoFormat[];
  embeddable?: boolean;
  upload_phase?: VideoUploadPhase;
  upload_session_id?: string;
  file_url?: string;
  file_size?: number;
  content_category?: string;
  content_tags?: string[];
  custom_labels?: string[];
  is_crosspost_video?: boolean;
  is_crossposting_eligible?: boolean;
  is_episode?: boolean;
  is_instagram_eligible?: boolean;
  is_reference_only?: boolean;
  post_id?: string;
  published?: boolean;
  scheduled_publish_time?: string;
  secret?: boolean;
  spherical?: boolean;
  universal_video_id?: string;
  ad_breaks?: AdBreak[];
  backdated_time?: string;
  backdated_time_granularity?: string;
  call_to_action?: VideoCallToAction;
  copyright_monitoring_status?: string;
  custom_thumbnail?: string;
  live_status?: string;
  premiere_living_room_status?: string;
  privacy?: Privacy;
  slideshow_spec?: SlideshowSpec;
  sponsor_relationship?: string;
  targeting?: Targeting;
  transcode_setting?: TranscodeSetting;
}

/**
 * Enum cho Video Status
 */
export enum VideoStatus {
  READY = 'ready',
  PROCESSING = 'processing',
  ERROR = 'error',
  UPLOADED = 'uploaded'
}

/**
 * Enum cho Video Upload Phase
 */
export enum VideoUploadPhase {
  START = 'start',
  TRANSFER = 'transfer',
  FINISH = 'finish',
  CANCEL = 'cancel'
}

/**
 * Interface cho Video Thumbnail
 */
export interface VideoThumbnail {
  id: string;
  uri: string;
  width: number;
  height: number;
  scale: number;
  is_preferred: boolean;
}

/**
 * Interface cho Video Format
 */
export interface VideoFormat {
  embed_html: string;
  filter: string;
  height: number;
  width: number;
  picture: string;
}

/**
 * Interface cho Ad Break
 */
export interface AdBreak {
  offset_time_millis: number;
  playtime_millis: number;
}

/**
 * Interface cho Call To Action (Video)
 */
export interface VideoCallToAction {
  type: string;
  value?: {
    link?: string;
    link_caption?: string;
    link_description?: string;
    link_title?: string;
    page?: string;
    product_link?: string;
    lead_gen_form_id?: string;
    app_destination?: string;
    app_link?: string;
    application?: string;
    event_id?: string;
    offer_id?: string;
  };
}

/**
 * Interface cho Privacy
 */
export interface Privacy {
  value: 'EVERYONE' | 'ALL_FRIENDS' | 'FRIENDS_OF_FRIENDS' | 'SELF' | 'CUSTOM';
  description?: string;
  friends?: string;
  networks?: string;
  allow?: string;
  deny?: string;
}

/**
 * Interface cho Slideshow Spec
 */
export interface SlideshowSpec {
  images_urls: string[];
  duration_ms?: number;
  transition_ms?: number;
}

/**
 * Interface cho Targeting
 */
export interface Targeting {
  geo_locations?: GeoLocation;
  age_min?: number;
  age_max?: number;
  genders?: number[];
  interests?: TargetingEntry[];
  behaviors?: TargetingEntry[];
  life_events?: TargetingEntry[];
  relationship_statuses?: number[];
  interested_in?: number[];
  education_statuses?: number[];
  college_years?: number[];
  education_schools?: TargetingEntry[];
  work_employers?: TargetingEntry[];
  work_positions?: TargetingEntry[];
  locales?: number[];
  generation?: TargetingEntry[];
  family_statuses?: TargetingEntry[];
  income?: TargetingEntry[];
  home_ownership?: TargetingEntry[];
  home_type?: TargetingEntry[];
  home_value?: TargetingEntry[];
  ethnic_affinity?: TargetingEntry[];
  politics?: TargetingEntry[];
  markets?: TargetingEntry[];
  industries?: TargetingEntry[];
  life_events_exclude?: TargetingEntry[];
  behaviors_exclude?: TargetingEntry[];
  interests_exclude?: TargetingEntry[];
  custom_audiences?: TargetingEntry[];
  excluded_custom_audiences?: TargetingEntry[];
  lookalike_audiences?: TargetingEntry[];
  excluded_lookalike_audiences?: TargetingEntry[];
  connections?: TargetingEntry[];
  excluded_connections?: TargetingEntry[];
  friends_of_connections?: TargetingEntry[];
  excluded_friends_of_connections?: TargetingEntry[];
  user_adclusters?: TargetingEntry[];
  excluded_user_adclusters?: TargetingEntry[];
  user_device?: string[];
  mobile_device_model?: string[];
  device_platforms?: string[];
  publisher_platforms?: string[];
  facebook_positions?: string[];
  instagram_positions?: string[];
  audience_network_positions?: string[];
  messenger_positions?: string[];
  excluded_publisher_categories?: string[];
  excluded_publisher_list_ids?: string[];
  publisher_visibility_categories?: string[];
  place_page_set_ids?: string[];
  page_types?: string[];
  user_os?: string[];
  wireless_carrier?: string[];
  brand_safety_content_filter_levels?: string[];
}

/**
 * Interface cho Geo Location
 */
export interface GeoLocation {
  countries?: string[];
  regions?: GeoLocationEntry[];
  cities?: GeoLocationEntry[];
  zips?: GeoLocationEntry[];
  places?: GeoLocationEntry[];
  custom_locations?: CustomLocation[];
  geo_markets?: GeoLocationEntry[];
  location_types?: string[];
}

/**
 * Interface cho Geo Location Entry
 */
export interface GeoLocationEntry {
  key: string;
  name?: string;
  type?: string;
  country_code?: string;
  country_name?: string;
  region?: string;
  region_id?: string;
  primary_city?: string;
  primary_city_id?: string;
  radius?: number;
  distance_unit?: 'mile' | 'kilometer';
}

/**
 * Interface cho Custom Location
 */
export interface CustomLocation {
  latitude: number;
  longitude: number;
  radius: number;
  distance_unit: 'mile' | 'kilometer';
  address_string?: string;
  primary_city_id?: string;
  region_id?: string;
  country?: string;
}

/**
 * Interface cho Targeting Entry
 */
export interface TargetingEntry {
  id: string;
  name?: string;
}

/**
 * Interface cho Transcode Setting
 */
export interface TranscodeSetting {
  preferred_quality?: 'low' | 'medium' | 'high';
}

/**
 * Interface cho yêu cầu upload Ad Image
 */
export interface UploadAdImageRequest {
  filename: string;
  bytes?: Buffer;
  copy_from?: {
    ad_image_hash: string;
  };
}

/**
 * Interface cho phản hồi upload Ad Image
 */
export interface UploadAdImageResponse {
  images: {
    [filename: string]: AdImage;
  };
}

/**
 * Interface cho yêu cầu upload Ad Video
 */
export interface UploadAdVideoRequest {
  title?: string;
  description?: string;
  file_url?: string;
  file_size?: number;
  upload_phase?: VideoUploadPhase;
  upload_session_id?: string;
  video_file_chunk?: Buffer;
  start_offset?: number;
  end_offset?: number;
  content_category?: string;
  embeddable?: boolean;
  published?: boolean;
  scheduled_publish_time?: string;
  secret?: boolean;
  spherical?: boolean;
  slideshow_spec?: SlideshowSpec;
  thumb?: string;
  transcode_setting?: TranscodeSetting;
  upload_setting_properties?: UploadSettingProperties;
  composer_session_id?: string;
  adaptive_type?: string;
  animated_effect_id?: string;
  application_id?: string;
  asked_fun_fact_prompt_id?: string;
  audio_story_wave_animation_handle?: string;
  chunk_session_id?: string;
  composer_entry_picker?: string;
  composer_entry_point?: string;
  composer_entry_time?: number;
  composer_session_events_log?: string;
  composer_source_surface?: string;
  composer_type?: string;
  formatting?: string;
  freeform_tags?: string[];
  fun_fact_prompt_id?: string;
  fun_fact_toastee_id?: string;
  is_group_linking_post?: boolean;
  is_voice_clip?: boolean;
  location_source_id?: string;
  offer_like_post_id?: string;
  og_action_type_id?: string;
  og_hide_object_attachment?: boolean;
  og_icon_id?: string;
  og_object_id?: string;
  og_phrase?: string;
  og_set_profile_badge?: boolean;
  og_suggestion_mechanism?: string;
  original_fov?: number;
  original_projection_type?: string;
  react_mode_metadata?: string;
  sales_promo_id?: string;
  text_format_metadata?: string;
  throwback_camera_roll_media?: string;
  video_start_time_ms?: number;
  voice_clip_creation_attribution?: string;
}

/**
 * Interface cho Upload Setting Properties
 */
export interface UploadSettingProperties {
  video_file_chunk_length_in_bytes?: number;
  start_offset?: number;
  end_offset?: number;
  file_size?: number;
}

/**
 * Interface cho phản hồi upload Ad Video
 */
export interface UploadAdVideoResponse {
  id: string;
  upload_session_id?: string;
  video_id?: string;
  start_offset?: number;
  end_offset?: number;
  success?: boolean;
}

/**
 * Interface cho phản hồi danh sách Ad Images
 */
export interface GetAdImagesResponse {
  data: AdImage[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
    previous?: string;
  };
}

/**
 * Interface cho phản hồi danh sách Ad Videos
 */
export interface GetAdVideosResponse {
  data: AdVideo[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
    previous?: string;
  };
}

/**
 * Interface cho Video Upload Status
 */
export interface VideoUploadStatus {
  video_id: string;
  phase: VideoUploadPhase;
  upload_session_id?: string;
  start_offset?: number;
  end_offset?: number;
  upload_progress?: number;
  processing_progress?: number;
  errors?: VideoUploadError[];
}

/**
 * Interface cho Video Upload Error
 */
export interface VideoUploadError {
  code: number;
  message: string;
  error_subcode?: number;
  error_user_title?: string;
  error_user_msg?: string;
  fbtrace_id?: string;
}

/**
 * Interface cho yêu cầu tạo Video Thumbnail
 */
export interface CreateVideoThumbnailRequest {
  is_preferred?: boolean;
  time_offset_millis?: number;
}

/**
 * Interface cho phản hồi tạo Video Thumbnail
 */
export interface CreateVideoThumbnailResponse {
  id: string;
  uri: string;
  width: number;
  height: number;
  is_preferred: boolean;
}

/**
 * Interface cho Video Stats
 */
export interface VideoStats {
  video_id: string;
  total_video_views?: number;
  total_video_views_unique?: number;
  total_video_10s_views?: number;
  total_video_15s_views?: number;
  total_video_30s_views?: number;
  total_video_60s_excludes_shorter_views?: number;
  total_video_avg_time_watched?: number;
  total_video_complete_views?: number;
  total_video_complete_views_unique?: number;
  total_video_impressions?: number;
  total_video_impressions_unique?: number;
  total_video_view_time?: number;
  total_video_view_time_by_age_bucket_and_gender?: Record<string, number>;
  total_video_view_time_by_region_id?: Record<string, number>;
  total_video_views_by_distribution_type?: Record<string, number>;
  total_video_views_autoplayed?: number;
  total_video_views_clicked_to_play?: number;
  total_video_views_sound_on?: number;
}
