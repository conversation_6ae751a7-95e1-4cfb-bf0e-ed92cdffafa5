import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, IsObject, ValidateNested, MaxLength, MinLength } from 'class-validator';

/**
 * DTO cho workflow definition
 * Synced với FE-001 WorkflowDefinition type
 */
export class WorkflowDefinitionDto {
  /**
   * Danh sách nodes trong workflow
   */
  @ApiProperty({
    description: 'Danh sách nodes trong workflow',
    example: [],
    type: 'array',
    items: { type: 'object' },
  })
  @IsObject({ each: true })
  nodes: any[];

  /**
   * Danh sách edges kết nối các nodes
   */
  @ApiProperty({
    description: 'Danh sách edges kết nối các nodes',
    example: [],
    type: 'array',
    items: { type: 'object' },
  })
  @IsObject({ each: true })
  edges: any[];

  /**
   * Metadata cho UI (viewport, zoom, etc.)
   */
  @ApiPropertyOptional({
    description: 'Metadata cho UI (viewport, zoom, etc.)',
    example: { x: 0, y: 0, zoom: 1 },
  })
  @IsOptional()
  @IsObject()
  metadata?: any;
}

/**
 * DTO cho việc tạo workflow mới
 * Following agent create-agent.dto patterns
 */
export class CreateWorkflowDto {
  /**
   * Tên của workflow (phải unique cho mỗi user)
   */
  @ApiProperty({
    description: 'Tên của workflow (phải unique cho mỗi user)',
    example: 'My First Workflow',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @MinLength(1, { message: 'Tên workflow không được để trống' })
  @MaxLength(255, { message: 'Tên workflow không được vượt quá 255 ký tự' })
  name: string;

  /**
   * Trạng thái kích hoạt của workflow
   */
  @ApiPropertyOptional({
    description: 'Trạng thái kích hoạt của workflow',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  /**
   * Định nghĩa workflow (nodes, edges, metadata)
   */
  @ApiPropertyOptional({
    description: 'Định nghĩa workflow (nodes, edges, metadata)',
    type: WorkflowDefinitionDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowDefinitionDto)
  definition?: WorkflowDefinitionDto;
}
