import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { ApiResponseDto } from '@common/response';
import { MODELS_ERROR_CODES } from '../../exceptions';
import { AdminDataFineTuneRepository } from '../../repositories/admin-data-fine-tune.repository';
import { DataFineTuneStatus } from '../../constants/data-fine-tune-status.enum';
import { ProviderFineTuneEnum } from '../../constants/provider.enum';
import {
  AdminValidateFineTuningJobDto,
  AdminFineTuningValidationResponseDto,
} from '../dto/fine-tuning-validation';
import * as crypto from 'crypto';

/**
 * Service xử lý validation cho admin fine-tuning jobs
 * Chỉ sử dụng system keys, không có R-point logic
 */
@Injectable()
export class AdminFineTuningValidationService {
  private readonly logger = new Logger(AdminFineTuningValidationService.name);

  constructor(
    private readonly adminDataFineTuneRepository: AdminDataFineTuneRepository,
  ) {}

  // /**
  //  * Validate admin fine-tuning job và ước tính thời gian
  //  */
  // async validateFineTuningJob(
  //   employeeId: number,
  //   dto: AdminValidateFineTuningJobDto,
  // ): Promise<ApiResponseDto<AdminFineTuningValidationResponseDto>> {
  //   try {
  //     this.logger.log(`Validating admin fine-tuning job for employee ${employeeId}`);

  //     const validationErrors: string[] = [];
  //     let estimatedDurationMinutes = 0;

  //     // 1. Validate admin dataset
  //     const datasetInfo = await this.validateAdminDataset(dto.datasetId, dto.provider, validationErrors);

  //     // 2. Validate system model
  //     const modelInfo = await this.validateSystemModel(dto.systemModelId, dto.provider, validationErrors);

  //     // 3. Validate system key
  //     const systemKeyInfo = await this.validateSystemKey(dto.provider, validationErrors);

  //     // 4. Tính toán thời gian ước tính nếu validation thành công
  //     if (validationErrors.length === 0 && datasetInfo.totalExamples > 0) {
  //       estimatedDurationMinutes = this.calculateEstimatedDuration(
  //         datasetInfo.estimatedTokens,
  //         dto.provider,
  //       );
  //     }

  //     // 5. Tạo validation token nếu thành công
  //     const validationToken = validationErrors.length === 0 
  //       ? this.generateValidationToken(employeeId, dto)
  //       : '';

  //     const response: AdminFineTuningValidationResponseDto = {
  //       isValid: validationErrors.length === 0,
  //       validationErrors,
  //       estimatedDurationMinutes,
  //       datasetInfo,
  //       modelInfo,
  //       systemKeyInfo,
  //       validationToken,
  //     };

  //     this.logger.log(`Admin fine-tuning validation completed for employee ${employeeId}`);
  //     return ApiResponseDto.success(response, 'Validation completed successfully');
  //   } catch (error) {
  //     this.logger.error(`Error validating admin fine-tuning job for employee ${employeeId}:`, error);
  //     throw error;
  //   }
  // }

  // /**
  //  * Validate admin dataset
  //  */
  // private async validateAdminDataset(
  //   datasetId: string,
  //   provider: ProviderFineTuneEnum,
  //   validationErrors: string[],
  // ) {
  //   const dataset = await this.adminDataFineTuneRepository.findByIdWithFullData(datasetId);
    
  //   if (!dataset) {
  //     validationErrors.push('Admin dataset không tồn tại');
  //     return {
  //       id: datasetId,
  //       name: '',
  //       totalExamples: 0,
  //       estimatedTokens: 0,
  //       provider: provider,
  //     };
  //   }

  //   if (dataset.status !== DataFineTuneStatus.APPROVED) {
  //     validationErrors.push('Admin dataset chưa được approve');
  //   }

  //   if (!dataset.isValid) {
  //     validationErrors.push('Admin dataset chưa được validate');
  //   }

  //   if (dataset.provider !== provider) {
  //     validationErrors.push(`Admin dataset provider (${dataset.provider}) không khớp với provider yêu cầu (${provider})`);
  //   }

  //   // Ước tính số examples từ estimated tokens (giả sử trung bình 50 tokens/example)
  //   const estimatedExamples = Math.floor(dataset.estimatedToken / 50);

  //   return {
  //     id: dataset.id,
  //     name: dataset.name,
  //     totalExamples: estimatedExamples,
  //     estimatedTokens: dataset.estimatedToken,
  //     provider: dataset.provider,
  //   };
  // }

  // /**
  //  * Validate system model
  //  */
  // private async validateSystemModel(
  //   systemModelId: string,
  //   provider: ProviderFineTuneEnum,
  //   validationErrors: string[],
  // ) {
  //   const systemModel = await this.systemModelsRepository.findById(systemModelId);
    
  //   if (!systemModel) {
  //     validationErrors.push('System model không tồn tại');
  //     return {
  //       id: systemModelId,
  //       modelId: '',
  //       provider: provider,
  //       trainingPricing: 0,
  //     };
  //   }

  //   if (!systemModel.active) {
  //     validationErrors.push('System model không active');
  //   }

  //   if (systemModel.provider !== provider) {
  //     validationErrors.push(`System model provider (${systemModel.provider}) không khớp với provider yêu cầu (${provider})`);
  //   }

  //   // Note: Giả sử tất cả system models đều hỗ trợ fine-tuning
  //   // Có thể thêm field supportFineTuning vào entity sau

  //   return {
  //     id: systemModel.id,
  //     modelId: systemModel.modelId,
  //     provider: systemModel.provider,
  //     trainingPricing: 0, // Default pricing, có thể thêm field vào entity sau
  //   };
  // }

  // /**
  //  * Validate system key cho provider
  //  */
  // private async validateSystemKey(
  //   provider: ProviderFineTuneEnum,
  //   validationErrors: string[],
  // ) {
  //   const systemKey = await this.systemKeyLlmRepository.findDefaultByProvider(provider as any);
    
  //   if (!systemKey) {
  //     validationErrors.push(`Không tìm thấy system key active cho provider ${provider}`);
  //     return {
  //       keyId: '',
  //       keyName: '',
  //       provider: provider,
  //     };
  //   }

  //   if (systemKey.deletedAt) {
  //     validationErrors.push(`System key cho provider ${provider} đã bị xóa`);
  //   }

  //   return {
  //     keyId: systemKey.id,
  //     keyName: systemKey.name,
  //     provider: systemKey.provider,
  //   };
  // }

  // /**
  //  * Tính toán thời gian ước tính dựa trên số tokens và provider
  //  */
  // private calculateEstimatedDuration(
  //   estimatedTokens: number,
  //   provider: ProviderFineTuneEnum,
  // ): number {
  //   // Ước tính thời gian dựa trên provider và số tokens
  //   let baseMinutesPerToken = 0.001; // 1ms per token

  //   switch (provider) {
  //     case ProviderFineTuneEnum.OPENAI:
  //       baseMinutesPerToken = 0.0008; // OpenAI nhanh hơn
  //       break;
  //     case ProviderFineTuneEnum.GOOGLE:
  //       baseMinutesPerToken = 0.0012; // Google chậm hơn
  //       break;
  //     default:
  //       baseMinutesPerToken = 0.001;
  //   }

  //   const estimatedMinutes = Math.ceil(estimatedTokens * baseMinutesPerToken);
    
  //   // Tối thiểu 5 phút, tối đa 180 phút
  //   return Math.max(5, Math.min(180, estimatedMinutes));
  // }

  // /**
  //  * Tạo validation token cho admin
  //  */
  // private generateValidationToken(
  //   employeeId: number,
  //   dto: AdminValidateFineTuningJobDto,
  // ): string {
  //   const payload = {
  //     employeeId,
  //     datasetId: dto.datasetId,
  //     systemModelId: dto.systemModelId,
  //     provider: dto.provider,
  //     timestamp: Date.now(),
  //     type: 'admin_fine_tuning',
  //   };

  //   const payloadString = JSON.stringify(payload);
  //   const hash = crypto.createHash('sha256').update(payloadString).digest('hex');
    
  //   return `admin_val_${hash.substring(0, 16)}`;
  // }

  // /**
  //  * Verify validation token
  //  */
  // async verifyValidationToken(
  //   token: string,
  //   employeeId: number,
  //   dto: AdminValidateFineTuningJobDto,
  // ): Promise<boolean> {
  //   try {
  //     const expectedToken = this.generateValidationToken(employeeId, dto);
  //     return token === expectedToken;
  //   } catch (error) {
  //     this.logger.error('Error verifying validation token:', error);
  //     return false;
  //   }
  // }
}
