import { SwaggerApiResponse } from '@/common/decorators/swagger-api-response.decorator';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { CurrentEmployee } from '@/modules/auth/decorators/current-employee.decorator';
import { JWTPayloadEmployee } from '@/modules/auth/interfaces';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  AssignEmployeeRoleDto, AvatarUploadResponseDto, ChangeEmployeePasswordDto, ChangePasswordResponseDto, CreateEmployeeDto, CreateEmployeeResponseDto, EmployeeAvatarUploadDto,
  EmployeeQueryDto, EmployeeResponseDto, EmployeeRoleResponseDto, UpdateEmployeeAvatarDto, EmployeeOverviewDto, RoleCountDto,
  UpdateEmployeeProfileDto, ChangeEmployeePasswordSelfDto, UpdateEmployeeProfileResponseDto, UpdateEmployeeDto
} from '@modules/employee/dto';
import {
  EmployeeCoverImageUploadDto,
  EmployeeCoverImageUploadResponseDto,
} from '@modules/employee/dto/cover-image-upload.dto';
import { Employee } from '@modules/employee/entities';
import { EmployeeService } from '@modules/employee/services';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
  getSchemaPath,
  ApiResponse as NestApiResponse
} from '@nestjs/swagger';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SendBulkDeleteOtpDto, SendBulkDeleteOtpResponseDto } from '../dto/send-bulk-delete-otp.dto';
import { BulkDeleteEmployeeDto, BulkDeleteEmployeeResponseDto } from '../dto/bulk-delete-employee.dto';

/**
 * Controller xử lý các API liên quan đến nhân viên
 */
@ApiTags(SWAGGER_API_TAGS.EMPLOYEES)
@ApiExtraModels(
  ApiResponseDto,
  EmployeeResponseDto,
  AvatarUploadResponseDto,
  ChangePasswordResponseDto,
  EmployeeRoleResponseDto,
  CreateEmployeeResponseDto,
  PaginatedResult,
  EmployeeOverviewDto,
  RoleCountDto,
  UpdateEmployeeProfileResponseDto,
  UpdateEmployeeDto,
)
@RequirePermissionEnum(Permission.EMPLOYEE_VIEW)
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@Controller('employees')
export class EmployeeController {
  constructor(
    private readonly employeeService: EmployeeService,
  ) { }





  /**
   * Tạo nhân viên mới
   * @param createEmployeeDto Thông tin nhân viên mới
   * @returns Nhân viên đã được tạo
   */
  // @ApiBearerAuth('JWT-auth')
  // @UseGuards(JwtEmployeeGuard)
  @Post()
  @ApiOperation({
    summary: 'Tạo nhân viên mới',
    description:
      'API này cho phép tạo một nhân viên mới trong hệ thống. Yêu cầu quyền quản trị.',
  })
  @ApiBody({
    type: CreateEmployeeDto,
    description: 'Thông tin nhân viên mới',
    examples: {
      example1: {
        value: {
          fullName: 'Nguyễn Văn A',
          email: '<EMAIL>',
          phoneNumber: '0987654321',
          password: 'Password123!',
          address: 'Hà Nội',
          roleIds: [1, 2],
        },
        summary: 'Thông tin nhân viên mới mẫu',
      },
      example2: {
        value: {
          fullName: 'Nguyễn Văn A',
          email: '<EMAIL>',
          phoneNumber: '0987654321',
          password: 'Password123!',
          address: 'Hà Nội',
          roleIds: [1, 2],
          avatarImageType: 'image/jpeg',
          avatarMaxSize: 2097152,
        },
        summary: 'Thông tin nhân viên mới với yêu cầu tạo URL upload avatar',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo nhân viên thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Tạo nhân viên thành công' },
            result: { $ref: getSchemaPath(CreateEmployeeResponseDto) },
          },
        },
      ],
    },
  })
  @NestApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'array',
          items: { type: 'string' },
          example: ['email must be an email'],
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Email đã tồn tại',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 409 },
        message: {
          type: 'string',
          example: 'Email <EMAIL> đã được sử dụng',
        },
        error: { type: 'string', example: 'Conflict' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Nhân viên đã được tạo' })
  async createEmployee(
    @Body() createEmployeeDto: CreateEmployeeDto,
  ): Promise<ApiResponseDto<CreateEmployeeResponseDto>> {
    const result = await this.employeeService.createEmployee(createEmployeeDto);
    return ApiResponseDto.success(result, 'Tạo nhân viên thành công');
  }

  /**
   * Tạo URL tạm thời để tải lên avatar nhân viên và lưu key vào database
   * @param employeeId ID của nhân viên
   * @param avatarUploadDto Thông tin về loại và kích thước avatar
   * @returns URL tạm thời và thông tin khóa S3
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Post(':id/avatar/upload-url')
  @ApiOperation({
    summary: 'Tạo URL tạm thời để tải lên avatar nhân viên',
    description:
      'API này tạo một URL tạm thời để tải lên avatar cho nhân viên và tự động lưu key vào database. URL này có thời hạn 5 phút. Sau khi upload thành công, không cần gọi API xác nhận riêng.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @ApiBody({
    type: EmployeeAvatarUploadDto,
    description: 'Thông tin về loại và kích thước avatar',
    examples: {
      example1: {
        value: {
          imageType: 'image/jpeg',
          maxSize: 2097152, // 2MB
        },
        summary: 'Thông tin avatar mẫu - 2MB',
      },
      example2: {
        value: {
          imageType: 'image/png',
          maxSize: 5242880, // 5MB
        },
        summary: 'Thông tin avatar mẫu - 5MB',
      },
      example3: {
        value: {
          imageType: 'image/jpeg',
          maxSize: 1048576, // 1MB
        },
        summary: 'Thông tin avatar mẫu - 1MB',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Tạo URL tải lên avatar thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            uploadUrl: {
              type: 'string',
              example: 'https://storage.example.com/upload?token=abc123...',
            },
            avatarKey: {
              type: 'string',
              example: 'employee-avatars/images/avatar-1-1682506092000-uuid',
            },
            expiresIn: {
              type: 'number',
              example: 300000,
              description: 'Thời gian hết hạn (giây)',
            },
            expiresAt: {
              type: 'number',
              example: 1746968772000,
              description: 'Thời điểm hết hạn (timestamp)',
            },
          },
        },
        message: {
          type: 'string',
          example: 'Tạo URL tải lên avatar thành công',
        },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Nhân viên với ID "1" không tồn tại',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'URL tạm thời để tải lên avatar' })
  async createAvatarUploadUrl(
    @Param('id', ParseIntPipe) employeeId: number,
    @Body() avatarUploadDto: EmployeeAvatarUploadDto,
  ): Promise<ApiResponseDto<AvatarUploadResponseDto>> {
    const result = await this.employeeService.createAvatarUploadUrl(
      employeeId,
      avatarUploadDto,
    );
    return ApiResponseDto.success(result, 'Tạo URL tải lên avatar thành công');
  }

  /**
   * Cập nhật avatar cho nhân viên
   * @param employeeId ID của nhân viên
   * @param updateAvatarDto Thông tin avatar mới
   * @returns Nhân viên đã được cập nhật
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Put(':id/avatar')
  @ApiOperation({
    summary: 'Cập nhật avatar cho nhân viên (Legacy)',
    description:
      'API này cập nhật avatar cho nhân viên với key có sẵn. Chỉ sử dụng khi cần cập nhật avatar với key đã tồn tại. Đối với upload avatar mới, khuyến nghị sử dụng API /avatar/upload-url thay thế.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @ApiBody({
    type: UpdateEmployeeAvatarDto,
    description: 'Thông tin avatar mới',
    examples: {
      example1: {
        value: {
          avatarKey: 'employee-avatars/images/avatar-1-1682506092000-uuid',
        },
        summary: 'Thông tin avatar mới mẫu',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật avatar thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 1 },
            fullName: { type: 'string', example: 'Nguyễn Văn A' },
            email: { type: 'string', example: '<EMAIL>' },
            phoneNumber: { type: 'string', example: '0987654321' },
            address: { type: 'string', example: 'Hà Nội' },
            avatar: {
              type: 'string',
              example: 'employee-avatars/images/avatar-1-1682506092000-uuid',
            },
            createdAt: { type: 'number', example: 1682506092000 },
            updatedAt: { type: 'number', example: 1682506092000 },
            enable: { type: 'boolean', example: true },
          },
        },
        message: { type: 'string', example: 'Cập nhật avatar thành công' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Nhân viên với ID "1" không tồn tại',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Avatar đã được cập nhật' })
  async updateAvatar(
    @Param('id', ParseIntPipe) employeeId: number,
    @Body() updateAvatarDto: UpdateEmployeeAvatarDto,
  ): Promise<ApiResponseDto<EmployeeResponseDto>> {
    const employee = await this.employeeService.updateAvatar(
      employeeId,
      updateAvatarDto,
    );
    return ApiResponseDto.success(employee, 'Cập nhật avatar thành công');
  }

  /**
   * Đổi mật khẩu cho nhân viên
   * @param employeeId ID của nhân viên
   * @param changePasswordDto Thông tin mật khẩu mới
   * @returns Thông báo kết quả
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Put(':id/password')
  @ApiOperation({
    summary: 'Đổi mật khẩu cho nhân viên',
    description:
      'API này cho phép đổi mật khẩu cho nhân viên. Yêu cầu quyền quản trị hoặc chính nhân viên đó.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @ApiBody({
    type: ChangeEmployeePasswordDto,
    description: 'Thông tin mật khẩu mới',
    examples: {
      example1: {
        value: {
          newPassword: 'NewPassword123!',
        },
        summary: 'Thông tin mật khẩu mới mẫu',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Đổi mật khẩu thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            message: { type: 'string', example: 'Đổi mật khẩu thành công' },
          },
        },
        message: { type: 'string', example: 'Đổi mật khẩu thành công' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Mật khẩu không đủ mạnh',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'string',
          example:
            'Mật khẩu không đủ mạnh. Mật khẩu trung bình. Mật khẩu nên chứa ít nhất 1 chữ hoa. Mật khẩu nên chứa ít nhất 1 ký tự đặc biệt.',
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Nhân viên với ID "1" không tồn tại',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Mật khẩu đã được đổi' })
  async changePassword(
    @Param('id', ParseIntPipe) employeeId: number,
    @Body() changePasswordDto: ChangeEmployeePasswordDto,
  ): Promise<ApiResponseDto<ChangePasswordResponseDto>> {
    const result = await this.employeeService.changePassword(
      employeeId,
      changePasswordDto,
    );
    return ApiResponseDto.success(result, 'Đổi mật khẩu thành công');
  }

  /**
   * Gán vai trò cho nhân viên
   * @param employeeId ID của nhân viên
   * @param assignRoleDto Thông tin vai trò cần gán
   * @returns Nhân viên đã được cập nhật
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Put(':id/roles')
  @ApiOperation({
    summary: 'Gán vai trò cho nhân viên',
    description:
      'API này cho phép gán các vai trò cho nhân viên. Yêu cầu quyền quản trị.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @ApiBody({
    type: AssignEmployeeRoleDto,
    description: 'Danh sách ID của các vai trò cần gán',
    examples: {
      example1: {
        value: {
          roleIds: [1, 2, 3],
        },
        summary: 'Danh sách vai trò mẫu',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Gán vai trò thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 1 },
            fullName: { type: 'string', example: 'Nguyễn Văn A' },
            email: { type: 'string', example: '<EMAIL>' },
            roles: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number', example: 1 },
                  name: { type: 'string', example: 'Admin' },
                  description: {
                    type: 'string',
                    example: 'Quyền quản trị viên',
                  },
                },
              },
            },
          },
        },
        message: { type: 'string', example: 'Gán vai trò thành công' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Nhân viên với ID "1" không tồn tại',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Vai trò đã được gán' })
  async assignRoles(
    @Param('id', ParseIntPipe) employeeId: number,
    @Body() assignRoleDto: AssignEmployeeRoleDto,
  ): Promise<ApiResponseDto<EmployeeResponseDto>> {
    const employee = await this.employeeService.assignRoles(
      employeeId,
      assignRoleDto,
    );
    return ApiResponseDto.success(employee, 'Gán vai trò thành công');
  }

  /**
   * Lấy danh sách vai trò của nhân viên
   * @param employeeId ID của nhân viên
   * @returns Danh sách vai trò
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Get(':id/roles')
  @ApiOperation({
    summary: 'Lấy danh sách vai trò của nhân viên',
    description: 'API này trả về danh sách các vai trò được gán cho nhân viên.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách vai trò thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              name: { type: 'string', example: 'Admin' },
              description: { type: 'string', example: 'Quyền quản trị viên' },
              permissions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'number', example: 1 },
                    module: { type: 'string', example: 'users' },
                    action: { type: 'string', example: 'read' },
                    description: {
                      type: 'string',
                      example: 'Xem danh sách người dùng',
                    },
                  },
                },
              },
            },
          },
        },
        message: {
          type: 'string',
          example: 'Lấy danh sách vai trò thành công',
        },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Nhân viên với ID "1" không tồn tại',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Danh sách vai trò' })
  async getEmployeeRoles(
    @Param('id', ParseIntPipe) employeeId: number,
  ): Promise<ApiResponseDto<any[]>> {
    const roles = await this.employeeService.getEmployeeRoles(employeeId);
    return ApiResponseDto.success(roles, 'Lấy danh sách vai trò thành công');
  }

  /**
   * Lấy thông tin tổng quan về nhân viên, vai trò và quyền
   * @returns Thông tin tổng quan nhân viên
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy thông tin tổng quan về nhân viên, vai trò và quyền',
    description:
      'API này trả về thông tin tổng quan về nhân viên, bao gồm số lượng nhân viên, danh sách vai trò, và tổng số quyền.',
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin tổng quan thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy thông tin tổng quan thành công' },
            result: { $ref: getSchemaPath(EmployeeOverviewDto) },
          },
        },
      ],
    },
  })
  @SwaggerApiResponse({ description: 'Thông tin tổng quan nhân viên' })
  async getEmployeeOverview(): Promise<ApiResponseDto<EmployeeOverviewDto>> {
    const overview = await this.employeeService.getEmployeeOverview();
    return ApiResponseDto.success(overview, 'Lấy thông tin tổng quan thành công');
  }

  /**
   * Lấy danh sách nhân viên có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách nhân viên có phân trang
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách nhân viên có phân trang',
    description:
      'API này trả về danh sách nhân viên có phân trang và hỗ trợ tìm kiếm, lọc.',
  })
  @ApiQuery({
    name: 'page',
    description: 'Trang hiện tại (bắt đầu từ 1)',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Số lượng bản ghi trên mỗi trang',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    description: 'Từ khóa tìm kiếm (tìm theo tên)',
    required: false,
    type: String,
    example: 'Nguyễn',
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Trường sắp xếp',
    required: false,
    type: String,
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortDirection',
    description: 'Hướng sắp xếp',
    required: false,
    enum: ['ASC', 'DESC'],
    example: 'DESC',
  })
  @ApiQuery({
    name: 'enable',
    description: 'Lọc theo trạng thái hoạt động',
    required: false,
    type: Boolean,
    example: true,
  })
  @ApiQuery({
    name: 'email',
    description: 'Lọc theo email',
    required: false,
    type: String,
    example: '<EMAIL>',
  })
  @ApiQuery({
    name: 'phoneNumber',
    description: 'Lọc theo số điện thoại',
    required: false,
    type: String,
    example: '0987654321',
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách nhân viên thành công',
    schema: ApiResponseDto.getPaginatedSchema(EmployeeResponseDto),
  })
  @SwaggerApiResponse({ description: 'Danh sách nhân viên có phân trang' })
  async getEmployees(
    @Query() queryDto: EmployeeQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<Employee>>> {
    const employees = await this.employeeService.findAll(queryDto);
    return ApiResponseDto.paginated(
      employees,
      'Lấy danh sách nhân viên thành công',
    );
  }

  /**
   * Lấy thông tin chi tiết nhân viên theo ID
   * @param employeeId ID của nhân viên
   * @returns Thông tin chi tiết nhân viên
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết nhân viên theo ID',
    description: 'API này trả về thông tin chi tiết của một nhân viên cụ thể.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin nhân viên thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy thông tin nhân viên thành công' },
            result: { $ref: getSchemaPath(EmployeeResponseDto) },
          },
        },
      ],
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Không tìm thấy nhân viên với ID "1"',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Thông tin chi tiết nhân viên' })
  async getEmployeeById(
    @Param('id', ParseIntPipe) employeeId: number,
  ): Promise<ApiResponseDto<EmployeeResponseDto>> {
    const employee = await this.employeeService.findById(employeeId);
    return ApiResponseDto.success(employee, 'Lấy thông tin nhân viên thành công');
  }

  /**
   * Cập nhật thông tin nhân viên (dành cho admin)
   * @param employeeId ID của nhân viên
   * @param updateEmployeeDto Thông tin cần cập nhật
   * @returns Nhân viên đã được cập nhật
   */
  @RequirePermissionEnum(Permission.EMPLOYEE_UPDATE)
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật thông tin nhân viên',
    description: 'API này cho phép admin cập nhật thông tin nhân viên bao gồm email, số điện thoại, mật khẩu và vai trò.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @ApiBody({
    type: UpdateEmployeeDto,
    description: 'Thông tin cần cập nhật',
    examples: {
      example1: {
        value: {
          fullName: 'Nguyễn Văn A',
          email: '<EMAIL>',
          phoneNumber: '0987654321',
          address: 'Hà Nội',
          enable: true,
          roleIds: [1, 2],
        },
        summary: 'Cập nhật thông tin cơ bản và vai trò',
      },
      example2: {
        value: {
          fullName: 'Trần Thị B',
          password: 'NewPassword123!',
          enable: false,
        },
        summary: 'Cập nhật tên, mật khẩu và vô hiệu hóa tài khoản',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật nhân viên thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Cập nhật nhân viên thành công' },
            result: { $ref: getSchemaPath(EmployeeResponseDto) },
          },
        },
      ],
    },
  })
  @NestApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'array',
          items: { type: 'string' },
          example: ['email must be an email'],
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Email hoặc số điện thoại đã tồn tại',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 409 },
        message: {
          type: 'string',
          example: 'Email <EMAIL> đã được sử dụng',
        },
        error: { type: 'string', example: 'Conflict' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Không tìm thấy nhân viên với ID "1"',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Nhân viên đã được cập nhật' })
  async updateEmployee(
    @Param('id', ParseIntPipe) employeeId: number,
    @Body() updateEmployeeDto: UpdateEmployeeDto,
  ): Promise<ApiResponseDto<EmployeeResponseDto>> {
    const employee = await this.employeeService.updateEmployee(employeeId, updateEmployeeDto);
    return ApiResponseDto.success(employee, 'Cập nhật nhân viên thành công');
  }

  /**
   * Cập nhật thông tin cá nhân của nhân viên hiện tại
   * Không cho phép thay đổi email và số điện thoại
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Put('update/profile/me')
  @ApiOperation({
    summary: 'Cập nhật thông tin cá nhân của nhân viên',
    description: 'API này cho phép nhân viên cập nhật thông tin cá nhân của mình. Không cho phép thay đổi email và số điện thoại.',
  })
  @ApiBody({
    type: UpdateEmployeeProfileDto,
    description: 'Thông tin cần cập nhật',
    examples: {
      example1: {
        value: {
          fullName: 'Nguyễn Văn A',
          address: 'Số 1, Đường ABC, Quận XYZ, Hà Nội',
        },
        summary: 'Cập nhật tên và địa chỉ',
      },
      example2: {
        value: {
          fullName: 'Trần Thị B',
        },
        summary: 'Chỉ cập nhật tên',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật thông tin thành công',
    type: UpdateEmployeeProfileResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @SwaggerApiResponse({ description: 'Thông tin nhân viên đã được cập nhật' })
  async updateProfile(
    @Body() updateProfileDto: UpdateEmployeeProfileDto,
    @CurrentEmployee() employee: JWTPayloadEmployee,
  ): Promise<ApiResponseDto<UpdateEmployeeProfileResponseDto>> {
    const updatedEmployee = await this.employeeService.updateEmployeeProfile(
      employee.id,
      updateProfileDto,
    );

    const response: UpdateEmployeeProfileResponseDto = {
      id: updatedEmployee.id,
      fullName: updatedEmployee.fullName,
      email: updatedEmployee.email,
      phoneNumber: updatedEmployee.phoneNumber,
      address: updatedEmployee.address,
      avatar: updatedEmployee.avatar,
      updatedAt: updatedEmployee.updatedAt,
    };

    return ApiResponseDto.success(response, 'Cập nhật thông tin thành công');
  }

  /**
   * Lấy thông tin chi tiết nhân viên đang đăng nhập hiện tại
   */
  @Get('/info/me')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết nhân viên đang đăng nhập hiện tại',
    description: 'API này trả về thông tin chi tiết của nhân viên đang đăng nhập hiện tại.',
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin nhân viên hiện tại thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy thông tin nhân viên hiện tại thành công' },
            result: { $ref: getSchemaPath(EmployeeResponseDto) },
          },
        },
      ],
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Không tìm thấy nhân viên hiện tại',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Thông tin chi tiết nhân viên hiện tại' })
  async getCurrentEmployee(
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<EmployeeResponseDto>> {
    const currentEmployee = await this.employeeService.findById(employee.id);
    return ApiResponseDto.success(currentEmployee, 'Lấy thông tin nhân viên hiện tại thành công');
  }

  /**
   * Thay đổi mật khẩu của nhân viên hiện tại
   * Yêu cầu xác thực mật khẩu cũ
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Put('me/change-password')
  @ApiOperation({
    summary: 'Thay đổi mật khẩu của nhân viên',
    description: 'API này cho phép nhân viên thay đổi mật khẩu của mình. Yêu cầu xác thực mật khẩu hiện tại.',
  })
  @ApiBody({
    type: ChangeEmployeePasswordSelfDto,
    description: 'Thông tin mật khẩu cũ và mới',
    examples: {
      example1: {
        value: {
          currentPassword: 'currentPassword123',
          newPassword: 'newPassword456',
        },
        summary: 'Thay đổi mật khẩu',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Đổi mật khẩu thành công',
    type: ChangePasswordResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Mật khẩu hiện tại không chính xác hoặc mật khẩu mới không đủ mạnh',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
  })
  @SwaggerApiResponse({ description: 'Mật khẩu đã được thay đổi' })
  async changeMyPassword(
    @Body() changePasswordDto: ChangeEmployeePasswordSelfDto,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<ChangePasswordResponseDto>> {
    const result = await this.employeeService.changeEmployeePasswordSelf(
      employee.id,
      changePasswordDto,
    );
    return ApiResponseDto.success(result, 'Đổi mật khẩu thành công');
  }

  /**
   * Gửi OTP để xóa nhiều nhân viên
   * @param sendOtpDto Thông tin yêu cầu gửi OTP
   * @param employee Thông tin nhân viên hiện tại
   * @returns Thông tin OTP đã gửi
   */
  @RequirePermissionEnum(Permission.EMPLOYEE_DELETE)
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Post('bulk-delete/send-otp')
  @ApiOperation({
    summary: 'Gửi OTP để xóa nhiều nhân viên',
    description: 'API này gửi mã OTP đến email của nhân viên hiện tại để xác thực việc xóa nhiều nhân viên.',
  })
  @ApiBody({
    type: SendBulkDeleteOtpDto,
    description: 'Danh sách ID nhân viên cần xóa',
    examples: {
      example1: {
        value: {
          employeeIds: [1, 2, 3],
        },
        summary: 'Xóa 3 nhân viên',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Gửi OTP thành công',
    type: SendBulkDeleteOtpResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
  })
  @SwaggerApiResponse({ description: 'OTP đã được gửi' })
  async sendBulkDeleteOtp(
    @Body() sendOtpDto: SendBulkDeleteOtpDto,
    @CurrentEmployee() employee: JWTPayloadEmployee,
  ): Promise<ApiResponseDto<SendBulkDeleteOtpResponseDto>> {
    const result = await this.employeeService.sendBulkDeleteOtp(sendOtpDto, employee.id);
    return ApiResponseDto.success(result, 'Gửi OTP thành công');
  }

  /**
   * Xóa nhiều nhân viên với xác thực OTP
   * @param bulkDeleteDto Thông tin xóa nhiều nhân viên
   * @param employee Thông tin nhân viên hiện tại
   * @returns Kết quả xóa nhiều nhân viên
   */
  @RequirePermissionEnum(Permission.EMPLOYEE_DELETE)
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Delete('bulk-delete')
  @ApiOperation({
    summary: 'Xóa nhiều nhân viên với xác thực OTP',
    description: 'API này xóa nhiều nhân viên sau khi xác thực mã OTP.',
  })
  @ApiBody({
    type: BulkDeleteEmployeeDto,
    description: 'Thông tin xóa nhiều nhân viên với OTP',
    examples: {
      example1: {
        value: {
          employeeIds: [1, 2, 3],
          otp: '123456',
          otpToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
        summary: 'Xóa 3 nhân viên với OTP',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa nhân viên thành công',
    type: BulkDeleteEmployeeResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'OTP không hợp lệ hoặc dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
  })
  @SwaggerApiResponse({ description: 'Kết quả xóa nhiều nhân viên' })
  async bulkDeleteEmployees(
    @Body() bulkDeleteDto: BulkDeleteEmployeeDto,
    @CurrentEmployee() employee: JWTPayloadEmployee,
  ): Promise<ApiResponseDto<BulkDeleteEmployeeResponseDto>> {
    const result = await this.employeeService.bulkDeleteEmployees(bulkDeleteDto, employee.id);
    return ApiResponseDto.success(result, result.message);
  }

  /**
   * Tạo URL tạm thời để tải lên ảnh bìa nhân viên
   * @param employee Thông tin nhân viên đang đăng nhập
   * @param coverImageUploadDto Thông tin về loại và kích thước ảnh bìa
   * @returns URL tạm thời và thông tin khóa S3
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Post('cover-image/upload-url')
  @ApiOperation({
    summary: 'Tạo URL tạm thời để tải lên ảnh bìa nhân viên',
    description: 'API này tạo URL tạm thời để nhân viên có thể upload ảnh bìa lên S3. Key sẽ được lưu vào database ngay lập tức.',
  })
  @ApiBody({
    type: EmployeeCoverImageUploadDto,
    description: 'Thông tin về loại và kích thước ảnh bìa',
    examples: {
      example1: {
        value: {
          imageType: 'image/jpeg',
          maxSize: 5242880,
        },
        summary: 'Upload ảnh JPEG tối đa 5MB',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    type: EmployeeCoverImageUploadResponseDto,
    description: 'URL tạm thời để tải lên ảnh bìa',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Chưa đăng nhập',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
  })
  @SwaggerApiResponse({ description: 'URL tạm thời để tải lên ảnh bìa' })
  async createCoverImageUploadUrl(
    @CurrentEmployee() employee: JWTPayloadEmployee,
    @Body() coverImageUploadDto: EmployeeCoverImageUploadDto,
  ): Promise<ApiResponseDto<EmployeeCoverImageUploadResponseDto>> {
    const result = await this.employeeService.createCoverImageUploadUrl(
      employee.id,
      coverImageUploadDto,
    );
    return ApiResponseDto.success(result, 'Tạo URL tải lên ảnh bìa thành công');
  }


}
