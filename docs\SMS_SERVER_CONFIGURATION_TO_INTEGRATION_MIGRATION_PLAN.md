# Kế hoạch Migration từ SmsServerConfiguration sang Integration

## Tổng quan

Tài liệu này mô tả kế hoạch chi tiết để chuyển đổi tất cả các phần sử dụng `SmsServerConfiguration` entity sang `Integration` entity, sau đ<PERSON> xóa `SmsServerConfiguration` hoàn toàn.

## Phân tích Files cần Migration

### 1. Entity & Repository
- ❌ `src/modules/integration/entities/sms-server-configuration.entity.ts`
- ❌ `src/modules/integration/repositories/sms-server-configuration.repository.ts`

### 2. Services
- 🔄 `src/modules/integration/admin/services/sms-server-configuration-admin.service.ts`
- 🔄 `src/modules/integration/user/services/sms-server-configuration-user.service.ts`
- 🔄 `src/modules/marketing/admin/services/admin-twilio-sms.service.ts`

### 3. Controllers
- ❌ `src/modules/integration/admin/controllers/sms-server-configuration-admin.controller.ts`
- ❌ `src/modules/integration/user/controllers/sms-server-configuration-user.controller.ts`

### 4. DTOs
- ❌ `src/modules/integration/user/dto/sms/update-sms-server.dto.ts`
- ❌ `src/modules/integration/admin/dto/sms-server-admin-response.dto.ts`

### 5. Module Exports
- 🔄 `src/modules/integration/repositories/index.ts`
- 🔄 `src/modules/integration/admin/dto/index.ts`

### 6. Database & Scripts
- ❌ `database/migrations/migrate-sms-server-to-integration.sql`
- ❌ `scripts/run-sms-server-configuration-migration.sh`

### 7. Documentation
- ❌ `src/modules/integration/docs/sms-plan.md`

## Migration Strategy

### Phase 1: Update Services to use Integration

#### 1.1 Update admin-twilio-sms.service.ts
```typescript
// Before
import { SmsServerConfiguration } from '@/modules/integration/entities';
import { SmsServerConfigurationRepository } from '@/modules/integration/repositories';

// After
import { Integration } from '@/modules/integration/entities';
import { IntegrationRepository } from '@/modules/integration/repositories';
import { KeyPairEncryptionService } from '@/shared/services/encryption/key-pair-encryption.service';
```

#### 1.2 Replace SmsServerConfiguration creation with Integration
```typescript
// Before
const smsConfig = new SmsServerConfiguration();
smsConfig.userId = createDto.userId || 0;
smsConfig.providerName = 'TWILIO';
smsConfig.apiKey = createDto.accountSid;
smsConfig.additionalSettings = { ... };

// After
const integration = new Integration();
integration.userId = createDto.userId;
integration.integrationName = `Twilio SMS - ${createDto.name}`;
integration.typeId = twilioProviderId; // From integration_providers table
integration.ownedType = createDto.userId ? 'USER' : 'ADMIN';
// Encrypt sensitive data
const encryptionResult = this.keyPairEncryptionService.encrypt(JSON.stringify({
  accountSid: createDto.accountSid,
  authToken: createDto.authToken,
  phoneNumber: createDto.phoneNumber,
  messagingServiceSid: createDto.messagingServiceSid
}));
integration.encryptedConfig = encryptionResult.encryptedData;
integration.secretKey = encryptionResult.secretKey;
integration.metadata = {
  provider: 'TWILIO',
  configName: createDto.name,
  createdByAdmin: true
};
```

#### 1.3 Update sms-server-configuration-admin.service.ts
- Replace all SmsServerConfiguration usage with Integration
- Update FPT SMS creation logic
- Update Twilio creation logic
- Add encryption for sensitive data

#### 1.4 Update sms-server-configuration-user.service.ts
- Replace all SmsServerConfiguration usage with Integration
- Update user SMS configuration logic

### Phase 2: Remove Controllers and DTOs

#### 2.1 Remove Controllers
- `sms-server-configuration-admin.controller.ts` - Move functionality to integration controllers
- `sms-server-configuration-user.controller.ts` - Move functionality to integration controllers

#### 2.2 Remove DTOs
- `update-sms-server.dto.ts` - Replace with integration DTOs
- `sms-server-admin-response.dto.ts` - Replace with integration response DTOs

### Phase 3: Update Module Configurations

#### 3.1 Remove from entities export
```typescript
// src/modules/integration/entities/index.ts
// Remove: export * from './sms-server-configuration.entity';
```

#### 3.2 Remove from repositories export
```typescript
// src/modules/integration/repositories/index.ts
// Remove: export * from './sms-server-configuration.repository';
```

#### 3.3 Remove from admin DTO export
```typescript
// src/modules/integration/admin/dto/index.ts
// Remove: export * from './sms-server-admin-response.dto';
```

#### 3.4 Update module providers
Remove SmsServerConfigurationRepository from all module providers

### Phase 4: Database Migration

#### 4.1 Verify Data Migration
```sql
-- Check if all SMS configs are migrated to Integration
SELECT 
    ssc.id as old_id,
    ssc.provider_name,
    ssc.user_id,
    i.id as integration_id,
    i.integration_name
FROM sms_server_configurations ssc
LEFT JOIN integration i ON i.metadata->>'old_sms_server_id' = ssc.id::text
WHERE i.id IS NULL;
```

#### 4.2 Drop SmsServerConfiguration table
```sql
-- Backup first
CREATE TABLE sms_server_configurations_backup AS 
SELECT * FROM sms_server_configurations;

-- Drop table
DROP TABLE IF EXISTS sms_server_configurations CASCADE;
```

### Phase 5: File Cleanup

#### 5.1 Remove Entity and Repository
```bash
rm src/modules/integration/entities/sms-server-configuration.entity.ts
rm src/modules/integration/repositories/sms-server-configuration.repository.ts
```

#### 5.2 Remove Services and Controllers
```bash
rm src/modules/integration/admin/services/sms-server-configuration-admin.service.ts
rm src/modules/integration/user/services/sms-server-configuration-user.service.ts
rm src/modules/integration/admin/controllers/sms-server-configuration-admin.controller.ts
rm src/modules/integration/user/controllers/sms-server-configuration-user.controller.ts
```

#### 5.3 Remove DTOs
```bash
rm src/modules/integration/user/dto/sms/update-sms-server.dto.ts
rm src/modules/integration/admin/dto/sms-server-admin-response.dto.ts
```

#### 5.4 Remove Migration Files
```bash
rm database/migrations/migrate-sms-server-to-integration.sql
rm scripts/run-sms-server-configuration-migration.sh
rm scripts/run-sms-server-configuration-migration.ps1
```

#### 5.5 Remove Documentation
```bash
rm src/modules/integration/docs/sms-plan.md
```

## Implementation Steps

### Step 1: Update admin-twilio-sms.service.ts
1. Replace imports
2. Update createTwilioConfig method
3. Add encryption logic
4. Update repository calls
5. Test Twilio SMS creation

### Step 2: Update sms-server-configuration-admin.service.ts
1. Replace SmsServerConfiguration with Integration
2. Update createFptSmsBrandname method
3. Update createTwilioConfig method
4. Add encryption for sensitive data
5. Update metadata structure

### Step 3: Update sms-server-configuration-user.service.ts
1. Replace SmsServerConfiguration with Integration
2. Update user SMS configuration methods
3. Add encryption logic
4. Update response DTOs

### Step 4: Remove Controllers
1. Move necessary functionality to integration controllers
2. Update API routes
3. Update Swagger documentation

### Step 5: Update Module Configurations
1. Remove from entity exports
2. Remove from repository exports
3. Remove from providers
4. Update imports in other modules

### Step 6: Database Cleanup
1. Verify all data migrated
2. Drop SmsServerConfiguration table
3. Remove migration files

### Step 7: File Cleanup
1. Remove entity and repository files
2. Remove service and controller files
3. Remove DTO files
4. Remove documentation files

## Testing Checklist

### Before Migration
- [ ] All SMS campaigns work with current structure
- [ ] SMS server configurations can be created/updated
- [ ] Twilio SMS functionality works
- [ ] FPT SMS functionality works

### After Migration
- [ ] SMS campaigns work with Integration entity
- [ ] SMS configurations can be created via Integration
- [ ] Encryption/decryption works correctly
- [ ] No compilation errors
- [ ] All tests pass
- [ ] API endpoints work correctly

## Risk Mitigation

### High Risk
- **Data Loss**: Create comprehensive backups before migration
- **Service Disruption**: Test thoroughly in staging environment
- **Encryption Issues**: Verify encryption/decryption works correctly

### Medium Risk
- **API Changes**: Update frontend to use new endpoints
- **Configuration Loss**: Ensure all SMS configs are properly migrated

### Low Risk
- **Documentation**: Update API documentation
- **Code Cleanup**: Remove unused imports and files

## Success Criteria

1. ✅ All SmsServerConfiguration usage replaced with Integration
2. ✅ SMS functionality works with Integration entity
3. ✅ Sensitive data properly encrypted
4. ✅ No compilation errors
5. ✅ All tests pass
6. ✅ Database cleaned up
7. ✅ Files removed successfully
8. ✅ Documentation updated
