import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  Min,
  ValidateNested
} from 'class-validator';
import { ProviderLlmEnum } from '../../../constants';
import {
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum,
  FeatureEnum
} from '../../../constants/model-capabilities.enum';
import { ModelPricingInterface } from '../../../interfaces/pricing.interface';

/**
 * DTO cho pricing của model
 */
export class ModelPricingDto implements ModelPricingInterface {
  /**
   * Giá cho input (per token hoặc per request)
   */
  @ApiProperty({
    description: 'Giá cho input (per token hoặc per request)',
    example: 1,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  inputRate: number;

  /**
   * Gi<PERSON> cho output (per token hoặc per request)
   */
  @ApiProperty({
    description: 'Gi<PERSON> cho output (per token hoặc per request)',
    example: 2,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  outputRate: number;
}

/**
 * DTO cho việc tạo mới model registry
 */
export class CreateModelRegistryDto {
  /**
   * Nhà cung cấp model
   */
  @ApiPropertyOptional({
    description: 'Nhà cung cấp model',
    enum: ProviderLlmEnum,
    example: ProviderLlmEnum.OPENAI,
    default: ProviderLlmEnum.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderLlmEnum)
  provider?: ProviderLlmEnum = ProviderLlmEnum.OPENAI;

  /**
   * Tên mẫu đại diện của model
   */
  @ApiProperty({
    description: 'Tên mẫu đại diện của model (phải unique và hợp lệ với provider)',
    example: 'gpt-4-turbo',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  modelBaseId: string;

  /**
   * Các loại dữ liệu đầu vào hỗ trợ cho base model
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu vào hỗ trợ cho base model',
    enum: InputModalityEnum,
    isArray: true,
    example: [InputModalityEnum.TEXT, InputModalityEnum.IMAGE],
    default: [InputModalityEnum.TEXT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(InputModalityEnum, { each: true })
  inputModalitiesBase?: InputModalityEnum[] = [InputModalityEnum.TEXT];

  /**
   * Các loại dữ liệu đầu vào hỗ trợ cho fine-tuned model
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu vào hỗ trợ cho fine-tuned model',
    enum: InputModalityEnum,
    isArray: true,
    example: [InputModalityEnum.TEXT],
    default: [InputModalityEnum.TEXT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(InputModalityEnum, { each: true })
  inputModalitiesFineTune?: InputModalityEnum[] = [InputModalityEnum.TEXT];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ cho base model
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu ra hỗ trợ cho base model',
    enum: OutputModalityEnum,
    isArray: true,
    example: [OutputModalityEnum.TEXT],
    default: [OutputModalityEnum.TEXT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(OutputModalityEnum, { each: true })
  outputModalitiesBase?: OutputModalityEnum[] = [OutputModalityEnum.TEXT];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ cho fine-tuned model
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu ra hỗ trợ cho fine-tuned model',
    enum: OutputModalityEnum,
    isArray: true,
    example: [OutputModalityEnum.TEXT],
    default: [OutputModalityEnum.TEXT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(OutputModalityEnum, { each: true })
  outputModalitiesFineTune?: OutputModalityEnum[] = [OutputModalityEnum.TEXT];

  /**
   * Các tham số sampling hỗ trợ cho base model
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling hỗ trợ cho base model',
    enum: SamplingParameterEnum,
    isArray: true,
    example: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
    default: [SamplingParameterEnum.TEMPERATURE],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SamplingParameterEnum, { each: true })
  samplingParametersBase?: SamplingParameterEnum[] = [SamplingParameterEnum.TEMPERATURE];

  /**
   * Các tham số sampling hỗ trợ cho fine-tuned model
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling hỗ trợ cho fine-tuned model',
    enum: SamplingParameterEnum,
    isArray: true,
    example: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
    default: [SamplingParameterEnum.TEMPERATURE],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SamplingParameterEnum, { each: true })
  samplingParametersFineTune?: SamplingParameterEnum[] = [SamplingParameterEnum.TEMPERATURE];

  /**
   * Các feature đặc biệt hỗ trợ cho base model
   */
  @ApiPropertyOptional({
    description: 'Các feature đặc biệt hỗ trợ cho base model',
    enum: FeatureEnum,
    isArray: true,
    example: [FeatureEnum.TOOL_CALL],
    default: [],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(FeatureEnum, { each: true })
  featuresBase?: FeatureEnum[] = [];

  /**
   * Các feature đặc biệt hỗ trợ cho fine-tuned model
   */
  @ApiPropertyOptional({
    description: 'Các feature đặc biệt hỗ trợ cho fine-tuned model',
    enum: FeatureEnum,
    isArray: true,
    example: [FeatureEnum.TOOL_CALL],
    default: [],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(FeatureEnum, { each: true })
  featuresFineTune?: FeatureEnum[] = [];

  /**
   * Giá cơ bản cho model (input/output rate)
   */
  @ApiPropertyOptional({
    description: 'Giá cơ bản cho model',
    type: ModelPricingDto,
    example: { inputRate: 1, outputRate: 2 },
    default: { inputRate: 1, outputRate: 1 },
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ModelPricingDto)
  basePricing?: ModelPricingDto = { inputRate: 1, outputRate: 1 };

  /**
   * Giá fine-tune cho model (input/output rate)
   */
  @ApiPropertyOptional({
    description: 'Giá fine-tune cho model',
    type: ModelPricingDto,
    example: { inputRate: 2, outputRate: 4 },
    default: { inputRate: 1, outputRate: 1 },
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ModelPricingDto)
  fineTunePricing?: ModelPricingDto = { inputRate: 1, outputRate: 1 };

  /**
   * Giá training cho model
   */
  @ApiPropertyOptional({
    description: 'Giá training cho model',
    example: 0,
    minimum: 0,
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  trainingPricing?: number = 0;

  /**
   * Có hỗ trợ fine-tuning không
   */
  @ApiPropertyOptional({
    description: 'Có hỗ trợ fine-tuning không',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  fineTune?: boolean = false;

  /**
   * Số tokens tối đa có thể sinh ra
   */
  @ApiPropertyOptional({
    description: 'Số tokens tối đa có thể sinh ra',
    example: 1000,
    minimum: 0,
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxTokens?: number = 0;

  /**
   * Độ dài context tối đa
   */
  @ApiPropertyOptional({
    description: 'Độ dài context tối đa',
    example: 1000,
    minimum: 0,
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  contexWindow?: number = 0;
}
