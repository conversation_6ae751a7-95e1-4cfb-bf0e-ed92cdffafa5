import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, IsEnum, IsUUID, IsArray, IsBoolean } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import { ImportResourceEnum } from '../../enums/import-resource.enum';

/**
 * DTO cho query parameters khi lấy danh sách audience
 */
export class AudienceQueryDto extends QueryDto {

  /**
   * Tìm kiếm tổng hợp trong tên, email và số điện thoại
   * @example "xin chào"
   */
  @ApiProperty({
    description: 'Tìm kiếm tổng hợp trong tên, email và số điện thoại. Nếu có search thì sẽ bỏ qua các trường name, email, phone riêng lẻ.',
    example: 'xin chào',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Từ khóa tìm kiếm phải là chuỗi' })
  declare search?: string;

  /**
   * Tìm kiếm theo tên
   * @example "Nguyễn"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên',
    example: 'Nguyễn',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi' })
  name?: string;

  /**
   * Tìm kiếm theo email
   * @example "example.com"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo email',
    example: 'example.com',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Email phải là chuỗi' })
  email?: string;

  /**
   * Tìm kiếm theo số điện thoại
   * @example "+84"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo số điện thoại',
    example: '+84',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  phone?: string;

  /**
   * Lọc theo audience có số điện thoại hay không
   * @example true
   */
  @ApiProperty({
    description: 'Lọc audience theo việc có số điện thoại hay không. true = có số điện thoại, false = không có số điện thoại',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'hasPhoneNumber phải là boolean' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return Boolean(value);
  })
  hasPhoneNumber?: boolean;

  /**
   * Tìm kiếm theo tag ID
   * @example 1
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tag ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Tag ID phải là số nguyên' })
  @Type(() => Number)
  tagId?: number;

  /**
   * Tìm kiếm theo tên trường tùy chỉnh
   * @example "address"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên trường tùy chỉnh',
    example: 'address',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên trường tùy chỉnh phải là chuỗi' })
  customFieldName?: string;

  /**
   * Tìm kiếm theo giá trị trường tùy chỉnh
   * @example "Hanoi"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo giá trị trường tùy chỉnh',
    example: 'Hanoi',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Giá trị trường tùy chỉnh phải là chuỗi' })
  customFieldValue?: string;

  /**
   * Tìm kiếm theo nền tảng (nguồn import)
   * @example "ZALO"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo nền tảng (nguồn import của audience)',
    example: 'ZALO',
    enum: ImportResourceEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(ImportResourceEnum, { message: 'Platform phải là một trong các giá trị: ZALO, FACEBOOK, WEB, MANUAL' })
  platform?: ImportResourceEnum;

  /**
   * Tìm kiếm theo Integration ID
   * @example "123e4567-e89b-12d3-a456-************"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo Integration ID (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'Integration ID phải là UUID hợp lệ' })
  integrationId?: string;

  /**
   * Lọc theo segment ID
   * @example 123
   */
  @ApiProperty({
    description: 'Lọc audience theo segment ID cụ thể',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Segment ID phải là số nguyên' })
  @Type(() => Number)
  segmentId?: number;

  /**
   * Lọc theo nhiều segment IDs (OR logic)
   * @example [123, 456, 789]
   */
  @ApiProperty({
    description: 'Lọc audience theo nhiều segment IDs (logic OR - audience thuộc ít nhất một segment)',
    example: [123, 456, 789],
    required: false,
    type: [Number],
  })
  @IsOptional()
  @IsArray({ message: 'Segment IDs phải là mảng' })
  @IsInt({ each: true, message: 'Mỗi segment ID phải là số nguyên' })
  @Type(() => Number)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id));
    }
    return Array.isArray(value) ? value : [value];
  })
  segmentIds?: number[];

  /**
   * Loại trừ audience thuộc segment ID
   * @example 456
   */
  @ApiProperty({
    description: 'Loại trừ audience thuộc segment ID cụ thể',
    example: 456,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Exclude segment ID phải là số nguyên' })
  @Type(() => Number)
  excludeSegmentId?: number;

  /**
   * Loại trừ audience thuộc nhiều segment IDs
   * @example [456, 789]
   */
  @ApiProperty({
    description: 'Loại trừ audience thuộc nhiều segment IDs (logic OR - loại trừ nếu thuộc ít nhất một segment)',
    example: [456, 789],
    required: false,
    type: [Number],
  })
  @IsOptional()
  @IsArray({ message: 'Exclude segment IDs phải là mảng' })
  @IsInt({ each: true, message: 'Mỗi exclude segment ID phải là số nguyên' })
  @Type(() => Number)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',').map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id));
    }
    return Array.isArray(value) ? value : [value];
  })
  excludeSegmentIds?: number[];
}
