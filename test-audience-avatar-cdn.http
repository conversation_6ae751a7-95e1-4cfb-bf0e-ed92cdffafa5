### Test API GET /marketing/audiences/:id với CDN URL cho avatar
### URL: https://v2.redai.vn/api/v1/marketing/audiences/1126

GET https://v2.redai.vn/api/v1/marketing/audiences/1126
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Expected Response:
### {
###   "code": 200,
###   "message": "Thông tin audience",
###   "result": {
###     "id": "1126",
###     "userId": 1,
###     "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
###     "email": "<EMAIL>",
###     "countryCode": 84,
###     "phoneNumber": "**********",
###     "avatar": "https://cdn.redai.vn/marketing/customer_avatars/2025/07/user_1/*************-cb390060-7917-4246-81dd-877719a2eef4.jpeg?expires=**********&signature=abc123",
###     "address": "kaka",
###     "zaloSocialId": "1997920606148698276",
###     "integrationId": null,
###     "avatarsExternal": [],
###     "importResource": "zalo",
###     "zaloOfficialAccountId": null,
###     "zaloUserIsFollower": true,
###     "userLastInteractionDate": "11/07/2025",
###     "customFields": [...],
###     "tags": [...],
###     "createdAt": "*************",
###     "updatedAt": "*************"
###   }
### }

### Test với audience khác (nếu có)
GET https://v2.redai.vn/api/v1/marketing/audiences/1127
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test với audience không tồn tại
GET https://v2.redai.vn/api/v1/marketing/audiences/99999
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Expected Response: 404 Not Found
