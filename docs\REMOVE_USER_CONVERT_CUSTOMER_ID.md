# Xóa trường userConvertCustomerId khỏi UserOrder Entity

## 📋 Tổng quan

Tài liệu này mô tả việc refactor UserOrder entity để xóa trường `userConvertCustomerId` và sử dụng trực tiếp các trường `convertCustomerName`, `convertCustomerPhone`, `convertCustomerEmail` để lưu thông tin khách hàng.

## 🎯 Mục tiêu

- **Đơn giản hóa cấu trúc dữ liệu**: Loại bỏ quan hệ phức tạp với bảng `user_convert_customers`
- **Tăng hiệu suất**: G<PERSON><PERSON><PERSON> số lượng JOIN khi query dữ liệu đơn hàng
- **D<PERSON> bảo trì**: Thông tin khách hàng được lưu trực tiếp trong đơn hàng

## 🔄 Thay đổi chính

### 1. Entity Changes

**UserOrder Entity** (`src/modules/business/entities/user-order.entity.ts`):
- ❌ Xóa: `userConvertCustomerId: string`
- ❌ Xóa: `userConvertCustomer: UserConvertCustomer` (relationship)
- ✅ Giữ lại: `convertCustomerName: string`
- ✅ Giữ lại: `convertCustomerPhone: string`
- ✅ Giữ lại: `convertCustomerEmail: string`
- ✅ Giữ lại: `countryCode: number`

### 2. DTO Changes

**UserOrderResponseDto** (`src/modules/business/user/dto/user-order-response.dto.ts`):
- ❌ Xóa: `userConvertCustomerId` field
- ✅ Giữ lại: `convertCustomerName`, `convertCustomerPhone`, `convertCustomerEmail`

**UserOrderListItemDto**:
- ❌ Xóa: `userConvertCustomer` relationship object
- ✅ Giữ lại: Các trường customer info trực tiếp

**QueryUserOrderDto** (cả user và admin):
- ❌ Xóa: `userConvertCustomerId` filter parameter

### 3. Repository Changes

**UserOrderRepository** (`src/modules/business/repositories/user-order.repository.ts`):
- ❌ Xóa: `.leftJoinAndSelect('user_order.userConvertCustomer', 'customer')`
- ❌ Xóa: Filter theo `userConvertCustomerId`
- ✅ Cập nhật: Admin query để select trực tiếp các trường customer
- ✅ Cập nhật: Mapping để include `convertCustomer*` fields

### 4. Service Changes

**UserOrderService** (`src/modules/business/user/services/order/user-order.service.ts`):
- ❌ Xóa: `userConvertCustomerId: customer.id` trong order data
- ✅ Giữ lại: Mapping trực tiếp customer info vào order

**UserConvertCustomerRepository**:
- ✅ Cập nhật: Logic merge và delete để không tham chiếu `userConvertCustomerId`

### 5. Database Migration

**Migration File**: `database/migrations/remove-user-convert-customer-id-from-user-orders.sql`
- Xóa foreign key constraints liên quan
- Xóa indexes liên quan
- Xóa cột `user_convert_customer_id`

## 🚀 Cách chạy Migration

### Linux/Mac:
```bash
chmod +x scripts/run-remove-user-convert-customer-id.sh
./scripts/run-remove-user-convert-customer-id.sh
```

### Windows:
```powershell
.\scripts\run-remove-user-convert-customer-id.ps1
```

## 📊 Trước và Sau

### Trước (Old Structure):
```typescript
// UserOrder entity
class UserOrder {
  userConvertCustomerId: string;  // ❌ Foreign key
  convertCustomerName: string;
  convertCustomerPhone: string;
  convertCustomerEmail: string;
  
  // Relationship
  userConvertCustomer: UserConvertCustomer;  // ❌ Complex relationship
}

// API Response
{
  "id": "order-123",
  "userConvertCustomerId": "customer-456",  // ❌ Redundant
  "convertCustomerName": "Nguyễn Văn A",
  "convertCustomerPhone": "0912345678",
  "convertCustomerEmail": "<EMAIL>"
}
```

### Sau (New Structure):
```typescript
// UserOrder entity
class UserOrder {
  // ❌ userConvertCustomerId: REMOVED
  convertCustomerName: string;    // ✅ Direct storage
  convertCustomerPhone: string;   // ✅ Direct storage
  convertCustomerEmail: string;   // ✅ Direct storage
  countryCode: number;
}

// API Response
{
  "id": "order-123",
  "convertCustomerName": "Nguyễn Văn A",     // ✅ Clean structure
  "convertCustomerPhone": "0912345678",      // ✅ Direct access
  "convertCustomerEmail": "<EMAIL>"  // ✅ No JOIN needed
}
```

## ✅ Lợi ích

1. **Hiệu suất tốt hơn**: Không cần JOIN với bảng `user_convert_customers`
2. **Code đơn giản hơn**: Ít relationship phức tạp
3. **API response sạch hơn**: Không có trường redundant
4. **Dễ maintain**: Thông tin customer được lưu trực tiếp

## ⚠️ Lưu ý

1. **Backup dữ liệu**: Chạy migration trong môi trường test trước
2. **Test thoroughly**: Kiểm tra tất cả API liên quan đến orders
3. **Monitor performance**: Theo dõi hiệu suất sau khi deploy

## 🧪 Testing

Sau khi chạy migration, test các API sau:
- `GET /user/orders` - Danh sách đơn hàng
- `GET /user/orders/:id` - Chi tiết đơn hàng
- `POST /user/orders` - Tạo đơn hàng mới
- Admin APIs cho orders

## 📁 Files đã thay đổi

### Entities:
- `src/modules/business/entities/user-order.entity.ts`

### DTOs:
- `src/modules/business/user/dto/user-order-response.dto.ts`
- `src/modules/business/user/dto/query-user-order.dto.ts`
- `src/modules/business/admin/dto/userconverts/user-order-response.dto.ts`
- `src/modules/business/admin/dto/userconverts/query-user-order.dto.ts`

### Repositories:
- `src/modules/business/repositories/user-order.repository.ts`
- `src/modules/business/repositories/user-convert-customer.repository.ts`

### Services:
- `src/modules/business/user/services/order/user-order.service.ts`

### Migration & Scripts:
- `database/migrations/remove-user-convert-customer-id-from-user-orders.sql`
- `scripts/run-remove-user-convert-customer-id.sh`
- `scripts/run-remove-user-convert-customer-id.ps1`

### Tests:
- `src/modules/business/admin/tests/dto/query-user-order.dto.spec.ts`

## 🎉 Kết luận

Việc refactor này giúp đơn giản hóa cấu trúc UserOrder entity, loại bỏ dependency không cần thiết và cải thiện hiệu suất API. Thông tin khách hàng vẫn được bảo toàn đầy đủ thông qua các trường `convertCustomer*`.
