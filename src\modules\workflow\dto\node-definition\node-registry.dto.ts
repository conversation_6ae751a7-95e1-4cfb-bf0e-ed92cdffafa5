import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean, IsArray, IsEnum, IsObject, IsUrl, IsHexColor, ValidateNested, IsNumber, Min, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { NodeCategory } from '../../entities';

/**
 * DTO để tạo node definition mới
 */
export class CreateNodeDefinitionDto {
  @ApiProperty({ description: 'Loại node (e.g., "google.sheet.getRows")' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Tên hiển thị của node' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Mô tả chức năng của node' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '<PERSON>h mục của node', enum: NodeCategory })
  @IsEnum(NodeCategory)
  category: NodeCategory;

  @ApiProperty({ description: 'JSON Schema định nghĩa cấu trúc input' })
  @IsObject()
  inputSchema: Record<string, any>;

  @ApiProperty({ description: 'JSON Schema định nghĩa cấu trúc output' })
  @IsObject()
  outputSchema: Record<string, any>;

  @ApiProperty({ description: 'Phiên bản của node definition' })
  @IsString()
  version: string;

  // UI-related fields removed - FE will handle styling and metadata based on node type
}

/**
 * DTO để cập nhật node definition
 */
export class UpdateNodeDefinitionDto {
  @ApiPropertyOptional({ description: 'Tên hiển thị của node' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Mô tả chức năng của node' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'JSON Schema định nghĩa cấu trúc input' })
  @IsOptional()
  @IsObject()
  inputSchema?: Record<string, any>;

  @ApiPropertyOptional({ description: 'JSON Schema định nghĩa cấu trúc output' })
  @IsOptional()
  @IsObject()
  outputSchema?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Phiên bản của node definition' })
  @IsOptional()
  @IsString()
  version?: string;

  // UI-related fields removed - FE will handle styling and metadata based on node type
}

/**
 * DTO để filter node definitions
 */
export class NodeFilterDto {
  @ApiPropertyOptional({ description: 'Từ khóa tìm kiếm' })
  @IsOptional()
  @IsString()
  query?: string;

  @ApiPropertyOptional({ description: 'Danh mục node', enum: NodeCategory })
  @IsOptional()
  @IsEnum(NodeCategory)
  category?: NodeCategory;

  // Removed deprecated and UI-related filters - simplified to core functionality

  @ApiPropertyOptional({ description: 'Số lượng items per page', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value))
  limit?: number;

  @ApiPropertyOptional({ description: 'Offset cho pagination', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  offset?: number;
}

/**
 * DTO để search node definitions
 */
export class SearchOptionsDto {
  @ApiPropertyOptional({ description: 'Số lượng kết quả tối đa', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value))
  limit?: number;

  // Removed deprecated option - simplified search

  @ApiPropertyOptional({ description: 'Chỉ search trong category cụ thể', enum: NodeCategory })
  @IsOptional()
  @IsEnum(NodeCategory)
  category?: NodeCategory;
}

// Deprecated and UI-related DTOs removed - simplified to core functionality

/**
 * DTO response cho node documentation - simplified version
 */
export class NodeDocumentationDto {
  @ApiProperty({ description: 'Type của node' })
  type: string;

  @ApiProperty({ description: 'Tên node' })
  name: string;

  @ApiProperty({ description: 'Mô tả node' })
  description: string;

  @ApiProperty({ description: 'Input schema' })
  inputSchema: Record<string, any>;

  @ApiProperty({ description: 'Output schema' })
  outputSchema: Record<string, any>;

  @ApiProperty({ description: 'Version' })
  version: string;

  @ApiProperty({ description: 'Category', enum: NodeCategory })
  category: NodeCategory;
}

/**
 * DTO response cho node version
 */
export class NodeVersionDto {
  @ApiProperty({ description: 'Version' })
  version: string;

  @ApiProperty({ description: 'Ngày tạo' })
  createdAt: Date;

  @ApiProperty({ description: 'Changelog' })
  changelog: string;

  @ApiProperty({ description: 'Breaking changes' })
  breakingChanges: boolean;
}

/**
 * DTO response cho registry statistics - simplified version
 */
export class NodeRegistryStatsDto {
  @ApiProperty({ description: 'Tổng số node types' })
  total: number;

  @ApiProperty({ description: 'Số lượng theo category' })
  byCategory: Record<string, number>;

  @ApiProperty({ description: 'Số version khác nhau' })
  versions: number;
}
