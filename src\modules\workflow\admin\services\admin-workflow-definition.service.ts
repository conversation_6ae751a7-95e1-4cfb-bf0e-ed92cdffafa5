import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { WorkflowRepository } from '../../repositories/workflow.repository';
import { WorkflowNodeRepository } from '../../repositories/workflow-node.repository';
import { WorkflowEdgeRepository } from '../../repositories/workflow-edge.repository';
import { WorkflowValidationService, ValidationResult } from '../../services/workflow-validation.service';
import { WorkflowImportExportService } from '../../services/workflow-import-export.service';
import { 
  UpdateWorkflowDefinitionDto, 
  BulkImportRequestDto,
  TemplateCreationRequestDto
} from '../../dto/definition';
import { Workflow } from '../../entities/workflow.entity';
import { PaginatedResult } from '@common/response';
import { QueryWorkflowDto } from '../../dto';

/**
 * Admin Service để manage workflow definitions
 * Following existing admin service patterns với full access privileges
 */
@Injectable()
export class AdminWorkflowDefinitionService {
  private readonly logger = new Logger(AdminWorkflowDefinitionService.name);

  constructor(
    private readonly workflowRepository: WorkflowRepository,
    private readonly workflowNodeRepository: WorkflowNodeRepository,
    private readonly workflowEdgeRepository: WorkflowEdgeRepository,
    private readonly validationService: WorkflowValidationService,
    private readonly importExportService: WorkflowImportExportService,
  ) {}

  /**
   * Get all workflow definitions với pagination (admin privilege)
   * @param params - Query parameters
   * @returns Paginated workflow definitions
   */
  async getAllWorkflowDefinitions(params: QueryWorkflowDto, employeeId: number): Promise<PaginatedResult<Workflow>> {
    this.logger.log(`Admin getting all workflow definitions with params: ${JSON.stringify(params)}`);

    const queryDto = {
      page: params.page,
      limit: params.limit,
      search: params.search,
    };

    const result = await this.workflowRepository.findPaginated(queryDto, employeeId);

    this.logger.log(`Retrieved ${result.items.length} workflows for admin`);

    return result;
  }

  /**
   * Update any workflow definition (admin privilege)
   * @param workflowId - ID của workflow
   * @param updateDto - Definition update data
   * @param adminId - ID của admin thực hiện update
   * @returns Updated workflow với validation results
   */
  async updateWorkflowDefinition(
    workflowId: string,
    updateDto: UpdateWorkflowDefinitionDto,
    adminId: number
  ): Promise<{ workflow: Workflow; validation: ValidationResult }> {
    this.logger.log(`Admin ${adminId} updating workflow definition: ${workflowId}`);

    // 1. Find workflow (admin can access any workflow)
    const workflow = await this.workflowRepository.findById(workflowId);
    if (!workflow) {
      throw new NotFoundException(`Workflow with ID '${workflowId}' not found`);
    }

    // 2. Validate definition structure
    const validationInput = {
      nodes: updateDto.nodes || [],
      edges: updateDto.edges || [],
      metadata: updateDto.metadata ? JSON.parse(JSON.stringify(updateDto.metadata)) : {}
    };
    const validation = await this.validationService.validateWorkflowDefinition(validationInput);
    
    if (!validation.isValid) {
      throw new BadRequestException({
        message: 'Workflow definition validation failed',
        errors: validation.errors,
        warnings: validation.warnings
      });
    }

    // 3. Update workflow definition
    const updatedDefinition = {
      nodes: updateDto.nodes || [],
      edges: updateDto.edges || [],
      metadata: {
        ...workflow.definition.metadata,
        ...updateDto.metadata,
        lastModified: {
          timestamp: Date.now(),
          adminId,
          changes: 'Updated by admin'
        }
      }
    };

    // 4. Save to database using repository
    await this.workflowRepository.updateWorkflowDefinition(workflowId, updatedDefinition);

    // 5. Update normalized tables
    await this.updateNormalizedTables(workflowId, updateDto);

    // 6. Get updated workflow
    const savedWorkflow = await this.workflowRepository.findById(workflowId);
    if (!savedWorkflow) {
      throw new NotFoundException('Workflow not found after update');
    }

    this.logger.log(`Admin workflow definition updated successfully: ${workflowId}`);

    return {
      workflow: savedWorkflow,
      validation
    };
  }

  /**
   * Validate workflow definition (admin privilege)
   * @param workflowId - ID của workflow
   * @param definition - Definition to validate
   * @returns Validation result
   */
  async validateWorkflowDefinition(
    workflowId: string,
    definition: Record<string, any>
  ): Promise<ValidationResult> {
    this.logger.log(`Admin validating workflow definition: ${workflowId}`);

    // Admin can validate any workflow without permission check
    const validationInput = {
      nodes: definition.nodes || [],
      edges: definition.edges || [],
      metadata: definition.metadata || {}
    };
    const validation = await this.validationService.validateWorkflowDefinition(validationInput);

    this.logger.log(`Admin validation completed for workflow: ${workflowId}, valid: ${validation.isValid}`);

    return validation;
  }

  /**
   * Bulk import workflows (admin privilege)
   * @param importRequest - Bulk import request
   * @param adminId - ID của admin
   * @returns Import results
   */
  async bulkImportWorkflows(
    importRequest: BulkImportRequestDto,
    adminId: number
  ): Promise<any> {
    this.logger.log(`Admin ${adminId} performing bulk import of ${importRequest.workflows.length} workflows`);

    const results = await this.importExportService.importWorkflows(
      importRequest.workflows,
      adminId,
      {
        ...importRequest.options,
        adminImport: true // Special flag for admin imports
      } as any
    );

    this.logger.log(`Admin bulk import completed: ${results.length} workflows processed`);

    return {
      results,
      summary: {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        processingTime: Date.now()
      }
    };
  }

  /**
   * Create workflow template (admin privilege)
   * @param templateRequest - Template creation request
   * @param adminId - ID của admin
   * @returns Template creation result
   */
  async createWorkflowTemplate(
    templateRequest: TemplateCreationRequestDto,
    adminId: number
  ): Promise<any> {
    this.logger.log(`Admin ${adminId} creating template from workflow: ${templateRequest.workflowId}`);

    const result = await this.importExportService.createWorkflowTemplate(
      templateRequest.workflowId,
      templateRequest.templateName,
      adminId
    );

    // Add admin-specific metadata
    (result.workflow.metadata as any) = {
      ...result.workflow.metadata,
      isAdminTemplate: true,
      adminCreatedBy: adminId,
      ...templateRequest.templateMetadata
    };

    this.logger.log(`Admin template created successfully: ${templateRequest.templateName}`);

    return result;
  }

  /**
   * Get workflow templates (admin privilege)
   * @param params - Query parameters
   * @returns Paginated templates
   */
  async getWorkflowTemplates(params: {
    page: number;
    limit: number;
    category?: string;
    search?: string;
  }): Promise<PaginatedResult<any>> {
    this.logger.log(`Admin getting workflow templates with params: ${JSON.stringify(params)}`);

    // Admin can see all templates including private ones
    const result = await this.workflowRepository.findTemplatesWithPagination({
      ...params,
      includePrivate: true
    });

    this.logger.log(`Retrieved ${result.data.length} templates for admin`);

    return result;
  }

  /**
   * Delete workflow template (admin privilege)
   * @param templateId - Template ID
   * @param adminId - ID của admin
   */
  async deleteWorkflowTemplate(templateId: string, adminId: number): Promise<void> {
    this.logger.log(`Admin ${adminId} deleting template: ${templateId}`);

    const result = await this.workflowRepository.deleteTemplate(templateId);
    
    if (!result.affected) {
      throw new NotFoundException(`Template with ID '${templateId}' not found`);
    }

    this.logger.log(`Admin template deleted successfully: ${templateId}`);
  }

  /**
   * Get workflow statistics (admin privilege)
   * @param timeRange - Time range for statistics
   * @returns Comprehensive statistics
   */
  async getWorkflowStatistics(timeRange?: string): Promise<any> {
    this.logger.log(`Admin getting workflow statistics for time range: ${timeRange}`);

    const statistics = await this.workflowRepository.getComprehensiveStatistics(timeRange);

    // Add validation statistics
    const validationStats = await this.getValidationStatistics();
    
    const result = {
      ...statistics,
      validation: validationStats,
      generatedAt: Date.now(),
      timeRange
    };

    this.logger.log('Admin workflow statistics generated successfully');

    return result;
  }

  /**
   * Validate all workflows (admin privilege)
   * @param adminId - ID của admin
   * @returns Validation results for all workflows
   */
  async validateAllWorkflows(adminId: number): Promise<any> {
    this.logger.log(`Admin ${adminId} validating all workflows`);

    const allWorkflows = await this.workflowRepository.findAllForValidation();
    const results: any[] = [];

    for (const workflow of allWorkflows) {
      try {
        const validationInput = {
          nodes: workflow.definition?.nodes || [],
          edges: workflow.definition?.edges || [],
          metadata: workflow.definition?.metadata || {}
        };
        const validation = await this.validationService.validateWorkflowDefinition(validationInput);
        results.push({
          workflowId: workflow.id,
          workflowName: workflow.name,
          userId: workflow.userId,
          validation
        });
      } catch (error) {
        results.push({
          workflowId: workflow.id,
          workflowName: workflow.name,
          userId: workflow.userId,
          validation: {
            isValid: false,
            errors: [{ code: 'VALIDATION_ERROR', message: error.message, severity: 'error' }],
            warnings: []
          }
        });
      }
    }

    const summary = {
      total: results.length,
      valid: results.filter(r => r.validation.isValid).length,
      invalid: results.filter(r => !r.validation.isValid).length,
      totalErrors: results.reduce((sum, r) => sum + r.validation.errors.length, 0),
      totalWarnings: results.reduce((sum, r) => sum + r.validation.warnings.length, 0)
    };

    this.logger.log(`Admin validation completed: ${summary.valid}/${summary.total} workflows valid`);

    return { results, summary };
  }

  /**
   * Fix workflow definitions (admin privilege)
   * @param fixOptions - Fix options
   * @param adminId - ID của admin
   * @returns Fix results
   */
  async fixWorkflowDefinitions(
    fixOptions: { fixTypes?: string[]; dryRun?: boolean },
    adminId: number
  ): Promise<any> {
    this.logger.log(`Admin ${adminId} fixing workflow definitions with options: ${JSON.stringify(fixOptions)}`);

    const allWorkflows = await this.workflowRepository.findAllForValidation();
    const results: any[] = [];

    for (const workflow of allWorkflows) {
      try {
        const validationInput = {
          nodes: workflow.definition?.nodes || [],
          edges: workflow.definition?.edges || [],
          metadata: workflow.definition?.metadata || {}
        };
        const validation = await this.validationService.validateWorkflowDefinition(validationInput);
        
        if (!validation.isValid) {
          const fixes = await this.generateFixes(workflow, validation, fixOptions.fixTypes);
          
          if (!fixOptions.dryRun && fixes.length > 0) {
            // Apply fixes
            await this.applyFixes(workflow.id, fixes, adminId);
          }

          results.push({
            workflowId: workflow.id,
            workflowName: workflow.name,
            issues: validation.errors.length + validation.warnings.length,
            fixes: fixes.length,
            applied: !fixOptions.dryRun
          });
        }
      } catch (error) {
        results.push({
          workflowId: workflow.id,
          workflowName: workflow.name,
          error: error.message
        });
      }
    }

    const summary = {
      total: results.length,
      fixed: results.filter(r => r.fixes > 0).length,
      totalFixes: results.reduce((sum, r) => sum + (r.fixes || 0), 0),
      dryRun: fixOptions.dryRun
    };

    this.logger.log(`Admin fix completed: ${summary.fixed} workflows processed, ${summary.totalFixes} fixes`);

    return { results, summary };
  }

  /**
   * Update normalized tables
   * @param workflowId - ID của workflow
   * @param definition - Workflow definition
   */
  private async updateNormalizedTables(
    workflowId: string,
    definition: UpdateWorkflowDefinitionDto
  ): Promise<void> {
    // Remove existing normalized records
    await this.workflowNodeRepository.deleteByWorkflowId(workflowId);
    await this.workflowEdgeRepository.deleteByWorkflowId(workflowId);

    // Create new normalized records
    if (definition.nodes) {
      const nodeEntities = definition.nodes.map(node => 
        this.workflowNodeRepository.create({
          workflowId,
          nodeId: node.id,
          nodeType: node.type,
          name: node.name,
          position: node.position,
          config: {
            inputs: node.inputs,
            outputs: node.outputs,
            config: node.config,
            metadata: node.metadata
          }
        })
      );
      await this.workflowNodeRepository.bulkInsert(nodeEntities);
    }

    if (definition.edges) {
      const edgeEntities = definition.edges.map(edge =>
        this.workflowEdgeRepository.create({
          workflowId,
          edgeId: edge.id,
          sourceNodeId: edge.sourceNodeId,
          targetNodeId: edge.targetNodeId,
          edgeType: edge.edgeType || 'normal',
          condition: edge.condition,
          metadata: edge.metadata
        })
      );
      await this.workflowEdgeRepository.bulkInsert(edgeEntities);
    }
  }

  /**
   * Get validation statistics
   * @returns Validation statistics
   */
  private async getValidationStatistics(): Promise<any> {
    const nodeStats = await this.workflowNodeRepository.getStatistics();
    const edgeStats = await this.workflowEdgeRepository.getStatistics();

    return {
      nodes: nodeStats,
      edges: edgeStats,
      generatedAt: Date.now()
    };
  }

  /**
   * Generate fixes for workflow issues
   * @param workflow - Workflow to fix
   * @param validation - Validation result
   * @param fixTypes - Types of fixes to apply
   * @returns Array of fixes
   */
  private async generateFixes(
    workflow: Workflow,
    validation: ValidationResult,
    fixTypes?: string[]
  ): Promise<any[]> {
    const fixes: any[] = [];

    for (const error of validation.errors) {
      switch (error.code) {
        case 'DUPLICATE_NODE_IDS':
          if (!fixTypes || fixTypes.includes('duplicate_ids')) {
            fixes.push({
              type: 'rename_duplicate_nodes',
              description: 'Rename duplicate node IDs',
              code: error.code
            });
          }
          break;
        case 'INVALID_EDGE_REFERENCES':
          if (!fixTypes || fixTypes.includes('invalid_references')) {
            fixes.push({
              type: 'remove_invalid_edges',
              description: 'Remove edges with invalid node references',
              code: error.code
            });
          }
          break;
        case 'CIRCULAR_DEPENDENCY':
          if (!fixTypes || fixTypes.includes('circular_deps')) {
            fixes.push({
              type: 'break_circular_dependencies',
              description: 'Break circular dependencies',
              code: error.code
            });
          }
          break;
      }
    }

    return fixes;
  }

  /**
   * Apply fixes to workflow
   * @param workflowId - Workflow ID
   * @param fixes - Fixes to apply
   * @param adminId - Admin ID
   */
  private async applyFixes(workflowId: string, fixes: any[], adminId: number): Promise<void> {
    // Implementation would depend on specific fix types
    // This is a placeholder for the fix application logic
    this.logger.log(`Applying ${fixes.length} fixes to workflow ${workflowId} by admin ${adminId}`);
  }
}
