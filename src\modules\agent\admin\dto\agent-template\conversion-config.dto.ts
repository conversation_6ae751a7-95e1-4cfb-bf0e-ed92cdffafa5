import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsIn, IsNotEmpty, IsObject, IsOptional, IsString} from 'class-validator';

/**
 * DTO cho cấu hình conversion - implement ConversionConfig interface
 */
export class ConversionConfigDto implements ConvertConfig {
  /**
   * Tên của field trong schema JSON
   */
  @ApiProperty({
    description: 'Tên của field trong schema JSON',
    example: 'email',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * Kiểu dữ liệu của field theo chuẩn JSON Schema
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu của field theo chuẩn JSON Schema',
    enum: ['string', 'number', 'boolean', 'array', 'object'],
    example: 'string',
  })
  @IsString()
  @IsIn(['string', 'number', 'boolean', 'array', 'object'])
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';

  /**
   * Mô tả (nội dung) của field
   */
  @ApiProperty({
    description: 'Mô tả (nội dung) của field',
    example: 'Địa chỉ email người dùng',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  /**
   * Trường này có bắt buộc không?
   */
  @ApiProperty({
    description: 'Trường này có bắt buộc không?',
    example: true,
  })
  @IsBoolean()
  required: boolean;

  /**
   * Giá trị mặc định theo chuẩn JSON Schema
   */
  @ApiPropertyOptional({
    description: 'Giá trị mặc định cho field theo chuẩn JSON Schema',
    example: '',
  })
  @IsOptional()
  default?: any;

  /**
   * Định nghĩa kiểu dữ liệu cho array (nếu type là array)
   */
  @ApiPropertyOptional({
    description: 'Định nghĩa kiểu dữ liệu cho array (nếu type là array)',
    example: { type: 'string', description: 'Danh sách chuỗi' },
  })
  @IsOptional()
  @IsObject()
  items?: {
    type: 'string' | 'number' | 'boolean' | 'object';
    description?: string;
  };

  /**
   * Định nghĩa properties cho object (nếu type là object)
   */
  @ApiPropertyOptional({
    description: 'Định nghĩa properties cho object (nếu type là object)',
  })
  @IsOptional()
  @IsObject()
  properties?: Record<string, ConvertConfig>;

  /**
   * Enum values (nếu cần giới hạn giá trị)
   */
  @ApiPropertyOptional({
    description: 'Enum values (nếu cần giới hạn giá trị)',
    example: ['option1', 'option2', 'option3'],
  })
  @IsOptional()
  @IsArray()
  enum?: any[];

  /**
   * Trường này có thể xóa không? (email và phone luôn là false)
   */
  @ApiPropertyOptional({
    description: 'Trường này có thể xóa không? (email và phone luôn là false)',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  deletable?: boolean;
}
