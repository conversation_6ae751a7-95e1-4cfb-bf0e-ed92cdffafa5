import { SystemConfigurationModule } from '@modules/system-configuration';
import { ToolsModule } from '@modules/tools/tools.module';
import { User } from '@modules/user/entities';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CdnService } from '@shared/services/cdn.service';
import { RedisService } from '@shared/services/redis.service';
import { S3Service } from '@shared/services/s3.service';
import { Cart, CartItem, MarketOrder, MarketOrderLine, Product, UserBuyProductMarketplace, FlashSale } from '../entities';
import { CartHelper, ProductHelper, ProductAutoHelper, PurchaseHistoryHelper, ValidationHelper, FlashSaleValidationHelper } from '../helpers';
import { CartItemRepository, CartRepository, MarketOrderLineRepository, MarketOrderRepository, ProductRepository, UserBuyProductMarketplaceRepository, FlashSaleRepository } from '../repositories';
import { CartUserController, OrderUserController, PaymentController, ProductUserController, FlashSaleUserController } from './controllers';
import {
  CartUserService,
  OrderUserService,
  PaymentService,
  ProductUserService,
  FlashSaleUserService
} from './services';
// Import entities và repositories cho resource sharing
import { Agent, AgentMemories, TypeAgent } from '@modules/agent/entities';
import { AgentMemoriesRepository, AgentRepository, TypeAgentRepository } from '@modules/agent/repositories';
import { KnowledgeFile } from '@modules/data/knowledge-files/entities';
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { AdminDataFineTune, UserDataFineTune } from '@modules/models/entities';
import { AdminDataFineTuneRepository, UserDataFineTuneRepository } from '@modules/models/repositories';
import { AdminTool, UserTool } from '@modules/tools/entities';
import { AdminToolRepository, UserToolRepository } from '@modules/tools/repositories';
// Import ResourceSharingService
import { ResourceSharingService } from '../shares/resource-sharing.service';
// Import Agent services
import { AgentSharingService } from '../services/agent-sharing.service';
// ✅ THÊM MỚI: Import Flash Sale Status Update Service
import { FlashSaleStatusUpdateService } from '../services/flash-sale-status-update.service';
// Import Validators
import { AgentValidator, FineTuneValidator, KnowledgeFileValidator } from '../validation';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product, Cart, CartItem, MarketOrder, MarketOrderLine, UserBuyProductMarketplace, FlashSale, User,
      KnowledgeFile, Agent, AgentMemories, TypeAgent,
      UserDataFineTune, AdminDataFineTune, UserTool, AdminTool
    ]),
    ScheduleModule.forRoot(),
    SystemConfigurationModule,
    ToolsModule,
  ],
  controllers: [ProductUserController, CartUserController, OrderUserController, PaymentController, FlashSaleUserController],
  providers: [
    ProductUserService,
    CartUserService,
    OrderUserService,
    PaymentService,
    FlashSaleUserService,
    ProductRepository,
    CartRepository,
    CartItemRepository,
    MarketOrderRepository,
    MarketOrderLineRepository,
    UserBuyProductMarketplaceRepository,
    FlashSaleRepository,
    UserRepository,
    KnowledgeFileRepository,
    AgentRepository,
    TypeAgentRepository,
    AgentMemoriesRepository,
    UserDataFineTuneRepository,
    AdminDataFineTuneRepository,
    UserToolRepository,
    AdminToolRepository,
    S3Service,
    RedisService,
    CdnService,
    ProductHelper,
    ProductAutoHelper,
    ValidationHelper,
    CartHelper,
    PurchaseHistoryHelper,
    FlashSaleValidationHelper,
    ResourceSharingService,
    AgentSharingService,
    // ✅ THÊM MỚI: Flash Sale Status Update Service
    FlashSaleStatusUpdateService,
    // Validators
    AgentValidator,
    KnowledgeFileValidator,
    FineTuneValidator
  ],
  exports: [ProductUserService, CartUserService, OrderUserService, PaymentService],
})
export class MarketplaceUserModule { }
