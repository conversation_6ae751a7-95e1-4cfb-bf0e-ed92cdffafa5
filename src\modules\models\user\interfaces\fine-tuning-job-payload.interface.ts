/**
 * Interface cho payload của Redis job fine-tuning
 */
export interface FineTuningJobPayload {
  /**
   * ID của user
   */
  userId: number;

  /**
   * ID của model mới được tạo
   */
  modelId: string;

  /**
   * Chi phí đã trừ (để refund nếu thất bại)
   */
  costDeducted: number;
}

/**
 * Enum cho tên job Redis
 */
export enum FineTuningJobName {
  MONITOR_FINE_TUNING = 'monitor-fine-tuning',
  UPDATE_MODEL_STATUS = 'update-model-status',
  REFUND_ON_FAILURE = 'refund-on-failure'
}
