import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards, 
  ParseIntPipe 
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth, 
  ApiParam, 
  ApiQuery, 
  ApiBody 
} from '@nestjs/swagger';
import { AdminEmailCampaignService } from '../services/admin-email-campaign.service';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { Employee } from '@modules/employee/entities/employee.entity';
import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import {
  CreateAdminEmailCampaignDto,
  CreateAdminEmailCampaignResponseDto,
  UpdateAdminEmailCampaignDto,
  UpdateAdminEmailCampaignResponseDto,
  AdminEmailCampaignQueryDto,
  AdminEmailCampaignItemDto,
  AdminEmailCampaignDetailDto,
  AdminEmailCampaignStatsDto,
  AdminEmailCampaignOverallStatsDto,
  BulkDeleteAdminEmailCampaignDto,
  BulkDeleteAdminEmailCampaignResponseDto,
  PauseAdminEmailCampaignResponseDto,
  ResumeAdminEmailCampaignResponseDto,
  SyncCampaignStatusResponseDto,
} from '../dto/admin-email-campaign';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';

/**
 * Controller xử lý tất cả API liên quan đến admin email campaigns
 * Bao gồm tạo, quản lý, thống kê và báo cáo email campaigns cho admin
 */
@RequirePermissionEnum(Permission.MARKETING_VIEW)
@ApiTags(SWAGGER_API_TAGS.ADMIN_EMAIL_CAMPAIGN)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/email-campaigns')
export class AdminEmailCampaignController {
  constructor(
    private readonly adminEmailCampaignService: AdminEmailCampaignService
  ) {}

  /**
   * Tạo admin email campaign mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo admin email campaign',
    description: `Tạo chiến dịch email marketing cho admin với các tùy chọn:

    **Loại đối tượng nhận (targetType):**
    - ADMIN_AUDIENCE: Gửi đến admin audience
    - USER: Gửi đến user thực tế thông qua segment criteria
    - USER_AUDIENCE: Gửi đến audience của user cụ thể
    - CUSTOM_EMAIL_LIST: Gửi đến danh sách email tùy chỉnh

    **Các trường bắt buộc theo targetType:**
    | targetType | Trường bắt buộc |
    |------------|-----------------|
    | ADMIN_AUDIENCE | segment (với id) |
    | USER | segment (với id) |
    | USER_AUDIENCE | userId + audiences |
    | CUSTOM_EMAIL_LIST | emailList |

    **Tính năng:**
    - Gửi ngay lập tức hoặc lên lịch gửi
    - Sử dụng template variables hoặc nội dung tùy chỉnh
    - Tự động tạo jobs và đẩy vào queue để worker xử lý`
  })
  @ApiResponse({
    status: 201,
    description: 'Admin email campaign đã được tạo thành công',
    type: CreateAdminEmailCampaignResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Admin email campaign đã được tạo thành công',
        data: {
          id: 1,
          name: 'Admin Newsletter Campaign',
          subject: 'Thông báo cập nhật hệ thống',
          status: 'SCHEDULED',
          totalRecipients: 500,
          jobCount: 1,
          scheduledAt: 1703980800,
          createdAt: 1703894400,
          createdBy: 1
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      examples: {
        'missing-target-type': {
          summary: 'Thiếu target type',
          value: {
            success: false,
            message: 'Validation failed',
            errors: ['Target type không được để trống']
          }
        },
        'invalid-target-type': {
          summary: 'Target type không hợp lệ',
          value: {
            success: false,
            message: 'Validation failed',
            errors: ['Target type phải là một trong các giá trị: ADMIN_AUDIENCE, USER, USER_AUDIENCE, CUSTOM_EMAIL_LIST']
          }
        },
        'missing-segment-id': {
          summary: 'Thiếu segment ID khi target type là ADMIN_AUDIENCE hoặc USER',
          value: {
            success: false,
            message: 'Validation failed',
            errors: ['Segment ID là bắt buộc khi target type là ADMIN_AUDIENCE hoặc USER']
          }
        },
        'missing-user-id': {
          summary: 'Thiếu user ID khi target type là USER_AUDIENCE',
          value: {
            success: false,
            message: 'Validation failed',
            errors: ['User ID là bắt buộc khi target type là USER_AUDIENCE']
          }
        },
        'missing-email-list': {
          summary: 'Thiếu email list khi target type là CUSTOM_EMAIL_LIST',
          value: {
            success: false,
            message: 'Validation failed',
            errors: ['Email list là bắt buộc khi target type là CUSTOM_EMAIL_LIST']
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy resource',
    schema: {
      examples: {
        'segment-not-found': {
          summary: 'Segment không tồn tại',
          value: {
            success: false,
            message: 'Segment với ID 999 không tồn tại'
          }
        },
        'user-not-found': {
          summary: 'User không tồn tại',
          value: {
            success: false,
            message: 'User với ID 999 không tồn tại'
          }
        }
      }
    }
  })
  async create(
    @CurrentEmployee() employee: Employee,
    @Body() createDto: CreateAdminEmailCampaignDto,
  ): Promise<AppApiResponse<CreateAdminEmailCampaignResponseDto>> {
    const result = await this.adminEmailCampaignService.create(createDto, employee.id);
    return wrapResponse(result, 'Admin email campaign đã được tạo thành công');
  }

  /**
   * Lấy danh sách admin email campaign với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách admin email campaign',
    description: `Lấy danh sách các chiến dịch email admin với các tùy chọn:
    - Phân trang với page và limit
    - Tìm kiếm theo tên campaign hoặc subject
    - Filter theo trạng thái (DRAFT, SCHEDULED, SENDING, COMPLETED, FAILED, CANCELLED)
    - Filter theo ngày tạo, template, segment
    - Sắp xếp theo các trường khác nhau
    - Thống kê cơ bản cho mỗi campaign`
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách admin email campaign với phân trang',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: {
              allOf: [
                { $ref: '#/components/schemas/PaginatedResult' },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/AdminEmailCampaignItemDto' }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  async findAll(
    @Query() queryDto: AdminEmailCampaignQueryDto,
  ): Promise<AppApiResponse<PaginatedResult<AdminEmailCampaignItemDto>>> {
    const result = await this.adminEmailCampaignService.findAll(queryDto);
    return wrapResponse(result, 'Danh sách admin email campaign');
  }

  /**
   * Lấy chi tiết admin email campaign
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết admin email campaign',
    description: 'Lấy thông tin chi tiết của một admin email campaign theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của admin email campaign',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết admin email campaign',
    type: AdminEmailCampaignDetailDto
  })
  @ApiResponse({
    status: 404,
    description: 'Admin email campaign không tồn tại'
  })
  async findById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<AppApiResponse<AdminEmailCampaignDetailDto>> {
    const result = await this.adminEmailCampaignService.findById(id);
    return wrapResponse(result, 'Chi tiết admin email campaign');
  }

  /**
   * Cập nhật admin email campaign
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật admin email campaign',
    description: 'Cập nhật thông tin của một admin email campaign theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của admin email campaign',
    type: 'number',
    example: 1
  })
  @ApiBody({
    type: UpdateAdminEmailCampaignDto,
    description: 'Thông tin admin email campaign cần cập nhật'
  })
  @ApiResponse({
    status: 200,
    description: 'Admin email campaign đã được cập nhật thành công',
    type: UpdateAdminEmailCampaignResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Admin email campaign không tồn tại'
  })
  @ApiResponse({
    status: 400,
    description: 'Không thể cập nhật campaign đang gửi hoặc đã hoàn thành'
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateAdminEmailCampaignDto,
    @CurrentEmployee() employee: Employee,
  ): Promise<AppApiResponse<UpdateAdminEmailCampaignResponseDto>> {
    const result = await this.adminEmailCampaignService.update(id, updateDto, employee.id);
    return wrapResponse(result, 'Admin email campaign đã được cập nhật thành công');
  }

  /**
   * Lấy thống kê admin email campaign
   */
  @Get(':id/stats')
  @ApiOperation({
    summary: 'Lấy thống kê admin email campaign',
    description: 'Lấy thống kê chi tiết của một admin email campaign theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của admin email campaign',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Thống kê admin email campaign',
    type: AdminEmailCampaignStatsDto
  })
  @ApiResponse({
    status: 404,
    description: 'Thống kê cho campaign không tồn tại'
  })
  async getStats(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<AppApiResponse<AdminEmailCampaignStatsDto>> {
    const result = await this.adminEmailCampaignService.getStats(id);
    return wrapResponse(result, 'Thống kê admin email campaign');
  }

  /**
   * Lấy tổng thống kê tất cả admin email campaign
   */
  @Get('stats/overview')
  @ApiOperation({
    summary: 'Lấy tổng thống kê admin email campaign',
    description: 'Lấy thống kê tổng quan của tất cả admin email campaign'
  })
  @ApiResponse({
    status: 200,
    description: 'Tổng thống kê admin email campaign',
    type: AdminEmailCampaignOverallStatsDto,
    schema: {
      example: {
        success: true,
        message: 'Tổng thống kê admin email campaign',
        data: {
          totalSent: 5600,
          totalOpens: 2240,
          totalClicks: 448,
          totalBounces: 56,
          totalUnsubscribes: 28,
          averageOpenRate: 40.0,
          averageClickRate: 8.0,
          averageBounceRate: 1.0,
          totalCampaigns: 15,
          activeCampaigns: 2,
          completedCampaigns: 12,
          failedCampaigns: 1
        }
      }
    }
  })
  async getOverallStats(): Promise<AppApiResponse<AdminEmailCampaignOverallStatsDto>> {
    const result = await this.adminEmailCampaignService.getOverallStats();
    return wrapResponse(result, 'Tổng thống kê admin email campaign');
  }

  /**
   * Xóa admin email campaign
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa admin email campaign',
    description: 'Xóa một admin email campaign theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của admin email campaign',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Admin email campaign đã được xóa thành công'
  })
  @ApiResponse({
    status: 404,
    description: 'Admin email campaign không tồn tại'
  })
  @ApiResponse({
    status: 400,
    description: 'Không thể xóa campaign đang gửi'
  })
  async delete(
    @Param('id', ParseIntPipe) id: number,
    @CurrentEmployee() employee: Employee,
  ): Promise<AppApiResponse<{ success: boolean }>> {
    await this.adminEmailCampaignService.delete(id, employee.id);
    return wrapResponse({ success: true }, 'Admin email campaign đã được xóa thành công');
  }

  /**
   * Xóa nhiều admin email campaign
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa nhiều admin email campaign',
    description: 'Xóa nhiều admin email campaign theo danh sách ID'
  })
  @ApiBody({
    type: BulkDeleteAdminEmailCampaignDto,
    description: 'Danh sách ID campaign cần xóa'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa nhiều admin email campaign',
    type: BulkDeleteAdminEmailCampaignResponseDto
  })
  @ApiResponse({
    status: 207,
    description: 'Một số admin email campaign không thể xóa',
    type: BulkDeleteAdminEmailCampaignResponseDto
  })
  async bulkDelete(
    @Body() bulkDeleteDto: BulkDeleteAdminEmailCampaignDto,
    @CurrentEmployee() employee: Employee,
  ): Promise<AppApiResponse<BulkDeleteAdminEmailCampaignResponseDto>> {
    const result = await this.adminEmailCampaignService.bulkDelete(bulkDeleteDto.ids, employee.id);
    return wrapResponse(result, result.message);
  }

  /**
   * Tạm dừng admin email campaign
   */
  @Post(':id/pause')
  @ApiOperation({
    summary: 'Tạm dừng admin email campaign',
    description: `Tạm dừng chiến dịch email admin đang gửi hoặc đã lên lịch:
    - Chỉ áp dụng cho chiến dịch có trạng thái SENDING hoặc SCHEDULED
    - Hủy tất cả jobs đang chờ trong queue
    - Cập nhật trạng thái chiến dịch thành PAUSED
    - Có thể tiếp tục chiến dịch sau này bằng API resume`,
  })
  @ApiParam({
    name: 'id',
    description: 'ID của admin email campaign',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Tạm dừng chiến dịch thành công',
    type: PauseAdminEmailCampaignResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Chiến dịch đã được tạm dừng thành công',
        data: {
          campaignId: 123,
          campaignName: 'Summer Sale Campaign',
          previousStatus: 'SENDING',
          currentStatus: 'PAUSED',
          canceledJobsCount: 150,
          pausedAt: 1703123456,
          message: 'Chiến dịch đã được tạm dừng thành công',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Trạng thái chiến dịch không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Chỉ có thể tạm dừng chiến dịch đang gửi (SENDING) hoặc đã lên lịch (SCHEDULED)',
        errorCode: 15080,
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Chiến dịch không tồn tại',
    schema: {
      example: {
        success: false,
        message: 'Campaign với ID 123 không tồn tại',
        errorCode: 15020,
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Không có quyền truy cập',
    schema: {
      example: {
        success: false,
        message: 'Bạn không có quyền truy cập campaign này',
        errorCode: 15134,
      },
    },
  })
  async pauseCampaign(
    @CurrentEmployee() employee: Employee,
    @Param('id', ParseIntPipe) campaignId: number,
  ): Promise<AppApiResponse<PauseAdminEmailCampaignResponseDto>> {
    const result = await this.adminEmailCampaignService.pauseCampaign(
      campaignId,
      employee.id,
    );
    return wrapResponse(result, result.message);
  }

  /**
   * Tiếp tục admin email campaign đã tạm dừng
   */
  @Post(':id/resume')
  @ApiOperation({
    summary: 'Tiếp tục admin email campaign đã tạm dừng',
    description: `Tiếp tục chiến dịch email admin đã tạm dừng:
    - Chỉ áp dụng cho chiến dịch có trạng thái PAUSED
    - Tạo lại jobs cho tất cả email chưa được gửi
    - Cập nhật trạng thái chiến dịch thành SENDING hoặc SCHEDULED (tùy vào scheduledAt)
    - Nếu scheduledAt > hiện tại: trạng thái SCHEDULED
    - Nếu scheduledAt <= hiện tại: trạng thái SENDING`,
  })
  @ApiParam({
    name: 'id',
    description: 'ID của admin email campaign',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Tiếp tục chiến dịch thành công',
    type: ResumeAdminEmailCampaignResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Chiến dịch đã được tiếp tục thành công',
        data: {
          campaignId: 123,
          campaignName: 'Summer Sale Campaign',
          previousStatus: 'PAUSED',
          currentStatus: 'SENDING',
          recreatedJobsCount: 150,
          resumedAt: 1703123456,
          message: 'Chiến dịch đã được tiếp tục thành công',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Trạng thái chiến dịch không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Chỉ có thể tiếp tục chiến dịch đã tạm dừng (PAUSED)',
        errorCode: 15081,
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Chiến dịch không tồn tại',
    schema: {
      example: {
        success: false,
        message: 'Campaign với ID 123 không tồn tại',
        errorCode: 15020,
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Không có quyền truy cập',
    schema: {
      example: {
        success: false,
        message: 'Bạn không có quyền truy cập campaign này',
        errorCode: 15134,
      },
    },
  })
  async resumeCampaign(
    @CurrentEmployee() employee: Employee,
    @Param('id', ParseIntPipe) campaignId: number,
  ): Promise<AppApiResponse<ResumeAdminEmailCampaignResponseDto>> {
    const result = await this.adminEmailCampaignService.resumeCampaign(
      campaignId,
      employee.id,
    );
    return wrapResponse(result, result.message);
  }

  /**
   * Cập nhật trạng thái campaign dựa trên queue status
   */
  @Post('sync-status')
  @ApiOperation({
    summary: 'Cập nhật trạng thái campaign dựa trên queue status',
    description: `Kiểm tra và cập nhật trạng thái các campaign dựa trên trạng thái job trong queue và thời gian lên lịch:

    **Logic cập nhật:**
    - SCHEDULED → FAILED: Nếu scheduledAt đã quá và không còn job nào trong queue
    - SENDING → COMPLETED: Nếu tất cả job đã hoàn thành thành công
    - SENDING → FAILED: Nếu tất cả job đã thất bại hoặc không còn job nào trong queue và quá thời gian

    **Điều kiện kiểm tra:**
    - Chỉ kiểm tra campaign có trạng thái SCHEDULED hoặc SENDING
    - Kiểm tra trạng thái job trong queue (waiting, active, completed, failed)
    - So sánh thời gian hiện tại với scheduledAt`
  })
  @ApiResponse({
    status: 200,
    description: 'Đã cập nhật trạng thái campaign thành công',
    schema: {
      example: {
        success: true,
        message: 'Đã cập nhật trạng thái campaign thành công',
        data: {
          totalCampaignsChecked: 25,
          updatedCampaigns: [
            {
              campaignId: 1,
              campaignName: 'Welcome Email',
              previousStatus: 'SCHEDULED',
              currentStatus: 'FAILED',
              reason: 'Quá thời gian lên lịch và không còn job trong queue'
            },
            {
              campaignId: 2,
              campaignName: 'Newsletter',
              previousStatus: 'SENDING',
              currentStatus: 'COMPLETED',
              reason: 'Tất cả job đã hoàn thành thành công'
            }
          ],
          summary: {
            scheduledToFailed: 1,
            sendingToCompleted: 1,
            sendingToFailed: 0
          }
        }
      }
    }
  })
  async syncCampaignStatus(
    @CurrentEmployee() employee: Employee,
  ): Promise<AppApiResponse<SyncCampaignStatusResponseDto>> {
    const result = await this.adminEmailCampaignService.syncCampaignStatus(employee.id);
    return wrapResponse(result, 'Đã cập nhật trạng thái campaign thành công');
  }
}
