import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiExtraModels
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { FlashSaleUserService } from '../services/flash-sale-user.service';
import { FLASH_SALE_ERROR_CODES } from '../../exceptions/flash-sale.exception';
import { FlashSaleStatus } from '../../enums/flash-sale-status.enum';
import {
  CreateFlashSaleDto,
  UpdateFlashSaleDto,
  UpdateFlashSaleStatusDto,
  BulkDeleteFlashSaleDto,
  BulkDeleteResponseDto,
  FlashSaleResponseDto,
  QueryFlashSaleUserDto,
  PaginatedFlashSaleResponseDto
} from '../dto';
import { SwaggerApiTag } from '@common/swagger';

/**
 * Controller xử lý các API liên quan đến Flash Sale cho user
 */
@ApiTags(SwaggerApiTag.MARKETPLACE_FLASH_SALE)
@ApiExtraModels(
  ApiResponseDto,
  FlashSaleResponseDto,
  PaginatedFlashSaleResponseDto,
  BulkDeleteResponseDto,
  CreateFlashSaleDto,
  UpdateFlashSaleDto,
  UpdateFlashSaleStatusDto,
  BulkDeleteFlashSaleDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketplace/flash-sale')
export class FlashSaleUserController {
  constructor(private readonly flashSaleUserService: FlashSaleUserService) {}

  /**
   * Tạo flash sale mới
   */
  @Post()
  @ApiOperation({ 
    summary: 'Tạo flash sale mới',
    description: 'User tạo flash sale mới cho sản phẩm của mình'
  })
  @ApiBody({
    type: CreateFlashSaleDto,
    examples: {
      draft: {
        summary: 'Tạo flash sale ở trạng thái DRAFT',
        description: 'Tạo flash sale và lưu ở trạng thái nháp để chỉnh sửa sau',
        value: {
          "productId": 123,
          "discountPercentage": 20,
          "displayTime": 30,
          "startTime": 1641081600000,
          "endTime": 1641168000000,
          "status": "DRAFT",
          "maxConfiguration": {
            "maxPerUser": 3,
            "totalInventory": 1000,
            "purchaseLimitPerOrder": 2,
            "timeWindowLimit": {
              "qty": 1,
              "windowMinutes": 60
            }
          }
        }
      },
      scheduled: {
        summary: 'Tạo flash sale và lên lịch ngay',
        description: 'Tạo flash sale với trạng thái SCHEDULED để tự động kích hoạt khi đến thời gian',
        value: {
          "productId": 123,
          "discountPercentage": 20,
          "displayTime": 30,
          "startTime": 1641081600000,
          "endTime": 1641168000000,
          "status": "SCHEDULED",
          "maxConfiguration": {
            "maxPerUser": 3,
            "totalInventory": 1000,
            "purchaseLimitPerOrder": 2,
            "timeWindowLimit": {
              "qty": 1,
              "windowMinutes": 60
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo flash sale thành công',
    schema: ApiResponseDto.getSchema(FlashSaleResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.INVALID_DISCOUNT_PERCENTAGE,
    FLASH_SALE_ERROR_CODES.INVALID_TIME_SEQUENCE,
    FLASH_SALE_ERROR_CODES.PRODUCT_NOT_ELIGIBLE,
    FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
    FLASH_SALE_ERROR_CODES.OVERLAPPING_FLASH_SALE,
    FLASH_SALE_ERROR_CODES.USER_FLASH_SALE_LIMIT_EXCEEDED,
    FLASH_SALE_ERROR_CODES.MAX_CONFIGURATION_INVALID,
    FLASH_SALE_ERROR_CODES.CREATION_FAILED
  )
  async createFlashSale(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateFlashSaleDto
  ): Promise<ApiResponseDto<FlashSaleResponseDto>> {
    const flashSale = await this.flashSaleUserService.createFlashSale(user.id, createDto);
    return ApiResponseDto.success(flashSale, 'Tạo flash sale thành công');
  }

  /**
   * Lấy danh sách flash sale của user
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách flash sale của user',
    description: 'Lấy danh sách flash sale mà user đã tạo'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Trang hiện tại',
    example: 1,
    schema: { minimum: 1, default: 1 }
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Số item mỗi trang',
    example: 10,
    schema: { minimum: 1, default: 10 }
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: FlashSaleStatus,
    description: 'Lọc theo trạng thái flash sale',
    example: FlashSaleStatus.ACTIVE
  })
  @ApiQuery({
    name: 'productId',
    required: false,
    type: Number,
    description: 'Lọc theo ID sản phẩm',
    example: 123
  })
  @ApiQuery({
    name: 'isActive',
    required: false,
    type: Boolean,
    description: 'Lọc flash sale đang hoạt động',
    example: true
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Sắp xếp theo (createdAt, startTime, endTime)',
    example: 'createdAt',
    schema: { default: 'createdAt' }
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Thứ tự sắp xếp',
    example: 'DESC',
    schema: { default: 'DESC' }
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách flash sale thành công',
    schema: ApiResponseDto.getSchema(PaginatedFlashSaleResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.RETRIEVAL_FAILED
  )
  async getUserFlashSales(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: QueryFlashSaleUserDto
  ): Promise<ApiResponseDto<PaginatedFlashSaleResponseDto>> {
    const result = await this.flashSaleUserService.getUserFlashSales(user.id, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách flash sale thành công');
  }

  /**
   * Lấy chi tiết flash sale của user
   */
  @Get(':id')
  @ApiOperation({ 
    summary: 'Lấy chi tiết flash sale',
    description: 'Lấy thông tin chi tiết của flash sale mà user đã tạo'
  })
  @ApiParam({ name: 'id', description: 'ID của flash sale', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết flash sale thành công',
    schema: ApiResponseDto.getSchema(FlashSaleResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
    FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
    FLASH_SALE_ERROR_CODES.RETRIEVAL_FAILED
  )
  async getUserFlashSaleById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<FlashSaleResponseDto>> {
    const flashSale = await this.flashSaleUserService.getUserFlashSaleById(user.id, id);
    return ApiResponseDto.success(flashSale, 'Lấy chi tiết flash sale thành công');
  }

  /**
   * Cập nhật flash sale của user
   */
  @Put(':id')
  @ApiOperation({ 
    summary: 'Cập nhật flash sale',
    description: 'Cập nhật thông tin flash sale (chỉ cho phép cập nhật flash sale ở trạng thái DRAFT)'
  })
  @ApiParam({ name: 'id', description: 'ID của flash sale', type: 'number' })
  @ApiBody({ type: UpdateFlashSaleDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật flash sale thành công',
    schema: ApiResponseDto.getSchema(FlashSaleResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
    FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
    FLASH_SALE_ERROR_CODES.INVALID_STATUS_TRANSITION,
    FLASH_SALE_ERROR_CODES.INVALID_DISCOUNT_PERCENTAGE,
    FLASH_SALE_ERROR_CODES.INVALID_TIME_SEQUENCE,
    FLASH_SALE_ERROR_CODES.OVERLAPPING_FLASH_SALE,
    FLASH_SALE_ERROR_CODES.MAX_CONFIGURATION_INVALID,
    FLASH_SALE_ERROR_CODES.UPDATE_FAILED
  )
  async updateUserFlashSale(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateFlashSaleDto
  ): Promise<ApiResponseDto<FlashSaleResponseDto>> {
    const flashSale = await this.flashSaleUserService.updateUserFlashSale(user.id, id, updateDto);
    return ApiResponseDto.success(flashSale, 'Cập nhật flash sale thành công');
  }

  /**
   * Cập nhật trạng thái flash sale của user
   */
  @Put(':id/status')
  @ApiOperation({
    summary: 'Cập nhật trạng thái flash sale',
    description: 'User cập nhật trạng thái flash sale của mình. Có thể chuyển giữa DRAFT, SCHEDULED, và CANCELLED'
  })
  @ApiParam({ name: 'id', description: 'ID của flash sale', type: 'number' })
  @ApiBody({
    type: UpdateFlashSaleStatusDto,
    examples: {
      schedule: {
        summary: 'Lên lịch flash sale',
        description: 'Chuyển flash sale từ DRAFT sang SCHEDULED để tự động kích hoạt',
        value: {
          "status": "SCHEDULED"
        }
      },
      draft: {
        summary: 'Chuyển về nháp',
        description: 'Chuyển flash sale về DRAFT để chỉnh sửa',
        value: {
          "status": "DRAFT"
        }
      },
      cancel: {
        summary: 'Hủy flash sale',
        description: 'Hủy flash sale (không thể hoàn tác)',
        value: {
          "status": "CANCELLED"
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái flash sale thành công',
    schema: ApiResponseDto.getSchema(FlashSaleResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
    FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
    FLASH_SALE_ERROR_CODES.INVALID_STATUS_TRANSITION,
    FLASH_SALE_ERROR_CODES.INVALID_TIME_RANGE,
    FLASH_SALE_ERROR_CODES.STATUS_UPDATE_FAILED
  )
  async updateFlashSaleStatus(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() statusDto: UpdateFlashSaleStatusDto
  ): Promise<ApiResponseDto<FlashSaleResponseDto>> {
    const flashSale = await this.flashSaleUserService.updateFlashSaleStatus(user.id, id, statusDto);
    return ApiResponseDto.success(flashSale, 'Cập nhật trạng thái flash sale thành công');
  }

  /**
   * ✅ MODIFIED: Bulk delete flash sales của user
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa nhiều flash sales',
    description: 'Xóa nhiều flash sales cùng lúc (có thể xóa flash sale ở mọi trạng thái, miễn là thuộc sở hữu của user). Tối đa 50 items mỗi lần.'
  })
  @ApiBody({
    type: BulkDeleteFlashSaleDto,
    examples: {
      single: {
        summary: 'Xóa 1 flash sale',
        description: 'Xóa một flash sale duy nhất',
        value: {
          "ids": [1]
        }
      },
      multiple: {
        summary: 'Xóa nhiều flash sales',
        description: 'Xóa nhiều flash sales cùng lúc',
        value: {
          "ids": [1, 2, 3, 4, 5]
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Bulk delete flash sales completed',
    schema: ApiResponseDto.getSchema(BulkDeleteResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.DELETE_FAILED
  )
  async deleteUserFlashSales(
    @CurrentUser() user: JwtPayload,
    @Body() deleteDto: BulkDeleteFlashSaleDto
  ): Promise<ApiResponseDto<BulkDeleteResponseDto>> {
    const result = await this.flashSaleUserService.deleteUserFlashSales(user.id, deleteDto);

    if (result.failureCount === 0) {
      return ApiResponseDto.success(result, `Xóa thành công ${result.successCount} flash sales`);
    } else if (result.successCount === 0) {
      return ApiResponseDto.success(result, `Không thể xóa flash sale nào. ${result.failureCount} thất bại`);
    } else {
      return ApiResponseDto.success(result, `Xóa thành công ${result.successCount} flash sales, ${result.failureCount} thất bại`);
    }
  }

  // ✅ API này đã được XÓA vì trùng lặp với /cart/flash-sale-suggestions
  // API suggestions cho cart nên ở CartController, không phải FlashSaleController
}
