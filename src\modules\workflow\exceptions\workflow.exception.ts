import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module Workflow (40000-40199)
 */
export const WORKFLOW_ERROR_CODES = {
  // ===== WORKFLOW ERRORS (40000-40049) =====
  /**
   * Lỗi khi không tìm thấy workflow
   */
  WORKFLOW_NOT_FOUND: new ErrorCode(
    40000,
    'Không tìm thấy workflow',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tên workflow đã tồn tại
   */
  WORKFLOW_NAME_EXISTS: new ErrorCode(
    40001,
    'Tên workflow đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi tạo workflow thất bại
   */
  WORKFLOW_CREATION_FAILED: new ErrorCode(
    40002,
    'Tạo workflow thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật workflow thất bại
   */
  WORKFLOW_UPDATE_FAILED: new ErrorCode(
    40003,
    'Cập nhật workflow thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa workflow thất bại
   */
  WORKFLOW_DELETE_FAILED: new ErrorCode(
    40004,
    'Xóa workflow thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy thông tin workflow thất bại
   */
  WORKFLOW_FETCH_FAILED: new ErrorCode(
    40005,
    'Lấy thông tin workflow thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy danh sách workflow thất bại
   */
  WORKFLOW_LIST_FAILED: new ErrorCode(
    40006,
    'Lấy danh sách workflow thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi tạo workflow thất bại
   */
  WORKFLOW_CREATE_FAILED: new ErrorCode(
    40007,
    'Tạo workflow thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy workflow thất bại
   */
  WORKFLOW_GET_FAILED: new ErrorCode(
    40008,
    'Lấy workflow thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi tìm kiếm workflow thất bại
   */
  WORKFLOW_SEARCH_FAILED: new ErrorCode(
    40009,
    'Tìm kiếm workflow thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy thống kê workflow thất bại
   */
  WORKFLOW_STATISTICS_FAILED: new ErrorCode(
    40010,
    'Lấy thống kê workflow thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== NODE ERRORS (40050-40099) =====
  /**
   * Lỗi khi không tìm thấy node
   */
  NODE_NOT_FOUND: new ErrorCode(
    40050,
    'Không tìm thấy node',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi node definition ID đã tồn tại
   */
  NODE_DEFINITION_ID_EXISTS: new ErrorCode(
    40051,
    'Node definition ID đã tồn tại trong workflow',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi tạo node thất bại
   */
  NODE_CREATION_FAILED: new ErrorCode(
    40052,
    'Tạo node thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật node thất bại
   */
  NODE_UPDATE_FAILED: new ErrorCode(
    40053,
    'Cập nhật node thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa node thất bại
   */
  NODE_DELETE_FAILED: new ErrorCode(
    40054,
    'Xóa node thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa nhiều nodes thất bại
   */
  BULK_DELETE_FAILED: new ErrorCode(
    40055,
    'Xóa nhiều nodes thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy thông tin node thất bại
   */
  NODE_FETCH_FAILED: new ErrorCode(
    40055,
    'Lấy thông tin node thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== CONNECTION ERRORS (40100-40149) =====
  /**
   * Lỗi khi không tìm thấy connection
   */
  CONNECTION_NOT_FOUND: new ErrorCode(
    40100,
    'Không tìm thấy connection',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi connection đã tồn tại
   */
  CONNECTION_EXISTS: new ErrorCode(
    40101,
    'Connection đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi tạo connection thất bại
   */
  CONNECTION_CREATION_FAILED: new ErrorCode(
    40102,
    'Tạo connection thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật connection thất bại
   */
  CONNECTION_UPDATE_FAILED: new ErrorCode(
    40103,
    'Cập nhật connection thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa connection thất bại
   */
  CONNECTION_DELETE_FAILED: new ErrorCode(
    40104,
    'Xóa connection thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi lấy thông tin connection thất bại
   */
  CONNECTION_FETCH_FAILED: new ErrorCode(
    40105,
    'Lấy thông tin connection thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
