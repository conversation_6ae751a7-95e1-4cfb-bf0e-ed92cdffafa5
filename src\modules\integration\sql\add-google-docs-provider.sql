-- <PERSON><PERSON>t để thêm Google Docs provider vào bảng integration_providers
-- Ch<PERSON>y script này để khởi tạo Google Docs integration provider

-- Kiểm tra xem provider đã tồn tại chưa
DO $$
BEGIN
    -- Thêm Google Docs provider nếu chưa tồn tại
    IF NOT EXISTS (SELECT 1 FROM integration_providers WHERE type = 'GOOGLE_DOCS') THEN
        INSERT INTO integration_providers (
            type,
            mcp_schema,
            created_by,
            created_at,
            updated_at
        ) VALUES (
            'GOOGLE_DOCS',
            '{
                "type": "object",
                "properties": {
                    "accessToken": {
                        "type": "string",
                        "description": "Google OAuth access token",
                        "required": true
                    },
                    "refreshToken": {
                        "type": "string", 
                        "description": "Google OAuth refresh token",
                        "required": true
                    },
                    "expiresAt": {
                        "type": "number",
                        "description": "Token expiration timestamp",
                        "required": false
                    },
                    "scope": {
                        "type": "string",
                        "description": "OAuth scopes granted",
                        "required": false
                    }
                },
                "required": ["accessToken", "refreshToken"]
            }'::jsonb,
            NULL, -- created_by (system created)
            EXTRACT(EPOCH FROM NOW())::BIGINT, -- created_at
            EXTRACT(EPOCH FROM NOW())::BIGINT  -- updated_at
        );
        
        RAISE NOTICE 'Google Docs provider đã được thêm thành công';
    ELSE
        RAISE NOTICE 'Google Docs provider đã tồn tại, bỏ qua việc thêm mới';
    END IF;
END $$;

-- Kiểm tra kết quả
SELECT 
    id,
    type,
    created_at,
    updated_at
FROM integration_providers 
WHERE type = 'GOOGLE_DOCS';

-- Thông tin về Google Docs provider
/*
Provider Type: GOOGLE_DOCS
Description: Tích hợp với Google Docs API để tạo, đọc, cập nhật documents
Required Scopes:
- https://www.googleapis.com/auth/documents
- https://www.googleapis.com/auth/drive.file  
- https://www.googleapis.com/auth/userinfo.email
- https://www.googleapis.com/auth/userinfo.profile

OAuth Flow:
1. Tạo auth URL với scopes cần thiết
2. User authorize và nhận authorization code
3. Exchange code để lấy access token và refresh token
4. Lưu tokens vào integration với encryption
5. Sử dụng tokens để gọi Google Docs API

API Endpoints:
- Google Docs API: https://docs.googleapis.com/v1
- Google Drive API: https://www.googleapis.com/drive/v3

Features:
- Tạo documents mới
- Đọc nội dung documents
- Cập nhật nội dung documents  
- Lấy danh sách documents
- Quản lý permissions
- Auto refresh tokens
*/
