/**
 * JSON Schema definitions cho workflow definition validation
 * Following existing validation patterns và WK-002 executor framework
 */

/**
 * Schema cho workflow node definition
 */
export const WorkflowNodeSchema = {
  type: 'object',
  properties: {
    id: {
      type: 'string',
      pattern: '^[a-zA-Z0-9_-]+$',
      minLength: 1,
      maxLength: 255,
      description: 'Unique identifier for the node within workflow'
    },
    type: {
      type: 'string',
      pattern: '^[a-z]+(\.[a-z]+)*$',
      description: 'Node type following category.subcategory.action pattern'
    },
    name: {
      type: 'string',
      minLength: 1,
      maxLength: 255,
      description: 'Human-readable name for the node'
    },
    description: {
      type: 'string',
      maxLength: 1000,
      description: 'Optional description of node functionality'
    },
    position: {
      type: 'object',
      properties: {
        x: { type: 'number' },
        y: { type: 'number' }
      },
      required: ['x', 'y'],
      additionalProperties: false,
      description: 'Node position on canvas'
    },
    size: {
      type: 'object',
      properties: {
        width: { type: 'number', minimum: 50, maximum: 1000 },
        height: { type: 'number', minimum: 30, maximum: 1000 }
      },
      required: ['width', 'height'],
      additionalProperties: false,
      description: 'Node size on canvas'
    },
    inputs: {
      type: 'object',
      description: 'Node input configuration and values',
      additionalProperties: true
    },
    outputs: {
      type: 'object',
      description: 'Node output configuration',
      additionalProperties: true
    },
    config: {
      type: 'object',
      description: 'Node-specific configuration',
      additionalProperties: true
    },
    metadata: {
      type: 'object',
      properties: {
        color: { type: 'string', pattern: '^#[0-9A-Fa-f]{6}$' },
        icon: { type: 'string' },
        category: { type: 'string' },
        tags: {
          type: 'array',
          items: { type: 'string' },
          maxItems: 10
        },
        ui: {
          type: 'object',
          properties: {
            collapsed: { type: 'boolean' },
            selected: { type: 'boolean' },
            highlighted: { type: 'boolean' }
          },
          additionalProperties: false
        }
      },
      additionalProperties: false,
      description: 'UI and display metadata'
    }
  },
  required: ['id', 'type', 'name', 'position'],
  additionalProperties: false
} as const;

/**
 * Schema cho workflow edge definition
 */
export const WorkflowEdgeSchema = {
  type: 'object',
  properties: {
    id: {
      type: 'string',
      pattern: '^[a-zA-Z0-9_-]+$',
      minLength: 1,
      maxLength: 255,
      description: 'Unique identifier for the edge within workflow'
    },
    sourceNodeId: {
      type: 'string',
      pattern: '^[a-zA-Z0-9_-]+$',
      description: 'ID of the source node'
    },
    sourcePort: {
      type: 'string',
      maxLength: 100,
      description: 'Output port of source node (optional)'
    },
    targetNodeId: {
      type: 'string',
      pattern: '^[a-zA-Z0-9_-]+$',
      description: 'ID of the target node'
    },
    targetPort: {
      type: 'string',
      maxLength: 100,
      description: 'Input port of target node (optional)'
    },
    edgeType: {
      type: 'string',
      enum: ['normal', 'conditional', 'error', 'success', 'loop'],
      default: 'normal',
      description: 'Type of edge connection'
    },
    condition: {
      type: 'object',
      properties: {
        expression: { type: 'string' },
        operator: { 
          type: 'string',
          enum: ['equals', 'not_equals', 'greater_than', 'less_than', 'contains', 'exists']
        },
        value: {},
        logicalOperator: {
          type: 'string',
          enum: ['and', 'or'],
          description: 'For multiple conditions'
        }
      },
      description: 'Condition for conditional edges'
    },
    metadata: {
      type: 'object',
      properties: {
        label: { type: 'string', maxLength: 100 },
        color: { type: 'string', pattern: '^#[0-9A-Fa-f]{6}$' },
        style: {
          type: 'string',
          enum: ['solid', 'dashed', 'dotted']
        },
        animated: { type: 'boolean' },
        ui: {
          type: 'object',
          properties: {
            selected: { type: 'boolean' },
            highlighted: { type: 'boolean' },
            path: { type: 'string' }
          },
          additionalProperties: false
        }
      },
      additionalProperties: false,
      description: 'UI and display metadata'
    }
  },
  required: ['id', 'sourceNodeId', 'targetNodeId'],
  additionalProperties: false
} as const;

/**
 * Schema cho workflow definition metadata
 */
export const WorkflowMetadataSchema = {
  type: 'object',
  properties: {
    version: {
      type: 'string',
      pattern: '^\\d+\\.\\d+\\.\\d+$',
      default: '1.0.0',
      description: 'Semantic version of workflow definition'
    },
    schemaVersion: {
      type: 'string',
      pattern: '^\\d+\\.\\d+$',
      default: '1.0',
      description: 'Version of workflow schema used'
    },
    canvas: {
      type: 'object',
      properties: {
        viewport: {
          type: 'object',
          properties: {
            x: { type: 'number' },
            y: { type: 'number' },
            zoom: { type: 'number', minimum: 0.1, maximum: 5 }
          },
          required: ['x', 'y', 'zoom'],
          additionalProperties: false
        },
        grid: {
          type: 'object',
          properties: {
            enabled: { type: 'boolean' },
            size: { type: 'number', minimum: 5, maximum: 100 },
            snapToGrid: { type: 'boolean' }
          },
          additionalProperties: false
        },
        background: {
          type: 'object',
          properties: {
            color: { type: 'string', pattern: '^#[0-9A-Fa-f]{6}$' },
            pattern: { type: 'string', enum: ['dots', 'lines', 'cross'] }
          },
          additionalProperties: false
        }
      },
      additionalProperties: false,
      description: 'Canvas display settings'
    },
    variables: {
      type: 'object',
      patternProperties: {
        '^[a-zA-Z_][a-zA-Z0-9_]*$': {
          type: 'object',
          properties: {
            type: { type: 'string', enum: ['string', 'number', 'boolean', 'object', 'array'] },
            value: {},
            description: { type: 'string' },
            required: { type: 'boolean' }
          },
          required: ['type'],
          additionalProperties: false
        }
      },
      additionalProperties: false,
      description: 'Workflow-level variables'
    },
    triggers: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          type: { type: 'string' },
          config: { type: 'object' },
          enabled: { type: 'boolean' }
        },
        required: ['type'],
        additionalProperties: false
      },
      description: 'Workflow trigger configurations'
    },
    settings: {
      type: 'object',
      properties: {
        timeout: { type: 'number', minimum: 1000, maximum: 3600000 },
        retryPolicy: {
          type: 'object',
          properties: {
            enabled: { type: 'boolean' },
            maxRetries: { type: 'number', minimum: 0, maximum: 10 },
            retryDelay: { type: 'number', minimum: 1000, maximum: 300000 }
          },
          additionalProperties: false
        },
        errorHandling: {
          type: 'object',
          properties: {
            continueOnError: { type: 'boolean' },
            notifyOnError: { type: 'boolean' },
            errorNotificationEmail: { type: 'string', format: 'email' }
          },
          additionalProperties: false
        },
        logging: {
          type: 'object',
          properties: {
            level: { type: 'string', enum: ['debug', 'info', 'warn', 'error'] },
            enabled: { type: 'boolean' },
            retentionDays: { type: 'number', minimum: 1, maximum: 365 }
          },
          additionalProperties: false
        }
      },
      additionalProperties: false,
      description: 'Workflow execution settings'
    },
    tags: {
      type: 'array',
      items: { type: 'string', maxLength: 50 },
      maxItems: 20,
      description: 'Workflow tags for categorization'
    },
    category: {
      type: 'string',
      maxLength: 100,
      description: 'Workflow category'
    },
    author: {
      type: 'object',
      properties: {
        userId: { type: 'number' },
        name: { type: 'string' },
        email: { type: 'string', format: 'email' }
      },
      description: 'Workflow author information'
    },
    lastModified: {
      type: 'object',
      properties: {
        timestamp: { type: 'number' },
        userId: { type: 'number' },
        changes: { type: 'string' }
      },
      description: 'Last modification information'
    }
  },
  additionalProperties: false
} as const;

/**
 * Main workflow definition schema
 */
export const WorkflowDefinitionSchema = {
  type: 'object',
  properties: {
    nodes: {
      type: 'array',
      items: WorkflowNodeSchema,
      minItems: 1,
      maxItems: 1000,
      description: 'Array of workflow nodes'
    },
    edges: {
      type: 'array',
      items: WorkflowEdgeSchema,
      maxItems: 2000,
      description: 'Array of workflow edges'
    },
    metadata: {
      ...WorkflowMetadataSchema,
      description: 'Workflow metadata and settings'
    }
  },
  required: ['nodes'],
  additionalProperties: false,
  
  // Custom validation rules
  $schema: 'http://json-schema.org/draft-07/schema#',
  title: 'Workflow Definition Schema',
  description: 'Schema for validating workflow definitions in RedAI platform'
} as const;

/**
 * Schema validation helper functions
 */
export const WorkflowSchemaValidators = {
  /**
   * Validate node ID uniqueness within workflow
   */
  validateUniqueNodeIds: (nodes: any[]): boolean => {
    const ids = nodes.map(node => node.id);
    return new Set(ids).size === ids.length;
  },

  /**
   * Validate edge references to existing nodes
   */
  validateEdgeReferences: (nodes: any[], edges: any[]): boolean => {
    const nodeIds = new Set(nodes.map(node => node.id));
    return edges.every(edge => 
      nodeIds.has(edge.sourceNodeId) && nodeIds.has(edge.targetNodeId)
    );
  },

  /**
   * Validate no circular dependencies
   */
  validateNoCycles: (edges: any[]): boolean => {
    // Simple cycle detection using DFS
    const graph = new Map<string, string[]>();
    
    // Build adjacency list
    edges.forEach(edge => {
      if (!graph.has(edge.sourceNodeId)) {
        graph.set(edge.sourceNodeId, []);
      }
      graph.get(edge.sourceNodeId)!.push(edge.targetNodeId);
    });

    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (node: string): boolean => {
      if (recursionStack.has(node)) return true;
      if (visited.has(node)) return false;

      visited.add(node);
      recursionStack.add(node);

      const neighbors = graph.get(node) || [];
      for (const neighbor of neighbors) {
        if (hasCycle(neighbor)) return true;
      }

      recursionStack.delete(node);
      return false;
    };

    for (const node of graph.keys()) {
      if (hasCycle(node)) return false;
    }

    return true;
  },

  /**
   * Validate workflow has at least one start node
   */
  validateHasStartNode: (nodes: any[], edges: any[]): boolean => {
    const targetNodes = new Set(edges.map(edge => edge.targetNodeId));
    return nodes.some(node => !targetNodes.has(node.id));
  },

  /**
   * Validate workflow has at least one end node
   */
  validateHasEndNode: (nodes: any[], edges: any[]): boolean => {
    const sourceNodes = new Set(edges.map(edge => edge.sourceNodeId));
    return nodes.some(node => !sourceNodes.has(node.id));
  }
};
