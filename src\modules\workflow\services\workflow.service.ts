import { Injectable, Logger, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { WorkflowRepository } from '../repositories/workflow.repository';
import { CreateWorkflowDto, UpdateWorkflowDto, QueryWorkflowDto, WorkflowResponseDto } from '../dto/workflow';
import { Workflow } from '../entities/workflow.entity';
import { PaginatedResult } from '@common/response';
import { plainToClass } from 'class-transformer';
import { WorkflowCacheService } from './workflow-cache.service';
import { WorkflowQueryOptimizerService } from './workflow-query-optimizer.service';

/**
 * Service for workflow CRUD operations
 * Follows existing service patterns from the codebase
 */
@Injectable()
export class WorkflowService {
  private readonly logger = new Logger(WorkflowService.name);

  constructor(
    private readonly workflowRepository: WorkflowRepository,
    private readonly cacheService: WorkflowCacheService,
    private readonly queryOptimizer: WorkflowQueryOptimizerService,
  ) {}

  /**
   * Create a new workflow
   */
  async create(createWorkflowDto: CreateWorkflowDto, currentUserId?: number): Promise<WorkflowResponseDto> {
    this.logger.log(`Creating workflow: ${createWorkflowDto.name}`);

    try {
      // Use currentUserId if not provided in DTO (for user endpoints)
      const userId = createWorkflowDto.userId ?? currentUserId;

      // Validate unique name per user
      if (userId) {
        const exists = await this.workflowRepository.existsByNameAndUserId(createWorkflowDto.name, userId);
        if (exists) {
          throw new ConflictException(`Workflow with name "${createWorkflowDto.name}" already exists for this user`);
        }
      }

      // Create workflow entity
      const workflow = this.workflowRepository.create({
        ...createWorkflowDto,
        userId,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      // Save to database
      const savedWorkflow = await this.workflowRepository.save(workflow);

      this.logger.log(`Workflow created successfully: ${savedWorkflow.id}`);
      return this.toResponseDto(savedWorkflow);
    } catch (error) {
      this.logger.error(`Failed to create workflow: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all workflows with pagination and filtering
   */
  async findAll(queryDto: QueryWorkflowDto, userId?: number, employeeId?: number): Promise<PaginatedResult<WorkflowResponseDto>> {
    this.logger.log(`Finding workflows with query: ${JSON.stringify(queryDto)}`);

    try {
      const result = await this.workflowRepository.findPaginated(queryDto, userId, employeeId);

      return {
        items: result.items.map(workflow => this.toResponseDto(workflow)),
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Failed to find workflows: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get workflow by ID
   */
  async findOne(id: string, userId?: number): Promise<WorkflowResponseDto> {
    this.logger.log(`Finding workflow by ID: ${id}`);
    const startTime = Date.now();

    try {
      let workflow: Workflow | null;
      let fromCache = false;

      if (userId) {
        // For user endpoints - use optimized query with caching
        const result = await this.queryOptimizer.findWorkflowByIdOptimized(id, userId);
        workflow = result.workflow;
        fromCache = result.fromCache;
      } else {
        // For admin endpoints - try cache first, then database
        workflow = await this.cacheService.getCachedWorkflow(id);
        if (workflow) {
          fromCache = true;
        } else {
          workflow = await this.workflowRepository.findById(id);
          if (workflow) {
            // Cache for future requests
            await this.cacheService.cacheWorkflow(workflow);
          }
        }
      }

      if (!workflow) {
        throw new NotFoundException(`Workflow with ID ${id} not found`);
      }

      const duration = Date.now() - startTime;
      this.logger.debug(
        `Found workflow ${id} (${fromCache ? 'cached' : 'database'}, ${duration}ms)`
      );

      return this.toResponseDto(workflow);
    } catch (error) {
      this.logger.error(`Failed to find workflow: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update workflow
   */
  async update(id: string, updateWorkflowDto: UpdateWorkflowDto, userId?: number): Promise<WorkflowResponseDto> {
    this.logger.log(`Updating workflow: ${id}`);

    try {
      // Find existing workflow
      const existingWorkflow = await this.findOne(id, userId);

      // Validate unique name if name is being updated
      if (updateWorkflowDto.name && updateWorkflowDto.name !== existingWorkflow.name) {
        const targetUserId = userId ?? existingWorkflow.userId;
        if (targetUserId) {
          const exists = await this.workflowRepository.existsByNameAndUserId(
            updateWorkflowDto.name,
            targetUserId,
            id
          );
          if (exists) {
            throw new ConflictException(`Workflow with name "${updateWorkflowDto.name}" already exists for this user`);
          }
        }
      }

      // Update workflow
      await this.workflowRepository.update(id, {
        ...updateWorkflowDto,
        updatedAt: Date.now(),
      });

      // Invalidate cache for this workflow and related data
      const targetUserId = userId ?? existingWorkflow.userId;
      if (targetUserId) {
        await this.queryOptimizer.invalidateWorkflowRelatedCache(id, targetUserId);
      }

      // Return updated workflow
      const updatedWorkflow = await this.findOne(id, userId);
      this.logger.log(`Workflow updated successfully: ${id}`);
      return updatedWorkflow;
    } catch (error) {
      this.logger.error(`Failed to update workflow: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete workflow
   */
  async remove(id: string, userId?: number): Promise<void> {
    this.logger.log(`Deleting workflow: ${id}`);

    try {
      // Verify workflow exists and user has access
      await this.findOne(id, userId);

      // Delete workflow
      const result = await this.workflowRepository.delete(id);

      if (result.affected === 0) {
        throw new NotFoundException(`Workflow with ID ${id} not found`);
      }

      this.logger.log(`Workflow deleted successfully: ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete workflow: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Search workflows by name
   */
  async search(searchQuery: string, userId?: number, limit: number = 10): Promise<WorkflowResponseDto[]> {
    this.logger.log(`Searching workflows with query: ${searchQuery}`);

    try {
      if (!searchQuery || searchQuery.trim().length === 0) {
        throw new BadRequestException('Search query cannot be empty');
      }

      let workflows: Workflow[];

      if (userId) {
        workflows = await this.workflowRepository.searchByName(userId, searchQuery.trim(), limit);
      } else {
        // For admin search - search across all users
        const queryDto: QueryWorkflowDto = {
          search: searchQuery.trim(),
          limit,
          page: 1,
        };
        const result = await this.workflowRepository.findPaginated(queryDto);
        workflows = result.items;
      }

      return workflows.map(workflow => this.toResponseDto(workflow));
    } catch (error) {
      this.logger.error(`Failed to search workflows: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get workflow statistics for a user
   */
  async getStatistics(userId: number): Promise<any> {
    this.logger.log(`Getting statistics for user: ${userId}`);

    try {
      return await this.workflowRepository.getStatisticsByUserId(userId);
    } catch (error) {
      this.logger.error(`Failed to get statistics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Convert workflow entity to response DTO
   */
  private toResponseDto(workflow: Workflow): WorkflowResponseDto {
    const responseDto = plainToClass(WorkflowResponseDto, workflow, {
      excludeExtraneousValues: true,
    });

    // Add computed fields
    if (workflow.definition && workflow.definition.nodes) {
      responseDto.nodeCount = Array.isArray(workflow.definition.nodes) ? workflow.definition.nodes.length : 0;
    }

    // Add status based on workflow state
    responseDto.status = workflow.isActive ? 'active' : 'inactive';

    return responseDto;
  }
}
