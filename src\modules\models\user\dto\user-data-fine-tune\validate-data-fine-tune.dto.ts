import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsUUID, IsNotEmpty, IsOptional, IsN<PERSON><PERSON>, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho request validate dataset fine tune
 */
export class ValidateDataFineTuneDto {
  /**
   * ID của model để validate
   */
  @ApiProperty({
    description: 'ID của model để validate',
    example: '550e8400-e29b-41d4-a716-************',
    type: 'string',
    format: 'uuid'
  })
  @IsUUID('4', { message: 'Model ID phải là UUID hợp lệ' })
  @IsNotEmpty({ message: 'Model ID không được để trống' })
  modelId: string;

  /**
   * Số epoch để huấn luyện (để tính ước lượng chi phí)
   */
  @ApiPropertyOptional({
    description: 'Số epoch để huấn luyện. Dùng để tính ước lượng chi phí fine-tuning. OpenAI: 1-50, Gemini: 1-100',
    example: 3,
    minimum: 1,
    maximum: 100,
    default: 3
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'epochs phải là số' })
  @Min(1, { message: 'epochs phải lớn hơn 0' })
  @Max(100, { message: 'epochs không được vượt quá 100' })
  epochs?: number = 3;

  /**
   * Kích thước batch (để tính ước lượng chi phí)
   */
  @ApiPropertyOptional({
    description: 'Kích thước batch. Dùng để tính ước lượng chi phí. Có thể là số hoặc "auto"',
    example: 'auto',
    default: 'auto'
  })
  @IsOptional()
  batchSize?: number | 'auto' = 'auto';

  /**
   * Hệ số tốc độ học (để tính ước lượng chi phí)
   */
  @ApiPropertyOptional({
    description: 'Hệ số tốc độ học. Dùng để tính ước lượng chi phí. Có thể là số hoặc "auto"',
    example: 'auto',
    default: 'auto'
  })
  @IsOptional()
  learningRateMultiplier?: number | 'auto' = 'auto';

  /**
   * Có tính toán ước lượng chi phí không
   */
  @ApiPropertyOptional({
    description: 'Có tính toán ước lượng chi phí fine-tuning không',
    example: true,
    default: true
  })
  @IsOptional()
  calculateCostEstimate?: boolean = true;
}

/**
 * DTO cho thông tin ước lượng chi phí fine-tuning
 */
export class CostEstimateDto {
  /**
   * Tổng số token training (training_tokens * epochs)
   */
  @ApiProperty({
    description: 'Tổng số token training (training_tokens * epochs)',
    example: 4500
  })
  totalTrainingTokens: number;

  /**
   * Chi phí ước lượng (points/VND)
   */
  @ApiProperty({
    description: 'Chi phí ước lượng cho fine-tuning (totalTrainingTokens * training_pricing)',
    example: 450000
  })
  estimatedCost: number;

  /**
   * Giá training từ model registry
   */
  @ApiProperty({
    description: 'Giá training từ model registry (training_pricing)',
    example: 100
  })
  trainingPricing: number;

  /**
   * Hyperparameters được sử dụng để tính toán
   */
  @ApiProperty({
    description: 'Hyperparameters được sử dụng để tính toán',
    example: {
      epochs: 3,
      batchSize: 'auto',
      learningRateMultiplier: 'auto'
    }
  })
  hyperparameters: {
    epochs: number;
    batchSize: number | 'auto';
    learningRateMultiplier: number | 'auto';
  };
}

/**
 * DTO cho response validation result
 */
export class ValidationResultDto {
  /**
   * Kết quả validation file train
   */
  @ApiProperty({
    description: 'Kết quả validation file train',
    type: 'object',
    properties: {
      isValid: { type: 'boolean', description: 'File có hợp lệ không' },
      totalTokens: { type: 'number', description: 'Tổng số token' },
      totalLines: { type: 'number', description: 'Tổng số dòng' },
      fileSizeBytes: { type: 'number', description: 'Kích thước file (bytes)' },
      errors: { 
        type: 'array', 
        items: { type: 'string' },
        description: 'Danh sách lỗi nếu có'
      }
    }
  })
  trainResult: {
    isValid: boolean;
    totalTokens: number;
    totalLines: number;
    fileSizeBytes: number;
    errors?: string[];
  };

  /**
   * Kết quả validation file validation (nếu có)
   */
  @ApiProperty({
    description: 'Kết quả validation file validation (nếu có)',
    type: 'object',
    nullable: true,
    properties: {
      isValid: { type: 'boolean', description: 'File có hợp lệ không' },
      totalTokens: { type: 'number', description: 'Tổng số token' },
      totalLines: { type: 'number', description: 'Tổng số dòng' },
      fileSizeBytes: { type: 'number', description: 'Kích thước file (bytes)' },
      errors: { 
        type: 'array', 
        items: { type: 'string' },
        description: 'Danh sách lỗi nếu có'
      }
    }
  })
  validResult?: {
    isValid: boolean;
    totalTokens: number;
    totalLines: number;
    fileSizeBytes: number;
    errors?: string[];
  } | null;

  /**
   * Tổng số token của cả train và validation
   */
  @ApiProperty({
    description: 'Tổng số token của cả train và validation',
    type: 'number',
    example: 15000
  })
  totalTokens: number;

  /**
   * Provider được sử dụng để validate
   */
  @ApiProperty({
    description: 'Provider được sử dụng để validate',
    type: 'string',
    example: 'OPENAI'
  })
  provider: string;

  /**
   * Model ID được sử dụng để validate
   */
  @ApiProperty({
    description: 'Model ID được sử dụng để validate',
    type: 'string',
    example: 'gpt-3.5-turbo'
  })
  modelUsed: string;

  /**
   * Thông báo tổng kết
   */
  @ApiProperty({
    description: 'Thông báo tổng kết',
    type: 'string',
    example: 'Validation thành công cho cả file train và validation'
  })
  message: string;

  /**
   * Ước lượng chi phí fine-tuning (nếu được yêu cầu)
   */
  @ApiProperty({
    description: 'Ước lượng chi phí fine-tuning',
    type: CostEstimateDto,
    nullable: true
  })
  costEstimate?: CostEstimateDto | null;
}
