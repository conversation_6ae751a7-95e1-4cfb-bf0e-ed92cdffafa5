import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';
import { ProviderLlmEnum } from '../../../constants';

/**
 * Enum cho các trường có thể sắp xếp
 */
export enum ModelRegistrySortBy {
  PROVIDER = 'provider',
  MODEL_NAME_PATTERN = 'modelNamePattern',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho việc truy vấn danh sách model registry
 */
export class ModelRegistryQueryDto extends QueryDto {
  /**
   * Lọc theo nhà cung cấp
   */
  @ApiPropertyOptional({
    description: 'Lọc theo nhà cung cấp',
    enum: ProviderLlmEnum,
    example: ProviderLlmEnum.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderLlmEnum)
  provider?: ProviderLlmEnum;

  /**
   * Lọc theo hỗ trợ fine-tuning
   */
  @ApiPropertyOptional({
    description: 'Lọc theo hỗ trợ fine-tuning',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  fineTune?: boolean;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: ModelRegistrySortBy,
    example: ModelRegistrySortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(ModelRegistrySortBy)
  sortBy?: ModelRegistrySortBy = ModelRegistrySortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
