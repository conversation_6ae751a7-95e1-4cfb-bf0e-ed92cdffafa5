import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { NodeDefinitionRepository } from '../repositories';
import { NodeDefinition, NodeCategory } from '../entities';
import {
  CreateNodeDefinitionDto,
  UpdateNodeDefinitionDto,
  NodeFilterDto,
  SearchOptionsDto,
  NodeDocumentationDto,
  NodeVersionDto,
  NodeRegistryStatsDto,
} from '../dto/node-definition';
import { ValidationResult } from '../interfaces';
import Ajv from 'ajv';
import { Logger } from '@nestjs/common';

/**
 * Service để quản lý node definitions registry
 * Hỗ trợ 192 node types với enhanced functionality
 */
@Injectable()
export class NodeDefinitionService {
  private readonly logger = new Logger(NodeDefinitionService.name);
  private readonly ajv = new Ajv({ allErrors: true });

  constructor(
    private readonly nodeDefinitionRepository: NodeDefinitionRepository,
  ) {}

  /**
   * Đăng ký node type mới
   * @param nodeData - Dữ liệu node definition
   * @returns Promise<NodeDefinition>
   */
  async registerNodeType(nodeData: CreateNodeDefinitionDto): Promise<NodeDefinition> {
    // Kiểm tra node type đã tồn tại
    const existingNode = await this.nodeDefinitionRepository.findByType(nodeData.type);
    if (existingNode) {
      throw new ConflictException(`Node type '${nodeData.type}' already exists`);
    }

    // Validate input và output schemas
    this.validateJsonSchema(nodeData.inputSchema, 'Input schema');
    this.validateJsonSchema(nodeData.outputSchema, 'Output schema');

    // Tạo node definition mới
    const nodeDefinition = this.nodeDefinitionRepository.create(nodeData);
    return await this.nodeDefinitionRepository.save(nodeDefinition);
  }

  /**
   * Cập nhật node definition
   * @param type - Type của node
   * @param updateData - Dữ liệu cập nhật
   * @returns Promise<NodeDefinition>
   */
  async updateNodeDefinition(
    type: string, 
    updateData: UpdateNodeDefinitionDto
  ): Promise<NodeDefinition> {
    const nodeDefinition = await this.nodeDefinitionRepository.findByType(type);
    if (!nodeDefinition) {
      throw new NotFoundException(`Node type '${type}' not found`);
    }

    // Validate schemas nếu có cập nhật
    if (updateData.inputSchema) {
      this.validateJsonSchema(updateData.inputSchema, 'Input schema');
    }
    if (updateData.outputSchema) {
      this.validateJsonSchema(updateData.outputSchema, 'Output schema');
    }

    // Cập nhật node definition
    Object.assign(nodeDefinition, updateData);
    return await this.nodeDefinitionRepository.save(nodeDefinition);
  }

  /**
   * Lấy node definitions theo category với filters
   * @param category - Category của node
   * @param filters - Filters optional
   * @returns Promise<NodeDefinition[]>
   */
  async getNodesByCategory(
    category: NodeCategory, 
    filters?: NodeFilterDto
  ): Promise<NodeDefinition[]> {
    if (filters && Object.keys(filters).length > 0) {
      const [nodes] = await this.nodeDefinitionRepository.advancedSearch({
        category,
        ...filters,
      });
      return nodes;
    }

    return await this.nodeDefinitionRepository.findByCategory(category);
  }

  /**
   * Tìm kiếm node definitions
   * @param query - Từ khóa tìm kiếm
   * @param options - Search options
   * @returns Promise<NodeDefinition[]>
   */
  async searchNodes(query: string, options?: SearchOptionsDto): Promise<NodeDefinition[]> {
    if (!query.trim()) {
      throw new BadRequestException('Search query cannot be empty');
    }

    // Sử dụng full-text search
    let nodes = await this.nodeDefinitionRepository.fullTextSearch(query);

    // Apply filters
    if (options?.category) {
      nodes = nodes.filter(node => node.category === options.category);
    }

    // Deprecated filtering removed - simplified search

    // Apply limit
    if (options?.limit) {
      nodes = nodes.slice(0, options.limit);
    }

    return nodes;
  }

  /**
   * Lấy documentation chi tiết của node
   * @param nodeType - Type của node
   * @returns Promise<NodeDocumentationDto>
   */
  async getNodeDocumentation(nodeType: string): Promise<NodeDocumentationDto> {
    const node = await this.nodeDefinitionRepository.findByType(nodeType);
    if (!node) {
      throw new NotFoundException(`Node type '${nodeType}' not found`);
    }

    return {
      type: node.type,
      name: node.name,
      description: node.description || '',
      inputSchema: node.inputSchema,
      outputSchema: node.outputSchema,
      version: node.version,
      category: node.category
    };
  }

  /**
   * Validate input data theo node schema
   * @param nodeType - Type của node
   * @param inputData - Dữ liệu input cần validate
   * @returns Promise<ValidationResult>
   */
  async validateNodeSchema(nodeType: string, inputData: any): Promise<ValidationResult> {
    const node = await this.nodeDefinitionRepository.findByType(nodeType);
    if (!node) {
      throw new NotFoundException(`Node type '${nodeType}' not found`);
    }

    const validate = this.ajv.compile(node.inputSchema);
    const isValid = validate(inputData);

    return {
      isValid,
      errors: isValid ? [] : (validate.errors || []).map(error => ({
        path: error.instancePath,
        message: error.message || 'Validation error',
        value: error.data,
      })),
    };
  }

  /**
   * Lấy version history của node (placeholder - cần implement versioning system)
   * @param nodeType - Type của node
   * @returns Promise<NodeVersionDto[]>
   */
  async getNodeVersionHistory(nodeType: string): Promise<NodeVersionDto[]> {
    const node = await this.nodeDefinitionRepository.findByType(nodeType);
    if (!node) {
      throw new NotFoundException(`Node type '${nodeType}' not found`);
    }

    // Placeholder implementation - trong thực tế cần table riêng cho version history
    return [
      {
        version: node.version,
        createdAt: new Date(), // Simplified - no createdAt field in entity
        changelog: 'Current version',
        breakingChanges: false,
      },
    ];
  }

  // Deprecate functionality removed - simplified node management

  /**
   * Bulk register multiple node definitions
   * @param nodeDefinitions - Array of node definitions to register
   * @returns Promise<{ registered: number; skipped: number; errors: string[] }>
   */
  async bulkRegisterNodes(nodeDefinitions: CreateNodeDefinitionDto[]): Promise<{
    registered: number;
    skipped: number;
    errors: string[];
  }> {
    let registered = 0;
    let skipped = 0;
    const errors: string[] = [];

    for (const nodeDefinition of nodeDefinitions) {
      try {
        const existingNode = await this.nodeDefinitionRepository.findByType(nodeDefinition.type);

        if (!existingNode) {
          // Validate schemas before registering
          this.validateJsonSchema(nodeDefinition.inputSchema, 'Input schema');
          this.validateJsonSchema(nodeDefinition.outputSchema, 'Output schema');

          await this.nodeDefinitionRepository.save(
            this.nodeDefinitionRepository.create(nodeDefinition)
          );
          registered++;
        } else {
          skipped++;
        }
      } catch (error) {
        errors.push(`${nodeDefinition.type}: ${error.message}`);
      }
    }

    return { registered, skipped, errors };
  }

  /**
   * Get nodes by multiple categories
   * @param categories - Array of categories
   * @param filters - Optional filters
   * @returns Promise<NodeDefinition[]>
   */
  async getNodesByCategories(
    categories: NodeCategory[],
    filters?: NodeFilterDto
  ): Promise<NodeDefinition[]> {
    const searchFilters = {
      ...filters,
      categories,
    };

    const [nodes] = await this.nodeDefinitionRepository.advancedSearch(searchFilters);
    return nodes;
  }

  /**
   * Lấy thống kê registry
   * @returns Promise<NodeRegistryStatsDto>
   */
  async getRegistryStatistics(): Promise<NodeRegistryStatsDto> {
    return await this.nodeDefinitionRepository.getStatistics();
  }

  /**
   * Lấy tất cả node definitions với advanced filtering
   * @param filters - Filter options
   * @returns Promise<[NodeDefinition[], number]>
   */
  async getNodesWithFilters(filters: NodeFilterDto): Promise<[NodeDefinition[], number]> {
    return await this.nodeDefinitionRepository.advancedSearch(filters);
  }

  /**
   * Lấy node definition theo type
   * @param type - Type của node
   * @returns Promise<NodeDefinition>
   */
  async getNodeByType(type: string): Promise<NodeDefinition> {
    const node = await this.nodeDefinitionRepository.findByType(type);
    if (!node) {
      throw new NotFoundException(`Node type '${type}' not found`);
    }
    return node;
  }

  /**
   * Xóa node definition
   * @param type - Type của node
   * @returns Promise<void>
   */
  async deleteNodeDefinition(type: string): Promise<void> {
    const node = await this.nodeDefinitionRepository.findByType(type);
    if (!node) {
      throw new NotFoundException(`Node type '${type}' not found`);
    }

    await this.nodeDefinitionRepository.remove(node);
  }

  /**
   * Validate JSON schema
   * @param schema - Schema cần validate
   * @param schemaName - Tên schema (để error message)
   * @private
   */
  private validateJsonSchema(schema: any, schemaName: string): void {
    try {
      this.ajv.compile(schema);
    } catch (error) {
      throw new BadRequestException(`Invalid ${schemaName}: ${error.message}`);
    }
  }
}
