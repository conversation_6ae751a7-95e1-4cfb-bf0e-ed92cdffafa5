import { IsString, IsOptional, IsN<PERSON>ber } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Workflow Version DTO
 */
export class WorkflowVersionDto {
  @ApiProperty({ description: 'Version number' })
  @IsString()
  version: string;

  @ApiPropertyOptional({ description: 'Version description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Created timestamp' })
  @IsNumber()
  createdAt: number;

  @ApiProperty({ description: 'Created by user ID' })
  @IsNumber()
  createdBy: number;
}
