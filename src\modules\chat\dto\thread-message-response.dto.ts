import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { InternalConversationThreadsAttachmentType } from '../entities/internal-conversation-messages-attachment.entity';
import { InternalConversationMessageRole } from '../entities/internal-conversation-message.entity';

/**
 * Response DTO for message attachment
 */
export class MessageAttachmentResponseDto {
  @ApiProperty({
    description: 'ID của attachment',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  attachmentId: string;

  @ApiProperty({
    description: 'Loại attachment',
    enum: InternalConversationThreadsAttachmentType,
    example: InternalConversationThreadsAttachmentType.IMAGE,
  })
  @Expose()
  attachmentType: InternalConversationThreadsAttachmentType;

  @ApiProperty({
    description: 'Storage key cho media files',
    example: 'media/2024/01/image.jpg',
  })
  @Expose()
  viewUrl?: string;
}

/**
 * Response DTO for thread messages
 */
export class ThreadMessageResponseDto {
  @ApiProperty({
    description: 'Avatar của người gửi tin nhắn',
    example: 'https://s3.amazonaws.com/bucket/path/to/avatar?signature=...',
    nullable: true,
  })
  @Expose()
  avatar?: string;

  @ApiProperty({
    description: 'ID của tin nhắn',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  messageId: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: 'Hello, I need help with my project',
  })
  @Expose()
  messageText: string;

  @ApiProperty({
    description: 'Thời gian tạo tin nhắn (timestamp)',
    example: 1703123456789,
  })
  @Expose()
  messageCreatedAt: number;

  @ApiProperty({
    description: 'Có file đính kèm hay không',
    example: true,
  })
  @Expose()
  hasAttachments: boolean;

  @ApiProperty({
    description: 'Vai trò của tin nhắn trong cuộc trò chuyện',
    enum: InternalConversationMessageRole,
    example: InternalConversationMessageRole.USER,
  })
  @Expose()
  role: InternalConversationMessageRole;

  @ApiProperty({
    description: 'Có phải là tin nhắn xác nhận tool call hay không',
    example: false,
  })
  @Expose()
  isToolCallConfirm: boolean;

  @ApiProperty({
    description: 'Danh sách file đính kèm (chỉ có khi hasAttachments = true)',
    type: [MessageAttachmentResponseDto],
    required: false,
  })
  @Expose()
  @Type(() => MessageAttachmentResponseDto)
  attachments?: MessageAttachmentResponseDto[];
}
