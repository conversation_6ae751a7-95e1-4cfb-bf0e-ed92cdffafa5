import { ModelData } from '../../repositories/models.repository';
import { SystemModelsResponseDto } from '../dto/user-models/system-models-response.dto';

/**
 * Mapper cho System Models
 */
export class SystemModelsMapper {
  /**
   * <PERSON>yển đổi ModelsWithJoinedData sang SystemModelsResponseDto
   * @param model ModelsWithJoinedData entity
   * @returns SystemModelsResponseDto
   */
  static toResponseDto(model: ModelData): SystemModelsResponseDto {
    return {
      id: model.id,
      modelId: model.modelName,
      inputModalities: model.inputModalities || [],
      outputModalities: model.outputModalities || [],
      samplingParameters: model.samplingParameters || [],
      features: model.features || [],
      basePricing: model.basePricing,
      fineTunePricing: model.fineTunePricing,
      trainingPricing: model.trainingPricing,
    };
  }

  /**
   * <PERSON>yển đổi mảng ModelsWithJoinedData sang mảng SystemModelsResponseDto
   * @param models Mảng ModelsWithJoinedData entities
   * @returns Mảng SystemModelsResponseDto
   */
  static toResponseDtoList(models: ModelData[]): SystemModelsResponseDto[] {
    return models.map(model => this.toResponseDto(model));
  }
}
