import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { UpdateBankInfoDto } from '../../dto/update-bank-info.dto';
import { UpdateBusinessInfoDto } from '../../dto/update-business-info.dto';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { S3Service } from '@/shared/services/s3.service';
import { AvatarUploadDto, UpdateAvatarDto } from '../../dto/avatar-upload.dto';
import { CoverImageUploadDto } from '../../dto/cover-image-upload.dto';
import { CategoryFolderEnum, TimeIntervalEnum, FileSizeEnum } from '@/shared/utils';
import { generateS3Key } from '@/shared/utils/generators';
import { BusinessInfo, User, UserManageNotification } from '../../entities';
import {
  UpdateNotificationSettingsDto,
  UserNotificationResponseDto,
} from '../../dto/notification.dto';
import {
  SendPhoneVerificationOtpDto,
  VerifyPhoneOtpDto,
  SendPhoneVerificationOtpResponseDto,
  VerifyPhoneOtpResponseDto,
} from '../../dto/verify-phone.dto';
import { RedisService } from '@/shared/services/redis.service';
import { SmsService } from '@/modules/sms/services/sms.service';
import { AUTH_ERROR_CODE } from '@/modules/auth/errors/auth-error.code';
import { RULE_CONTRACT_ERROR_CODES } from '@/modules/rule-contract/errors/rule-contract-error.code';
import { AffiliateAccountRepository } from '@/modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateAccountStatus } from '@/modules/affiliate/enums/affiliate-account-status.enum';
import { UserTypeEnum } from '../../enums/user-type.enum';
import { USER_ERROR_CODES } from '../../exceptions/user-error.codes';

@Injectable()
export class AccountService {
  private readonly logger = new Logger(AccountService.name);
  private readonly phoneOtpPrefix = 'phone_verify_otp:';
  private readonly phoneOtpCooldownPrefix = 'phone_otp_cooldown:';
  private readonly otpExpiryTime = 5 * 60; // 5 phút
  private readonly cooldownTime = 60; // 60 giây

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(BusinessInfo)
    private businessInfoRepository: Repository<BusinessInfo>,
    private readonly s3Service: S3Service,
    @InjectRepository(UserManageNotification)
    private readonly userManageNotificationRepository: Repository<UserManageNotification>,
    private readonly redisService: RedisService,
    private readonly smsService: SmsService,
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
  ) {}

  /**
   * Cập nhật thông tin tài khoản ngân hàng của người dùng
   * @param userId ID của người dùng
   * @param updateBankInfoDto Thông tin tài khoản ngân hàng cần cập nhật
   */
  async updateBankInfo(
    userId: number,
    updateBankInfoDto: UpdateBankInfoDto,
  ): Promise<User> {
    // Tìm người dùng theo ID
    const user = await this.userRepository.findOne({ where: { id: userId } });

    // Nếu không tìm thấy người dùng, ném ngoại lệ
    if (!user) {
      throw new AppException(ErrorCode.USER_NOT_FOUND);
    }

    // Cập nhật thông tin tài khoản ngân hàng
    user.bankCode = updateBankInfoDto.bankCode;
    user.accountNumber = updateBankInfoDto.accountNumber;
    user.accountHolder = updateBankInfoDto.accountHolder;

    // Cập nhật chi nhánh ngân hàng nếu có
    if (updateBankInfoDto.bankBranch !== undefined) {
      user.bankBranch = updateBankInfoDto.bankBranch;
    }

    // Cập nhật thời gian sửa đổi
    user.updatedAt = Date.now();

    // Lưu thông tin người dùng đã cập nhật vào cơ sở dữ liệu
    return this.userRepository.save(user);
  }

  /**
   * Lấy thông tin doanh nghiệp của người dùng
   * @param userId ID của người dùng
   * @returns Thông tin doanh nghiệp hoặc null nếu không tìm thấy
   */
  async getBusinessInfo(userId: number): Promise<BusinessInfo | null> {
    // Tìm thông tin doanh nghiệp theo user ID
    const businessInfo = await this.businessInfoRepository.findOne({
      where: { userId },
    });

    // Trả về thông tin doanh nghiệp hoặc null nếu không tìm thấy
    return businessInfo || null;
  }

  /**
   * Cập nhật thông tin doanh nghiệp của người dùng
   * @param userId ID của người dùng
   * @param updateBusinessInfoDto Thông tin doanh nghiệp cần cập nhật
   * @returns Thông tin doanh nghiệp đã cập nhật
   */
  async updateBusinessInfo(
    userId: number,
    updateBusinessInfoDto: UpdateBusinessInfoDto,
  ): Promise<BusinessInfo> {
    // Tìm thông tin doanh nghiệp theo user ID
    let businessInfo = await this.businessInfoRepository.findOne({
      where: { userId },
    });

    // Nếu không tìm thấy thông tin doanh nghiệp, tạo mới
    if (!businessInfo) {
      businessInfo = this.businessInfoRepository.create({
        userId,
        createdAt: Date.now(),
        status: 'PENDING', // Trạng thái mặc định khi tạo mới
      });
    }

    // Cập nhật thông tin doanh nghiệp
    if (updateBusinessInfoDto.businessName !== undefined) {
      businessInfo.businessName = updateBusinessInfoDto.businessName;
    }

    if (updateBusinessInfoDto.businessEmail !== undefined) {
      businessInfo.businessEmail = updateBusinessInfoDto.businessEmail;
    }

    if (updateBusinessInfoDto.businessPhone !== undefined) {
      businessInfo.businessPhone = updateBusinessInfoDto.businessPhone;
    }

    if (updateBusinessInfoDto.businessRegistrationCertificate !== undefined) {
      businessInfo.businessRegistrationCertificate =
        updateBusinessInfoDto.businessRegistrationCertificate;
    }

    if (updateBusinessInfoDto.businessAddress !== undefined) {
      businessInfo.businessAddress = updateBusinessInfoDto.businessAddress;
    }

    if (updateBusinessInfoDto.taxCode !== undefined) {
      businessInfo.taxCode = updateBusinessInfoDto.taxCode;
    }

    if (updateBusinessInfoDto.representativeName !== undefined) {
      businessInfo.representativeName =
        updateBusinessInfoDto.representativeName;
    }

    if (updateBusinessInfoDto.representativePosition !== undefined) {
      businessInfo.representativePosition =
        updateBusinessInfoDto.representativePosition;
    }

    // Cập nhật thời gian sửa đổi
    businessInfo.updatedAt = Date.now();

    // Lưu thông tin doanh nghiệp đã cập nhật vào cơ sở dữ liệu
    return this.businessInfoRepository.save(businessInfo);
  }

  /**
   * Lấy thông tin tài khoản ngân hàng của người dùng
   * @param userId ID của người dùng
   * @returns Thông tin tài khoản ngân hàng
   */
  async getBankInfo(userId: number): Promise<User> {
    // Tìm người dùng theo ID
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: [
        'id',
        'bankCode',
        'accountNumber',
        'accountHolder',
        'bankBranch',
      ],
    });

    // Nếu không tìm thấy người dùng, ném ngoại lệ
    if (!user) {
      throw new AppException(ErrorCode.USER_NOT_FOUND);
    }

    return user;
  }

  /**
   * Tạo URL tạm thời để tải lên avatar
   * @param userId ID của người dùng
   * @param avatarUploadDto Thông tin về loại và kích thước avatar
   * @returns URL tạm thời và thông tin khóa S3
   */
  async createAvatarUploadUrl(
    userId: number,
    avatarUploadDto: AvatarUploadDto,
  ) {
    try {
      // Tìm người dùng theo ID
      const user = await this.userRepository.findOne({ where: { id: userId } });

      // Nếu không tìm thấy người dùng, ném ngoại lệ
      if (!user) {
        throw new AppException(ErrorCode.USER_NOT_FOUND);
      }

      // Tạo key cho avatar trên S3
      const avatarKey = generateS3Key({
        baseFolder: userId.toString(),
        categoryFolder: CategoryFolderEnum.PROFILE,
      });

      // Kiểm tra và giới hạn kích thước tối đa nếu cần
      const maxSize = avatarUploadDto.maxSize;

      // Tạo URL tạm thời để tải lên avatar
      const uploadUrl = await this.s3Service.createPresignedWithID(
        avatarKey,
        TimeIntervalEnum.FIVE_MINUTES,
        avatarUploadDto.imageType,
        maxSize,
      );

      this.userRepository.update(userId, { avatar: avatarKey });

      return {
        uploadUrl,
        avatarKey,
        expiresIn: TimeIntervalEnum.FIVE_MINUTES,
      };
    } catch (error) {
      this.logger.error(
        `Error creating avatar upload URL: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.UNCATEGORIZED_EXCEPTION, error.message);
    }
  }

  /**
   * Cập nhật avatar của người dùng
   * @param userId ID của người dùng
   * @param updateAvatarDto Thông tin avatar mới
   * @returns Thông tin người dùng đã cập nhật
   */
  async updateAvatar(
    userId: number,
    updateAvatarDto: UpdateAvatarDto,
  ): Promise<User> {
    try {
      // Tìm người dùng theo ID
      const user = await this.userRepository.findOne({ where: { id: userId } });

      // Nếu không tìm thấy người dùng, ném ngoại lệ
      if (!user) {
        throw new AppException(ErrorCode.USER_NOT_FOUND);
      }

      // Cập nhật avatar
      user.avatar = updateAvatarDto.avatarKey;
      user.updatedAt = Date.now();

      // Lưu thông tin người dùng đã cập nhật vào cơ sở dữ liệu
      return this.userRepository.save(user);
    } catch (error) {
      this.logger.error(`Error updating avatar: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.UNCATEGORIZED_EXCEPTION, error.message);
    }
  }

  /**
   * Tạo URL tạm thời để tải lên ảnh bìa
   * @param userId ID của người dùng
   * @param coverImageUploadDto Thông tin về loại và kích thước ảnh bìa
   * @returns URL tạm thời và thông tin khóa S3
   */
  async createCoverImageUploadUrl(
    userId: number,
    coverImageUploadDto: CoverImageUploadDto,
  ) {
    try {
      // Tìm người dùng theo ID
      const user = await this.userRepository.findOne({ where: { id: userId } });

      // Nếu không tìm thấy người dùng, ném ngoại lệ
      if (!user) {
        throw new AppException(ErrorCode.USER_NOT_FOUND);
      }

      // Tạo key cho ảnh bìa trên S3
      const coverImageKey = generateS3Key({
        baseFolder: userId.toString(),
        categoryFolder: CategoryFolderEnum.USER_COVER_IMAGE,
        useTimeFolder: true,
      });

      // Kiểm tra và giới hạn kích thước tối đa nếu cần
      const maxSize = Math.min(coverImageUploadDto.maxSize, FileSizeEnum.TEN_MB);

      // Tạo URL tạm thời để tải lên ảnh bìa
      const uploadUrl = await this.s3Service.createPresignedWithID(
        coverImageKey,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        coverImageUploadDto.imageType,
        maxSize,
      );

      // Lưu key ảnh bìa vào database ngay lập tức
      await this.userRepository.update(userId, {
        coverImage: coverImageKey,
        updatedAt: Date.now()
      });

      return {
        uploadUrl,
        coverImageKey,
        expiresIn: TimeIntervalEnum.FIFTEEN_MINUTES,
      };
    } catch (error) {
      this.logger.error(
        `Error creating cover image upload URL: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(ErrorCode.UNCATEGORIZED_EXCEPTION, error.message);
    }
  }



  /**
   * Lấy thông tin cài đặt thông báo của user
   * @param userId ID của user
   * @returns Thông tin cài đặt thông báo của user
   */
  async getNotificationSettings(
    userId: number,
  ): Promise<UserNotificationResponseDto> {
    try {
      // Tìm thông tin cài đặt thông báo của user
      const notificationSettings =
        await this.userManageNotificationRepository.findOne({
          where: { userId },
        });

      // Nếu không tìm thấy, tạo mới với các giá trị mặc định
      if (!notificationSettings) {
        const newNotificationSettings =
          this.userManageNotificationRepository.create({
            userId,
            receiveAccountSystemEmails: true,
            receiveBillingEmails: true,
            receiveNewFeatureEmails: true,
            receiveAffiliateEmails: true,
            receiveDocumentationEmails: true,
            receivePromotionalEmails: true,
          });

        const savedSettings = await this.userManageNotificationRepository.save(
          newNotificationSettings,
        );
        return savedSettings;
      }

      return notificationSettings;
    } catch (error) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể lấy thông tin cài đặt thông báo',
      );
    }
  }

  /**
   * Cập nhật thông tin cài đặt thông báo của user
   * @param userId ID của user
   * @param updateDto Thông tin cài đặt thông báo cần cập nhật
   * @returns Thông tin cài đặt thông báo đã cập nhật
   */
  async updateNotificationSettings(
    userId: number,
    updateDto: UpdateNotificationSettingsDto,
  ): Promise<UserNotificationResponseDto> {
    try {
      // Tìm thông tin cài đặt thông báo của user
      let notificationSettings =
        await this.userManageNotificationRepository.findOne({
          where: { userId },
        });

      // Nếu không tìm thấy, tạo mới với các giá trị mặc định
      if (!notificationSettings) {
        notificationSettings = this.userManageNotificationRepository.create({
          userId,
          receiveAccountSystemEmails: true,
          receiveBillingEmails: true,
          receiveNewFeatureEmails: true,
          receiveAffiliateEmails: true,
          receiveDocumentationEmails: true,
          receivePromotionalEmails: true,
        });
      }

      // Cập nhật các trường được cung cấp
      if (updateDto.receiveAccountSystemEmails !== undefined) {
        notificationSettings.receiveAccountSystemEmails =
          updateDto.receiveAccountSystemEmails;
      }
      if (updateDto.receiveBillingEmails !== undefined) {
        notificationSettings.receiveBillingEmails =
          updateDto.receiveBillingEmails;
      }
      if (updateDto.receiveNewFeatureEmails !== undefined) {
        notificationSettings.receiveNewFeatureEmails =
          updateDto.receiveNewFeatureEmails;
      }
      if (updateDto.receiveAffiliateEmails !== undefined) {
        notificationSettings.receiveAffiliateEmails =
          updateDto.receiveAffiliateEmails;
      }
      if (updateDto.receiveDocumentationEmails !== undefined) {
        notificationSettings.receiveDocumentationEmails =
          updateDto.receiveDocumentationEmails;
      }
      if (updateDto.receivePromotionalEmails !== undefined) {
        notificationSettings.receivePromotionalEmails =
          updateDto.receivePromotionalEmails;
      }

      // Lưu thông tin đã cập nhật
      const updatedSettings =
        await this.userManageNotificationRepository.save(notificationSettings);
      return updatedSettings;
    } catch (error) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể cập nhật thông tin cài đặt thông báo',
      );
    }
  }

  /**
   * Gửi OTP xác thực số điện thoại
   * @param userId ID của người dùng
   * @param sendPhoneVerificationOtpDto Thông tin số điện thoại cần xác thực
   * @returns Thông tin về OTP đã gửi
   */
  async sendPhoneVerificationOtp(
    userId: number,
    sendPhoneVerificationOtpDto: SendPhoneVerificationOtpDto,
  ): Promise<SendPhoneVerificationOtpResponseDto> {
    try {
      const { phoneNumber } = sendPhoneVerificationOtpDto;

      // Tìm người dùng hiện tại
      const currentUser = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!currentUser) {
        throw new AppException(
          ErrorCode.USER_NOT_FOUND,
          'Không tìm thấy người dùng',
        );
      }

      // Kiểm tra xem có tài khoản nào khác đã sử dụng số điện thoại này chưa (trừ chính user hiện tại)
      const existingUser = await this.userRepository.findOne({
        where: {
          phoneNumber,
          id: Not(userId),
        },
      });

      if (existingUser) {
        throw new AppException(
          AUTH_ERROR_CODE.PHONE_ALREADY_EXISTS,
          'Số điện thoại này đã được sử dụng bởi tài khoản khác',
        );
      }

      // Kiểm tra cooldown - người dùng phải chờ trước khi gửi OTP mới
      const cooldownKey = `${this.phoneOtpCooldownPrefix}${userId}`;
      const cooldownData = await this.redisService.get(cooldownKey);

      if (cooldownData) {
        throw new AppException(
          AUTH_ERROR_CODE.TOO_MANY_REQUESTS,
          `Vui lòng chờ ${this.cooldownTime} giây trước khi gửi lại OTP`,
        );
      }

      // Tạo OTP 6 số
      const otp = this.generateOTP();

      // Lưu thông tin OTP vào Redis
      const otpData = {
        userId,
        phoneNumber,
        otp,
        createdAt: Date.now(),
      };

      const otpKey = `${this.phoneOtpPrefix}${userId}`;
      await this.redisService.setWithExpiry(
        otpKey,
        JSON.stringify(otpData),
        this.otpExpiryTime,
      );

      // Thiết lập cooldown
      await this.redisService.setWithExpiry(
        cooldownKey,
        'true',
        this.cooldownTime,
      );

      // Gửi SMS OTP
      try {
        await this.smsService.sendSmsOtpVerify2FA(otp, phoneNumber, userId);
        this.logger.log(
          `Đã gửi OTP xác thực số điện thoại đến ${phoneNumber} cho user ${userId}`,
        );
      } catch (error) {
        this.logger.error(
          `Lỗi khi gửi SMS OTP đến ${phoneNumber}: ${error.message}`,
          error.stack,
        );
        throw new AppException(
          AUTH_ERROR_CODE.SMS_SEND_FAILED,
          'Không thể gửi SMS xác thực',
        );
      }

      // Tính toán thời điểm hết hạn
      const expiresAt = Date.now() + this.otpExpiryTime * 1000;

      // Che một phần số điện thoại
      const maskedPhoneNumber = this.maskPhoneNumber(phoneNumber);

      return {
        message: `Đã gửi mã OTP đến số điện thoại ${maskedPhoneNumber}`,
        maskedPhoneNumber,
        expiresAt,
        cooldownSeconds: this.cooldownTime,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi gửi OTP xác thực số điện thoại cho user ${userId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể gửi OTP xác thực số điện thoại',
      );
    }
  }

  /**
   * Xác thực OTP số điện thoại
   * @param userId ID của người dùng
   * @param verifyPhoneOtpDto Thông tin OTP cần xác thực
   * @returns Kết quả xác thực
   */
  async verifyPhoneOtp(
    userId: number,
    verifyPhoneOtpDto: VerifyPhoneOtpDto,
  ): Promise<VerifyPhoneOtpResponseDto> {
    try {
      const { phoneNumber, otp } = verifyPhoneOtpDto;

      // Lấy thông tin OTP từ Redis
      const otpKey = `${this.phoneOtpPrefix}${userId}`;
      const otpDataString = await this.redisService.get(otpKey);

      if (!otpDataString) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.OTP_EXPIRED,
          'Mã OTP đã hết hạn hoặc không tồn tại',
        );
      }

      const otpData = JSON.parse(otpDataString);

      // Kiểm tra số điện thoại có khớp không
      if (otpData.phoneNumber !== phoneNumber) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.INVALID_OTP,
          'Số điện thoại không khớp với OTP đã gửi',
        );
      }

      // Kiểm tra OTP có đúng không
      if (otpData.otp !== otp) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.INVALID_OTP,
          'Mã OTP không chính xác',
        );
      }

      // Kiểm tra lại xem có tài khoản nào khác đã sử dụng số điện thoại này chưa
      const existingUser = await this.userRepository.findOne({
        where: {
          phoneNumber,
          id: Not(userId),
        },
      });

      if (existingUser) {
        throw new AppException(
          AUTH_ERROR_CODE.PHONE_ALREADY_EXISTS,
          'Số điện thoại này đã được sử dụng bởi tài khoản khác',
        );
      }

      // Cập nhật thông tin người dùng
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        throw new AppException(
          ErrorCode.USER_NOT_FOUND,
          'Không tìm thấy người dùng',
        );
      }

      // Cập nhật số điện thoại và trạng thái xác thực
      user.phoneNumber = phoneNumber;
      user.isVerifyPhone = true;
      user.updatedAt = Date.now();

      await this.userRepository.save(user);

      // Xóa OTP khỏi Redis sau khi xác thực thành công
      await this.redisService.del(otpKey);

      // Xóa cooldown
      const cooldownKey = `${this.phoneOtpCooldownPrefix}${userId}`;
      await this.redisService.del(cooldownKey);

      this.logger.log(
        `User ${userId} đã xác thực số điện thoại ${phoneNumber} thành công`,
      );

      return {
        message: 'Xác thực số điện thoại thành công',
        isVerifyPhone: true,
        phoneNumber,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi xác thực OTP số điện thoại cho user ${userId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể xác thực OTP số điện thoại',
      );
    }
  }

  /**
   * Tạo OTP 6 số ngẫu nhiên
   * @returns OTP 6 số
   */
  private generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Che một phần số điện thoại để bảo mật
   * @param phoneNumber Số điện thoại gốc
   * @returns Số điện thoại đã che
   */
  private maskPhoneNumber(phoneNumber: string): string {
    if (phoneNumber.length <= 4) {
      return phoneNumber;
    }
    const visiblePart = phoneNumber.slice(-4);
    const maskedPart = '*'.repeat(Math.max(0, phoneNumber.length - 4));
    return maskedPart + visiblePart;
  }

  /**
   * Nâng cấp tài khoản cá nhân lên doanh nghiệp
   * @param userId ID của người dùng
   * @returns Thông tin tài khoản đã nâng cấp
   */
  async upgradeToBusinessAccount(userId: number): Promise<User> {
    try {
      // Tìm người dùng theo ID
      const user = await this.userRepository.findOne({ where: { id: userId } });

      if (!user) {
        throw new AppException(
          ErrorCode.USER_NOT_FOUND,
          'Không tìm thấy người dùng',
        );
      }

      // Kiểm tra xem tài khoản đã là doanh nghiệp chưa
      if (user.type === UserTypeEnum.BUSINESS) {
        throw new AppException(
          USER_ERROR_CODES.INVALID_INPUT_DATA,
          'Tài khoản đã là loại doanh nghiệp',
        );
      }

      // Cập nhật loại tài khoản thành BUSINESS
      user.type = UserTypeEnum.BUSINESS;
      user.isSignatureRuleContract = false;
      user.updatedAt = Date.now();

      // Lưu thông tin người dùng đã cập nhật
      const updatedUser = await this.userRepository.save(user);

      // Kiểm tra và cập nhật AffiliateAccount nếu có
      const affiliateAccount = await this.affiliateAccountRepository.findByUserId(userId);
      if (affiliateAccount) {
        await this.affiliateAccountRepository.updateStatus(
          affiliateAccount.id,
          AffiliateAccountStatus.DRAFT,
        );
        this.logger.log(
          `Đã cập nhật trạng thái AffiliateAccount thành DRAFT cho user ${userId}`,
        );
      }

      this.logger.log(
        `Đã nâng cấp tài khoản user ${userId} từ INDIVIDUAL lên BUSINESS`,
      );

      return updatedUser;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi nâng cấp tài khoản lên doanh nghiệp cho user ${userId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể nâng cấp tài khoản lên doanh nghiệp',
      );
    }
  }
}
