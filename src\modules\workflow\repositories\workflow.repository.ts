import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { Workflow } from '../entities';
import { QueryWorkflowDto, SortDirection, WorkflowSortField } from '../dto/workflow';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho Workflow entity
 * Following existing repository patterns
 */
@Injectable()
export class WorkflowRepository extends Repository<Workflow> {
  constructor(private dataSource: DataSource) {
    super(Workflow, dataSource.createEntityManager());
  }

  /**
   * Tìm workflows theo user ID với pagination
   */
  async findByUserIdWithPagination(
    userId: number,
    page: number,
    limit: number,
    search?: string,
    isActive?: boolean,
  ) {
    const queryBuilder = this.createQueryBuilder('workflow')
      .where('workflow.userId = :userId', { userId });

    if (search) {
      queryBuilder.andWhere('workflow.name ILIKE :search', { search: `%${search}%` });
    }

    if (isActive !== undefined) {
      queryBuilder.andWhere('workflow.isActive = :isActive', { isActive });
    }

    return queryBuilder
      .orderBy('workflow.updatedAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();
  }

  /**
   * Tìm workflow theo ID và user ID
   */
  async findByIdAndUserId(id: string, userId: number): Promise<Workflow | null> {
    return this.findOne({
      where: {
        id,
        userId,
      },
    });
  }

  /**
   * Kiểm tra tên workflow đã tồn tại cho user
   */
  async existsByNameAndUserId(name: string, userId: number, excludeId?: string): Promise<boolean> {
    const queryBuilder = this.createQueryBuilder('workflow')
      .where('workflow.name = :name', { name })
      .andWhere('workflow.userId = :userId', { userId });

    if (excludeId) {
      queryBuilder.andWhere('workflow.id != :excludeId', { excludeId });
    }

    const count = await queryBuilder.getCount();
    return count > 0;
  }

  /**
   * Lấy thống kê workflows cho user
   */
  async getStatisticsByUserId(userId: number) {
    const [totalWorkflows, activeWorkflows] = await Promise.all([
      this.count({ where: { userId } }),
      this.count({ where: { userId, isActive: true } }),
    ]);

    return {
      totalWorkflows,
      activeWorkflows,
      inactiveWorkflows: totalWorkflows - activeWorkflows,
    };
  }

  /**
   * Tìm workflows active của user
   */
  async findActiveByUserId(userId: number): Promise<Workflow[]> {
    return this.find({
      where: {
        userId,
        isActive: true,
      },
      order: {
        updatedAt: 'DESC',
      },
    });
  }

  /**
   * Search workflows theo tên
   */
  async searchByName(userId: number, searchQuery: string, limit: number = 10): Promise<Workflow[]> {
    return this.createQueryBuilder('workflow')
      .where('workflow.userId = :userId', { userId })
      .andWhere('workflow.name ILIKE :search', { search: `%${searchQuery}%` })
      .orderBy('workflow.updatedAt', 'DESC')
      .limit(limit)
      .getMany();
  }



  /**
   * Update workflow definition
   * @param id - Workflow ID
   * @param definition - New definition
   * @returns Promise with update result
   */
  async updateWorkflowDefinition(id: string, definition: any): Promise<any> {
    return this.update(id, { definition });
  }

  /**
   * Find workflows with pagination using QueryWorkflowDto
   * Follows existing pagination patterns from the codebase
   */
  async findPaginated(queryDto: QueryWorkflowDto, userId?: number, employeeId?: number): Promise<PaginatedResult<Workflow>> {
    const {
      page = 1,
      limit = 10,
      search,
      isActive,
      sortBy = WorkflowSortField.CREATED_AT,
      sortDirection = SortDirection.DESC
    } = queryDto;

    const queryBuilder = this.createQueryBuilder('workflow');

    // Apply search filter
    if (search) {
      queryBuilder.andWhere('workflow.name ILIKE :search', { search: `%${search}%` });
    }

    // Apply status filter
    if (isActive !== undefined) {
      queryBuilder.andWhere('workflow.isActive = :isActive', { isActive });
    }
    
    // Apply user filter
    if (userId) {
      queryBuilder.andWhere('workflow.userId = :userId', { userId });
    }

    // Apply employee filter
    if (employeeId) {
      queryBuilder.andWhere('workflow.employeeId NOT IS NULL');
    }

    // Apply sorting
    queryBuilder.orderBy(`workflow.${sortBy}`, sortDirection);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find templates with pagination
   * @param params - Query parameters
   * @returns Promise with paginated result
   */
  async findTemplatesWithPagination(params: {
    page: number;
    limit: number;
    category?: string;
    search?: string;
    includePrivate?: boolean;
  }): Promise<any> {
    // Placeholder implementation
    return {
      data: [],
      total: 0,
      page: params.page,
      limit: params.limit,
      totalPages: 0
    };
  }

  /**
   * Delete template
   * @param templateId - Template ID
   * @returns Promise with delete result
   */
  async deleteTemplate(templateId: string): Promise<any> {
    // Placeholder implementation
    return { affected: 0 };
  }

  /**
   * Get comprehensive statistics
   * @param timeRange - Time range for statistics
   * @returns Promise with statistics
   */
  async getComprehensiveStatistics(timeRange?: string): Promise<any> {
    // Placeholder implementation
    return {
      totalWorkflows: 0,
      activeWorkflows: 0,
      totalExecutions: 0
    };
  }

  /**
   * Find all workflows for validation
   * @returns Promise with workflows
   */
  async findAllForValidation(): Promise<Workflow[]> {
    return this.find();
  }

  /**
   * Find workflow by ID
   * @param id - Workflow ID
   * @returns Promise with workflow or null
   */
  async findById(id: string): Promise<Workflow | null> {
    return this.findOne({ where: { id } });
  }

}
