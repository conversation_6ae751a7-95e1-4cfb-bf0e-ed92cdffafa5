import { ProviderLlmEnum } from '@/modules/models/constants/provider.enum';

/**
 * Event được emit khi tích hợp key LLM thành công
 */
export class IntegrationKeyCreatedEvent {
  /**
   * ID của integration (key LLM)
   */
  integrationId: string;

  /**
   * Provider LLM
   */
  provider: ProviderLlmEnum;

  /**
   * API key đã được mã hóa
   */
  apiKey: string;

  /**
 * ID của user
 */
  userId?: number;

  /**
   * Metadata bổ sung
   */
  metadata?: any;

  constructor(data: {
    integrationId: string;
    provider: ProviderLlmEnum;
    apiKey: string;
    userId?: number;
    metadata?: any;
  }) {
    this.integrationId = data.integrationId;
    this.provider = data.provider;
    this.userId = data.userId;
    this.apiKey = data.apiKey;
    this.metadata = data.metadata;
  }
}
