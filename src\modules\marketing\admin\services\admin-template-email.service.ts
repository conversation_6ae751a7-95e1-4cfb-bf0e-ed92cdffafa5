import { Injectable, Logger } from '@nestjs/common';
import { AdminTemplateEmailRepository } from '../repositories/admin-template-email.repository';
import { AdminTemplateEmail } from '../entities/admin-template-email.entity';
import { CategoryTemplateAutoEnum } from '@modules/email/interface/category-template-auto.enum';
import { TemplateEmailQueryDto, TemplateEmailResponseDto, TemplateEmailListResponseDto, TemplateEmailOverviewResponseDto, DeleteMultipleTemplateEmailResultDto } from '../dto/template-email';
import { PaginatedResult } from '@/common/response';
import { Like, FindOptionsWhere } from 'typeorm';

/**
 * Service xử lý logic liên quan đến template email bên admin
 */
@Injectable()
export class AdminTemplateEmailService {
  private readonly logger = new Logger(AdminTemplateEmailService.name);

  constructor(
    private readonly adminTemplateEmailRepository: AdminTemplateEmailRepository,
  ) {}

  /**
   * Tìm template email theo category
   * @param category Danh mục của template
   * @returns Template email
   */
  async findByCategory(category: string): Promise<AdminTemplateEmail> {
    this.logger.debug(`Tìm template email theo category: ${category}`);
    return this.adminTemplateEmailRepository.findByCategory(category);
  }

  /**
   * Tìm template email theo category sử dụng enum
   * @param category Danh mục của template (enum)
   * @returns Template email
   */
  async findTemplateAutoByCategory(category: CategoryTemplateAutoEnum): Promise<AdminTemplateEmail> {
    this.logger.debug(`Tìm template email theo category enum: ${category}`);
    return this.adminTemplateEmailRepository.findTemplateAutoByCategory(category);
  }

  /**
   * Lấy danh sách template email với phân trang và filter
   * @param query Tham số truy vấn
   * @returns Danh sách template email với phân trang (không có content)
   */
  async findAll(query: TemplateEmailQueryDto): Promise<PaginatedResult<TemplateEmailListResponseDto>> {
    this.logger.debug('Lấy danh sách template email với phân trang và filter');

    const { page = 1, limit = 10, search, category, subject, sortBy, sortDirection } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện tìm kiếm
    let where: FindOptionsWhere<AdminTemplateEmail> | FindOptionsWhere<AdminTemplateEmail>[] = {};

    // Nếu có search, tìm kiếm trong cả category và subject (OR condition)
    if (search) {
      where = [
        { category: Like(`%${search}%`) },
        { subject: Like(`%${search}%`) }
      ];
    } else {
      // Nếu không có search, sử dụng điều kiện AND cho category và subject
      const andCondition: FindOptionsWhere<AdminTemplateEmail> = {};

      if (category) {
        andCondition.category = Like(`%${category}%`);
      }

      if (subject) {
        andCondition.subject = Like(`%${subject}%`);
      }

      where = andCondition;
    }

    // Đếm tổng số template email
    const total = await this.adminTemplateEmailRepository.count({ where });

    // Lấy danh sách template email với phân trang và sắp xếp
    const templates = await this.adminTemplateEmailRepository.find({
      where,
      order: { [sortBy as string]: sortDirection },
      skip: offset,
      take: limit,
    });

    // Chuyển đổi kết quả thành DTO (không có content)
    const items = templates.map(template => this.mapToListDto(template));

    // Tạo thông tin phân trang theo format PaginatedResult
    const totalPages = Math.ceil(total / limit);

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
        hasItems: total > 0,
      },
    };
  }

  /**
   * Lấy thông tin overview về template email
   * @returns Thông tin overview template email
   */
  async getOverview(): Promise<TemplateEmailOverviewResponseDto> {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Tổng số templates
    const totalTemplates = await this.adminTemplateEmailRepository.count();

    // Tổng số template hoạt động (giả sử tất cả đều hoạt động)
    const activeTemplates = totalTemplates;

    // Tổng số template bản nháp (giả sử không có)
    const draftTemplates = 0;

    // Số template thêm mới tuần này
    const newTemplatesThisWeek = await this.adminTemplateEmailRepository.repository
      .createQueryBuilder('template')
      .where('template.createdAt >= :oneWeekAgo', { oneWeekAgo: oneWeekAgo.getTime() })
      .getCount();

    const overview = new TemplateEmailOverviewResponseDto();
    overview.totalTemplates = totalTemplates;
    overview.activeTemplates = activeTemplates;
    overview.draftTemplates = draftTemplates;
    overview.testSentThisWeek = 0; // Cần implement logic từ bảng email history
    overview.newTemplatesThisWeek = newTemplatesThisWeek;
    overview.updatedAt = now.getTime();

    return overview;
  }

  /**
   * Lấy tất cả template email (phương thức cũ để hỗ trợ tương thích ngược)
   * @returns Danh sách template email
   */
  async findAllTemplates(): Promise<AdminTemplateEmail[]> {
    this.logger.debug('Lấy tất cả template email');
    return this.adminTemplateEmailRepository.findAll();
  }

  /**
   * Lấy template email theo ID
   * @param id ID của template
   * @returns Template email
   */
  async findById(id: number): Promise<AdminTemplateEmail> {
    this.logger.debug(`Lấy template email theo ID: ${id}`);
    return this.adminTemplateEmailRepository.findById(id);
  }

  /**
   * Tạo mới template email
   * @param data Dữ liệu template email
   * @param employeeId ID của nhân viên tạo
   * @returns Template email đã tạo
   */
  async create(data: Partial<AdminTemplateEmail>, employeeId: number): Promise<AdminTemplateEmail> {
    this.logger.debug(`Tạo mới template email với category: ${data.category}`);

    // Thêm thông tin người tạo và thời gian
    const templateData = {
      ...data,
      createdBy: employeeId,
      createdAt: Date.now(),
    };

    return this.adminTemplateEmailRepository.create(templateData);
  }

  /**
   * Cập nhật template email
   * @param id ID của template
   * @param data Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns Template email đã cập nhật
   */
  async update(id: number, data: Partial<AdminTemplateEmail>, employeeId: number): Promise<AdminTemplateEmail> {
    this.logger.debug(`Cập nhật template email với ID: ${id}`);

    // Thêm thông tin người cập nhật và thời gian
    const templateData = {
      ...data,
      updatedBy: employeeId,
      updatedAt: Date.now(),
    };

    return this.adminTemplateEmailRepository.update(id, templateData);
  }

  /**
   * Xóa template email
   * @param id ID của template
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    this.logger.debug(`Xóa template email với ID: ${id}`);
    return this.adminTemplateEmailRepository.delete(id);
  }

  /**
   * Xóa nhiều template email
   * @param ids Danh sách ID của các template cần xóa
   * @returns Kết quả xóa nhiều template
   */
  async deleteMultiple(ids: number[]): Promise<DeleteMultipleTemplateEmailResultDto> {
    this.logger.debug(`Xóa nhiều template email với IDs: ${ids.join(', ')}`);

    const result = new DeleteMultipleTemplateEmailResultDto();
    result.deletedIds = [];
    result.failedIds = [];

    for (const id of ids) {
      try {
        const success = await this.adminTemplateEmailRepository.delete(id);
        if (success) {
          result.deletedIds.push(id);
        } else {
          result.failedIds.push({ id, reason: 'Template không tồn tại' });
        }
      } catch (error) {
        result.failedIds.push({ id, reason: error.message || 'Lỗi không xác định' });
      }
    }

    result.totalDeleted = result.deletedIds.length;
    result.totalFailed = result.failedIds.length;

    return result;
  }

  /**
   * Chuyển đổi entity thành DTO chi tiết (public method)
   * @param template Template email entity
   * @returns Template email DTO
   */
  async mapToDetailDto(template: AdminTemplateEmail): Promise<TemplateEmailResponseDto> {
    return this.mapToDto(template);
  }

  /**
   * Tìm template email theo danh sách category
   * @param categories Danh sách category
   * @returns Danh sách template email
   */
  async findByCategories(categories: string[]): Promise<AdminTemplateEmail[]> {
    this.logger.debug(`Tìm template email theo danh sách category: ${categories.join(', ')}`);

    // Sử dụng queryBuilder để tìm kiếm theo danh sách category
    const templates = await this.adminTemplateEmailRepository.repository
      .createQueryBuilder('template')
      .where('template.category IN (:...categories)', { categories })
      .getMany();

    return templates;
  }

  /**
   * Tìm template email theo danh sách category enum
   * @param categories Danh sách category enum
   * @returns Danh sách template email
   */
  async findTemplateAutoByCategories(categories: CategoryTemplateAutoEnum[]): Promise<AdminTemplateEmail[]> {
    return this.findByCategories(categories as unknown as string[]);
  }

  /**
   * Chuyển đổi từ entity sang DTO (cho danh sách - không có content)
   * @param template Template email entity
   * @returns Template email list DTO
   */
  private mapToListDto(template: AdminTemplateEmail): TemplateEmailListResponseDto {
    const dto = new TemplateEmailListResponseDto();
    dto.id = template.id;
    dto.category = template.category;
    dto.subject = template.subject;
    dto.name = template.name;
    dto.placeholders = template.placeholders;
    dto.createdAt = template.createdAt;
    dto.updatedAt = template.updatedAt;
    return dto;
  }

  /**
   * Chuyển đổi từ entity sang DTO (cho chi tiết - có content)
   * @param template Template email entity
   * @returns Template email DTO
   */
  private mapToDto(template: AdminTemplateEmail): TemplateEmailResponseDto {
    const dto = new TemplateEmailResponseDto();
    dto.id = template.id;
    dto.category = template.category;
    dto.subject = template.subject;
    dto.name = template.name;
    dto.content = template.content;
    dto.placeholders = template.placeholders;
    dto.createdAt = template.createdAt;
    dto.updatedAt = template.updatedAt;
    return dto;
  }
}
