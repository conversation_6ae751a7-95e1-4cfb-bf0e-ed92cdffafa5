# Hướng Dẫn Sử Dụng API Ảnh Bìa (Cover Image)

## Tổng Quan

Đã thêm thành công tính năng ảnh bìa cho cả User và Employee với các API để tạo URL tạm thời upload và cập nhật ảnh bìa.

## Thay Đổi Database

### Bảng `users`
- Thêm cột `cover_image` VARCHAR(500) NULL COMMENT 'ảnh bìa của người dùng'

### Bảng `employees`  
- Thêm cột `cover_image` VARCHAR(500) NULL COMMENT 'ảnh bìa của nhân viên'

**Chạy script SQL:** `database-migration-add-cover-image.sql`

## API Endpoints

### User Cover Image APIs

#### 1. Tạo URL Upload Ảnh Bìa User (Đã lưu vào database)
```
POST /user/account/cover-image/upload-url
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

Body:
{
  "imageType": "image/jpeg",
  "maxSize": 5242880
}

Response:
{
  "success": true,
  "message": "Tạo URL tải lên ảnh bìa thành công",
  "data": {
    "uploadUrl": "https://s3.example.com/presigned-url",
    "coverImageKey": "123/user_cover_images/2024/01/uuid.jpg",
    "expiresIn": 900
  }
}
```

**Lưu ý:** API này đã tự động lưu `coverImageKey` vào database, không cần gọi thêm API cập nhật.



### Employee Cover Image APIs

#### 1. Tạo URL Upload Ảnh Bìa Employee (Đã lưu vào database)
```
POST /employee/cover-image/upload-url
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

Body:
{
  "imageType": "image/jpeg",
  "maxSize": 5242880
}

Response:
{
  "success": true,
  "message": "Tạo URL tải lên ảnh bìa thành công",
  "data": {
    "uploadUrl": "https://s3.example.com/presigned-url",
    "coverImageKey": "456/employee_cover_images/2024/01/uuid.jpg",
    "expiresIn": 900
  }
}
```

**Lưu ý:** API này đã tự động lưu `coverImageKey` vào database, không cần gọi thêm API cập nhật.

## Quy Trình Upload

1. **Gọi API tạo URL upload** - `coverImageKey` sẽ được lưu vào database ngay lập tức
2. **Upload file lên S3** sử dụng URL tạm thời được trả về
3. **Hoàn tất** - Không cần gọi thêm API nào khác, ảnh bìa đã được cập nhật

**Lưu ý quan trọng:** Các API PUT cập nhật ảnh bìa đã được xóa vì không cần thiết.

## Cấu Hình

### S3 Folder Structure
- User: `{userId}/user_cover_images/{year}/{month}/{uuid}.{ext}`
- Employee: `{employeeId}/employee_cover_images/{year}/{month}/{uuid}.{ext}`

### Giới Hạn
- Kích thước tối đa: 10MB (có thể điều chỉnh)
- Thời gian hết hạn URL: 15 phút
- Định dạng hỗ trợ: JPEG, PNG, WEBP, GIF

### Error Codes

#### User Errors
- Sử dụng các error code hiện có trong `ErrorCode`

#### Employee Errors
- `15051`: Tạo URL tải lên ảnh bìa thất bại

## Files Đã Thay Đổi

### Entities
- `src/modules/user/entities/user.entity.ts`
- `src/modules/employee/entities/employee.entity.ts`

### DTOs
- `src/modules/user/dto/cover-image-upload.dto.ts` (mới)
- `src/modules/employee/dto/cover-image-upload.dto.ts` (mới)

### Services
- `src/modules/user/user/service/account.service.ts`
- `src/modules/employee/services/employee.service.ts`

### Controllers
- `src/modules/user/user/controller/account.controller.ts`
- `src/modules/employee/controller/employee.controller.ts`

### Utils
- `src/shared/utils/generators/s3-key-generator.util.ts`

### Error Codes
- `src/modules/employee/exceptions/employee-error.code.ts`

## Lưu Ý

1. **Key được lưu ngay lập tức** khi tạo URL upload, không cần API xác nhận
2. **Tự động tạo thư mục theo thời gian** để tổ chức file tốt hơn
3. **Giới hạn kích thước** được áp dụng ở cả client và server
4. **URL có thời hạn** 15 phút để bảo mật
5. **Hỗ trợ đầy đủ Swagger documentation** cho tất cả API
