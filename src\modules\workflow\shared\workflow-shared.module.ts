import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueueModule } from '@/shared/queue/queue.module';
import {
  Workflow,
  WorkflowNode,
  WorkflowEdge,
  WorkflowExecution,
  WorkflowExecutionLog,
  NodeDefinition,
} from '../entities';
import {
  WorkflowRepository,
  WorkflowNodeRepository,
  WorkflowEdgeRepository,
  WorkflowExecutionLogRepository,
  NodeDefinitionRepository,
} from '../repositories';
import { WorkflowExecutionRepository } from '../repositories/workflow-execution.repository';
import { WorkflowValidationService } from '../services/workflow-validation.service';
import { WorkflowImportExportService } from '../services/workflow-import-export.service';
import { NodeDefinitionService } from '../services/node-definition.service';
import { WorkflowQueueService } from '../services/workflow-queue.service';
import { WorkflowTriggerService } from '../services/workflow-trigger.service';
import { WorkflowExecutionService } from '../services/workflow-execution.service';
import { WorkflowSSEService } from '../services/workflow-sse.service';
import { WorkflowExecutionSSEIntegrationService } from '../services/workflow-execution-sse-integration.service';
// NodeTestService import removed - focusing on real execution only
import { WorkflowCacheService } from '../services/workflow-cache.service';
import { WorkflowQueryOptimizerService } from '../services/workflow-query-optimizer.service';

/**
 * Shared module cho các services và repositories dùng chung
 * giữa admin và user modules
 */
@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Workflow,
      WorkflowNode,
      WorkflowEdge,
      WorkflowExecution,
      WorkflowExecutionLog,
      NodeDefinition,
    ]),
    QueueModule,
  ],
  providers: [
    // Repositories
    WorkflowRepository,
    WorkflowNodeRepository,
    WorkflowEdgeRepository,
    WorkflowExecutionRepository,
    WorkflowExecutionLogRepository,
    NodeDefinitionRepository,
    
    // Shared Services
    WorkflowValidationService,
    WorkflowImportExportService,
    NodeDefinitionService,
    WorkflowQueueService,
    WorkflowTriggerService,
    WorkflowExecutionService,
    WorkflowSSEService,
    WorkflowExecutionSSEIntegrationService,
    // NodeTestService removed - focusing on real execution only
    WorkflowCacheService,
    WorkflowQueryOptimizerService,
  ],
  exports: [
    // Export repositories
    WorkflowRepository,
    WorkflowNodeRepository,
    WorkflowEdgeRepository,
    WorkflowExecutionRepository,
    WorkflowExecutionLogRepository,
    NodeDefinitionRepository,
    
    // Export shared services
    WorkflowValidationService,
    WorkflowImportExportService,
    NodeDefinitionService,
    WorkflowQueueService,
    WorkflowTriggerService,
    WorkflowExecutionService,
    WorkflowSSEService,
    WorkflowExecutionSSEIntegrationService,
    // NodeTestService export removed - focusing on real execution only
    WorkflowCacheService,
    WorkflowQueryOptimizerService,
  ],
})
export class WorkflowSharedModule {}
