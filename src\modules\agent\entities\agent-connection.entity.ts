import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { AgentConnectionConfig } from '../interfaces/agent-connect.interface';

/**
 * Entity đại diện cho bảng agent_connection trong cơ sở dữ liệu
 * Bảng quản lý tất cả các kết nối tích hợp của agent với các dịch vụ bên ngoài
 */
@Entity('agent_connection')
@Index('idx_agent_connection_agent_id', ['agentId'])
@Index('idx_agent_connection_integration_id', ['integrationId'])
@Index('idx_agent_connection_agent_integration', ['agentId', 'integrationId'], { unique: true })
export class AgentConnection {
  /**
   * UUID định danh duy nhất cho mỗi connection
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID của agent
   * Tham chiếu đến bảng agents
   */
  @Column({ name: 'agent_id', type: 'uuid' })
  agentId: string;

  /**
   * ID của integration
   * Tham chiếu đến bảng integration
   */
  @Column({ name: 'integration_id', type: 'uuid' })
  integrationId: string;

  /**
   * Cấu hình kết nối dạng JSONB
   * Chứa các thông tin cấu hình riêng cho từng loại integration:
   */
  @Column({ name: 'config', type: 'jsonb', nullable: true })
  config: AgentConnectionConfig | null;
}
