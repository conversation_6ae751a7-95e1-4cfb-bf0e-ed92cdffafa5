import { ProviderLlmEnum } from '@/modules/models/constants';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc tạo mới system key LLM
 */
export class CreateSystemKeyLlmDto {
  @ApiProperty({
    description: 'Tên định danh cho key',
    example: 'OpenAI Production Key',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Nhà cung cấp model',
    enum: ProviderLlmEnum,
    example: ProviderLlmEnum.OPENAI,
  })
  @IsEnum(ProviderLlmEnum)
  @IsNotEmpty()
  provider: ProviderLlmEnum;

  @ApiProperty({
    description: 'API Key truy cập LLM',
    example: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
  })
  @IsString()
  @IsNotEmpty()
  apiKey: string;
}
