import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ProviderLlmEnum } from '@/modules/models/constants/provider.enum';

/**
 * Interface cho model từ provider
 */
export interface ProviderModel {
  id: string;
  name?: string;
  isFineTune: boolean;
  baseModelId?: string;
  owned_by?: string;
  created?: number;
}

/**
 * Service để fetch models từ các provider
 */
@Injectable()
export class ProviderModelFetcherService {
  private readonly logger = new Logger(ProviderModelFetcherService.name);

  constructor(private readonly httpService: HttpService) {}

  /**
   * Lấy tất cả models từ provider (base + fine-tune)
   * @param provider Provider LLM
   * @param apiKey API key đã decrypt
   * @param baseModelIds Danh sách model base IDs từ registry
   * @returns Promise<ProviderModel[]>
   */
  async fetchModels(
    provider: ProviderLlmEnum,
    apiKey: string,
    baseModelIds: string[]
  ): Promise<ProviderModel[]> {
    try {
      this.logger.log(`Fetching models from ${provider} with ${baseModelIds.length} base models`);

      switch (provider) {
        case ProviderLlmEnum.OPENAI:
          return await this.fetchOpenAIModels(apiKey, baseModelIds);
        case ProviderLlmEnum.GEMINI:
          return await this.fetchGeminiModels(apiKey, baseModelIds);
        case ProviderLlmEnum.ANTHROPIC:
          return await this.fetchAnthropicModels(apiKey, baseModelIds);
        case ProviderLlmEnum.DEEPSEEK:
          return await this.fetchDeepSeekModels(apiKey, baseModelIds);
        case ProviderLlmEnum.XAI:
          return await this.fetchXAIModels(apiKey, baseModelIds);
        default:
          this.logger.warn(`Unsupported provider: ${provider}`);
          return [];
      }
    } catch (error) {
      this.logger.error(`Error fetching models from ${provider}: ${error.message}`);
      return [];
    }
  }

  /**
   * Fetch OpenAI models (base + fine-tune)
   */
  private async fetchOpenAIModels(apiKey: string, baseModelIds: string[]): Promise<ProviderModel[]> {
    try {
      const response = await firstValueFrom(
        this.httpService.get('https://api.openai.com/v1/models', {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 15000,
        })
      );

      const allModels = response.data?.data || [];

      this.logger.debug(`OpenAI models: ${JSON.stringify(allModels)}`);

      const result: ProviderModel[] = [];

      for (const model of allModels) {
        // Check if it's a base model
        if (baseModelIds.includes(model.id)) {
          result.push({
            id: model.id,
            name: model.id,
            isFineTune: false,
            owned_by: model.owned_by,
            created: model.created,
          });
        }
        // Check if it's a fine-tune model (starts with "ft:")
        else if (model.id.startsWith('ft:')) {
          const baseModel = this.extractOpenAIBaseModel(model.id);
          if (baseModel && baseModelIds.includes(baseModel)) {
            result.push({
              id: model.id,
              name: model.id,
              isFineTune: true,
              baseModelId: baseModel,
              owned_by: model.owned_by,
              created: model.created,
            });
          }
        }
      }

      this.logger.log(`Found ${result.length} OpenAI models (${result.filter(m => !m.isFineTune).length} base, ${result.filter(m => m.isFineTune).length} fine-tune)`);
      return result;
    } catch (error) {
      this.logger.error(`Error fetching OpenAI models: ${error.message}`);
      return [];
    }
  }

  /**
   * Fetch Gemini models (base + fine-tune)
   */
  private async fetchGeminiModels(apiKey: string, baseModelIds: string[]): Promise<ProviderModel[]> {
    try {
      const result: ProviderModel[] = [];

      // Add base models
      for (const baseModelId of baseModelIds) {
        result.push({
          id: baseModelId,
          name: baseModelId,
          isFineTune: false,
        });
      }

      // Fetch fine-tuned models
      try {
        const response = await firstValueFrom(
          this.httpService.get(`https://generativelanguage.googleapis.com/v1beta/tunedModels?key=${apiKey}`, {
            timeout: 15000,
          })
        );

        const tunedModels = response.data?.tunedModels || [];
        for (const model of tunedModels) {
          const baseModel = model.baseModel?.replace('models/', '');
          if (baseModel && baseModelIds.includes(baseModel)) {
            result.push({
              id: model.name,
              name: model.displayName || model.name,
              isFineTune: true,
              baseModelId: baseModel,
            });
          }
        }
      } catch (error) {
        this.logger.warn(`Could not fetch Gemini fine-tuned models: ${error.message}`);
      }

      this.logger.log(`Found ${result.length} Gemini models (${result.filter(m => !m.isFineTune).length} base, ${result.filter(m => m.isFineTune).length} fine-tune)`);
      return result;
    } catch (error) {
      this.logger.error(`Error fetching Gemini models: ${error.message}`);
      return [];
    }
  }

  /**
   * Fetch Anthropic models (chỉ base models, không có public fine-tune API)
   */
  private async fetchAnthropicModels(apiKey: string, baseModelIds: string[]): Promise<ProviderModel[]> {
    // Anthropic không có public API để list models
    // Chỉ trả về base models từ registry
    return baseModelIds.map(id => ({
      id,
      name: id,
      isFineTune: false,
    }));
  }

  /**
   * Fetch DeepSeek models
   */
  private async fetchDeepSeekModels(apiKey: string, baseModelIds: string[]): Promise<ProviderModel[]> {
    // DeepSeek sử dụng OpenAI-compatible API
    return baseModelIds.map(id => ({
      id,
      name: id,
      isFineTune: false,
    }));
  }

  /**
   * Fetch XAI models
   */
  private async fetchXAIModels(apiKey: string, baseModelIds: string[]): Promise<ProviderModel[]> {
    // XAI (Grok) chỉ trả về base models từ registry
    return baseModelIds.map(id => ({
      id,
      name: id,
      isFineTune: false,
    }));
  }

  /**
   * Extract base model từ OpenAI fine-tune model ID
   * Format: ft:gpt-4-turbo:org:suffix:id
   */
  private extractOpenAIBaseModel(fineTuneId: string): string | null {
    const parts = fineTuneId.split(':');
    return parts.length >= 2 ? parts[1] : null;
  }
}
