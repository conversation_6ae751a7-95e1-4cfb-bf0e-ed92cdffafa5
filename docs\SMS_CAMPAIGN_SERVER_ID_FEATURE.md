# SMS Campaign Server ID Feature

## Tổng quan
Tính năng cho phép:
1. Chỉ định SMS server cụ thể khi tạo SMS campaign admin thông qua `serverId` (UUID của bảng integration)
2. <PERSON><PERSON><PERSON> SMS đến nhiều nguồn recipients: segments, audiences, users và danh sách số điện thoại trực tiếp

## Thay đổi chính

### 1. DTO Changes

#### CreateSmsCampaignAdminDto
```typescript
export class CreateSmsCampaignAdminDto {
  // ... existing fields

  @ApiPropertyOptional({
    description: 'Danh sách số điện thoại trực tiếp (bao gồm mã quốc gia)',
    example: ['+84901234567', '+84987654321', '84912345678'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  phoneNumbers?: string[];

  @ApiPropertyOptional({
    description: 'ID của SMS server (integration UUID). <PERSON><PERSON><PERSON> không có thì sử dụng server mặc định',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  serverId?: string;
}
```

#### SmsCampaignAdminResponseDto
```typescript
export class SmsCampaignAdminResponseDto {
  // ... existing fields
  
  @ApiPropertyOptional({
    description: 'ID của SMS server integration (UUID)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  serverId?: string | null;
}
```

### 2. Entity Changes

#### SmsCampaignAdmin Entity
```typescript
export class SmsCampaignAdmin {
  // ... existing fields
  
  /**
   * ID của SMS server integration (UUID)
   */
  @Column({ name: 'server_id', type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  serverId: string | null;
}
```

### 3. Service Logic Changes

#### SmsCampaignAdminService
- **getSmsServerConfig()**: Method mới để lấy config từ serverId hoặc mặc định
- **getSmsServerConfigFromIntegration()**: Method mới để lấy config từ integration cụ thể
- **getDefaultSmsServerConfig()**: Method cũ được giữ lại cho trường hợp fallback
- **getRecipientsFromSegmentOrAudience()**: Cập nhật để hỗ trợ phoneNumbers
- **validateAndFormatPhoneNumber()**: Method mới để validate và format số điện thoại

```typescript
private async getSmsServerConfig(serverId?: string): Promise<Record<string, any> | null> {
  if (serverId) {
    return this.getSmsServerConfigFromIntegration(serverId);
  } else {
    return this.getDefaultSmsServerConfig();
  }
}
```

### 4. Database Migration

#### Migration: AddServerIdToSmsCampaignAdmin
```sql
-- Add server_id column
ALTER TABLE "sms_campaign_admin" 
ADD COLUMN IF NOT EXISTS "server_id" uuid;

-- Add comment
COMMENT ON COLUMN "sms_campaign_admin"."server_id" IS 'ID của SMS server integration (UUID)';

-- Add index
CREATE INDEX IF NOT EXISTS "IDX_sms_campaign_admin_server_id" 
ON "sms_campaign_admin" ("server_id");
```

## Cách sử dụng

### 1. Tạo SMS Campaign với Server cụ thể
```json
POST /api/v1/admin/sms-campaigns
{
  "name": "Campaign với server cụ thể",
  "campaignType": "ADS",
  "templateId": 1,
  "audienceIds": [1, 2, 3],
  "serverId": "550e8400-e29b-41d4-a716-************",
  "placeholders": {
    "customerName": "Test User"
  }
}
```

### 2. Tạo SMS Campaign với Server mặc định
```json
POST /api/v1/admin/sms-campaigns
{
  "name": "Campaign với server mặc định",
  "campaignType": "NOTIFICATION",
  "templateId": 1,
  "segmentIds": [1, 2],
  "placeholders": {
    "message": "Thông báo quan trọng"
  }
}
```

### 3. Tạo SMS Campaign với danh sách số điện thoại
```json
POST /api/v1/admin/sms-campaigns
{
  "name": "Campaign với phone numbers",
  "campaignType": "ADS",
  "templateId": 1,
  "phoneNumbers": [
    "+84901234567",
    "84987654321",
    "0912345678"
  ],
  "placeholders": {
    "customerName": "Khách hàng",
    "discount": "50%"
  }
}
```

### 4. Tạo SMS Campaign kết hợp nhiều nguồn
```json
POST /api/v1/admin/sms-campaigns
{
  "name": "Campaign kết hợp",
  "campaignType": "NOTIFICATION",
  "templateId": 1,
  "audienceIds": [1, 2],
  "segmentIds": [1],
  "phoneNumbers": ["+84901111111"],
  "serverId": "550e8400-e29b-41d4-a716-************",
  "placeholders": {
    "message": "Thông báo từ nhiều nguồn"
  }
}
```

## Validation & Error Handling

### 1. ServerId Validation
- Phải là UUID hợp lệ
- Integration phải tồn tại trong database
- Integration phải là SMS provider (FPT_SMS, TWILIO, VONAGE, SPEED_SMS)

### 2. Error Cases
- **400**: ServerId không hợp lệ (không phải UUID)
- **400**: Integration không tồn tại
- **400**: Integration không phải SMS provider
- **400**: Không thể giải mã cấu hình SMS server

### 3. Fallback Behavior
- Nếu không có serverId → sử dụng server mặc định
- Nếu serverId không hợp lệ → throw error (không fallback)
- Nếu giải mã thất bại → throw error (không fallback)

## Worker Integration

Job data được truyền đến worker bao gồm:
```typescript
const jobData = {
  campaignId: campaign.id,
  recipients: recipients,
  smsServerConfig: smsServerConfig, // Config đã được giải mã
  content: smsContent,
  serverId: serverId, // UUID của integration
  // ... other fields
};
```

## Testing

Sử dụng file `test-sms-campaign-with-server-id.http` để test các trường hợp:
1. Campaign với serverId hợp lệ
2. Campaign không có serverId (mặc định)
3. Campaign với serverId không hợp lệ
4. Lấy danh sách và chi tiết campaign
