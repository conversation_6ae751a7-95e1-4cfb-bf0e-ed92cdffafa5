import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsNumber, IsUUID } from 'class-validator';

/**
 * DTO cho webhook trigger response
 */
export class WebhookTriggerResponseDto {
  @ApiProperty({
    description: 'ID của workflow execution được tạo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  executionId: string;

  @ApiProperty({
    description: 'ID của job trong queue (optional)',
    example: '456',
    required: false,
  })
  @IsOptional()
  @IsString()
  jobId?: string;

  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Workflow triggered successfully',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Timestamp khi trigger',
    example: 1640995200000,
  })
  @IsNumber()
  timestamp: number;
}

/**
 * DTO cho Facebook webhook data
 */
export class FacebookWebhookDto {
  @ApiProperty({
    description: 'Object type từ Facebook',
    example: 'page',
  })
  @IsString()
  object: string;

  @ApiProperty({
    description: 'Entry data từ Facebook webhook',
    type: 'array',
    items: {
      type: 'object',
    },
  })
  @IsObject()
  entry: any[];
}

/**
 * DTO cho Zalo webhook data
 */
export class ZaloWebhookDto {
  @ApiProperty({
    description: 'Event name từ Zalo',
    example: 'user_send_text',
  })
  @IsString()
  event_name: string;

  @ApiProperty({
    description: 'App ID từ Zalo',
    example: '123456789',
  })
  @IsString()
  app_id: string;

  @ApiProperty({
    description: 'User ID từ Zalo',
    example: '987654321',
  })
  @IsString()
  user_id: string;

  @ApiProperty({
    description: 'Timestamp từ Zalo',
    example: '1640995200',
  })
  @IsString()
  timestamp: string;

  @ApiProperty({
    description: 'Message data từ Zalo',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  message?: any;

  @ApiProperty({
    description: 'Recipient data từ Zalo',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  recipient?: any;

  @ApiProperty({
    description: 'Sender data từ Zalo',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  sender?: any;
}

/**
 * DTO cho Google webhook data
 */
export class GoogleWebhookDto {
  @ApiProperty({
    description: 'Message data từ Google Pub/Sub',
    type: 'object',
    additionalProperties: true,
  })
  @IsObject()
  message: {
    data: string; // Base64 encoded data
    messageId: string;
    publishTime: string;
    attributes?: Record<string, string>;
  };

  @ApiProperty({
    description: 'Subscription name',
    example: 'projects/my-project/subscriptions/my-subscription',
  })
  @IsString()
  subscription: string;
}

/**
 * DTO cho generic webhook data
 */
export class GenericWebhookDto {
  @ApiProperty({
    description: 'Event type',
    example: 'user.created',
  })
  @IsOptional()
  @IsString()
  event?: string;

  @ApiProperty({
    description: 'Event data',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  data?: any;

  @ApiProperty({
    description: 'Timestamp',
    example: 1640995200000,
  })
  @IsOptional()
  @IsNumber()
  timestamp?: number;

  @ApiProperty({
    description: 'Source system',
    example: 'external-api',
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiProperty({
    description: 'Additional metadata',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho webhook validation error
 */
export class WebhookValidationErrorDto {
  @ApiProperty({
    description: 'Error code',
    example: 'INVALID_SIGNATURE',
  })
  @IsString()
  code: string;

  @ApiProperty({
    description: 'Error message',
    example: 'Invalid webhook signature',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Timestamp',
    example: 1640995200000,
  })
  @IsNumber()
  timestamp: number;
}
