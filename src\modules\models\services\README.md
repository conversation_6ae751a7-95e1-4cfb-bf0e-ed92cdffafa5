# Models Services

Các service hỗ trợ cho module Models.

## TokenCounterService

Service đơn giản để đếm token chính xác cho các AI providers khác nhau.

### 🎯 Hàm chính - API đơn giản

#### `countTokens(text, provider, modelId, apiKey?)`

**Đầu vào:**
- `text: string` - <PERSON><PERSON><PERSON> bản cần đếm token
- `provider: ProviderLlmEnum` - Provider enum (OPENAI, GEMINI, ANTHROPIC, DEEPSEEK, XAI)
- `modelId: string` - ID của model (vd: 'gpt-4', 'gemini-pro')
- `apiKey?: string` - API key (bắt buộc cho GEMINI, optional cho OPENAI)

**Đầu ra:**
- `Promise<number>` - Số token

### 🔧 Cách sử dụng

#### 1. Inject service vào constructor:
```typescript
@Injectable()
export class MyService {
  constructor(
    private readonly tokenCounter: TokenCounterService
  ) {}
}

## JsonlFileValidatorService

Service để validate và đếm token cho file .jsonl từ S3 theo yêu cầu của các AI providers.

### 🎯 Hàm chính

#### `validateJsonlFile(s3Key, provider, modelId, apiKey?)`

**Đầu vào:**
- `s3Key: string` - Key của file .jsonl trên S3
- `provider: ProviderLlmEnum` - Provider để validate (OPENAI hoặc GEMINI)
- `modelId: string` - Model ID để đếm token chính xác
- `apiKey?: string` - API key (bắt buộc cho GEMINI)

**Đầu ra:**
- `Promise<JsonlValidationResult>` - Kết quả validation với:
  - `isValid: boolean` - File có hợp lệ không
  - `totalTokens: number` - Tổng số token trong file
  - `totalLines: number` - Tổng số dòng trong file
  - `fileSizeBytes: number` - Kích thước file tính bằng bytes
  - `errors?: string[]` - Danh sách lỗi nếu có

### 📋 Quy tắc validation theo provider

#### OpenAI:
- Tối thiểu: 10 dòng
- Tối đa: 50,000 token mỗi dòng
- Kích thước file tối đa: 250MB

#### Gemini:
- Tối thiểu: 20 dòng
- Tối đa: 5,000 dòng
- Không giới hạn token mỗi dòng
- Không giới hạn kích thước file

### 🔧 Cách sử dụng

#### 1. Inject service vào constructor:
```typescript
@Injectable()
export class MyService {
  constructor(
    private readonly jsonlValidator: JsonlFileValidatorService
  ) {}
}
```

#### 2. Validate file JSONL cho OpenAI:
```typescript
async validateOpenAIFile(s3Key: string) {
  try {
    const result = await this.jsonlValidator.validateJsonlFile(
      s3Key,
      ProviderLlmEnum.OPENAI,
      'gpt-3.5-turbo'
    );

    console.log(`File hợp lệ: ${result.isValid}`);
    console.log(`Tổng token: ${result.totalTokens}`);
    console.log(`Tổng dòng: ${result.totalLines}`);
    console.log(`Kích thước: ${result.fileSizeBytes} bytes`);

    return result;
  } catch (error) {
    if (error instanceof AppException) {
      console.error(`Validation failed: ${error.message}`);
    }
    throw error;
  }
}
```

#### 3. Validate file JSONL cho Gemini:
```typescript
async validateGeminiFile(s3Key: string, apiKey: string) {
  try {
    const result = await this.jsonlValidator.validateJsonlFile(
      s3Key,
      ProviderLlmEnum.GEMINI,
      'gemini-pro',
      apiKey
    );

    console.log(`File hợp lệ: ${result.isValid}`);
    console.log(`Tổng token: ${result.totalTokens}`);
    console.log(`Tổng dòng: ${result.totalLines}`);

    return result;
  } catch (error) {
    if (error instanceof AppException) {
      console.error(`Validation failed: ${error.message}`);
    }
    throw error;
  }
}
```

### ⚠️ Lưu ý

1. **File format**: File phải có định dạng .jsonl với mỗi dòng là một JSON object hợp lệ
2. **S3 Key**: Đảm bảo file tồn tại trên S3 và có quyền truy cập
3. **API Key**: Bắt buộc cho Gemini, tùy chọn cho OpenAI
4. **Error handling**: Service sẽ throw AppException với mã lỗi cụ thể khi validation thất bại
5. **Performance**: Service đọc file theo stream để xử lý file lớn hiệu quả
```

### 2. Sử dụng hàm chính:
```typescript
// OpenAI models (không cần API key)
const openaiTokens = await this.tokenCounter.countTokensSimple(
  "Hello world!", 
  ProviderLlmEnum.OPENAI, 
  'gpt-4'
);

// Gemini models (cần API key)
const geminiTokens = await this.tokenCounter.countTokensSimple(
  "Hello world!", 
  ProviderLlmEnum.GEMINI, 
  'gemini-pro',
  'your-google-api-key'
);

// Anthropic models (sử dụng estimation)
const anthropicTokens = await this.tokenCounter.countTokensSimple(
  "Hello world!", 
  ProviderLlmEnum.ANTHROPIC, 
  'claude-3-sonnet'
);
```

## 📋 Providers được hỗ trợ

| Provider | Method | Độ chính xác | API Key |
|----------|--------|--------------|---------|
| OPENAI | tiktoken | Cao | Không cần |
| GEMINI | Google API | Cao | Bắt buộc |
| ANTHROPIC | Estimation | Trung bình | Không cần |
| DEEPSEEK | Estimation | Trung bình | Không cần |
| XAI | Estimation | Trung bình | Không cần |

## 🎯 Ví dụ thực tế

```typescript
@Injectable()
export class FineTuneService {
  constructor(
    private readonly tokenCounter: TokenCounterService
  ) {}

  async estimateTrainingCost(
    trainingData: string, 
    provider: ProviderLlmEnum, 
    modelId: string,
    apiKey?: string
  ) {
    // Đếm token
    const tokens = await this.tokenCounter.countTokensSimple(
      trainingData, 
      provider, 
      modelId, 
      apiKey
    );

    // Ước lượng chi phí
    const pricePerToken = 0.002; // $0.002 per token
    const cost = this.tokenCounter.estimateCost(tokens, pricePerToken);

    return { tokens, cost };
  }
}
```

## ⚡ Tính năng bổ sung

### Đếm token cho nhiều đoạn text:
```typescript
const texts = ["Text 1", "Text 2", "Text 3"];
const totalTokens = this.tokenCounter.countTokensForTexts(texts);
```

### Ước lượng chi phí:
```typescript
const cost = this.tokenCounter.estimateCost(tokenCount, pricePerToken);
```

## 🚨 Lưu ý quan trọng

1. **Google API Key**: Bắt buộc cho GEMINI provider
2. **Fallback**: Tự động chuyển sang estimation nếu có lỗi
3. **Error Handling**: Service tự động xử lý lỗi và fallback
4. **Performance**: OpenAI sử dụng tiktoken (nhanh), Gemini gọi API (chậm hơn)

## 📝 Error Handling

Service tự động xử lý lỗi và fallback sang estimation:

```typescript
try {
  const tokens = await this.tokenCounter.countTokensSimple(
    text, 
    ProviderLlmEnum.GEMINI, 
    'gemini-pro'
    // Thiếu API key
  );
  // Sẽ tự động fallback sang estimation
} catch (error) {
  // Service đã xử lý lỗi nội bộ
  console.log(`Tokens: ${tokens}`); // Vẫn có kết quả
}
```
