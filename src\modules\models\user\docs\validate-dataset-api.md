# API Validate Dataset Fine-Tune

## <PERSON><PERSON> tả
API này cho phép validate dữ liệu file train và validation của dataset fine-tune theo định dạng của từng provider (OpenAI, Gemini).

## Endpoint
```
POST /user/data-fine-tune/{id}/validate
```

## Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | string (UUID) | Yes | ID của dataset fine-tune |

## Request Body
```json
{
  "modelId": "550e8400-e29b-41d4-a716-************",
  "epochs": 3,
  "batchSize": "auto",
  "learningRateMultiplier": "auto",
  "calculateCostEstimate": true
}
```

### Request Body Schema
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| modelId | string (UUID) | Yes | ID của model để validate |
| epochs | number | No | Số epoch để huấn luyện (1-100, default: 3) |
| batchSize | number \| "auto" | No | Kích thước batch (default: "auto") |
| learningRateMultiplier | number \| "auto" | No | Hệ số tốc độ học (default: "auto") |
| calculateCostEstimate | boolean | No | Có tính ước lượng chi phí không (default: true) |

## Response

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "trainResult": {
      "isValid": true,
      "totalTokens": 1000,
      "totalLines": 15,
      "fileSizeBytes": 5000
    },
    "validResult": {
      "isValid": true,
      "totalTokens": 500,
      "totalLines": 8,
      "fileSizeBytes": 2500
    },
    "totalTokens": 1500,
    "provider": "OPENAI",
    "modelUsed": "gpt-3.5-turbo",
    "message": "Validation thành công cho cả file train và validation",
    "costEstimate": {
      "totalTrainingTokens": 4500,
      "estimatedCost": 450000,
      "trainingPricing": 100,
      "hyperparameters": {
        "epochs": 3,
        "batchSize": "auto",
        "learningRateMultiplier": "auto"
      }
    }
  },
  "message": "Success"
}
```

### Response Schema
| Field | Type | Description |
|-------|------|-------------|
| trainResult | object | Kết quả validation file train |
| trainResult.isValid | boolean | File train có hợp lệ không |
| trainResult.totalTokens | number | Tổng số token trong file train |
| trainResult.totalLines | number | Tổng số dòng trong file train |
| trainResult.fileSizeBytes | number | Kích thước file train (bytes) |
| trainResult.errors | string[] | Danh sách lỗi nếu có |
| validResult | object \| null | Kết quả validation file validation (null nếu không có) |
| validResult.isValid | boolean | File validation có hợp lệ không |
| validResult.totalTokens | number | Tổng số token trong file validation |
| validResult.totalLines | number | Tổng số dòng trong file validation |
| validResult.fileSizeBytes | number | Kích thước file validation (bytes) |
| validResult.errors | string[] | Danh sách lỗi nếu có |
| totalTokens | number | Tổng số token của cả train và validation |
| provider | string | Provider được sử dụng để validate |
| modelUsed | string | Model ID được sử dụng để validate |
| message | string | Thông báo tổng kết |

## Error Responses

### 400 Bad Request - Validation Error
```json
{
  "success": false,
  "error": {
    "code": "USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA",
    "message": "File JSONL có 5 lỗi validation",
    "details": {
      "errors": [
        "Dòng 1: OpenAI format yêu cầu trường 'messages' là một array",
        "Dòng 3: 'role' phải là 'system', 'user', hoặc 'assistant'",
        "Dòng 5: JSON không hợp lệ - Unexpected token"
      ]
    }
  }
}
```

### 404 Not Found - Dataset Not Found
```json
{
  "success": false,
  "error": {
    "code": "USER_DATA_FINE_TUNE_NOT_FOUND",
    "message": "Không tìm thấy dataset fine tune"
  }
}
```

### 404 Not Found - Model Not Found
```json
{
  "success": false,
  "error": {
    "code": "USER_DATA_FINE_TUNE_NOT_FOUND",
    "message": "Không tìm thấy model"
  }
}
```

## Validation Rules

### OpenAI Format
- **Số lượng dòng**: Tối thiểu 10 dòng
- **Token limit**: Tối đa 50,000 token mỗi dòng
- **File size**: Tối đa 250MB
- **Schema**: Mỗi dòng phải có format:
```json
{
  "messages": [
    {
      "role": "system|user|assistant",
      "content": "string content"
    }
  ]
}
```

### Gemini Format
- **Số lượng dòng**: Tối thiểu 20 dòng, tối đa 5,000 dòng
- **Token limit**: Không giới hạn token mỗi dòng
- **File size**: Không giới hạn kích thước file
- **Schema**: Mỗi dòng phải có format:
```json
{
  "systemInstruction": {
    "role": "system",
    "parts": [{"text": "system instruction"}]
  },
  "contents": [
    {
      "role": "user|model",
      "parts": [{"text": "content text"}]
    }
  ]
}
```

## Lưu ý
1. **API Key**: Đối với Gemini, API key sẽ được tự động lấy từ model integration
2. **File format**: File phải có định dạng .jsonl với mỗi dòng là một JSON object hợp lệ
3. **S3 Storage**: File phải được lưu trữ trên S3 và có quyền truy cập
4. **Performance**: API sử dụng stream processing để xử lý file lớn hiệu quả
5. **Error handling**: API sẽ trả về lỗi chi tiết khi validation thất bại
6. **Cost Estimation**: Chỉ áp dụng cho OpenAI models. Công thức: `estimatedCost = totalTrainingTokens * training_pricing`
7. **Pricing**: `training_pricing` được lấy từ model registry, fallback về 1 nếu không có

## Ví dụ sử dụng

### cURL
```bash
curl -X POST "https://api.example.com/user/data-fine-tune/550e8400-e29b-41d4-a716-************/validate" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "modelId": "550e8400-e29b-41d4-a716-446655440001",
    "epochs": 3,
    "batchSize": "auto",
    "learningRateMultiplier": "auto",
    "calculateCostEstimate": true
  }'
```

### JavaScript/TypeScript
```typescript
const response = await fetch('/user/data-fine-tune/550e8400-e29b-41d4-a716-************/validate', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    modelId: '550e8400-e29b-41d4-a716-446655440001'
  })
});

const result = await response.json();
console.log('Validation result:', result);
```
