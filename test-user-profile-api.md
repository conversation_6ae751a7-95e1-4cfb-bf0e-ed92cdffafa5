# Test API GET /v1/users/profile với coverImage

## <PERSON><PERSON> tả thay đổi

Đã thêm trường `coverImage` và `coverImageUrl` vào API GET `/v1/users/profile` để trả về thông tin ảnh bìa của người dùng.

## Các thay đổi đã thực hiện

### 1. C<PERSON><PERSON> nhật UserDto
- Thêm trường `coverImage`: Key của ảnh bìa trên hệ thống lưu trữ
- Thêm trường `coverImageUrl`: URL xem ảnh bìa có thời hạn (1 giờ)

### 2. Cập nhật UserService.getUserProfile()
- Thêm logic xử lý URL ảnh bìa tương tự như avatar
- S<PERSON> dụng `CdnService.generateUrlView()` để tạo URL có thời hạn
- Thêm `coverImage` và `coverImageUrl` vào response DTO

## Cách test API

### Request
```http
GET /v1/users/profile
Authorization: Bearer <JWT_TOKEN>
```

### Expected Response
```json
{
  "success": true,
  "data": {
    "id": 1,
    "fullName": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "phoneNumber": "0987654321",
    "countryCode": 84,
    "isFirstPasswordChange": false,
    "isActive": true,
    "isVerifyEmail": true,
    "isVerifyPhone": true,
    "createdAt": 1625097600000,
    "updatedAt": 1625097600000,
    "address": "123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh",
    "taxCode": "0123456789",
    "pointsBalance": 1000,
    "type": "INDIVIDUAL",
    "platform": "web",
    "citizenId": "************",
    "citizenIssuePlace": "Cục Cảnh sát ĐKQL cư trú và DLQG về dân cư",
    "citizenIssueDate": "2020-01-01",
    "avatar": "users/1/avatar.jpg",
    "avatarUrl": "https://cdn.example.com/users/1/avatar.jpg?token=abc123",
    "coverImage": "users/1/cover_images/2024/01/cover.jpg",
    "coverImageUrl": "https://cdn.example.com/users/1/cover_images/2024/01/cover.jpg?token=def456",
    "dateOfBirth": "1990-01-01",
    "gender": "MALE",
    "bankCode": "VCB",
    "accountNumber": "**********",
    "accountHolder": "NGUYEN VAN A",
    "bankBranch": "Chi nhánh Quận 1"
  },
  "message": "Success"
}
```

## Test Cases

### Case 1: User có coverImage
- User đã upload ảnh bìa
- Expect: `coverImage` có giá trị S3 key, `coverImageUrl` có URL có thời hạn

### Case 2: User chưa có coverImage  
- User chưa upload ảnh bìa
- Expect: `coverImage` = null, `coverImageUrl` = undefined

### Case 3: CdnService generateUrlView thất bại
- `coverImage` có giá trị nhưng CDN service không tạo được URL
- Expect: `coverImage` có giá trị, `coverImageUrl` = undefined

## Swagger Documentation

API documentation sẽ tự động cập nhật vì sử dụng `UserDto` schema trong `@ApiResponse`.

## Tương thích ngược

- API vẫn trả về tất cả các trường cũ
- Chỉ thêm 2 trường mới: `coverImage` và `coverImageUrl`
- Không ảnh hưởng đến client hiện tại
