# Zalo Group Detail Enhanced API Documentation

## Tổng quan

API này đã được cập nhật để lấy thông tin chi tiết đầy đủ từ Zalo API và đồng bộ vào database local. Khi gọi API, hệ thống sẽ:

1. **Gọi Zalo API** để lấy thông tin mới nhất
2. **Đồng bộ dữ liệu** vào database local
3. **Trả về thông tin đầy đủ** bao gồm tất cả các trường từ Zalo API

## Endpoint

```
GET /v1/zalo-group-management/{integrationId}/{groupId}
```

## Thông tin mới được thêm

### **Từ Zalo API `group_info`:**
- `groupLink`: Link tham gia nhóm
- `zaloStatus`: Trạng thái nhóm trên <PERSON> (enabled/disabled)
- `totalMember`: Tổng số thành viên (từ Zalo API)
- `maxMember`: S<PERSON> thành viên tối đa
- `autoDeleteDate`: Ng<PERSON>y nhóm tự động giải tán

### **Từ Zalo API `asset_info`:**
- `assetType`: Loại sản phẩm GMF (gmf10/gmf50/gmf100)
- `assetId`: ID gói GMF sử dụng cho nhóm
- `validThrough`: Ngày hết hạn của gói GMF
- `autoRenew`: Có tự động gia hạn gói GMF không

### **Từ Zalo API `group_setting`:**
- `lockSendMsg`: Khóa tính năng nhắn tin của thành viên
- `joinAppr`: Yêu cầu duyệt thành viên mới
- `enableMsgHistory`: Cho phép thành viên mới đọc tin nhắn cũ
- `enableLinkJoin`: Cho phép tham gia nhóm bằng link

### **Thông tin đồng bộ:**
- `lastSyncAt`: Thời điểm đồng bộ dữ liệu từ Zalo API lần cuối

## Response Example

```json
{
  "code": 200,
  "message": "Lấy thông tin nhóm thành công",
  "result": {
    "id": "1-d331ae2f-b314-4095-963f-6a7c157658a0-a9574804ec5f05015c4e",
    "userId": 1,
    "zaloOfficialAccountId": "d331ae2f-b314-4095-963f-6a7c157658a0",
    "groupId": "a9574804ec5f05015c4e",
    "groupName": "Cộng đồng RedAI - AI Automation",
    "description": "Cộng đồng RedAI - Nơi hỗ trợ nhà bán hàng...",
    "avatarUrl": "https://s480-ava-grp-talk.zadn.vn/d/2/3/4/2/480/19870960a2b3bc6f3cb4f4b51099b852.jpg",
    "memberCount": 9,
    "adminCount": 1,
    "status": "active",
    "isOaAdmin": true,
    "createdByOa": false,
    "lastActivityAt": "2025-07-10T11:22:57.064Z",
    
    // === Thông tin mới từ Zalo API ===
    
    // Từ group_info
    "groupLink": "https://zalo.me/g/zkfgeb641",
    "zaloStatus": "enabled",
    "totalMember": 9,
    "maxMember": "50",
    "autoDeleteDate": "10/11/2025",
    
    // Từ asset_info
    "assetType": "gmf50",
    "assetId": "1c0c8e7b2f27c6799f36",
    "validThrough": "28/12/2025",
    "autoRenew": true,
    
    // Từ group_setting
    "lockSendMsg": false,
    "joinAppr": true,
    "enableMsgHistory": true,
    "enableLinkJoin": true,
    
    // Thông tin đồng bộ
    "lastSyncAt": "2025-07-10T11:30:00.000Z",
    
    // Metadata (backward compatibility)
    "metadata": {
      "asset_info": {
        "asset_type": "gmf50",
        "asset_id": "1c0c8e7b2f27c6799f36",
        "valid_through": "28/12/2025",
        "auto_renew": true
      },
      "group_setting": {
        "lock_send_msg": false,
        "join_appr": true,
        "enable_msg_history": true,
        "enable_link_join": true
      }
    },
    
    "createdAt": "2025-07-10T11:22:57.064Z",
    "updatedAt": "2025-07-10T11:30:00.000Z"
  }
}
```

## Cách hoạt động

### **1. Gọi API lần đầu (nhóm chưa có trong DB):**
```
GET /v1/zalo-group-management/{integrationId}/{groupId}
```
- Gọi Zalo API `getgroup` để lấy thông tin mới nhất
- Tạo bản ghi mới trong database với đầy đủ thông tin
- Trả về thông tin đầy đủ

### **2. Gọi API lần sau (nhóm đã có trong DB):**
```
GET /v1/zalo-group-management/{integrationId}/{groupId}
```
- Gọi Zalo API `getgroup` để lấy thông tin mới nhất
- Cập nhật bản ghi hiện có trong database
- Cập nhật `lastSyncAt` = thời điểm hiện tại
- Trả về thông tin đã được cập nhật

## Database Migration

Trước khi sử dụng API mới, cần chạy migration script:

```sql
-- Chạy file docs/ZALO_GROUP_MIGRATION.sql
-- Hoặc chạy từng lệnh:

ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS group_link VARCHAR(500);
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS zalo_status VARCHAR(50);
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS total_member INTEGER;
-- ... (xem file migration đầy đủ)
```

## Error Handling

### **Lỗi từ Zalo API:**
```json
{
  "code": 500,
  "message": "Lỗi khi lấy thông tin nhóm",
  "error": "EXTERNAL_SERVICE_ERROR"
}
```

### **Nhóm không tồn tại:**
```json
{
  "code": 404,
  "message": "Nhóm không tồn tại hoặc OA không có quyền truy cập",
  "error": "GROUP_NOT_FOUND"
}
```

### **Không có quyền:**
```json
{
  "code": 403,
  "message": "OA không có quyền quản lý thông tin nhóm",
  "error": "PERMISSION_DENIED"
}
```

## Sử dụng

### **JavaScript Example:**
```javascript
async function getGroupDetail(integrationId, groupId) {
  const response = await fetch(
    `/v1/zalo-group-management/${integrationId}/${groupId}`,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    }
  );
  
  const data = await response.json();
  
  if (data.code === 200) {
    const group = data.result;
    
    console.log('Thông tin nhóm:', {
      name: group.groupName,
      members: group.totalMember,
      maxMembers: group.maxMember,
      assetType: group.assetType,
      validThrough: group.validThrough,
      joinApproval: group.joinAppr,
      lastSync: group.lastSyncAt,
    });
    
    return group;
  } else {
    throw new Error(data.message);
  }
}
```

## Lợi ích

1. **Dữ liệu luôn mới nhất**: Mỗi lần gọi API đều sync từ Zalo
2. **Thông tin đầy đủ**: Bao gồm tất cả thông tin từ Zalo API
3. **Performance tốt**: Lưu cache trong database local
4. **Backward compatibility**: Giữ nguyên cấu trúc metadata cũ
5. **Tracking**: Biết được lần sync cuối cùng qua `lastSyncAt`

## Lưu ý

- **Quyền cần thiết**: OA phải có quyền quản lý thông tin nhóm
- **Rate limiting**: Zalo API có giới hạn số lần gọi
- **Sync frequency**: Nên cache kết quả trong 5-10 phút để tránh gọi API quá nhiều
- **Database size**: Các trường mới sẽ tăng kích thước bảng
