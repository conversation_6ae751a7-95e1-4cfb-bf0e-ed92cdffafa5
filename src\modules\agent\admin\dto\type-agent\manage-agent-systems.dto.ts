import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsString, IsOptional, IsEnum } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * Enum cho các trường sắp xếp của agent systems trong type agent
 */
export enum TypeAgentSystemSortBy {
  NAME_CODE = 'nameCode',
  NAME = 'name',
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho việc thêm agent systems vào type agent
 */
export class AddAgentSystemsDto {
  /**
   * Danh sách ID của các agent systems cần thêm
   */
  @ApiProperty({
    description: 'Danh sách ID của các agent systems cần thêm',
    example: ['agent-system-uuid-1', 'agent-system-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  agentSystemIds: string[];
}

/**
 * DTO cho việc gỡ agent systems khỏi type agent
 */
export class RemoveAgentSystemsDto {
  /**
   * Danh sách ID của các agent systems cần gỡ
   */
  @ApiProperty({
    description: 'Danh sách ID của các agent systems cần gỡ',
    example: ['agent-system-uuid-1', 'agent-system-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  agentSystemIds: string[];
}

/**
 * DTO cho query parameters của API danh sách agent systems
 */
export class TypeAgentSystemsQueryDto extends QueryDto {
  /**
   * Override từ khóa tìm kiếm với mô tả cụ thể hơn
   */
  @ApiPropertyOptional({
    description: 'Từ khóa tìm kiếm theo tên hoặc nameCode của agent system',
    example: 'chatbot',
  })
  @IsOptional()
  @IsString()
  declare search?: string;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: TypeAgentSystemSortBy,
    default: TypeAgentSystemSortBy.NAME_CODE,
  })
  @IsOptional()
  @IsEnum(TypeAgentSystemSortBy)
  sortBy?: TypeAgentSystemSortBy = TypeAgentSystemSortBy.NAME_CODE;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.ASC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.ASC;
}

/**
 * DTO cho thông tin agent system trong type agent
 */
export class TypeAgentSystemItemDto {
  /**
   * ID của agent system
   */
  @ApiProperty({
    description: 'ID của agent system',
    example: 'agent-system-uuid-1',
  })
  id: string;

  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'Customer Support Agent',
  })
  name: string;

  /**
   * Mô tả về agent system
   */
  @ApiProperty({
    description: 'Mô tả về agent system',
    example: 'Agent hỗ trợ khách hàng 24/7',
  })
  description: string;

  /**
   * Trạng thái hoạt động
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động',
    example: true,
  })
  active: boolean;

  /**
   * Avatar URL của agent
   */
  @ApiProperty({
    description: 'Avatar URL của agent',
    example: 'https://cdn.example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string | null;

  /**
   * Model ID được sử dụng
   */
  @ApiProperty({
    description: 'Model ID được sử dụng',
    example: 'gpt-4',
  })
  modelId: string;
}
