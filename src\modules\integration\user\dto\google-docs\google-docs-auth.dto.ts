import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl } from 'class-validator';

/**
 * DTO cho việc tạo URL xác thực OAuth Google Docs
 */
export class GoogleDocsAuthUrlDto {
  /**
   * URL callback sau khi xác thực thành công
   */
  @ApiProperty({
    description: 'URL callback sau khi xác thực thành công',
    example: 'https://app.redai.vn/integration/google-docs/callback',
  })
  @IsString({ message: 'redirectUri phải là chuỗi' })
  @IsUrl({}, { message: 'redirectUri phải là URL hợp lệ' })
  redirectUri: string;

  /**
   * State để bảo mật OAuth flow (tùy chọn)
   */
  @ApiPropertyOptional({
    description: 'State để bảo mật OAuth flow',
    example: 'random-state-string',
  })
  @IsOptional()
  @IsString({ message: 'state phải là chuỗi' })
  state?: string;
}

/**
 * DTO response cho URL xác thực OAuth Google Docs
 */
export class GoogleDocsAuthUrlResponseDto {
  /**
   * URL xác thực OAuth
   */
  @ApiProperty({
    description: 'URL xác thực OAuth',
    example: 'https://accounts.google.com/o/oauth2/v2/auth?client_id=...&redirect_uri=...&scope=...',
  })
  authUrl: string;

  /**
   * State được sử dụng trong OAuth flow
   */
  @ApiProperty({
    description: 'State được sử dụng trong OAuth flow',
    example: 'random-state-string',
  })
  state: string;
}

/**
 * DTO cho việc xử lý OAuth callback từ Google
 */
export class GoogleDocsOAuthCallbackDto {
  /**
   * Authorization code từ Google
   */
  @ApiProperty({
    description: 'Authorization code từ Google',
    example: '4/0AX4XfWjYZ...',
  })
  @IsString({ message: 'code phải là chuỗi' })
  code: string;

  /**
   * State từ OAuth flow
   */
  @ApiPropertyOptional({
    description: 'State từ OAuth flow',
    example: 'random-state-string',
  })
  @IsOptional()
  @IsString({ message: 'state phải là chuỗi' })
  state?: string;

  /**
   * URL redirect được sử dụng trong OAuth flow
   */
  @ApiProperty({
    description: 'URL redirect được sử dụng trong OAuth flow',
    example: 'https://app.redai.vn/integration/google-docs/callback',
  })
  @IsString({ message: 'redirectUri phải là chuỗi' })
  @IsUrl({}, { message: 'redirectUri phải là URL hợp lệ' })
  redirectUri: string;
}

/**
 * DTO cho việc tạo Google Docs integration
 */
export class CreateGoogleDocsIntegrationDto {
  /**
   * Tên của integration
   */
  @ApiProperty({
    description: 'Tên của integration',
    example: 'My Google Docs Integration',
  })
  @IsString({ message: 'integrationName phải là chuỗi' })
  integrationName: string;

  /**
   * Access token từ Google OAuth
   */
  @ApiProperty({
    description: 'Access token từ Google OAuth',
    example: 'ya29.a0AfH6SMC...',
  })
  @IsString({ message: 'accessToken phải là chuỗi' })
  accessToken: string;

  /**
   * Refresh token từ Google OAuth
   */
  @ApiProperty({
    description: 'Refresh token từ Google OAuth',
    example: '1//04...',
  })
  @IsString({ message: 'refreshToken phải là chuỗi' })
  refreshToken: string;

  /**
   * Thời gian hết hạn của access token (timestamp)
   */
  @ApiPropertyOptional({
    description: 'Thời gian hết hạn của access token (timestamp)',
    example: 1640995200000,
  })
  @IsOptional()
  expiresAt?: number;

  /**
   * Scope được cấp quyền
   */
  @ApiPropertyOptional({
    description: 'Scope được cấp quyền',
    example: 'https://www.googleapis.com/auth/documents',
  })
  @IsOptional()
  @IsString({ message: 'scope phải là chuỗi' })
  scope?: string;
}

/**
 * DTO cho việc cập nhật Google Docs integration
 */
export class UpdateGoogleDocsIntegrationDto {
  /**
   * Tên của integration
   */
  @ApiPropertyOptional({
    description: 'Tên của integration',
    example: 'Updated Google Docs Integration',
  })
  @IsOptional()
  @IsString({ message: 'integrationName phải là chuỗi' })
  integrationName?: string;

  /**
   * Access token mới (nếu cần cập nhật)
   */
  @ApiPropertyOptional({
    description: 'Access token mới',
    example: 'ya29.a0AfH6SMC...',
  })
  @IsOptional()
  @IsString({ message: 'accessToken phải là chuỗi' })
  accessToken?: string;

  /**
   * Refresh token mới (nếu cần cập nhật)
   */
  @ApiPropertyOptional({
    description: 'Refresh token mới',
    example: '1//04...',
  })
  @IsOptional()
  @IsString({ message: 'refreshToken phải là chuỗi' })
  refreshToken?: string;

  /**
   * Thời gian hết hạn mới của access token
   */
  @ApiPropertyOptional({
    description: 'Thời gian hết hạn mới của access token (timestamp)',
    example: 1640995200000,
  })
  @IsOptional()
  expiresAt?: number;
}
