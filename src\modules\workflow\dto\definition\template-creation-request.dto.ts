import { IsString, IsOptional, IsA<PERSON>y, IsBoolean, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Template Creation Request DTO
 */
export class TemplateCreationRequestDto {
  @ApiProperty({ description: 'Source workflow ID' })
  @IsString()
  workflowId: string;

  @ApiProperty({ description: 'Template name' })
  @IsString()
  templateName: string;

  @ApiPropertyOptional({ description: 'Template description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Template tags' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Template category' })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ description: 'Is public template' })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ description: 'Template metadata' })
  @IsOptional()
  @IsObject()
  templateMetadata?: {
    author?: string;
    version?: string;
    compatibility?: string[];
    requirements?: string[];
  };
}
