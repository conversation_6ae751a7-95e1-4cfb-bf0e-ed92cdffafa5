import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsObject,
  IsNumber,
  IsUUID,
  IsBoolean,
  Min,
  Max,
  IsNotEmpty,
  ValidateNested,
  IsPositive,
  IsInt,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ExecutionStatus } from '../../constants';

/**
 * DTO cho workflow execution response
 */
export class WorkflowExecutionDto {
  @ApiProperty({
    description: 'ID của execution',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'ID của workflow',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  workflowId: string;

  @ApiProperty({
    description: 'Trạng thái execution',
    enum: ExecutionStatus,
    example: ExecutionStatus.RUNNING,
  })
  @IsEnum(ExecutionStatus)
  status: ExecutionStatus;

  @ApiPropertyOptional({
    description: 'Trigger event data',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  triggerEvent?: Record<string, any> | null;

  @ApiProperty({
    description: 'Thời điểm bắt đầu (Unix timestamp)',
    example: 1640995200000,
  })
  @IsNumber()
  startedAt: number;

  @ApiPropertyOptional({
    description: 'Thời điểm kết thúc (Unix timestamp)',
    example: 1640995260000,
  })
  @IsOptional()
  @IsNumber()
  finishedAt?: number | null;

  @ApiPropertyOptional({
    description: 'Thời gian thực thi (milliseconds)',
    example: 60000,
  })
  @IsOptional()
  @IsNumber()
  duration?: number | null;

  @ApiPropertyOptional({
    description: 'Kết quả execution',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  result?: Record<string, any> | null;

  @ApiPropertyOptional({
    description: 'Thông tin lỗi',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  error?: {
    message: string;
    code?: string;
    stack?: string;
    nodeId?: string;
    timestamp?: number;
  } | null;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any> | null;

  @ApiProperty({
    description: 'Số lần retry đã thực hiện',
    example: 0,
  })
  @IsNumber()
  retryCount: number;

  @ApiProperty({
    description: 'Số lần retry tối đa',
    example: 3,
  })
  @IsNumber()
  maxRetries: number;

  @ApiPropertyOptional({
    description: 'Thời điểm retry tiếp theo',
    example: 1640995320000,
  })
  @IsOptional()
  @IsNumber()
  nextRetryAt?: number | null;

  @ApiPropertyOptional({
    description: 'ID của execution cha (nếu là retry)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  parentExecutionId?: string | null;

  @ApiProperty({
    description: 'Thời điểm tạo',
    example: '2023-01-01T00:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Thời điểm cập nhật',
    example: '2023-01-01T00:01:00Z',
  })
  updatedAt: Date;
}

/**
 * DTO cho tạo workflow execution
 */
export class CreateWorkflowExecutionDto {
  @ApiProperty({
    description: 'ID của workflow',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID(4, { message: 'workflowId must be a valid UUID v4' })
  @IsNotEmpty({ message: 'workflowId is required' })
  workflowId: string;

  @ApiPropertyOptional({
    description: 'Trigger event data',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  triggerEvent?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Số lần retry tối đa',
    example: 3,
    minimum: 0,
    maximum: 10,
  })
  @IsOptional()
  @IsInt({ message: 'maxRetries must be an integer' })
  @Min(0, { message: 'maxRetries must be at least 0' })
  @Max(10, { message: 'maxRetries must not exceed 10' })
  maxRetries?: number;
}

/**
 * DTO cho cập nhật execution status
 */
export class UpdateExecutionStatusDto {
  @ApiProperty({
    description: 'Status mới',
    enum: ExecutionStatus,
    example: ExecutionStatus.COMPLETED,
  })
  @IsEnum(ExecutionStatus)
  status: ExecutionStatus;

  @ApiPropertyOptional({
    description: 'Kết quả execution',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  result?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Thông tin lỗi',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  error?: any;

  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho query executions
 */
export class QueryExecutionsDto {
  @ApiPropertyOptional({
    description: 'ID của workflow để filter',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  workflowId?: string;

  @ApiPropertyOptional({
    description: 'Status để filter',
    enum: ExecutionStatus,
    example: ExecutionStatus.RUNNING,
  })
  @IsOptional()
  @IsEnum(ExecutionStatus)
  status?: ExecutionStatus;

  @ApiPropertyOptional({
    description: 'Số trang (bắt đầu từ 1)',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Số items per page',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Sắp xếp theo field',
    example: 'startedAt',
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'startedAt';

  @ApiPropertyOptional({
    description: 'Thứ tự sắp xếp',
    enum: ['ASC', 'DESC'],
    example: 'DESC',
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * DTO cho execution statistics
 */
export class ExecutionStatisticsDto {
  @ApiProperty({
    description: 'Tổng số executions',
    example: 100,
  })
  @IsNumber()
  total: number;

  @ApiProperty({
    description: 'Số lượng theo status',
    type: 'object',
    additionalProperties: { type: 'number' },
  })
  @IsObject()
  byStatus: Record<ExecutionStatus, number>;

  @ApiPropertyOptional({
    description: 'Thời gian thực thi trung bình (milliseconds)',
    example: 45000,
  })
  @IsOptional()
  @IsNumber()
  avgDuration?: number | null;

  @ApiProperty({
    description: 'Tỷ lệ thành công (%)',
    example: 95.5,
  })
  @IsNumber()
  successRate: number;
}

/**
 * DTO cho cancel execution
 */
export class CancelExecutionDto {
  @ApiPropertyOptional({
    description: 'Lý do hủy execution',
    example: 'User requested cancellation',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}
