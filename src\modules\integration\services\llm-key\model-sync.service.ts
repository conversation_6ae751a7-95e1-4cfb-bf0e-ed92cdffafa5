import { Injectable, Logger } from '@nestjs/common';
import { DataSource, IsNull } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { Models } from '@/modules/models/entities/models.entity';
import { ModelIntegration } from '@/modules/models/entities/model-integration.entity';
import { ModelRegistry } from '@/modules/models/entities/model-registry.entity';
import { ProviderModel } from './provider-model-fetcher.service';
import { ProviderLlmEnum } from '@/modules/models/constants/provider.enum';

/**
 * Service để sync models vào database
 */
@Injectable()
export class ModelSyncService {
  private readonly logger = new Logger(ModelSyncService.name);

  constructor(private readonly dataSource: DataSource) {}

  /**
   * Sync models từ provider vào database (cho lần đầu tạo integration)
   * @param integrationId ID của integration
   * @param provider Provider LLM
   * @param userId ID của user (optional - undefined cho admin integrations)
   * @param providerModels Danh sách models từ provider
   */
  @Transactional()
  async syncModels(
    integrationId: string,
    provider: ProviderLlmEnum,
    userId: number | undefined,
    providerModels: ProviderModel[]
  ): Promise<void> {
    try {
      this.logger.log(`Syncing ${providerModels.length} models for integration ${integrationId}`);

      const modelsRepository = this.dataSource.getRepository(Models);
      const modelIntegrationRepository = this.dataSource.getRepository(ModelIntegration);
      const modelRegistryRepository = this.dataSource.getRepository(ModelRegistry);

      // Lấy model registries theo provider để map với detail_id
      const registries = await modelRegistryRepository.find({
        where: { provider },
        select: ['id', 'modelBaseId']
      });

      const registryMap = new Map(registries.map(r => [r.modelBaseId, r.id]));

      for (const providerModel of providerModels) {
        // Tìm registry ID tương ứng
        const registryId = registryMap.get(
          providerModel.isFineTune ? providerModel.baseModelId! : providerModel.id
        );

        if (!registryId) {
          this.logger.warn(`No registry found for model ${providerModel.id}`);
          continue;
        }

        // Kiểm tra model đã tồn tại chưa
        let existingModel = await modelsRepository.findOne({
          where: {
            modelId: providerModel.id,
            // Fine-tune models thuộc về user cụ thể, base models thuộc hệ thống (userId = null)
            // Nếu userId undefined (admin), fine-tune models vẫn thuộc hệ thống
            userId: providerModel.isFineTune && userId ? userId : IsNull(),
          }
        });

        let modelId: string;

        if (existingModel) {
          // Model đã tồn tại, sử dụng ID hiện có
          modelId = existingModel.id;
          this.logger.debug(`Model ${providerModel.id} already exists with ID ${modelId}`);
        } else {
          // Tạo model mới
          const newModel = modelsRepository.create({
            modelId: providerModel.id,
            modelRegistryId: registryId,
            detailId: null, // Có thể thêm logic tạo detail sau
            active: true,
            // Fine-tune models thuộc về user cụ thể, base models thuộc hệ thống
            // Nếu userId undefined (admin), fine-tune models vẫn thuộc hệ thống
            userId: providerModel.isFineTune && userId ? userId : null,
            isFineTune: providerModel.isFineTune,
          });

          const savedModel = await modelsRepository.save(newModel);
          modelId = savedModel.id;
          
          this.logger.log(`Created new model ${providerModel.id} with ID ${modelId}`);
        }

        // Kiểm tra mapping với integration đã tồn tại chưa
        const existingMapping = await modelIntegrationRepository.findOne({
          where: {
            modelId,
            integrationId,
          }
        });

        if (!existingMapping) {
          // Tạo mapping mới
          const mapping = modelIntegrationRepository.create({
            modelId,
            integrationId,
          });

          await modelIntegrationRepository.save(mapping);
          this.logger.debug(`Created mapping for model ${modelId} and integration ${integrationId}`);
        } else {
          this.logger.debug(`Mapping already exists for model ${modelId} and integration ${integrationId}`);
        }
      }

      this.logger.log(`Successfully synced ${providerModels.length} models for integration ${integrationId}`);
    } catch (error) {
      this.logger.error(`Error syncing models for integration ${integrationId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Re-sync models từ provider vào database (cho re-crawl)
   * @param integrationId ID của integration
   * @param provider Provider LLM
   * @param userId ID của user (optional - undefined cho admin integrations)
   * @param providerModels Danh sách models từ provider
   */
  @Transactional()
  async reSyncModels(
    integrationId: string,
    provider: ProviderLlmEnum,
    userId: number | undefined,
    providerModels: ProviderModel[]
  ): Promise<void> {
    try {
      this.logger.log(`Re-syncing ${providerModels.length} models for integration ${integrationId}`);

      // Bước 1: Xóa các fine-tune models cũ trước
      await this.removeOldFineTuneModels(integrationId, provider, userId);

      // Bước 2: Xóa tất cả mappings cũ
      await this.removeMappings(integrationId);

      // Bước 3: Sync models mới (sử dụng logic sync thông thường)
      await this.syncModels(integrationId, provider, userId, providerModels);

      this.logger.log(`Successfully re-synced models for integration ${integrationId}`);
    } catch (error) {
      this.logger.error(`Error re-syncing models for integration ${integrationId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa mapping của integration (khi xóa key)
   * @param integrationId ID của integration
   */
  @Transactional()
  async removeMappings(integrationId: string): Promise<void> {
    try {
      this.logger.log(`Removing model mappings for integration ${integrationId}`);

      const modelIntegrationRepository = this.dataSource.getRepository(ModelIntegration);

      const result = await modelIntegrationRepository.delete({
        integrationId,
      });

      this.logger.log(`Removed ${result.affected} model mappings for integration ${integrationId}`);
    } catch (error) {
      this.logger.error(`Error removing mappings for integration ${integrationId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa các fine-tune models cũ của integration trước khi re-crawl
   * @param integrationId ID của integration
   * @param provider Provider LLM
   * @param userId ID của user (optional)
   */
  @Transactional()
  async removeOldFineTuneModels(integrationId: string, provider: ProviderLlmEnum, userId?: number): Promise<void> {
    try {
      this.logger.log(`Removing old fine-tune models for integration ${integrationId}, provider ${provider}`);

      const modelsRepository = this.dataSource.getRepository(Models);
      const modelIntegrationRepository = this.dataSource.getRepository(ModelIntegration);

      // Tìm tất cả fine-tune models của integration này
      const fineTuneModels = await modelsRepository
        .createQueryBuilder('m')
        .innerJoin('model_integration', 'mi', 'mi.model_id = m.id')
        .where('mi.integration_id = :integrationId', { integrationId })
        .andWhere('m.is_fine_tune = :isFineTune', { isFineTune: true })
        .andWhere('m.provider = :provider', { provider })
        .select(['m.id'])
        .getMany();

      if (fineTuneModels.length === 0) {
        this.logger.log(`No fine-tune models found for integration ${integrationId}`);
        return;
      }

      const modelIds = fineTuneModels.map(m => m.id);
      this.logger.log(`Found ${modelIds.length} fine-tune models to remove for integration ${integrationId}`);

      // Xóa model mappings trước
      await modelIntegrationRepository
        .createQueryBuilder()
        .delete()
        .where('integration_id = :integrationId', { integrationId })
        .andWhere('model_id IN (:...modelIds)', { modelIds })
        .execute();

      // Xóa các fine-tune models
      const deleteResult = await modelsRepository
        .createQueryBuilder()
        .delete()
        .where('id IN (:...modelIds)', { modelIds })
        .andWhere('is_fine_tune = :isFineTune', { isFineTune: true })
        .andWhere('provider = :provider', { provider })
        .execute();

      this.logger.log(`Removed ${deleteResult.affected || 0} fine-tune models for integration ${integrationId}`);
    } catch (error) {
      this.logger.error(`Error removing old fine-tune models for integration ${integrationId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thống kê models theo integration
   * @param integrationId ID của integration
   */
  async getModelStats(integrationId: string): Promise<{
    totalModels: number;
    baseModels: number;
    fineTuneModels: number;
  }> {
    try {
      const modelIntegrationRepository = this.dataSource.getRepository(ModelIntegration);
      
      const query = modelIntegrationRepository
        .createQueryBuilder('mi')
        .leftJoin('models', 'm', 'm.id = mi.model_id')
        .where('mi.integration_id = :integrationId', { integrationId })
        .select([
          'COUNT(*) as total_models',
          'COUNT(CASE WHEN m.is_fine_tune = false THEN 1 END) as base_models',
          'COUNT(CASE WHEN m.is_fine_tune = true THEN 1 END) as fine_tune_models'
        ]);

      const result = await query.getRawOne();

      return {
        totalModels: parseInt(result.total_models) || 0,
        baseModels: parseInt(result.base_models) || 0,
        fineTuneModels: parseInt(result.fine_tune_models) || 0,
      };
    } catch (error) {
      this.logger.error(`Error getting model stats for integration ${integrationId}: ${error.message}`);
      return {
        totalModels: 0,
        baseModels: 0,
        fineTuneModels: 0,
      };
    }
  }
}
