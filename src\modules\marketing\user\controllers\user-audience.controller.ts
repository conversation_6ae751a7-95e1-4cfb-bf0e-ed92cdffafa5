import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { UserAudienceService } from '../services/user-audience.service';
import { UserAudienceCustomFieldService } from '../services/user-audience-custom-field.service';
import { CreateAudienceDto, UpdateAudienceDto, UpdateAudienceBasicDto, AudienceResponseDto, AudienceQueryDto, AudienceAllQueryDto, MergeUserAudienceDto, MergeUserAudienceResponseDto, UpdateAudienceCustomFieldsDto, UpdateAudienceCustomFieldsResponseDto } from '../dto/audience';
import { CreateAvatarUploadUrlDto, AvatarUploadUrlResponseDto } from '../dto/audience/avatar-upload.dto';
import { PaginatedResponseDto } from '../dto/common';
import { JwtUserGuard } from '@/modules/auth/guards';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { BulkDeleteAudienceDto, BulkDeleteResponseDto } from '@/modules/marketing/common/dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';

/**
 * Controller xử lý tất cả API liên quan đến quản lý audience
 * Bao gồm CRUD, import/export, merge, custom fields và avatar management
 */
@ApiTags(SWAGGER_API_TAGS.USER_AUDIENCE)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/audiences')
export class UserAudienceController {
  constructor(
    private readonly userAudienceService: UserAudienceService,
    private readonly userAudienceCustomFieldService: UserAudienceCustomFieldService,
  ) {}

  /**
   * Tạo audience mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo audience mới',
    description: `Tạo một audience (khách hàng) mới trong hệ thống:
    - Thông tin cơ bản: tên, email, số điện thoại
    - Custom fields tùy chỉnh theo nhu cầu
    - Gán tags để phân loại
    - Upload avatar (tùy chọn)
    - Validation email và phone number unique`
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Audience đã được tạo thành công',
    type: AudienceResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Audience đã được tạo thành công',
        data: {
          id: 1234,
          userId: 123,
          name: 'Nguyễn Văn A',
          email: '<EMAIL>',
          countryCode: 84,
          phoneNumber: '*********',
          avatar: 'customer_avatars/2024/01/**********-uuid.jpg',
          zaloSocialId: null,
          integrationId: null,
          avatarsExternal: null,
          importResource: 'MANUAL',
          zaloOfficialAccountId: null,
          zaloUserIsFollower: null,
          userLastInteractionDate: null,
          customFields: [
            {
              fieldId: 1,
              fieldName: 'Nghề nghiệp',
              value: 'Kỹ sư phần mềm'
            }
          ],
          tags: [
            { id: 1, name: 'VIP Customer', color: '#FF6B6B' },
            { id: 2, name: 'Tech Industry', color: '#4ECDC4' }
          ],
          createdAt: **********,
          updatedAt: **********
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Validation failed',
        errors: [
          'Tên không được để trống',
          'Email không hợp lệ',
          'Số điện thoại không hợp lệ'
        ]
      }
    }
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.AUDIENCE_PHONE_ALREADY_EXISTS,
  )
  async create(@CurrentUser() user: JwtPayload, @Body() createAudienceDto: CreateAudienceDto): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.create(user.id, createAudienceDto);
    return wrapResponse(result, 'Audience đã được tạo thành công');
  }

  
  /**
   * Lấy danh sách audience với phân trang và filter
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách audience với phân trang và filter',
    description: `Lấy danh sách audience với các tùy chọn:
    - Phân trang với page và limit
    - Tìm kiếm theo tên, email, phone
    - Filter theo tags
    - Filter theo custom fields
    - Filter theo nền tảng (platform): ZALO, FACEBOOK, WEB, MANUAL
    - Filter theo Integration ID (UUID)
    - Filter theo việc có số điện thoại hay không
    - Sắp xếp theo các trường khác nhau
    - Export data (CSV, Excel)`
  })
  @ApiQuery({
    name: 'page',
    description: 'Số trang (bắt đầu từ 1)',
    required: false,
    type: Number,
    example: 1
  })
  @ApiQuery({
    name: 'limit',
    description: 'Số lượng items mỗi trang (tối đa 100)',
    required: false,
    type: Number,
    example: 20
  })
  @ApiQuery({
    name: 'search',
    description: 'Tìm kiếm theo tên, email hoặc phone',
    required: false,
    type: String,
    example: 'Nguyễn Văn A'
  })
  @ApiQuery({
    name: 'tagIds',
    description: 'Filter theo tag IDs (cách nhau bởi dấu phẩy)',
    required: false,
    type: String,
    example: '1,2,3'
  })
  @ApiQuery({
    name: 'platform',
    description: 'Filter theo platform',
    required: false,
    enum: ['ZALO', 'FACEBOOK', 'WEB', 'MANUAL'],
    example: 'ZALO'
  })
  @ApiQuery({
    name: 'hasPhoneNumber',
    description: 'Filter theo việc có số điện thoại hay không',
    required: false,
    type: Boolean,
    example: true
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách audience với phân trang',
    type: PaginatedResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Danh sách audience',
        data: {
          items: [
            {
              id: 1234,
              userId: 123,
              name: 'Nguyễn Văn A',
              email: '<EMAIL>',
              countryCode: 84,
              phoneNumber: '*********',
              avatar: 'customer_avatars/2024/01/**********-uuid.jpg',
              zaloSocialId: '**********123456789',
              integrationId: '550e8400-e29b-41d4-a716-************',
              avatarsExternal: ['https://zalo.me/avatar1.jpg'],
              importResource: 'ZALO',
              zaloOfficialAccountId: 456,
              zaloUserIsFollower: true,
              userLastInteractionDate: '2024-06-20',
              tags: [
                { id: 1, name: 'VIP Customer', color: '#FF6B6B' }
              ],
              customFields: [
                {
                  fieldId: 1,
                  fieldName: 'Nghề nghiệp',
                  value: 'Kỹ sư phần mềm'
                }
              ],
              createdAt: **********,
              updatedAt: **********
            }
          ],
          pagination: {
            page: 1,
            limit: 20,
            total: 1250,
            totalPages: 63
          }
        }
      }
    }
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: AudienceQueryDto
  ): Promise<AppApiResponse<PaginatedResponseDto<AudienceResponseDto>>> {
    const result = await this.userAudienceService.findAll(user.id, query);
    return wrapResponse(result, 'Danh sách audience');
  }

  /**
   * Lấy tất cả audience với filter (không phân trang)
   */
  @Get('all')
  @ApiOperation({
    summary: 'Lấy tất cả audience với filter (không phân trang)',
    description: `Lấy tất cả audience với các tùy chọn filter mà không phân trang:
    - Tìm kiếm theo tên, email, phone
    - Filter theo tags, platform, integration
    - **Filter theo segment**: segmentId, segmentIds, excludeSegmentId, excludeSegmentIds
    - Giới hạn số lượng kết quả (mặc định: 1000, tối đa: 10000)
    - Tùy chọn chỉ trả về thông tin cơ bản để tăng tốc độ
    - Sắp xếp theo các trường khác nhau

    **Segment Filtering:**
    - \`segmentId\`: Lấy audience thuộc segment cụ thể
    - \`segmentIds\`: Lấy audience thuộc ít nhất một trong các segment (OR logic)
    - \`excludeSegmentId\`: Loại trừ audience thuộc segment cụ thể
    - \`excludeSegmentIds\`: Loại trừ audience thuộc ít nhất một trong các segment

    **Performance:**
    - \`basicInfo=true\`: Chỉ trả về id, name, email, phone để tăng tốc độ
    - \`limit\`: Giới hạn số lượng kết quả (mặc định 1000, tối đa 10000)`
  })
  @ApiQuery({
    name: 'search',
    description: 'Tìm kiếm theo tên, email hoặc phone',
    required: false,
    type: String,
    example: 'Nguyễn Văn A'
  })
  @ApiQuery({
    name: 'segmentId',
    description: 'Filter theo segment ID cụ thể',
    required: false,
    type: Number,
    example: 123
  })
  @ApiQuery({
    name: 'segmentIds',
    description: 'Filter theo nhiều segment IDs (cách nhau bởi dấu phẩy)',
    required: false,
    type: String,
    example: '123,456,789'
  })
  @ApiQuery({
    name: 'basicInfo',
    description: 'Chỉ trả về thông tin cơ bản để tăng tốc độ',
    required: false,
    type: Boolean,
    example: true
  })
  @ApiQuery({
    name: 'limit',
    description: 'Giới hạn số lượng kết quả (mặc định 1000, tối đa 10000)',
    required: false,
    type: Number,
    example: 1000
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách tất cả audience với filter',
    schema: {
      example: {
        success: true,
        message: 'Danh sách tất cả audience',
        data: [
          {
            id: 1234,
            name: 'Nguyễn Văn A',
            email: '<EMAIL>',
            phone: '+84*********',
            avatar: 'https://cdn.redai.com/customer_avatars/avatar1.jpg',
            tags: [
              { id: 1, name: 'VIP Customer' }
            ],
            customFields: [
              {
                fieldId: 1,
                fieldName: 'Nghề nghiệp',
                value: 'Kỹ sư phần mềm'
              }
            ],
            createdAt: '2024-06-24T15:30:00Z'
          }
        ]
      }
    }
  })
  async findAllAudiences(
    @CurrentUser() user: JwtPayload,
    @Query() query: AudienceAllQueryDto
  ): Promise<AppApiResponse<AudienceResponseDto[]>> {
    const result = await this.userAudienceService.findAllAudiences(user.id, query);
    return wrapResponse(result, 'Danh sách tất cả audience');
  }

  /**
   * Lấy thông tin chi tiết audience theo ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết audience theo ID',
    description: `Lấy thông tin đầy đủ của một audience cụ thể bao gồm:
    - Thông tin cơ bản (tên, email, phone, avatar)
    - Tất cả custom fields và giá trị
    - Danh sách tags được gán
    - Lịch sử tương tác (campaigns đã nhận, email opens, clicks)
    - Thống kê engagement
    - Ngày tạo và cập nhật cuối`
  })
  @ApiParam({
    name: 'id',
    description: 'ID của audience cần lấy thông tin',
    type: 'number',
    example: 1234
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin chi tiết audience',
    type: AudienceResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Thông tin audience',
        data: {
          id: 1234,
          userId: 123,
          name: 'Nguyễn Văn A',
          email: '<EMAIL>',
          countryCode: 84,
          phoneNumber: '*********',
          avatar: 'customer_avatars/2024/01/**********-uuid.jpg',
          zaloSocialId: '**********123456789',
          integrationId: '550e8400-e29b-41d4-a716-************',
          avatarsExternal: ['https://zalo.me/avatar1.jpg', 'https://facebook.com/avatar2.jpg'],
          importResource: 'ZALO',
          zaloOfficialAccountId: 456,
          zaloUserIsFollower: true,
          userLastInteractionDate: '2024-06-20',
          customFields: [
            {
              id: 248,
              audienceId: 1125,
              fieldId: 43,
              fieldName: 'Trường boolean',
              fieldType: 'boolean',
              fieldValue: true,
              configJson: {
                required: true,
                defaultValue: false
              },
              createdAt: *************,
              updatedAt: *************
            },
            {
              id: 249,
              audienceId: 1125,
              fieldId: 68,
              fieldName: 'Tuổi',
              fieldType: 'number',
              fieldValue: 23,
              configJson: {
                required: false,
                min: 0,
                max: 120
              },
              createdAt: *************,
              updatedAt: *************
            }
          ],
          tags: [
            { id: 1, name: 'VIP Customer', color: '#FF6B6B' },
            { id: 2, name: 'Tech Industry', color: '#4ECDC4' }
          ],
          createdAt: **********,
          updatedAt: **********
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Audience không tồn tại',
    schema: {
      example: {
        success: false,
        message: 'Audience với ID 1234 không tồn tại',
        errorCode: 'AUDIENCE_NOT_FOUND'
      }
    }
  })
  async findOne(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.findOne(user.id, +id);
    return wrapResponse(result, 'Thông tin audience');
  }

  /**
   * Cập nhật thông tin audience
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật thông tin audience',
    description: `Cập nhật thông tin audience với các tùy chọn:
    - Cập nhật thông tin cơ bản (tên, email, phone)
    - Cập nhật custom fields (thêm mới, sửa, xóa)
    - Cập nhật tags (gán thêm, bỏ gán)
    - Cập nhật avatar
    - Hỗ trợ partial update (chỉ cập nhật fields được gửi)
    - Validation email và phone unique`
  })
  @ApiParam({
    name: 'id',
    description: 'ID của audience cần cập nhật',
    type: 'number',
    example: 1234
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Audience đã được cập nhật thành công',
    type: AudienceResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Audience đã được cập nhật thành công',
        data: {
          id: 1234,
          name: 'Nguyễn Văn A - Updated',
          email: '<EMAIL>',
          phone: '+84912345679',
          avatar: 'https://cdn.redai.com/customer_avatars/2024/01/updated-avatar.jpg',
          customFields: [
            {
              fieldId: 1,
              fieldName: 'Nghề nghiệp',
              value: 'Senior Software Engineer'
            },
            {
              fieldId: 3,
              fieldName: 'Công ty',
              value: 'RedAI Technology'
            }
          ],
          tags: [
            { id: 1, name: 'VIP Customer' },
            { id: 3, name: 'Enterprise Client' }
          ],
          updatedAt: '2024-06-24T16:45:00Z'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Audience không tồn tại',
    schema: {
      example: {
        success: false,
        message: 'Audience với ID 1234 không tồn tại',
        errorCode: 'AUDIENCE_NOT_FOUND'
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Email đã tồn tại trong hệ thống',
        errorCode: 'AUDIENCE_EMAIL_ALREADY_EXISTS'
      }
    }
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.AUDIENCE_PHONE_ALREADY_EXISTS,
  )
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateAudienceDto: UpdateAudienceDto,
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.update(user.id, +id, updateAudienceDto);
    return wrapResponse(result, 'Audience đã được cập nhật thành công');
  }

  /**
   * Cập nhật cơ bản thông tin audience
   */
  @Put(':id/basic')
  @ApiOperation({
    summary: 'Cập nhật cơ bản thông tin audience',
    description: 'Cập nhật các thông tin cơ bản của audience bao gồm: tên, email, số điện thoại, tag, địa chỉ'
  })
  @ApiResponse({
    status: 200,
    description: 'Audience đã được cập nhật thành công',
    type: AudienceResponseDto
  })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.AUDIENCE_PHONE_ALREADY_EXISTS,
  )
  async updateBasic(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateAudienceBasicDto: UpdateAudienceBasicDto,
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.updateBasic(user.id, +id, updateAudienceBasicDto);
    return wrapResponse(result, 'Thông tin cơ bản của audience đã được cập nhật thành công');
  }

  /**
   * Xóa audience khỏi hệ thống
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa audience khỏi hệ thống',
    description: `Xóa audience và tất cả dữ liệu liên quan:
    - Xóa thông tin cơ bản của audience
    - Xóa tất cả custom field values
    - Xóa các liên kết với tags
    - Xóa khỏi tất cả segments
    - Xóa lịch sử tương tác (campaigns, opens, clicks)
    - Xóa avatar từ storage
    - Thao tác này không thể hoàn tác`
  })
  @ApiParam({
    name: 'id',
    description: 'ID của audience cần xóa',
    type: 'number',
    example: 1234
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Audience đã được xóa thành công',
    schema: {
      example: {
        success: true,
        message: 'Audience đã được xóa thành công',
        data: {
          success: true,
          deletedId: 1234,
          deletedAt: '2024-06-24T16:50:00Z'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Audience không tồn tại',
    schema: {
      example: {
        success: false,
        message: 'Audience với ID 1234 không tồn tại',
        errorCode: 'AUDIENCE_NOT_FOUND'
      }
    }
  })
  async remove(@CurrentUser() user: JwtPayload, @Param('id') id: string): Promise<AppApiResponse<{ success: boolean }>> {
    const result = await this.userAudienceService.remove(user.id, +id);
    return wrapResponse({ success: result }, 'Audience đã được xóa thành công');
  }

  /**
   * Xóa nhiều audience cùng lúc (bulk delete)
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa nhiều audience cùng lúc (bulk delete)',
    description: `Xóa nhiều audience trong một request với các tính năng:
    - Xóa tối đa 100 audience mỗi lần
    - Xử lý parallel để tăng tốc độ
    - Trả về kết quả chi tiết cho từng audience
    - Partial success: một số xóa thành công, một số thất bại
    - Xóa cascade: custom fields, tags, segments, lịch sử
    - Transaction safety: rollback nếu có lỗi nghiêm trọng`
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tất cả audience đã được xóa thành công',
    schema: {
      example: {
        success: true,
        message: 'Đã xóa thành công 5 audience',
        data: {
          totalRequested: 5,
          successCount: 5,
          failureCount: 0,
          results: [
            { id: 1234, success: true, message: 'Xóa thành công' },
            { id: 1235, success: true, message: 'Xóa thành công' },
            { id: 1236, success: true, message: 'Xóa thành công' },
            { id: 1237, success: true, message: 'Xóa thành công' },
            { id: 1238, success: true, message: 'Xóa thành công' }
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.MULTI_STATUS,
    description: 'Một số audience không thể xóa (partial success)',
    schema: {
      example: {
        success: false,
        message: 'Đã xóa thành công 3/5 audience',
        data: {
          totalRequested: 5,
          successCount: 3,
          failureCount: 2,
          results: [
            { id: 1234, success: true, message: 'Xóa thành công' },
            { id: 1235, success: false, message: 'Audience không tồn tại' },
            { id: 1236, success: true, message: 'Xóa thành công' },
            { id: 1237, success: false, message: 'Audience đang được sử dụng trong campaign active' },
            { id: 1238, success: true, message: 'Xóa thành công' }
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Validation failed',
        errors: [
          'Danh sách IDs không được để trống',
          'Tối đa 100 audience mỗi lần xóa',
          'ID phải là số nguyên dương'
        ]
      }
    }
  })
  async bulkDelete(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteAudienceDto
  ): Promise<AppApiResponse<BulkDeleteResponseDto>> {
    const result = await this.userAudienceService.bulkDelete(user.id, bulkDeleteDto.ids);
    return wrapResponse(result, result.message);
  }

  /**
   * Tạo presigned URL để upload avatar cho audience
   */
  @Post(':id/avatar/upload-url')
  @ApiOperation({
    summary: 'Tạo presigned URL để upload avatar cho audience',
    description: `Tạo presigned URL để frontend upload avatar trực tiếp lên S3:
    - Hỗ trợ các format: JPG, PNG, GIF, WebP
    - Kích thước tối đa: 5MB
    - Tự động resize về 300x300px
    - URL có thời hạn 15 phút
    - Sau khi upload thành công, gọi API confirm để cập nhật database
    - Tự động xóa avatar cũ khi upload avatar mới`
  })
  @ApiParam({
    name: 'id',
    description: 'ID của audience cần upload avatar',
    type: 'number',
    example: 1234
  })
  @ApiResponse({
    status: 201,
    description: 'Presigned URL đã được tạo thành công',
    type: AvatarUploadUrlResponseDto,
    schema: {
      example: {
        success: true,
        message: 'URL upload đã được tạo thành công',
        data: {
          uploadUrl: 'https://s3.amazonaws.com/redai-storage/customer_avatars/2024/06/**********-uuid.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=...',
          fileKey: 'customer_avatars/2024/06/**********-uuid.jpg',
          expiresIn: 900,
          maxFileSize: 5242880,
          allowedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
          uploadInstructions: {
            method: 'PUT',
            headers: {
              'Content-Type': 'image/jpeg'
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Audience không tồn tại',
    schema: {
      example: {
        success: false,
        message: 'Audience với ID 1234 không tồn tại',
        errorCode: 'AUDIENCE_NOT_FOUND'
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Validation failed',
        errors: [
          'File type không được hỗ trợ',
          'File size vượt quá giới hạn 5MB'
        ]
      }
    }
  })
  async createAvatarUploadUrl(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() createAvatarUploadUrlDto: CreateAvatarUploadUrlDto
  ): Promise<AppApiResponse<AvatarUploadUrlResponseDto>> {
    const result = await this.userAudienceService.createAvatarUploadUrl(
      user.id,
      +id,
      createAvatarUploadUrlDto
    );
    return wrapResponse(result, 'URL upload avatar đã được tạo thành công');
  }



  /**
   * Xóa avatar của audience
   */
  @Delete(':id/avatar')
  @ApiOperation({ summary: 'Xóa avatar của audience' })
  @ApiResponse({
    status: 200,
    description: 'Avatar đã được xóa thành công',
    type: AudienceResponseDto
  })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async removeAvatar(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.userAudienceService.removeAvatar(user.id, +id);
    return wrapResponse(result, 'Avatar đã được xóa thành công');
  }

  /**
   * Merge nhiều audience thành một audience mới
   */
  @Post('merge')
  @ApiOperation({
    summary: 'Merge nhiều audience thành một audience mới',
    description: 'API này nhận vào danh sách ID audience của user, tạo audience mới với thông tin được merge, và xóa các audience cũ'
  })
  @ApiResponse({
    status: 201,
    description: 'Merge audience thành công',
    type: MergeUserAudienceResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc email đã tồn tại' })
  @ApiResponse({ status: 404, description: 'Một hoặc nhiều audience không tồn tại' })
  @ApiResponse({ status: 500, description: 'Lỗi server khi merge audience' })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND,
  )
  async mergeAudiences(
    @CurrentUser() user: JwtPayload,
    @Body() mergeDto: MergeUserAudienceDto
  ): Promise<AppApiResponse<MergeUserAudienceResponseDto>> {
    const result = await this.userAudienceService.mergeAudiences(user.id, mergeDto);
    return wrapResponse(result, result.message);
  }

  /**
   * Cập nhật hàng loạt các trường tùy chỉnh của audience
   */
  @Put(':id/custom-fields/batch')
  @ApiOperation({
    summary: 'Cập nhật hàng loạt các trường tùy chỉnh của audience',
    description: `Cập nhật các trường tùy chỉnh của audience với nhiều operations trong một request:
    - ADD: Thêm trường tùy chỉnh mới (cần fieldId và fieldValue)
    - UPDATE: Cập nhật giá trị trường tùy chỉnh (cần customFieldId và fieldValue)
    - DELETE: Xóa trường tùy chỉnh (cần customFieldId)

    Có thể thực hiện nhiều operations cùng lúc trong một request.
    Mỗi operation sẽ được xử lý độc lập và trả về kết quả riêng.

    Endpoint này dành cho batch operations. Để thao tác từng custom field riêng lẻ,
    sử dụng endpoints tại /user/marketing/audiences/:audienceId/custom-fields/:id`
  })
  @ApiParam({
    name: 'id',
    description: 'ID của audience',
    example: 1234,
    type: Number,
  })
  @ApiBody({
    description: 'Danh sách các operations cập nhật custom field',
    schema: {
      type: 'object',
      properties: {
        operations: {
          type: 'array',
          description: 'Danh sách các operation cần thực hiện',
          items: {
            type: 'object',
            properties: {
              operation: {
                type: 'string',
                enum: ['add', 'update', 'delete'],
                description: 'Loại operation cần thực hiện',
                example: 'add'
              },
              fieldId: {
                type: 'number',
                description: 'ID của field definition (bắt buộc cho operation ADD)',
                example: 1
              },
              customFieldId: {
                type: 'number',
                description: 'ID của custom field hiện có (bắt buộc cho operation UPDATE và DELETE)',
                example: 123
              },
              fieldValue: {
                description: 'Giá trị của trường tùy chỉnh (bắt buộc cho operation ADD và UPDATE)',
                example: 'Hà Nội'
              }
            },
            required: ['operation']
          }
        }
      },
      required: ['operations'],
      example: {
        operations: [
          {
            operation: 'add',
            fieldId: 1,
            fieldValue: 'Hà Nội'
          },
          {
            operation: 'update',
            customFieldId: 123,
            fieldValue: 'TP.HCM'
          },
          {
            operation: 'delete',
            customFieldId: 456
          }
        ]
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật custom fields thành công',
    schema: {
      example: {
        success: true,
        message: 'Cập nhật trường tùy chỉnh hoàn tất',
        data: {
          audienceId: 1234,
          successCount: 2,
          errorCount: 1,
          results: [
            {
              operation: 'add',
              status: 'success',
              customFieldId: 567,
              fieldId: 1,
              fieldValue: 'Hà Nội',
              message: 'Thêm trường tùy chỉnh thành công'
            },
            {
              operation: 'update',
              status: 'success',
              customFieldId: 123,
              fieldId: 2,
              fieldValue: 'TP.HCM',
              message: 'Cập nhật trường tùy chỉnh thành công'
            },
            {
              operation: 'delete',
              status: 'error',
              customFieldId: 456,
              message: 'Không tìm thấy custom field với ID 456 cho audience này'
            }
          ],
          message: 'Đã thực hiện 3 operations: 2 thành công, 1 thất bại'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Validation failed',
        errors: [
          'fieldId là bắt buộc cho operation ADD',
          'customFieldId là bắt buộc cho operation UPDATE và DELETE',
          'fieldValue là bắt buộc cho operation ADD và UPDATE'
        ]
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Audience không tồn tại',
    schema: {
      example: {
        success: false,
        message: 'Audience với ID 1234 không tồn tại',
        errorCode: 'AUDIENCE_NOT_FOUND'
      }
    }
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
  )
  async updateCustomFields(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateDto: UpdateAudienceCustomFieldsDto,
  ): Promise<AppApiResponse<UpdateAudienceCustomFieldsResponseDto>> {
    const result = await this.userAudienceCustomFieldService.updateCustomFields(
      user.id,
      +id,
      updateDto,
    );
    return wrapResponse(result, 'Cập nhật trường tùy chỉnh hoàn tất');
  }
}
