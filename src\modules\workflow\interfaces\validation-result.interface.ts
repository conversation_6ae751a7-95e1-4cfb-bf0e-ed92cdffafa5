/**
 * Interface cho kết quả validation
 */
export interface ValidationResult {
  /**
   * Trạng thái validation có thành công hay không
   */
  isValid: boolean;

  /**
   * Danh sách lỗi validation (nếu có)
   */
  errors: ValidationError[];
}

/**
 * Interface cho validation error
 */
export interface ValidationError {
  /**
   * Đường dẫn đến field bị lỗi
   */
  path: string;

  /**
   * Thông báo lỗi
   */
  message: string;

  /**
   * Giá trị gây ra lỗi
   */
  value?: any;
}
