# Mã Hóa Dữ Liệu Nhạy Cảm Trong Rule Contract

## 📋 Tổng Quan

Tài liệu này mô tả cơ chế mã hóa các trường dữ liệu nhạy cảm trong `RuleContractContext`, bao gồm:
- `cccd`: Số CCCD/CMND
- `issuePlace`: <PERSON><PERSON><PERSON> cấp CCCD
- `issueDate`: <PERSON><PERSON>y cấp CCCD

## 🔧 Cấu Trúc Triển Khai

### 1. **Database Schema**
```sql
-- Thêm trường lưu public key
ALTER TABLE rule_contract_states 
ADD COLUMN context_data_public_key TEXT NULL 
COMMENT 'Public key để mã hóa/giải mã các trường nhạy cảm trong contextData';
```

### 2. **Entity Update**
```typescript
// RuleContractStateEntity
@Column({
  name: 'context_data_public_key',
  type: 'text',
  nullable: true,
  comment: 'Public key để mã hóa/giải mã các trường nhạy cảm trong contextData'
})
contextDataPublicKey: string | null;
```

### 3. **Encryption Service**
```typescript
// RuleContractContextEncryptionService
export class RuleContractContextEncryptionService {
  // Mã hóa các trường nhạy cảm
  encryptSensitiveFields(contextData, publicKey?): { encryptedContextData, publicKey }
  
  // Giải mã các trường nhạy cảm  
  decryptSensitiveFields(contextData, publicKey): RuleContractContext
  
  // Kiểm tra dữ liệu đã mã hóa
  isContextDataEncrypted(contextData): boolean
}
```

## 🚀 Cách Hoạt Động

### **Khi Lưu Dữ Liệu (Encryption)**
```typescript
// 1. Tách các trường nhạy cảm
const sensitiveData = {
  cccd: individualData.cccd,
  issuePlace: individualData.issuePlace, 
  issueDate: individualData.issueDate
};

// 2. Mã hóa tất cả cùng lúc
const { encryptedData, publicKey } = keyPairEncryption.encrypt(sensitiveData);

// 3. Lưu vào database
individualData.cccd = encryptedData;           // Chứa tất cả dữ liệu đã mã hóa
individualData.issuePlace = '[ENCRYPTED]';     // Đánh dấu đã mã hóa
individualData.issueDate = '[ENCRYPTED]';      // Đánh dấu đã mã hóa

// 4. Lưu public key riêng
entity.contextDataPublicKey = publicKey;
```

### **Khi Đọc Dữ Liệu (Decryption)**
```typescript
// 1. Kiểm tra dữ liệu đã mã hóa
if (issuePlace === '[ENCRYPTED]' && issueDate === '[ENCRYPTED]') {
  
  // 2. Giải mã từ trường cccd
  const decryptedData = keyPairEncryption.decrypt(cccd, publicKey);
  const sensitiveData = JSON.parse(decryptedData);
  
  // 3. Khôi phục các trường gốc
  individualData.cccd = sensitiveData.cccd;
  individualData.issuePlace = sensitiveData.issuePlace;
  individualData.issueDate = sensitiveData.issueDate;
}
```

## 🔐 Bảo Mật

### **Encryption Strategy**
- **Algorithm**: AES-256-CBC + HMAC-SHA256
- **Key Management**: Public key per record + Private key in environment
- **Data Integrity**: HMAC verification
- **Fallback**: Trả về dữ liệu gốc nếu giải mã thất bại

### **Key Storage**
```typescript
// Public key: Lưu trong database per record
entity.contextDataPublicKey = "abc123def456...";

// Private key: Environment variable
KEY_PAIR_PRIVATE_KEY=xyz789ghi012...
```

## 📊 Cấu Trúc Dữ Liệu

### **Trước Khi Mã Hóa**
```json
{
  "individualData": {
    "name": "Nguyễn Văn A",
    "cccd": "123456789012",
    "issuePlace": "Công an TP.HCM", 
    "issueDate": "2020-01-15",
    "address": "123 Đường ABC"
  }
}
```

### **Sau Khi Mã Hóa**
```json
{
  "individualData": {
    "name": "Nguyễn Văn A",
    "cccd": "YIBYsn6w5/orexeuNx5PhJ9oXElIqn3H3DKyrv2Iloc=",
    "issuePlace": "[ENCRYPTED]",
    "issueDate": "[ENCRYPTED]", 
    "address": "123 Đường ABC"
  }
}
```

### **Database Record**
```json
{
  "context_data": { /* JSON đã mã hóa */ },
  "context_data_public_key": "abc123def456..."
}
```

## 🧪 Testing

### **1. Test Encryption**
```typescript
const service = new RuleContractContextEncryptionService(keyPairEncryption);

const contextData = {
  individualData: {
    cccd: "123456789012",
    issuePlace: "Công an TP.HCM",
    issueDate: "2020-01-15"
  }
};

const { encryptedContextData, publicKey } = service.encryptSensitiveFields(contextData);

// Verify encryption
expect(encryptedContextData.individualData.issuePlace).toBe('[ENCRYPTED]');
expect(encryptedContextData.individualData.issueDate).toBe('[ENCRYPTED]');
expect(encryptedContextData.individualData.cccd).not.toBe("123456789012");
```

### **2. Test Decryption**
```typescript
const decryptedContextData = service.decryptSensitiveFields(encryptedContextData, publicKey);

// Verify decryption
expect(decryptedContextData.individualData.cccd).toBe("123456789012");
expect(decryptedContextData.individualData.issuePlace).toBe("Công an TP.HCM");
expect(decryptedContextData.individualData.issueDate).toBe("2020-01-15");
```

## 🔄 Migration Guide

### **1. Chạy SQL Migration**
```sql
-- Thêm cột mới
ALTER TABLE rule_contract_states 
ADD COLUMN context_data_public_key TEXT NULL;
```

### **2. Deploy Code**
- Deploy service và repository mới
- Dữ liệu cũ vẫn hoạt động bình thường (backward compatible)

### **3. Kiểm Tra**
```sql
-- Xem dữ liệu đã mã hóa
SELECT user_id, context_data_public_key, 
       JSON_EXTRACT(context_data, '$.individualData.issuePlace') as issue_place
FROM rule_contract_states 
WHERE context_data_public_key IS NOT NULL;
```

## ⚠️ Lưu Ý Quan Trọng

1. **Backward Compatibility**: Dữ liệu cũ không có `contextDataPublicKey` vẫn hoạt động bình thường
2. **Fallback Strategy**: Nếu giải mã thất bại, trả về dữ liệu gốc
3. **Performance**: Mã hóa/giải mã chỉ áp dụng cho dữ liệu nhạy cảm
4. **Key Management**: Private key phải được bảo mật tuyệt đối trong environment variables

## 🔍 API Endpoints Affected

Các API sau sẽ tự động mã hóa/giải mã dữ liệu nhạy cảm:

- `POST /api/rule-contract/xstate/events` - Submit individual data
- `GET /api/rule-contract/xstate/status` - Get contract status
- `GET /api/rule-contract/xstate/context` - Get context data
- `PUT /api/rule-contract/xstate/reset` - Reset contract state

## 🛠️ Troubleshooting

### **Lỗi Giải Mã**
```typescript
// Log sẽ hiển thị
"Lỗi khi giải mã contextData: Invalid encrypted data format"

// Giải pháp: Kiểm tra public key và dữ liệu đã mã hóa
```

### **Dữ liệu Không Được Mã Hóa**
```typescript
// Kiểm tra service đã được inject
if (!this.contextEncryptionService) {
  throw new Error('RuleContractContextEncryptionService not injected');
}
```
