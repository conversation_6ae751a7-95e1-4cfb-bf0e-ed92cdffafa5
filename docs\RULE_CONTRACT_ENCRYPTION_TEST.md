# Test Mã Hóa Ảnh Rule Contract

## 📋 Tổng Quan

Hướng dẫn test cơ chế mã hóa ảnh cho Rule Contract sau khi đã sửa lỗi.

## 🔧 Các Lỗi Đã Sửa

### 1. <PERSON><PERSON>u Trúc ContextData
**Vấn đề**: `RuleContractContext` không có các field URL ảnh trực tiếp trong `individualData` và `businessData`.

**Giải pháp**: L<PERSON><PERSON> tất cả URLs ảnh trực tiếp vào `contextData` để có thể mã hóa:
```typescript
// Thay vì lưu trong sub-objects
contextData.individualData.citizenIdFrontUrl = url; // ❌ Không có field này

// Lưu trực tiếp vào contextData
contextData.citizenIdFrontUrl = url; // ✅ Có thể mã hóa
```

### 2. <PERSON><PERSON><PERSON> <PERSON>ì<PERSON> M<PERSON> Hóa
**Cập nhật**: Mã hóa tất cả URLs quan trọng:
```typescript
protected getEncryptionConfig(): EncryptionFieldConfig[] {
  return [
    // Individual contract images
    { urlField: 'citizenIdFrontUrl', publicKeyField: 'citizenIdFrontUrl_public_key', jsonPath: 'contextData' },
    { urlField: 'citizenIdBackUrl', publicKeyField: 'citizenIdBackUrl_public_key', jsonPath: 'contextData' },
    
    // Business contract images  
    { urlField: 'businessLicenseUrl', publicKeyField: 'businessLicenseUrl_public_key', jsonPath: 'contextData' },
    { urlField: 'representativeIdFrontUrl', publicKeyField: 'representativeIdFrontUrl_public_key', jsonPath: 'contextData' },
    { urlField: 'representativeIdBackUrl', publicKeyField: 'representativeIdBackUrl_public_key', jsonPath: 'contextData' },
    
    // Signature & Contract
    { urlField: 'signatureData', publicKeyField: 'signatureData_public_key', jsonPath: 'contextData' },
    { urlField: 'contractUrl', publicKeyField: 'contractUrl_public_key', jsonPath: 'contextData' },
    { urlField: 'signedContractUrl', publicKeyField: 'signedContractUrl_public_key', jsonPath: 'contextData' },
  ];
}
```

### 3. Repository Methods
**Cập nhật**: Tất cả methods lưu URLs trực tiếp vào contextData:
```typescript
// Cập nhật CCCD URLs
state.contextData = {
  ...state.contextData,
  citizenIdFrontUrl,
  citizenIdBackUrl,
} as any;

// Cập nhật business license URL
state.contextData = {
  ...state.contextData,
  businessLicenseUrl,
} as any;
```

## 🧪 Test Cases

### 1. Test Cấu Hình Mã Hóa
```bash
# Kiểm tra trạng thái mã hóa
GET /api/rule-contract/xstate/encryption-status

# Expected Response:
{
  "code": 200,
  "result": {
    "encryptionEnabled": true,
    "configValid": true,
    "configErrors": [],
    "imageUrls": {
      "citizenIdFrontUrl": null,
      "citizenIdBackUrl": null,
      "signatureImageUrl": null,
      "businessLicenseUrl": null,
      "representativeIdFrontUrl": null,
      "representativeIdBackUrl": null,
      "contractUrl": null,
      "signedContractUrl": null
    }
  }
}
```

### 2. Test Individual Contract Flow
```bash
# 1. Chọn loại hợp đồng cá nhân
POST /api/rule-contract/xstate/select-contract-type
{
  "contractType": "INDIVIDUAL"
}

# 2. Gửi thông tin cá nhân (với URLs ảnh)
POST /api/rule-contract/xstate/submit-individual-info
{
  "name": "Nguyễn Văn A",
  "address": "123 ABC Street",
  "phone": "**********",
  "dateOfBirth": "1990-01-01",
  "cccd": "**********12",
  "issuePlace": "TP.HCM",
  "issueDate": "2020-01-01",
  "citizenIdFrontUrl": "https://example.com/front.jpg",
  "citizenIdBackUrl": "https://example.com/back.jpg"
}

# 3. Kiểm tra URLs đã được mã hóa
GET /api/rule-contract/xstate/status
# Response sẽ có URLs đã giải mã
```

### 3. Test Business Contract Flow
```bash
# 1. Chọn loại hợp đồng doanh nghiệp
POST /api/rule-contract/xstate/select-contract-type
{
  "contractType": "BUSINESS"
}

# 2. Gửi thông tin doanh nghiệp (với URLs ảnh)
POST /api/rule-contract/xstate/submit-business-info
{
  "businessName": "Công ty ABC",
  "representativeName": "Nguyễn Văn B",
  "representativePosition": "Giám đốc",
  "businessEmail": "<EMAIL>",
  "businessPhone": "**********",
  "businessAddress": "456 XYZ Street",
  "taxCode": "**********",
  "businessLicenseUrl": "https://example.com/license.jpg",
  "representativeIdFrontUrl": "https://example.com/rep-front.jpg",
  "representativeIdBackUrl": "https://example.com/rep-back.jpg"
}
```

### 4. Test Signature Flow
```bash
# 1. Hoàn thành chữ ký
POST /api/rule-contract/xstate/signature-completed
{
  "signatureData": "https://example.com/signature.jpg"
}

# 2. Kiểm tra signature đã được mã hóa
GET /api/rule-contract/xstate/encryption-status
```

## 🔍 Kiểm Tra Database

### 1. Xem ContextData Raw
```sql
SELECT 
  user_id,
  current_state,
  context_data
FROM rule_contract_states 
WHERE user_id = 123;
```

**Expected**: contextData sẽ chứa encrypted storage keys và public keys:
```json
{
  "userId": 123,
  "contractType": "INDIVIDUAL",
  "citizenIdFrontUrl": "encrypted/123/**********-encrypted.jpg",
  "citizenIdFrontUrl_public_key": "abc123def456...",
  "citizenIdBackUrl": "encrypted/123/**********-encrypted.jpg", 
  "citizenIdBackUrl_public_key": "def456ghi789...",
  "signatureData": "encrypted/123/1234567892-encrypted.jpg",
  "signatureData_public_key": "ghi789jkl012..."
}
```

### 2. Kiểm Tra S3 Storage
```bash
# Kiểm tra file đã được upload và mã hóa
aws s3 ls s3://your-bucket/encrypted/123/
```

**Expected**: Sẽ thấy các file encrypted:
```
**********-encrypted.jpg
**********-encrypted.jpg  
1234567892-encrypted.jpg
```

## ⚠️ Lưu Ý Test

### 1. Environment Setup
```env
# Bật mã hóa
ENCRYPTION_ENABLED=true

# Đảm bảo có đủ config
KEY_PAIR_PRIVATE_KEY=your_32_character_key
CF_R2_ACCESS_KEY=your_access_key
CF_R2_SECRET_KEY=your_secret_key
CDN_URL=your_cdn_url
CDN_SECRET_KEY=your_cdn_secret
```

### 2. Test Data
- Sử dụng URLs ảnh thật để test mã hóa
- Không sử dụng URLs đã có signature (CDN URLs)
- Test với cả individual và business contract

### 3. Fallback Testing
```env
# Test với mã hóa tắt
ENCRYPTION_ENABLED=false
```
**Expected**: URLs sẽ được lưu trực tiếp, không mã hóa

### 4. Error Handling
- Test với URLs không hợp lệ
- Test với key mã hóa sai
- Test với S3 không khả dụng

## 📊 Validation Checklist

### ✅ Cấu Hình
- [ ] `ENCRYPTION_ENABLED=true`
- [ ] Tất cả environment variables đã set
- [ ] `/encryption-status` API trả về `configValid: true`

### ✅ Individual Contract
- [ ] URLs CCCD được mã hóa khi save
- [ ] URLs CCCD được giải mã khi load
- [ ] Response API có URLs đã giải mã
- [ ] Database có encrypted storage keys

### ✅ Business Contract  
- [ ] Business license URL được mã hóa
- [ ] Representative ID URLs được mã hóa
- [ ] Tất cả URLs được giải mã trong response

### ✅ Signature & Contract
- [ ] Signature image được mã hóa
- [ ] Contract URLs được mã hóa
- [ ] Signed contract URLs được mã hóa

### ✅ Error Handling
- [ ] Fallback khi mã hóa thất bại
- [ ] Fallback khi giải mã thất bại
- [ ] Logging chi tiết cho debug

## 🚀 Next Steps

1. **Run Tests**: Chạy tất cả test cases trên
2. **Monitor Performance**: Theo dõi performance impact
3. **Check Logs**: Kiểm tra logs để đảm bảo không có lỗi
4. **Production Ready**: Sẵn sàng deploy lên production
