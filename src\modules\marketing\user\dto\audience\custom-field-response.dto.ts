import { ApiProperty } from '@nestjs/swagger';
import { CustomFieldDataType } from '@modules/marketing/common/enums/custom-field-data-type.enum';

/**
 * DTO cho phản hồi thông tin trường tùy chỉnh
 */
export class CustomFieldResponseDto {
  /**
   * ID của trường tùy chỉnh
   * @example 1
   */
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  id: number;

  /**
   * ID của audience
   * @example 1
   */
  @ApiProperty({
    description: 'ID của audience',
    example: 1,
  })
  audienceId: number;

  /**
   * ID tham chiếu đến định nghĩa trường tùy chỉnh
   * @example 1
   */
  @ApiProperty({
    description: 'ID tham chiếu đến định nghĩa trường tùy chỉnh',
    example: 1,
  })
  fieldId: number;

  /**
   * Tên hiển thị của trường tùy chỉnh từ definition
   * @example "Địa chỉ khách hàng"
   */
  @ApiProperty({
    description: 'Tên hiển thị của trường tùy chỉnh từ definition',
    example: 'Địa chỉ khách hàng',
    nullable: true,
  })
  fieldName?: string;

  /**
   * Kiểu dữ liệu của trường tùy chỉnh từ definition
   * @example "text"
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu của trường tùy chỉnh từ definition',
    example: 'text',
    enum: CustomFieldDataType,
    nullable: true,
  })
  fieldType?: CustomFieldDataType;

  /**
   * Giá trị của trường tùy chỉnh
   * @example "Hà Nội, Việt Nam"
   */
  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    example: 'Hà Nội, Việt Nam',
  })
  fieldValue: any;

  /**
   * Cấu hình JSON của trường tùy chỉnh từ definition
   * @example {
   *   "id": "address_field",
   *   "type": "select",
   *   "label": "Địa chỉ",
   *   "options": [
   *     {"label": "Hà Nội", "value": "hanoi"},
   *     {"label": "TP.HCM", "value": "hcm"}
   *   ],
   *   "validation": {
   *     "options": [
   *       {"label": "Hà Nội", "value": "hanoi"},
   *       {"label": "TP.HCM", "value": "hcm"}
   *     ]
   *   },
   *   "displayName": "Địa chỉ"
   * }
   */
  @ApiProperty({
    description: 'Cấu hình JSON của trường tùy chỉnh từ definition',
    example: {
      id: "address_field",
      type: "select",
      label: "Địa chỉ",
      options: [
        {"label": "Hà Nội", "value": "hanoi"},
        {"label": "TP.HCM", "value": "hcm"}
      ],
      validation: {
        options: [
          {"label": "Hà Nội", "value": "hanoi"},
          {"label": "TP.HCM", "value": "hcm"}
        ]
      },
      displayName: "Địa chỉ"
    },
    nullable: true,
  })
  configJson?: any;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
