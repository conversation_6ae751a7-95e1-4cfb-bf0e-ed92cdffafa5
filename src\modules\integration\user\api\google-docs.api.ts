import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { AppException } from '@/common/exceptions/app.exception';
import { INTEGRATION_ERROR_CODES } from '../../exceptions/integration-error.code';
import { Request } from '@/shared/services/google/interfaces/google-docs.interface';
import {
  GoogleDocumentsListQueryDto,
  GoogleDocumentInfoQueryDto,
  GoogleDocumentReadQueryDto,
  GoogleDocumentCreateDto,
  GoogleDocumentUpdateDto,
  GoogleDocumentsListResponseDto,
  GoogleDocumentInfoResponseDto,
  GoogleDocumentContentResponseDto,
  GoogleDocumentCreateResponseDto,
} from '../dto/google-docs';

/**
 * Service xử lý các API calls đến Google Docs API
 */
@Injectable()
export class GoogleDocsApi {
  private readonly logger = new Logger(GoogleDocsApi.name);
  private readonly baseUrl = 'https://docs.googleapis.com/v1';
  private readonly driveBaseUrl = 'https://www.googleapis.com/drive/v3';

  constructor(private readonly httpService: HttpService) {}

  /**
   * Lấy danh sách documents từ Google Drive
   */
  async getDocumentsList(
    accessToken: string,
    query: GoogleDocumentsListQueryDto,
  ): Promise<GoogleDocumentsListResponseDto> {
    try {
      this.logger.log('Fetching documents list from Google Drive');

      // Tạo query string cho Google Drive API
      const params = new URLSearchParams({
        q: `mimeType='application/vnd.google-apps.document'${query.search ? ` and name contains '${query.search}'` : ''}`,
        pageSize: query.pageSize?.toString() || '10',
        orderBy: `${query.orderBy} ${query.sortOrder}`,
        fields: 'nextPageToken,files(id,name,webViewLink,createdTime,modifiedTime,owners,lastModifyingUser)',
      });

      if (query.pageToken) {
        params.append('pageToken', query.pageToken);
      }

      const response = await firstValueFrom(
        this.httpService.get(`${this.driveBaseUrl}/files?${params.toString()}`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }),
      );

      const files = response.data.files || [];
      const documents: GoogleDocumentInfoResponseDto[] = files.map((file: any) => ({
        documentId: file.id,
        title: file.name,
        webViewLink: file.webViewLink,
        createdTime: file.createdTime,
        modifiedTime: file.modifiedTime,
        createdBy: file.owners?.[0] ? {
          displayName: file.owners[0].displayName,
          emailAddress: file.owners[0].emailAddress,
        } : undefined,
        lastModifiedBy: file.lastModifyingUser ? {
          displayName: file.lastModifyingUser.displayName,
          emailAddress: file.lastModifyingUser.emailAddress,
        } : undefined,
      }));

      return {
        documents,
        nextPageToken: response.data.nextPageToken,
      };
    } catch (error) {
      this.logger.error(`Error fetching documents list: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.GOOGLE_DOCS_API_ERROR,
        `Lỗi khi lấy danh sách documents: ${error.response?.data?.error?.message || error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của một document
   */
  async getDocumentInfo(
    accessToken: string,
    query: GoogleDocumentInfoQueryDto,
  ): Promise<GoogleDocumentInfoResponseDto> {
    try {
      this.logger.log(`Fetching document info for: ${query.documentId}`);

      // Lấy thông tin từ Drive API
      const driveResponse = await firstValueFrom(
        this.httpService.get(`${this.driveBaseUrl}/files/${query.documentId}`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
          params: {
            fields: 'id,name,webViewLink,createdTime,modifiedTime,owners,lastModifyingUser',
          },
        }),
      );

      const file = driveResponse.data;

      return {
        documentId: file.id,
        title: file.name,
        webViewLink: file.webViewLink,
        createdTime: file.createdTime,
        modifiedTime: file.modifiedTime,
        createdBy: file.owners?.[0] ? {
          displayName: file.owners[0].displayName,
          emailAddress: file.owners[0].emailAddress,
        } : undefined,
        lastModifiedBy: file.lastModifyingUser ? {
          displayName: file.lastModifyingUser.displayName,
          emailAddress: file.lastModifyingUser.emailAddress,
        } : undefined,
      };
    } catch (error) {
      this.logger.error(`Error fetching document info: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.GOOGLE_DOCS_API_ERROR,
        `Lỗi khi lấy thông tin document: ${error.response?.data?.error?.message || error.message}`,
      );
    }
  }

  /**
   * Đọc nội dung của document
   */
  async readDocumentContent(
    accessToken: string,
    query: GoogleDocumentReadQueryDto,
  ): Promise<GoogleDocumentContentResponseDto> {
    try {
      this.logger.log(`Reading document content for: ${query.documentId}`);

      // Lấy nội dung từ Docs API
      const docsResponse = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/documents/${query.documentId}`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }),
      );

      const document = docsResponse.data;
      
      // Extract text content từ document structure
      let content = '';
      let wordCount = 0;
      let characterCount = 0;

      if (document.body && document.body.content) {
        content = this.extractTextFromContent(document.body.content, query.format || 'text');
        wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
        characterCount = content.length;
      }

      return {
        documentId: document.documentId,
        title: document.title,
        content,
        wordCount,
        characterCount,
      };
    } catch (error) {
      this.logger.error(`Error reading document content: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.GOOGLE_DOCS_API_ERROR,
        `Lỗi khi đọc nội dung document: ${error.response?.data?.error?.message || error.message}`,
      );
    }
  }

  /**
   * Tạo document mới
   */
  async createDocument(
    accessToken: string,
    createDto: GoogleDocumentCreateDto,
  ): Promise<GoogleDocumentCreateResponseDto> {
    try {
      this.logger.log(`Creating new document: ${createDto.title}`);

      // Tạo document mới
      const createResponse = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/documents`,
          {
            title: createDto.title,
          },
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      const document = createResponse.data;

      // Nếu có nội dung ban đầu, thêm vào document
      if (createDto.content) {
        await this.updateDocumentContent(accessToken, {
          documentId: document.documentId,
          content: createDto.content,
          insertIndex: 1,
          replaceAll: false,
        });
      }

      // Nếu có folderId, di chuyển document vào folder
      if (createDto.folderId) {
        await this.moveDocumentToFolder(accessToken, document.documentId, createDto.folderId);
      }

      return {
        documentId: document.documentId,
        title: document.title,
        webViewLink: `https://docs.google.com/document/d/${document.documentId}/edit`,
        createdTime: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Error creating document: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.GOOGLE_DOCS_API_ERROR,
        `Lỗi khi tạo document: ${error.response?.data?.error?.message || error.message}`,
      );
    }
  }

  /**
   * Cập nhật nội dung document
   */
  async updateDocumentContent(
    accessToken: string,
    updateDto: GoogleDocumentUpdateDto,
  ): Promise<{ success: boolean; updatedRange?: string }> {
    try {
      this.logger.log(`Updating document content: ${updateDto.documentId}`);

      const requests: Request[] = [];

      if (updateDto.replaceAll) {
        // Xóa toàn bộ nội dung hiện tại
        requests.push({
          deleteContentRange: {
            range: {
              startIndex: 1,
              endIndex: -1, // Đến cuối document
            },
          },
        });
      }

      // Thêm nội dung mới
      requests.push({
        insertText: {
          location: {
            index: updateDto.insertIndex || 1,
          },
          text: updateDto.content,
        },
      });

      await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/documents/${updateDto.documentId}:batchUpdate`,
          {
            requests,
          },
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      return { success: true };
    } catch (error) {
      this.logger.error(`Error updating document content: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.GOOGLE_DOCS_API_ERROR,
        `Lỗi khi cập nhật nội dung document: ${error.response?.data?.error?.message || error.message}`,
      );
    }
  }

  /**
   * Di chuyển document vào folder
   */
  private async moveDocumentToFolder(
    accessToken: string,
    documentId: string,
    folderId: string,
  ): Promise<void> {
    try {
      await firstValueFrom(
        this.httpService.patch(
          `${this.driveBaseUrl}/files/${documentId}`,
          {},
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
            params: {
              addParents: folderId,
            },
          },
        ),
      );
    } catch (error) {
      this.logger.warn(`Could not move document to folder: ${error.message}`);
      // Không throw error vì document đã được tạo thành công
    }
  }

  /**
   * Extract text content từ Google Docs content structure
   */
  private extractTextFromContent(content: any[], format: 'text' | 'html' | 'markdown' = 'text'): string {
    let text = '';

    for (const element of content) {
      if (element.paragraph) {
        const paragraphText = this.extractTextFromParagraph(element.paragraph, format);
        if (paragraphText) {
          text += paragraphText + '\n';
        }
      } else if (element.table) {
        // Handle table content if needed
        text += this.extractTextFromTable(element.table, format);
      }
    }

    return text.trim();
  }

  /**
   * Extract text từ paragraph
   */
  private extractTextFromParagraph(paragraph: any, format: 'text' | 'html' | 'markdown'): string {
    if (!paragraph.elements) return '';

    let text = '';
    for (const element of paragraph.elements) {
      if (element.textRun && element.textRun.content) {
        text += element.textRun.content;
      }
    }

    return text;
  }

  /**
   * Extract text từ table
   */
  private extractTextFromTable(table: any, format: 'text' | 'html' | 'markdown'): string {
    let text = '';
    
    if (table.tableRows) {
      for (const row of table.tableRows) {
        if (row.tableCells) {
          const cellTexts = row.tableCells.map((cell: any) => {
            if (cell.content) {
              return this.extractTextFromContent(cell.content, format);
            }
            return '';
          });
          text += cellTexts.join('\t') + '\n';
        }
      }
    }

    return text;
  }
}
