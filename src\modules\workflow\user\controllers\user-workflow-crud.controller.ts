import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Param, 
  Patch, 
  Delete, 
  Query, 
  UseGuards,
  HttpStatus
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiBearerAuth, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery,
  ApiExtraModels
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@modules/auth/interfaces';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { WORKFLOW_ERROR_CODES } from '../../constants/workflow-error-codes';
import { WorkflowService } from '../../services/workflow.service';
import { 
  CreateWorkflowDto, 
  UpdateWorkflowDto, 
  QueryWorkflowDto, 
  WorkflowResponseDto 
} from '../../dto/workflow';

/**
 * User controller for workflow CRUD operations
 * Following existing user controller patterns from the codebase
 */
@ApiTags(SWAGGER_API_TAGS.USER_WORKFLOW)
@Controller('api/v1/user/workflows')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, WorkflowResponseDto, PaginatedResult)
export class UserWorkflowCrudController {
  constructor(
    private readonly workflowService: WorkflowService,
  ) {}

  /**
   * Get user's workflows with pagination and filtering
   */
  @Get()
  @ApiOperation({ summary: 'Get user workflows with pagination' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (1-based)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page', example: 10 })
  @ApiQuery({ name: 'search', required: false, description: 'Search by workflow name', example: 'customer' })
  @ApiQuery({ name: 'isActive', required: false, description: 'Filter by active status', example: true })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field', example: 'createdAt' })
  @ApiQuery({ name: 'sortDirection', required: false, description: 'Sort direction', example: 'DESC' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User workflows retrieved successfully',
    schema: ApiResponseDto.getPaginatedSchema(WorkflowResponseDto)
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.DATA_FETCH_ERROR
  )
  async findAll(
    @Query() queryDto: QueryWorkflowDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<PaginatedResult<WorkflowResponseDto>>> {
    const result = await this.workflowService.findAll(queryDto, undefined, userId);
    return ApiResponseDto.paginated(result, 'User workflows retrieved successfully');
  }

  /**
   * Get user's workflow details by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get user workflow details by ID' })
  @ApiParam({ name: 'id', description: 'Workflow ID', example: '123e4567-e89b-12d3-a456-426614174000' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow details retrieved successfully',
    schema: ApiResponseDto.getSchema(WorkflowResponseDto)
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.NOT_FOUND,
    WORKFLOW_ERROR_CODES.DATA_FETCH_ERROR
  )
  async findOne(
    @Param('id') id: string,
    @CurrentUser() user: JWTPayload
  ): Promise<ApiResponseDto<WorkflowResponseDto>> {
    const result = await this.workflowService.findOne(id, Number(user.id));
    return ApiResponseDto.success(result, 'Workflow details retrieved successfully');
  }

  /**
   * Create new workflow for user
   */
  @Post()
  @ApiOperation({ summary: 'Create new workflow' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Workflow created successfully',
    schema: ApiResponseDto.getSchema(WorkflowResponseDto)
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.CREATION_FAILED,
    WORKFLOW_ERROR_CODES.MISSING_REQUIRED_FIELDS,
    WORKFLOW_ERROR_CODES.INVALID_DATA,
    WORKFLOW_ERROR_CODES.DUPLICATE_NAME
  )
  async create(
    @Body() createWorkflowDto: CreateWorkflowDto,
    @CurrentUser() user: JWTPayload
  ): Promise<ApiResponseDto<WorkflowResponseDto>> {
    const result = await this.workflowService.create(createWorkflowDto, Number(user.id));
    return ApiResponseDto.created(result, 'Workflow created successfully');
  }

  /**
   * Update user's workflow
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update user workflow' })
  @ApiParam({ name: 'id', description: 'Workflow ID', example: '123e4567-e89b-12d3-a456-426614174000' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow updated successfully',
    schema: ApiResponseDto.getSchema(WorkflowResponseDto)
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.NOT_FOUND,
    WORKFLOW_ERROR_CODES.UPDATE_FAILED,
    WORKFLOW_ERROR_CODES.INVALID_DATA,
    WORKFLOW_ERROR_CODES.DUPLICATE_NAME
  )
  async update(
    @Param('id') id: string,
    @Body() updateWorkflowDto: UpdateWorkflowDto,
    @CurrentUser() user: JWTPayload
  ): Promise<ApiResponseDto<WorkflowResponseDto>> {
    const result = await this.workflowService.update(id, updateWorkflowDto, Number(user.id));
    return ApiResponseDto.success(result, 'Workflow updated successfully');
  }

  /**
   * Delete user's workflow
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete user workflow' })
  @ApiParam({ name: 'id', description: 'Workflow ID', example: '123e4567-e89b-12d3-a456-426614174000' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow deleted successfully',
    schema: ApiResponseDto.getSchema(null)
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.NOT_FOUND,
    WORKFLOW_ERROR_CODES.DELETE_FAILED
  )
  async remove(
    @Param('id') id: string,
    @CurrentUser() user: JWTPayload
  ): Promise<ApiResponseDto<null>> {
    await this.workflowService.remove(id, Number(user.id));
    return ApiResponseDto.success(null, 'Workflow deleted successfully');
  }

  /**
   * Search user's workflows
   */
  @Get('search')
  @ApiOperation({ summary: 'Search user workflows by name' })
  @ApiQuery({ name: 'q', description: 'Search query', example: 'customer' })
  @ApiQuery({ name: 'limit', required: false, description: 'Maximum results', example: 10 })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Search results retrieved successfully',
    schema: ApiResponseDto.getSchema([WorkflowResponseDto])
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.DATA_FETCH_ERROR,
    WORKFLOW_ERROR_CODES.INVALID_SEARCH_QUERY
  )
  async search(
    @Query('q') searchQuery: string,
    @Query('limit') limit: number = 10,
    @CurrentUser() user: JWTPayload
  ): Promise<ApiResponseDto<WorkflowResponseDto[]>> {
    const result = await this.workflowService.search(searchQuery, Number(user.id), limit);
    return ApiResponseDto.success(result, 'Search results retrieved successfully');
  }

  /**
   * Get user's workflow statistics
   */
  @Get('statistics')
  @ApiOperation({ summary: 'Get user workflow statistics' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Statistics retrieved successfully',
    schema: ApiResponseDto.getSchema(Object)
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.DATA_FETCH_ERROR
  )
  async getStatistics(
    @CurrentUser() user: JWTPayload
  ): Promise<ApiResponseDto<any>> {
    const result = await this.workflowService.getStatistics(Number(user.id));
    return ApiResponseDto.success(result, 'Statistics retrieved successfully');
  }
}
