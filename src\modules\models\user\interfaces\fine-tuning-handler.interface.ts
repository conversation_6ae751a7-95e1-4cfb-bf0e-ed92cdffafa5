import { ProviderFineTuneEnum } from '@modules/models/constants/provider.enum';
import { CreateFineTuningJobDto, CreateFineTuningJobResponseDto } from '../dto/user-data-fine-tune/create-fine-tuning-job.dto';
import { UserDataFineTune } from '@modules/models/entities/user-data-fine-tune.entity';

/**
 * Interface cho thông tin model và API key đã validate
 */
export interface ValidatedModelInfo {
  baseModelInfo: {
    id: string; // System model ID
    modelId: string; // System model name (gpt-4o, etc.)
    trainingPricing: number;
    provider: string;
    registryId?: string;
  };
  apiKey: string;
  userKeyInfo?: {
    id: string;
    name: string;
    provider: string;
  };
  systemKeyInfo?: {
    id: string;
    name: string;
    provider: string;
  };
}

/**
 * Interface cho thông tin token và chi phí
 */
export interface TokenCostInfo {
  totalTokens: number;
  totalCost: number;
}

/**
 * Interface cho kết quả tạo job từ provider
 */
export interface ProviderJobResult {
  id?: string;
  name?: string;
  status?: string;
  state?: string;
  fineTunedModel?: string | null;
  trainingFileId?: string;
  trainingDataUri?: string;
  validationFileId?: string;
  hyperparameters?: any;
}

/**
 * Interface cho context xử lý fine-tuning
 */
export interface FineTuningContext {
  userId: number;
  dto: CreateFineTuningJobDto;
  dataset: UserDataFineTune;
  modelInfo: ValidatedModelInfo;
  tokenInfo: TokenCostInfo;
  queryRunner: any;
}

/**
 * Interface chính cho Fine-Tuning Handler
 */
export interface IFineTuningHandler {
  /**
   * Provider mà handler này hỗ trợ
   */
  readonly provider: ProviderFineTuneEnum;

  /**
   * Validate các tham số đặc thù của provider
   * @param dto DTO chứa thông tin tạo job
   */
  validateProviderSpecificParams(dto: CreateFineTuningJobDto): Promise<void>;

  /**
   * Xử lý tạo fine-tuning job cho provider cụ thể
   * @param context Context chứa tất cả thông tin cần thiết
   * @returns Kết quả từ provider
   */
  processFineTuningJob(context: FineTuningContext): Promise<ProviderJobResult>;

  /**
   * Xử lý response từ provider thành format chuẩn
   * @param jobResult Kết quả từ provider
   * @param context Context chứa thông tin bổ sung
   * @returns Response DTO chuẩn
   */
  formatResponse(
    jobResult: ProviderJobResult,
    context: FineTuningContext,
    remainingBalance: number,
  ): CreateFineTuningJobResponseDto;
}
