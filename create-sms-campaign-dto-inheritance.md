# Cập Nhật CreateSmsCampaignDto Kế Thừa Từ QueryDto

## Tóm Tắt
Đã cập nhật `CreateSmsCampaignDto` để kế thừa từ `QueryDto`, cho phép DTO này có các tham số phân trang và tìm kiếm chuẩn.

## Thay Đổi Thực Hiện

### 1. Import QueryDto
```typescript
import { QueryDto } from '@/common/dto/query.dto';
```

### 2. Cập Nhật Class Declaration
```typescript
/**
 * DTO cho tạo SMS campaign với template (API gộp)
 * Kế thừa từ QueryDto để có các tham số phân trang và tìm kiếm
 */
export class CreateSmsCampaignDto extends QueryDto {
  // ... existing properties
}
```

## Thuộc Tính Được Thêm Từ QueryDto

Sau khi kế thừa, `CreateSmsCampaignDto` sẽ có thêm các thuộc tính sau:

### 1. <PERSON><PERSON> Trang
```typescript
page: number = 1;           // Số trang hiện tại (bắt đầu từ 1)
limit: number = 10;         // Số lượng bản ghi trên mỗi trang
```

### 2. Tìm Kiếm
```typescript
search?: string;            // Từ khóa tìm kiếm
```

### 3. Sắp Xếp
```typescript
sortBy?: string;            // Trường cần sắp xếp
sortDirection?: SortDirection; // Hướng sắp xếp (ASC/DESC)
```

## Tác Động

### ✅ Tích Cực
1. **Consistency**: Tuân theo pattern chung của hệ thống
2. **Extensibility**: Có thể sử dụng các tham số phân trang nếu cần
3. **Standardization**: Chuẩn hóa với các DTO khác trong hệ thống

### ⚠️ Lưu Ý
1. **Backward Compatibility**: API vẫn hoạt động bình thường vì service chỉ sử dụng các thuộc tính cụ thể
2. **Optional Parameters**: Các thuộc tính từ QueryDto đều là optional nên không ảnh hưởng đến validation hiện tại
3. **No Breaking Changes**: Không có thay đổi nào phá vỡ API hiện tại

## Ví Dụ Sử Dụng

### Trước Khi Thay Đổi
```json
{
  "name": "Chiến dịch SMS khuyến mãi Black Friday",
  "campaignType": "ADS",
  "templateId": 1,
  "smsIntegrationConfig": {...},
  "audiences": [...]
}
```

### Sau Khi Thay Đổi (Tương Thích Ngược)
```json
{
  "name": "Chiến dịch SMS khuyến mãi Black Friday",
  "campaignType": "ADS",
  "templateId": 1,
  "smsIntegrationConfig": {...},
  "audiences": [...],
  // Các tham số mới (optional)
  "page": 1,
  "limit": 10,
  "search": "keyword",
  "sortBy": "createdAt",
  "sortDirection": "DESC"
}
```

## Files Đã Thay Đổi

1. **src/modules/marketing/user/dto/sms-campaign/create-sms-campaign.dto.ts**
   - Import QueryDto
   - Cập nhật class để kế thừa từ QueryDto
   - Cập nhật documentation

## Kiểm Tra

- ✅ No TypeScript errors
- ✅ No ESLint errors  
- ✅ Controller vẫn hoạt động bình thường
- ✅ Service method không bị ảnh hưởng
- ✅ Backward compatibility được đảm bảo

## Kết Luận

Việc kế thừa từ QueryDto giúp CreateSmsCampaignDto tuân theo pattern chung của hệ thống mà không phá vỡ functionality hiện tại. Các thuộc tính mới từ QueryDto đều là optional và có thể được sử dụng trong tương lai nếu cần thiết.
