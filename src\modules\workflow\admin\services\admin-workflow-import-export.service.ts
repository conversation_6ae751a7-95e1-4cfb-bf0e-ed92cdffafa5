import { Injectable, Logger } from '@nestjs/common';
import { WorkflowImportExportService } from '../../services/workflow-import-export.service';

/**
 * Admin service để manage workflow import/export
 * Following existing admin service patterns
 */
@Injectable()
export class AdminWorkflowImportExportService {
  private readonly logger = new Logger(AdminWorkflowImportExportService.name);

  constructor(
    private readonly workflowImportExportService: WorkflowImportExportService,
  ) {}

  // Placeholder implementation
  // This will be implemented in future tasks
}
