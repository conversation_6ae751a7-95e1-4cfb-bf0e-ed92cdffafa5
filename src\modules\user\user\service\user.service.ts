import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Not } from 'typeorm';
import { User } from '@modules/user/entities';
import { CreateUserDto, ChangePasswordDto, UserDto, UpdatePersonalInfoDto } from '@modules/user/dto';
import * as bcrypt from 'bcrypt';
import { AppException, ErrorCode } from '@/common';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly cdnService: CdnService,
  ) {}

  async findAll(options: { page: number; limit: number }) {
    const [items, total] = await this.userRepository.findAndCount({
      skip: (options.page - 1) * options.limit,
      take: options.limit,
      order: { createdAt: 'DESC' },
    });

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: options.limit,
        totalPages: Math.ceil(total / options.limit),
        currentPage: options.page,
      },
    };
  }

  async findOne(id: number): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id }
    });

    if (!user) {
      throw new AppException(ErrorCode.USER_NOT_FOUND, `User with ID "${id}" not found`);
    }

    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { email } });
  }

  async findByPhone(phoneNumber: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { phoneNumber } });
  }

  /**
   * Tìm nhiều người dùng theo danh sách ID
   * @param ids Danh sách ID của người dùng
   * @returns Danh sách người dùng
   */
  async findByIds(ids: number[]): Promise<User[]> {
    return this.userRepository.find({
      where: { id: In(ids) }
    });
  }

  /**
   * Tạo người dùng mới
   * @param createUserDto Thông tin người dùng cần tạo
   * @returns Người dùng đã được tạo
   */
  async create(createUserDto: CreateUserDto): Promise<User> {
    // Kiểm tra email đã tồn tại chưa
    if (createUserDto.email) {
      const existingUserByEmail = await this.findByEmail(createUserDto.email);
      if (existingUserByEmail) {
        throw new AppException(ErrorCode.EMAIL_ALREADY_EXISTS, `Email ${createUserDto.email} đã được sử dụng`);
      }
    }

    // Kiểm tra số điện thoại đã tồn tại chưa
    if (createUserDto.phoneNumber) {
      const existingUserByPhone = await this.findByPhone(createUserDto.phoneNumber);
      if (existingUserByPhone) {
        throw new AppException(ErrorCode.PHONE_NUMBER_ALREADY_EXISTS, `Số điện thoại ${createUserDto.phoneNumber} đã được sử dụng`);
      }
    }

    // Tạo người dùng mới
    const newUser = this.userRepository.create(createUserDto);
    return this.userRepository.save(newUser);
  }

  /**
   * Đổi mật khẩu cho người dùng
   * @param userId ID của người dùng
   * @param changePasswordDto Thông tin đổi mật khẩu
   * @returns Thông báo kết quả
   */
  async changePassword(userId: number, changePasswordDto: ChangePasswordDto): Promise<{ message: string }> {
    const { currentPassword, newPassword, confirmPassword } = changePasswordDto;

    // Kiểm tra mật khẩu mới và xác nhận mật khẩu mới có khớp nhau không
    if (newPassword !== confirmPassword) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Mật khẩu mới và xác nhận mật khẩu mới không khớp');
    }

    // Tìm người dùng theo ID
    const user = await this.findOne(userId);

    // Nếu không phải lần đổi mật khẩu đầu tiên, cần kiểm tra mật khẩu hiện tại
    if (!user.isFirstPasswordChange) {
      if (!currentPassword) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'Mật khẩu hiện tại là bắt buộc');
      }

      // Kiểm tra mật khẩu hiện tại có chính xác không
      const isPasswordValid = await this.comparePasswords(currentPassword, user.password);
      if (!isPasswordValid) {
        throw new AppException(ErrorCode.EMAIL_OR_PASSWORD_NOT_VALID, 'Mật khẩu hiện tại không chính xác');
      }
    }

    // Mã hóa mật khẩu mới
    const hashedPassword = await this.hashPassword(newPassword);

    // Cập nhật mật khẩu mới và thời gian cập nhật
    user.password = hashedPassword;
    user.updatedAt = Date.now();

    // Nếu là lần đổi mật khẩu đầu tiên, cập nhật trạng thái
    if (user.isFirstPasswordChange) {
      user.isFirstPasswordChange = false;
    }

    // Lưu thay đổi vào cơ sở dữ liệu
    await this.userRepository.save(user);

    return { message: 'Đổi mật khẩu thành công' };
  }

  /**
   * Mã hóa mật khẩu
   * @param password Mật khẩu cần mã hóa
   * @returns Mật khẩu đã mã hóa
   */
  private async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt();
    return bcrypt.hash(password, salt);
  }

  /**
   * So sánh mật khẩu
   * @param plainPassword Mật khẩu gốc
   * @param hashedPassword Mật khẩu đã mã hóa
   * @returns true nếu mật khẩu khớp, ngược lại false
   */
  private async comparePasswords(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  /**
   * Lấy số point của người dùng
   * @param userId ID của người dùng
   * @returns Số point hiện tại của người dùng
   */
  async getUserPoints(userId: number): Promise<{ pointsBalance: number }> {
    const user = await this.findOne(userId);
    return { pointsBalance: user.pointsBalance };
  }

  /**
   * Lấy thông tin chi tiết của người dùng
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của người dùng
   */
  async getUserProfile(userId: number): Promise<UserDto> {
    try {
      // Tìm người dùng theo ID
      const user = await this.findOne(userId);

      // Xử lý URL ảnh đại diện
      let avatarUrl: string | undefined = undefined;
      if (user.avatar) {
        const url = this.cdnService.generateUrlView(
          user.avatar,
          TimeIntervalEnum.ONE_HOUR
        );
        if (url) {
          avatarUrl = url;
        }
      }

      // Xử lý URL ảnh bìa
      let coverImageUrl: string | undefined = undefined;
      if (user.coverImage) {
        const url = this.cdnService.generateUrlView(
          user.coverImage,
          TimeIntervalEnum.ONE_HOUR
        );
        if (url) {
          coverImageUrl = url;
        }
      }

      // Chuyển đổi dữ liệu từ entity sang DTO
      const userDto: UserDto = {
        id: user.id,
        fullName: user.fullName,
        email: user.email,
        phoneNumber: user.phoneNumber,
        isActive: user.isActive,
        isVerifyEmail: user.isVerifyEmail,
        isVerifyPhone: user.isVerifyPhone || false,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        address: user.address,
        taxCode: user.taxCode,
        pointsBalance: user.pointsBalance,
        type: user.type,
        platform: user.platform,
        citizenId: user.citizenId,
        citizenIssuePlace: user.citizenIssuePlace,
        citizenIssueDate: user.citizenIssueDate,
        avatar: user.avatar,
        avatarUrl: avatarUrl,
        coverImage: user.coverImage,
        coverImageUrl: coverImageUrl,
        dateOfBirth: user.dateOfBirth,
        gender: user.gender,
        bankCode: user.bankCode,
        accountNumber: user.accountNumber,
        accountHolder: user.accountHolder,
        bankBranch: user.bankBranch,
        countryCode: user.countryCode,
        isFirstPasswordChange: user.isFirstPasswordChange
      };

      return userDto;
    } catch (error) {
      this.logger.error(`Error getting user profile: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi khi lấy thông tin người dùng');
    }
  }

  /**
   * Cập nhật thông tin cá nhân của người dùng
   * @param userId ID của người dùng
   * @param updatePersonalInfoDto Thông tin cá nhân cần cập nhật
   * @returns Thông tin người dùng sau khi cập nhật
   */
  async updatePersonalInfo(userId: number, updatePersonalInfoDto: UpdatePersonalInfoDto): Promise<UserDto> {
    try {
      // Tìm người dùng theo ID
      const user = await this.findOne(userId);

      // Kiểm tra số điện thoại đã tồn tại chưa (nếu có cập nhật số điện thoại hoặc mã quốc gia)
      const isPhoneNumberChanged = updatePersonalInfoDto.phoneNumber !== undefined && updatePersonalInfoDto.phoneNumber !== user.phoneNumber;
      const isCountryCodeChanged = updatePersonalInfoDto.countryCode !== undefined && updatePersonalInfoDto.countryCode !== user.countryCode;

      if (isPhoneNumberChanged || isCountryCodeChanged) {
        const newPhoneNumber = updatePersonalInfoDto.phoneNumber !== undefined ? updatePersonalInfoDto.phoneNumber : user.phoneNumber;
        const newCountryCode = updatePersonalInfoDto.countryCode !== undefined ? updatePersonalInfoDto.countryCode : user.countryCode;

        if (newPhoneNumber) {
          const existingUserWithPhone = await this.userRepository.findOne({
            where: {
              phoneNumber: newPhoneNumber,
              countryCode: newCountryCode,
              id: Not(userId) // Loại trừ người dùng hiện tại
            }
          });

          if (existingUserWithPhone) {
            throw new AppException(
              ErrorCode.PHONE_NUMBER_ALREADY_EXISTS,
              `Số điện thoại +${newCountryCode}${newPhoneNumber} đã được sử dụng bởi người dùng khác`
            );
          }
        }
      }

      // Cập nhật thông tin người dùng
      if (updatePersonalInfoDto.fullName !== undefined) {
        user.fullName = updatePersonalInfoDto.fullName;
      }

      if (updatePersonalInfoDto.gender !== undefined) {
        user.gender = updatePersonalInfoDto.gender;
      }

      if (updatePersonalInfoDto.dateOfBirth !== undefined) {
        user.dateOfBirth = new Date(updatePersonalInfoDto.dateOfBirth);
      }

      if (updatePersonalInfoDto.address !== undefined) {
        user.address = updatePersonalInfoDto.address;
      }

      if (updatePersonalInfoDto.phoneNumber !== undefined) {
        user.phoneNumber = updatePersonalInfoDto.phoneNumber;
      }

      if (updatePersonalInfoDto.countryCode !== undefined) {
        user.countryCode = updatePersonalInfoDto.countryCode;
      }

      // Cập nhật thời gian sửa đổi
      user.updatedAt = Date.now();

      // Lưu thông tin người dùng đã cập nhật vào cơ sở dữ liệu
      await this.userRepository.save(user);

      // Trả về thông tin người dùng đã cập nhật
      return this.getUserProfile(userId);
    } catch (error) {
      this.logger.error(`Error updating personal info: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Lỗi khi cập nhật thông tin cá nhân');
    }
  }
}
