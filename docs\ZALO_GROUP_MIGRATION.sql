-- Migration script để thêm các cột mới vào bảng zalo_groups
-- Thực hiện theo thứ tự để đảm bảo không có lỗi

-- 1. Thêm các cột thông tin từ Zalo API group_info
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS group_link VARCHAR(500);
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS zalo_status VARCHAR(50);
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS total_member INTEGER;
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS max_member VARCHAR(10);
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS auto_delete_date VARCHAR(20);

-- 2. Thêm các cột thông tin từ Zalo API asset_info
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS asset_type VARCHAR(20);
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS asset_id VARCHAR(100);
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS valid_through VARCHAR(20);
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS auto_renew BOOLEAN;

-- 3. Thêm các cột thông tin từ Zalo API group_setting
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS lock_send_msg BOOLEAN;
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS join_appr BOOLEAN;
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS enable_msg_history BOOLEAN;
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS enable_link_join BOOLEAN;

-- 4. Thêm cột thông tin đồng bộ
ALTER TABLE zalo_groups ADD COLUMN IF NOT EXISTS last_sync_at TIMESTAMP;

-- 5. Thêm comment cho các cột mới
COMMENT ON COLUMN zalo_groups.group_link IS 'Link tham gia nhóm từ Zalo API';
COMMENT ON COLUMN zalo_groups.zalo_status IS 'Trạng thái nhóm trên Zalo (enabled/disabled)';
COMMENT ON COLUMN zalo_groups.total_member IS 'Tổng số thành viên từ Zalo API';
COMMENT ON COLUMN zalo_groups.max_member IS 'Số thành viên tối đa của nhóm';
COMMENT ON COLUMN zalo_groups.auto_delete_date IS 'Ngày nhóm tự động giải tán';

COMMENT ON COLUMN zalo_groups.asset_type IS 'Loại sản phẩm GMF (gmf10/gmf50/gmf100)';
COMMENT ON COLUMN zalo_groups.asset_id IS 'ID gói GMF sử dụng cho nhóm';
COMMENT ON COLUMN zalo_groups.valid_through IS 'Ngày hết hạn của gói GMF';
COMMENT ON COLUMN zalo_groups.auto_renew IS 'Có tự động gia hạn gói GMF không';

COMMENT ON COLUMN zalo_groups.lock_send_msg IS 'Khóa tính năng nhắn tin của thành viên';
COMMENT ON COLUMN zalo_groups.join_appr IS 'Yêu cầu duyệt thành viên mới';
COMMENT ON COLUMN zalo_groups.enable_msg_history IS 'Cho phép thành viên mới đọc tin nhắn cũ';
COMMENT ON COLUMN zalo_groups.enable_link_join IS 'Cho phép tham gia nhóm bằng link';

COMMENT ON COLUMN zalo_groups.last_sync_at IS 'Thời điểm đồng bộ dữ liệu từ Zalo API lần cuối';

-- 6. Tạo index cho các cột thường được query
CREATE INDEX IF NOT EXISTS idx_zalo_groups_asset_id ON zalo_groups(asset_id);
CREATE INDEX IF NOT EXISTS idx_zalo_groups_asset_type ON zalo_groups(asset_type);
CREATE INDEX IF NOT EXISTS idx_zalo_groups_zalo_status ON zalo_groups(zalo_status);
CREATE INDEX IF NOT EXISTS idx_zalo_groups_last_sync_at ON zalo_groups(last_sync_at);

-- 7. Cập nhật dữ liệu hiện có (nếu cần)
-- Đặt giá trị mặc định cho các bản ghi hiện có
UPDATE zalo_groups 
SET 
    zalo_status = 'enabled',
    total_member = member_count,
    auto_renew = false,
    lock_send_msg = false,
    join_appr = true,
    enable_msg_history = true,
    enable_link_join = true,
    last_sync_at = NOW()
WHERE 
    zalo_status IS NULL;

-- 8. Thông báo hoàn thành
SELECT 'Migration completed successfully. Added new columns to zalo_groups table.' as status;
