import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Workflow error codes for API responses
 * Following existing error code patterns from the codebase
 */
export const WORKFLOW_ERROR_CODES = {
  // General CRUD errors (15000-15099)
  NOT_FOUND: new ErrorCode(
    15000,
    'Workflow not found',
    HttpStatus.NOT_FOUND
  ),
  DATA_FETCH_ERROR: new ErrorCode(
    15001,
    'Failed to fetch workflow data',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  CREATION_FAILED: new ErrorCode(
    15002,
    'Failed to create workflow',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  UPDATE_FAILED: new ErrorCode(
    15003,
    'Failed to update workflow',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  DELETE_FAILED: new ErrorCode(
    15004,
    'Failed to delete workflow',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),

  // Validation errors (15010-15019)
  MISSING_REQUIRED_FIELDS: new ErrorCode(
    15010,
    'Missing required fields',
    HttpStatus.BAD_REQUEST
  ),
  INVALID_DATA: new ErrorCode(
    15011,
    'Invalid workflow data',
    HttpStatus.BAD_REQUEST
  ),
  DUPLICATE_NAME: new ErrorCode(
    15012,
    'Workflow name already exists for this user',
    HttpStatus.CONFLICT
  ),
  INVALID_SEARCH_QUERY: new ErrorCode(
    15013,
    'Invalid search query',
    HttpStatus.BAD_REQUEST
  ),

  // Access control errors (15020-15029)
  ACCESS_DENIED: new ErrorCode(
    15020,
    'Access denied to workflow',
    HttpStatus.FORBIDDEN
  ),
  INVALID_STATUS: new ErrorCode(
    15021,
    'Invalid workflow status',
    HttpStatus.BAD_REQUEST
  ),
  WORKFLOW_INACTIVE: new ErrorCode(
    15022,
    'Workflow is not active',
    HttpStatus.BAD_REQUEST
  ),

  // Legacy error codes (for backward compatibility)
  WORKFLOW_NOT_FOUND: 'WORKFLOW_NOT_FOUND',
  WORKFLOW_ACCESS_DENIED: 'WORKFLOW_ACCESS_DENIED',
  WORKFLOW_INVALID_STATUS: 'WORKFLOW_INVALID_STATUS',

  // Node errors
  NODE_NOT_FOUND: 'NODE_NOT_FOUND',
  NODE_EXECUTION_FAILED: 'NODE_EXECUTION_FAILED',
  NODE_INVALID_CONFIG: 'NODE_INVALID_CONFIG',
  NODE_MISSING_CONNECTION: 'NODE_MISSING_CONNECTION',

  // Connection errors
  CONNECTION_NOT_FOUND: 'CONNECTION_NOT_FOUND',
  CONNECTION_INVALID: 'CONNECTION_INVALID',

  // Execution errors
  EXECUTION_FAILED: 'EXECUTION_FAILED',
  EXECUTION_TIMEOUT: 'EXECUTION_TIMEOUT',
  EXECUTION_CANCELLED: 'EXECUTION_CANCELLED',
} as const;

export type WorkflowErrorCode = typeof WORKFLOW_ERROR_CODES[keyof typeof WORKFLOW_ERROR_CODES];
