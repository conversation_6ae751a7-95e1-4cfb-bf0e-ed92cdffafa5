import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { ProviderFineTuneEnum } from '../../../constants/provider.enum';

/**
 * DTO cho admin validation fine-tuning job - chỉ để validate và ước tính cost
 */
export class AdminValidateFineTuningJobDto {
  /**
   * ID của dataset training
   */
  @ApiProperty({
    description: 'UUID của admin dataset training',
    example: '112b8f44-82a1-4426-8b19-2413dab6c262',
  })
  @IsNotEmpty()
  @IsUUID()
  datasetId: string;

  /**
   * ID của system model cơ sở
   */
  @ApiProperty({
    description: 'UUID của system model cơ sở',
    example: '96ba4303-a318-4d16-b064-e4489eec78a8',
  })
  @IsNotEmpty()
  @IsUUID()
  systemModelId: string;

  /**
   * Nhà cung cấp AI
   */
  @ApiProperty({
    description: 'Nhà cung cấp AI',
    enum: ProviderFineTuneEnum,
    example: ProviderFineTuneEnum.OPENAI,
  })
  @IsNotEmpty()
  @IsEnum(ProviderFineTuneEnum)
  provider: ProviderFineTuneEnum;

  // Admin chỉ sử dụng system key, không cần user key
}

/**
 * DTO response cho admin fine-tuning validation
 */
export class AdminFineTuningValidationResponseDto {
  /**
   * Validation có thành công không
   */
  @ApiProperty({
    description: 'Validation có thành công không',
    example: true,
  })
  isValid: boolean;

  /**
   * Danh sách lỗi validation (nếu có)
   */
  @ApiProperty({
    description: 'Danh sách lỗi validation',
    example: [],
    type: [String],
  })
  validationErrors: string[];

  /**
   * Ước tính thời gian hoàn thành (phút)
   */
  @ApiProperty({
    description: 'Ước tính thời gian hoàn thành (phút)',
    example: 45,
  })
  estimatedDurationMinutes: number;

  /**
   * Thông tin dataset
   */
  @ApiProperty({
    description: 'Thông tin dataset',
    example: {
      id: '112b8f44-82a1-4426-8b19-2413dab6c262',
      name: 'Customer Service Dataset',
      totalExamples: 1500,
      estimatedTokens: 75000,
      provider: 'OPENAI'
    },
  })
  datasetInfo: {
    id: string;
    name: string;
    totalExamples: number;
    estimatedTokens: number;
    provider: string;
  };

  /**
   * Thông tin model
   */
  @ApiProperty({
    description: 'Thông tin model',
    example: {
      id: '96ba4303-a318-4d16-b064-e4489eec78a8',
      modelId: 'gpt-4o-mini-2024-07-18',
      provider: 'OPENAI',
      trainingPricing: 3.0
    },
  })
  modelInfo: {
    id: string;
    modelId: string;
    provider: string;
    trainingPricing: number;
  };

  /**
   * Thông tin system API key được sử dụng
   */
  @ApiProperty({
    description: 'Thông tin system API key được sử dụng',
    example: {
      keyId: 'sys_key_123',
      keyName: 'OpenAI System Key',
      provider: 'OPENAI'
    },
  })
  systemKeyInfo: {
    keyId: string;
    keyName: string;
    provider: string;
  };

  /**
   * Validation token (để sử dụng cho execute API)
   */
  @ApiProperty({
    description: 'Validation token để sử dụng cho execute API',
    example: 'admin_val_abc123def456',
  })
  validationToken: string;
}
