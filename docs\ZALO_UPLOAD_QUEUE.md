# Zalo Upload Queue - <PERSON><PERSON> thống Queue cho Upload File

## Tổng quan

Hệ thống queue cho upload file <PERSON>alo (Image, File, GIF) giúp xử lý upload bất đồng bộ, tránh timeout và cải thiện trải nghiệm người dùng.

## Kiến trúc

### 1. **Queue Flow**
```
Client Request → Controller → Queue → Worker → Zalo API → Database → Response
```

### 2. **Components**

#### **Queue Service** (`QueueService`)
- Thêm job vào queue
- Quản lý job options
- Monitor job status

#### **Worker** (`ZaloUploadWorker`)
- Xử lý job từ queue
- Upload file qua Zalo API
- Lưu kết quả vào database
- Update job progress

#### **Controller** (`ZaloUploadController`)
- Nhận request từ client
- Validate file và permissions
- Tạo job và trả về job ID
- Cung cấp API check job status

## API Endpoints

### 1. **Upload GIF (Async)**

```
POST /v1/marketing/zalo/upload/{integrationId}/upload/gif
```

**Request:**
```bash
curl -X POST \
  'http://localhost:3000/v1/marketing/zalo/upload/d331ae2f-b314-4095-963f-6a7c157658a0/upload/gif' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -F 'file=@animation.gif' \
  -F 'description=Test GIF upload'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "job_123456789",
    "message": "Upload GIF đã được thêm vào queue xử lý"
  },
  "message": "Upload GIF đã được thêm vào queue xử lý"
}
```

### 2. **Check Job Status**

```
GET /v1/marketing/zalo/upload/job/{jobId}/status
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "job_123456789",
    "state": "completed",
    "progress": 100,
    "result": {
      "success": true,
      "uploadRecord": {
        "id": 123,
        "attachmentId": "wESbL53O_shdvIPILC7iR_JpC552r_fjukKg",
        "originalFilename": "animation.gif",
        "fileSize": 1024000,
        "mimeType": "image/gif",
        "uploadedAt": 1640995200000,
        "description": "Test GIF upload"
      },
      "message": "Upload GIF thành công"
    }
  },
  "message": "Lấy trạng thái job thành công"
}
```

## Job States

| State | Description |
|-------|-------------|
| `waiting` | Job đang chờ trong queue |
| `active` | Job đang được xử lý |
| `completed` | Job hoàn thành thành công |
| `failed` | Job thất bại |
| `delayed` | Job bị delay (retry) |

## Job Data Structure

### **ZaloUploadGifJobData**
```typescript
interface ZaloUploadGifJobData {
  userId: number;
  integrationId: string;
  fileInfo: {
    data: Buffer;
    filename: string;
    mimetype: string;
    size: number;
  };
  description?: string;
  timestamp: number;
  trackingId?: string;
}
```

## Queue Configuration

### **Queue Options**
```typescript
{
  name: QueueName.ZALO_UPLOAD,
  defaultJobOptions: {
    removeOnComplete: 30,    // Giữ 30 job hoàn thành
    removeOnFail: 50,        // Giữ 50 job thất bại
    attempts: 3,             // Retry 3 lần
    backoff: {
      type: 'exponential',
      delay: 2000,           // Delay 2s cho retry
    },
    // Note: timeout được set trong từng job riêng lẻ (60s)
  },
}
```

## Worker Processing

### **Upload Flow**
1. **Validation** (Progress: 10%)
   - Lấy thông tin OA từ integrationId
   - Validate quyền truy cập

2. **Upload to Zalo** (Progress: 20-60%)
   - Gọi Zalo API upload
   - Nhận attachment_id

3. **Save to Database** (Progress: 60-100%)
   - Lưu thông tin upload
   - Cập nhật metadata với job info

### **Error Handling**
- Retry tự động với exponential backoff
- Log chi tiết lỗi
- Trả về error details trong job result

## Monitoring & Debugging

### **1. Queue Stats**
```typescript
const stats = await queueService.getQueueStats('zalo-upload');
// { waiting: 5, active: 2, completed: 100, failed: 3 }
```

### **2. Job Inspection**
```typescript
const job = await queueService.getJobStatus('zalo-upload', jobId);
console.log(job.state, job.progress, job.result);
```

### **3. Failed Jobs**
```bash
# Redis CLI
redis-cli
> LRANGE bull:zalo-upload:failed 0 -1
```

## Benefits

### **1. Performance**
- ✅ Non-blocking upload
- ✅ Parallel processing
- ✅ Resource optimization

### **2. Reliability**
- ✅ Automatic retry
- ✅ Error recovery
- ✅ Job persistence

### **3. User Experience**
- ✅ Immediate response
- ✅ Progress tracking
- ✅ No timeout issues

### **4. Scalability**
- ✅ Horizontal scaling
- ✅ Load distribution
- ✅ Queue prioritization

## Migration from Sync to Async

### **Before (Sync)**
```typescript
// Client chờ đến khi upload xong
const result = await uploadGif(file);
return result; // Có thể timeout
```

### **After (Async)**
```typescript
// Client nhận job ID ngay lập tức
const { jobId } = await uploadGif(file);

// Client poll job status
const status = await getJobStatus(jobId);
```

## Best Practices

1. **Job Size**: Giữ job data nhỏ gọn
2. **Timeout**: Set timeout phù hợp cho file size
3. **Retry**: Cấu hình retry hợp lý
4. **Monitoring**: Theo dõi queue metrics
5. **Cleanup**: Dọn dẹp job cũ định kỳ

## Troubleshooting

### **Job bị stuck**
- Kiểm tra worker có đang chạy
- Xem log worker process
- Restart worker nếu cần

### **Upload thất bại**
- Kiểm tra Zalo API credentials
- Validate file format và size
- Xem error details trong job result

### **Queue đầy**
- Tăng số worker processes
- Optimize job processing time
- Cleanup old jobs
