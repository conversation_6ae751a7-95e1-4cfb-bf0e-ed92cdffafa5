import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { ProviderFineTuneEnum } from '@modules/models/constants/provider.enum';

/**
 * Custom validator cho Google Cloud configuration
 * Validate dựa trên provider và userKeyLlmId:
 * - Google AI + System model (không có userKeyLlmId): googleCloud optional
 * - Google AI + User model (có userKeyLlmId): googleCloud required
 * - Các provider khác: googleCloud không cần
 */
export function IsValidGoogleCloudConfig(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidGoogleCloudConfig',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const dto = args.object as any;
          
          // Chỉ validate khi provider là Google AI
          if (dto.provider !== ProviderFineTuneEnum.GEMINI) {
            return true; // Không cần validate cho provider khác
          }

          const isUserModel = !!dto.userKeyLlmId;

          if (isUserModel) {
            // Case 4: User model - Google Cloud config bắt buộc
            if (!value) {
              return false; // Missing googleCloud config
            }

            // Validate required fields
            if (!value.projectId || !value.bucketName) {
              return false; // Missing required fields
            }

            return true;
          } else {
            // Case 3: System model - Google Cloud config optional
            if (value) {
              // Nếu có cung cấp, validate format
              if (!value.projectId || !value.bucketName) {
                return false; // Invalid format
              }
            }
            return true; // Optional cho system model
          }
        },
        
        defaultMessage(args: ValidationArguments) {
          const dto = args.object as any;
          const isUserModel = !!dto.userKeyLlmId;

          if (dto.provider === ProviderFineTuneEnum.GEMINI) {
            if (isUserModel) {
              return 'Google Cloud configuration (projectId, bucketName) là bắt buộc khi sử dụng user model với Google AI';
            } else {
              return 'Google Cloud configuration không hợp lệ (cần projectId và bucketName nếu cung cấp)';
            }
          }

          return 'Google Cloud configuration không hợp lệ';
        }
      }
    });
  };
}
