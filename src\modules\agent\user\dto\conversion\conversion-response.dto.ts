import { ApiProperty } from '@nestjs/swagger';
import { ConvertConfigItemDto } from './convert-config-item.dto';

/**
 * DTO cho response thông tin conversion config của agent
 */
export class ConversionResponseDto {
  /**
   * <PERSON>h sách cấu hình chuyển đổi
   */
  @ApiProperty({
    description: '<PERSON>h sách cấu hình chuyển đổi',
    type: [ConvertConfigItemDto],
    example: [
      {
        name: 'customer_email',
        type: 'string',
        description: 'Email của khách hàng',
        required: true,
        deletable: false
      },
      {
        name: 'customer_phone',
        type: 'string',
        description: 'Số điện thoại của khách hàng',
        required: true,
        deletable: false
      },
      {
        name: 'customer_name',
        type: 'string',
        description: 'Tên đầy đủ của khách hàng',
        required: true,
        deletable: true
      },
      {
        name: 'order_amount',
        type: 'number',
        description: 'S<PERSON> tiền đơn hàng',
        required: false,
        deletable: true
      },
      {
        name: 'product_categories',
        type: 'array',
        description: '<PERSON><PERSON> mục sản phẩm quan tâm',
        required: false,
        items: {
          type: 'string',
          description: 'Tên danh mục sản phẩm'
        },
        deletable: true
      },
      {
        name: 'customer_address',
        type: 'object',
        description: 'Địa chỉ khách hàng',
        required: false,
        properties: {
          'street': {
            name: 'street',
            type: 'string',
            description: 'Tên đường',
            required: true,
            deletable: true
          },
          'city': {
            name: 'city',
            type: 'string',
            description: 'Thành phố',
            required: true,
            deletable: true
          },
          'postal_code': {
            name: 'postal_code',
            type: 'string',
            description: 'Mã bưu điện',
            required: false,
            deletable: true
          }
        },
        deletable: true
      },
      {
        name: 'customer_preferences',
        type: 'string',
        description: 'Sở thích khách hàng',
        required: false,
        enum: ['standard', 'premium', 'vip'],
        deletable: true
      }
    ],
  })
  convertConfig: ConvertConfigItemDto[];

  /**
   * Số lượng fields trong conversion config
   */
  @ApiProperty({
    description: 'Số lượng fields trong conversion config',
    example: 5,
  })
  totalFields: number;

  /**
   * Số lượng fields bắt buộc
   */
  @ApiProperty({
    description: 'Số lượng fields bắt buộc',
    example: 3,
  })
  requiredFields: number;

  /**
   * Thời điểm cập nhật conversion config gần nhất (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật conversion config gần nhất (timestamp millis)',
    example: 1672531200000,
  })
  updatedAt: number;
}
