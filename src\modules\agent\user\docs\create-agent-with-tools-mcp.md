# API Tạo Agent với Custom Tools và MCP Servers

## Endpoint
**POST** `/v1/user/agents`

## <PERSON><PERSON> tả
Tạo agent mới với khả năng liên kết custom tools và MCP servers ngay khi tạo.

## Request Body Example

```json
{
  "name": "My AI Assistant",
  "typeId": 1,
  "systemModelId": "system-model-uuid",
  "modelConfig": {
    "temperature": 0.7,
    "top_p": 0.9
  },
  "instruction": "Bạn là trợ lý AI thông minh, hãy giúp người dùng giải quyết các vấn đề.",
  "customToolIds": [
    "tool-uuid-1",
    "tool-uuid-2"
  ],
  "mcpIds": [
    "uuid-mcp-id-1",
    "uuid-mcp-id-2"
  ],
  "profile": {
    "gender": "MALE",
    "position": "AI Assistant",
    "skills": ["Problem Solving", "Data Analysis"],
    "languages": ["Vietnamese", "English"]
  },
  "resources": {
    "mediaIds": ["media-uuid-1"],
    "urlIds": ["url-uuid-1"],
    "productIds": [1, 2]
  }
}
```

## Response

```json
{
  "success": true,
  "message": "Tạo agent thành công",
  "data": {
    "id": "agent-uuid-here",
    "avatarUploadUrl": "https://s3.example.com/upload?signature=abc123"
  }
}
```

## Logic Xử Lý

### 1. Custom Tools (`customToolIds`)
- Kiểm tra từng tool có tồn tại và thuộc về user không
- Tạo liên kết trong bảng `agent_user_tools`
- Bỏ qua tools không hợp lệ, không làm fail toàn bộ request
- Log warning cho tools không thể liên kết

### 2. MCP Servers (`mcpIds`)
- Kiểm tra từng MCP có tồn tại và thuộc về user không
- Tạo liên kết trong bảng `agent_user_mcp`
- Bỏ qua MCP không hợp lệ, không làm fail toàn bộ request
- Log warning cho MCP không thể liên kết

### 3. Validation Rules
- `customToolIds`: Mảng UUID của user custom tools
- `mcpIds`: Mảng UUID của user MCP servers
- Cả hai đều optional, có thể bỏ trống
- Không duplicate check - nếu đã liên kết thì skip

## Error Codes

| Code | Message | Description |
|------|---------|-------------|
| 40218 | Lỗi khi liên kết Agent với Tool | Custom tools linking failed |
| 40217 | Lỗi khi hủy liên kết hàng loạt Agent với MCP servers | MCP linking failed |

## Use Cases

### 1. Tạo Agent với Tools và MCP
```json
{
  "name": "Data Analyst Agent",
  "typeId": 1,
  "systemModelId": "gpt-4",
  "customToolIds": ["data-processor-tool", "chart-generator-tool"],
  "mcpIds": ["database-mcp", "api-connector-mcp"],
  "instruction": "Analyze data and generate insights"
}
```

### 2. Tạo Agent chỉ với MCP
```json
{
  "name": "Customer Service Agent", 
  "typeId": 2,
  "userModelId": "user-model-uuid",
  "keyLlmId": "user-key-uuid",
  "mcpIds": ["crm-mcp", "knowledge-base-mcp"]
}
```

### 3. Tạo Agent chỉ với Custom Tools
```json
{
  "name": "Content Creator Agent",
  "typeId": 3,
  "systemModelId": "claude-3",
  "customToolIds": ["image-generator", "text-formatter", "seo-optimizer"]
}
```

## Workflow

1. **Validation**: Kiểm tra TypeAgent, model config, tên agent
2. **Create Agent**: Tạo agent entity và agent_user relationship
3. **Process Blocks**: Xử lý resources, strategy, multi-agent, outputs
4. **Link Tools**: Liên kết custom tools (nếu có)
5. **Link MCP**: Liên kết MCP servers (nếu có)
6. **Generate Avatar URL**: Tạo presigned URL cho avatar (nếu có)

## Notes

- Tools và MCP linking không làm fail request nếu một số items không hợp lệ
- Chỉ log warning và continue với items hợp lệ
- Agent vẫn được tạo thành công ngay cả khi không có tools/MCP nào được liên kết
- Có thể thêm tools/MCP sau khi tạo agent qua các API riêng biệt
