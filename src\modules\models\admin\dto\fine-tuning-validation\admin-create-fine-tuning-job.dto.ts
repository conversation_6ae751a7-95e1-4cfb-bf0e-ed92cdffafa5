import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import { ProviderFineTuneEnum } from '../../../constants/provider.enum';

/**
 * DTO cho việc tạo admin fine-tuning job
 */
export class AdminCreateFineTuningJobDto {
  /**
   * Tên hiển thị cho model fine-tuned
   */
  @ApiProperty({
    description: 'Tên hiển thị cho model fine-tuned',
    example: 'Admin Custom GPT Model',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * Mô tả về model fine-tuned (tùy chọn)
   */
  @ApiPropertyOptional({
    description: 'Mô tả về model fine-tuned',
    example: 'Model được fine-tune bởi admin cho hệ thống',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * ID của dataset để fine-tune
   */
  @ApiProperty({
    description: 'UUID của admin dataset để fine-tune',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID()
  datasetId: string;

  /**
   * ID của system model cơ sở
   */
  @ApiProperty({
    description: 'UUID của system model cơ sở',
    example: '96ba4303-a318-4d16-b064-e4489eec78a8',
  })
  @IsNotEmpty()
  @IsUUID()
  systemModelId: string;

  /**
   * Nhà cung cấp AI
   */
  @ApiProperty({
    description: 'Nhà cung cấp AI',
    enum: ProviderFineTuneEnum,
    example: ProviderFineTuneEnum.OPENAI,
  })
  @IsNotEmpty()
  @IsEnum(ProviderFineTuneEnum)
  provider: ProviderFineTuneEnum;

  // Admin chỉ sử dụng system key, không cần user key
}

/**
 * DTO response cho việc tạo admin fine-tuning job
 */
export class AdminCreateFineTuningJobResponseDto {
  /**
   * ID của fine-tuning job đã tạo
   */
  @ApiProperty({
    description: 'UUID của fine-tuning job đã tạo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  jobId: string;

  /**
   * ID của fine-tune history record
   */
  @ApiProperty({
    description: 'UUID của fine-tune history record',
    example: '456e7890-e89b-12d3-a456-************',
  })
  historyId: string;

  /**
   * Tên model đã tạo
   */
  @ApiProperty({
    description: 'Tên model đã tạo',
    example: 'Admin Custom GPT Model',
  })
  modelName: string;

  /**
   * Trạng thái hiện tại của job
   */
  @ApiProperty({
    description: 'Trạng thái hiện tại của job',
    example: 'running',
  })
  status: string;

  /**
   * Ước tính thời gian hoàn thành (phút)
   */
  @ApiProperty({
    description: 'Ước tính thời gian hoàn thành (phút)',
    example: 45,
  })
  estimatedDurationMinutes: number;

  /**
   * Thông tin dataset được sử dụng
   */
  @ApiProperty({
    description: 'Thông tin dataset được sử dụng',
    example: {
      id: '112b8f44-82a1-4426-8b19-2413dab6c262',
      name: 'Customer Service Dataset',
      totalExamples: 1500
    },
  })
  datasetInfo: {
    id: string;
    name: string;
    totalExamples: number;
  };

  /**
   * Thông tin model cơ sở
   */
  @ApiProperty({
    description: 'Thông tin model cơ sở',
    example: {
      id: '96ba4303-a318-4d16-b064-e4489eec78a8',
      modelId: 'gpt-4o-mini-2024-07-18',
      provider: 'OPENAI'
    },
  })
  baseModelInfo: {
    id: string;
    modelId: string;
    provider: string;
  };

  /**
   * Thông tin system API key được sử dụng
   */
  @ApiProperty({
    description: 'Thông tin system API key được sử dụng',
    example: {
      keyId: 'sys_key_123',
      keyName: 'OpenAI System Key',
      provider: 'OPENAI'
    },
  })
  systemKeyInfo: {
    keyId: string;
    keyName: string;
    provider: string;
  };

  /**
   * Thời gian tạo job
   */
  @ApiProperty({
    description: 'Thời gian tạo job (timestamp)',
    example: 1703123456789,
  })
  createdAt: number;
}
