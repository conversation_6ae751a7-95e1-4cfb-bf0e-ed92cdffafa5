import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  AgentMcpResponseDto,
  BulkUnlinkAgentMcpDto,
  BulkUnlinkAgentMcpResponseDto,
  QueryAgentMcpDto
} from '../dto/agent-mcp.dto';
import { AgentMcpService } from '../services/agent-mcp.service';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý API cho Agent MCP
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT_MCP)
@Controller('user/agents/:agentId/mcps')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentMcpController {
  constructor(private readonly agentMcpService: AgentMcpService) { }

  /**
   * Lấy danh sách MCP servers của Agent
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách MCP servers của Agent',
    description: 'Lấy tất cả MCP servers được liên kết với Agent với phân trang và tìm kiếm',
  })
  @ApiParam({ name: 'agentId', description: 'ID của Agent' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang (bắt đầu từ 1)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng items per page', example: 20 })
  @ApiQuery({ name: 'search', required: false, description: 'Tìm kiếm theo tên MCP server', example: 'affiliate' })
  @ApiQuery({ name: 'transport', required: false, description: 'Lọc theo transport type', enum: ['http', 'sse'] })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách MCP servers của Agent với pagination',
    type: ApiResponseDto.paginated<AgentMcpResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Agent không tồn tại',
  })
  async getAgentMcps(
    @CurrentUser('id') userId: number,
    @Param('agentId') agentId: string,
    @Query() queryDto: QueryAgentMcpDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentMcpResponseDto>>> {
    const result = await this.agentMcpService.getAgentMcps(userId, agentId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách MCP servers của Agent thành công');
  }

  /**
   * Hủy liên kết Agent với nhiều MCP servers
   */
  @Delete('bulk')
  @ApiOperation({
    summary: 'Hủy liên kết Agent với nhiều MCP servers',
    description: 'Xóa liên kết hàng loạt giữa Agent và nhiều MCP servers',
  })
  @ApiParam({ name: 'agentId', description: 'ID của Agent' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Kết quả hủy liên kết hàng loạt',
    type: ApiResponseDto<BulkUnlinkAgentMcpResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Agent không tồn tại',
  })
  async bulkUnlinkAgentMcp(
    @CurrentUser('id') userId: number,
    @Param('agentId') agentId: string,
    @Body() bulkDto: BulkUnlinkAgentMcpDto,
  ): Promise<ApiResponseDto<BulkUnlinkAgentMcpResponseDto>> {
    const result = await this.agentMcpService.bulkUnlinkAgentMcp(userId, agentId, bulkDto);
    return ApiResponseDto.success(result, 'Hủy liên kết hàng loạt hoàn thành');
  }

  /**
   * Liên kết nhiều MCP servers với Agent (bulk operation)
   */
  @Post()
  @ApiOperation({
    summary: 'Liên kết nhiều MCP servers với Agent',
    description: 'Tạo liên kết hàng loạt giữa Agent và nhiều MCP servers',
  })
  @ApiParam({ name: 'agentId', description: 'ID của Agent' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Liên kết hàng loạt thành công',
    type: ApiResponseDto<null>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Agent không tồn tại',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'MCP không tồn tại hoặc không thuộc về bạn',
  })
  async bulkLinkAgentMcp(
    @CurrentUser('id') userId: number,
    @Param('agentId') agentId: string,
    @Body() bulkDto: BulkUnlinkAgentMcpDto,
  ): Promise<ApiResponseDto<null>> {
    await this.agentMcpService.bulkLinkAgentMcp(userId, agentId, bulkDto);
    return ApiResponseDto.success(null, 'Liên kết hàng loạt hoàn thành');
  }

  /**
   * Xóa tất cả liên kết MCP của Agent
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa tất cả liên kết MCP của Agent',
    description: 'Hủy tất cả liên kết giữa Agent và MCP servers',
  })
  @ApiParam({ name: 'agentId', description: 'ID của Agent' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Đã xóa tất cả liên kết MCP của Agent',
    type: ApiResponseDto<{ removedCount: number }>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Agent không tồn tại',
  })
  async removeAllAgentMcps(
    @CurrentUser('id') userId: number,
    @Param('agentId') agentId: string,
  ): Promise<ApiResponseDto<{ removedCount: number }>> {
    const removedCount = await this.agentMcpService.removeAllAgentMcps(userId, agentId);
    return ApiResponseDto.success(
      { removedCount },
      `Đã xóa ${removedCount} liên kết MCP của Agent thành công`
    );
  }
}
