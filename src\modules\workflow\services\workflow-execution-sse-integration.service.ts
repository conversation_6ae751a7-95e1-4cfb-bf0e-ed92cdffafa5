import { Injectable, Logger } from '@nestjs/common';
import { WorkflowExecutionService } from './workflow-execution.service';
import { WorkflowSSEService } from './workflow-sse.service';
import { WorkflowExecution } from '../entities';
import { ExecutionStatus } from '../constants';
import {
  WorkflowExecutionEventData,
  WorkflowNodeEventData,
  WORKFLOW_SSE_EVENTS,
} from '../constants/workflow-sse-events';

/**
 * Service tích hợp WorkflowExecution với SSE events
 * Automatically publishes SSE events when execution status changes
 */
@Injectable()
export class WorkflowExecutionSSEIntegrationService {
  private readonly logger = new Logger(WorkflowExecutionSSEIntegrationService.name);

  constructor(
    private readonly workflowExecutionService: WorkflowExecutionService,
    private readonly workflowSSEService: WorkflowSSEService,
  ) {}

  /**
   * Create execution và publish SSE event
   * @param data Execution data
   * @returns Promise với execution đã tạo
   */
  async createExecutionWithSSE(data: Partial<WorkflowExecution>): Promise<WorkflowExecution> {
    const execution = await this.workflowExecutionService.create(data);

    // Publish execution created event
    await this.publishExecutionEvent(execution, 'created');

    return execution;
  }

  /**
   * Start execution và publish SSE event
   * @param id Execution ID
   * @returns Promise với execution đã start
   */
  async startExecutionWithSSE(id: string): Promise<WorkflowExecution> {
    const execution = await this.workflowExecutionService.start(id);

    // Publish execution started event
    await this.publishExecutionEvent(execution, 'started');

    return execution;
  }

  /**
   * Complete execution và publish SSE event
   * @param id Execution ID
   * @param result Execution result
   * @returns Promise với execution đã complete
   */
  async completeExecutionWithSSE(id: string, result?: any): Promise<WorkflowExecution> {
    const execution = await this.workflowExecutionService.complete(id, result);

    // Publish execution completed event
    await this.publishExecutionEvent(execution, 'completed');

    return execution;
  }

  /**
   * Fail execution và publish SSE event
   * @param id Execution ID
   * @param error Error information
   * @returns Promise với execution đã fail
   */
  async failExecutionWithSSE(id: string, error: any): Promise<WorkflowExecution> {
    const execution = await this.workflowExecutionService.fail(id, error);

    // Publish execution failed event
    await this.publishExecutionEvent(execution, 'failed');

    return execution;
  }

  /**
   * Cancel execution và publish SSE event
   * @param id Execution ID
   * @param reason Cancel reason
   * @returns Promise với execution đã cancel
   */
  async cancelExecutionWithSSE(id: string, reason?: string): Promise<WorkflowExecution> {
    const execution = await this.workflowExecutionService.cancel(id, reason);

    // Publish execution cancelled event
    await this.publishExecutionEvent(execution, 'cancelled');

    return execution;
  }

  /**
   * Pause execution và publish SSE event
   * @param id Execution ID
   * @returns Promise với execution đã pause
   */
  async pauseExecutionWithSSE(id: string): Promise<WorkflowExecution> {
    const execution = await this.workflowExecutionService.pause(id);

    // Publish execution paused event
    await this.publishExecutionEvent(execution, 'paused');

    return execution;
  }

  /**
   * Resume execution và publish SSE event
   * @param id Execution ID
   * @returns Promise với execution đã resume
   */
  async resumeExecutionWithSSE(id: string): Promise<WorkflowExecution> {
    const execution = await this.workflowExecutionService.resume(id);

    // Publish execution resumed event
    await this.publishExecutionEvent(execution, 'resumed');

    return execution;
  }

  /**
   * Update execution progress và publish SSE event
   * @param id Execution ID
   * @param progress Progress information
   * @returns Promise với execution đã update
   */
  async updateExecutionProgressWithSSE(
    id: string,
    progress: {
      currentNode?: string;
      completedNodes: number;
      totalNodes: number;
      percentage: number;
    },
  ): Promise<WorkflowExecution> {
    const execution = await this.workflowExecutionService.updateStatus(id, ExecutionStatus.RUNNING, {
      metadata: {
        progress,
        lastProgressUpdate: Date.now(),
      },
    });

    // Publish execution progress event
    await this.publishExecutionEvent(execution, 'progress', { progress });

    return execution;
  }

  /**
   * Publish node event
   * @param executionId Execution ID
   * @param nodeData Node event data
   */
  async publishNodeEvent(
    executionId: string,
    nodeData: {
      nodeId: string;
      nodeType: string;
      status: string;
      input?: any;
      output?: any;
      error?: any;
      duration?: number;
      metadata?: any;
    },
  ): Promise<void> {
    try {
      const execution = await this.workflowExecutionService.findById(executionId);

      const nodeEvent: WorkflowNodeEventData = {
        executionId,
        workflowId: execution.workflowId,
        nodeId: nodeData.nodeId,
        nodeType: nodeData.nodeType,
        userId: execution.metadata?.userId || 0,
        status: nodeData.status,
        timestamp: Date.now(),
        input: nodeData.input,
        output: nodeData.output,
        error: nodeData.error,
        duration: nodeData.duration,
        metadata: nodeData.metadata,
      };

      await this.workflowSSEService.publishNodeEvent(nodeEvent);

      this.logger.debug(
        `Published node event: ${nodeData.status} for node ${nodeData.nodeId} in execution ${executionId}`,
      );
    } catch (error) {
      this.logger.error(`Failed to publish node event: ${error.message}`);
      throw error;
    }
  }

  /**
   * Publish batch node events
   * @param executionId Execution ID
   * @param nodeEvents Array of node events
   */
  async publishBatchNodeEvents(
    executionId: string,
    nodeEvents: Array<{
      nodeId: string;
      nodeType: string;
      status: string;
      input?: any;
      output?: any;
      error?: any;
      duration?: number;
      metadata?: any;
    }>,
  ): Promise<void> {
    try {
      const execution = await this.workflowExecutionService.findById(executionId);

      const publishPromises = nodeEvents.map(async (nodeData) => {
        const nodeEvent: WorkflowNodeEventData = {
          executionId,
          workflowId: execution.workflowId,
          nodeId: nodeData.nodeId,
          nodeType: nodeData.nodeType,
          userId: execution.metadata?.userId || 0,
          status: nodeData.status,
          timestamp: Date.now(),
          input: nodeData.input,
          output: nodeData.output,
          error: nodeData.error,
          duration: nodeData.duration,
          metadata: nodeData.metadata,
        };

        return this.workflowSSEService.publishNodeEvent(nodeEvent);
      });

      await Promise.all(publishPromises);

      this.logger.debug(
        `Published ${nodeEvents.length} node events for execution ${executionId}`,
      );
    } catch (error) {
      this.logger.error(`Failed to publish batch node events: ${error.message}`);
      throw error;
    }
  }

  // Private helper methods

  /**
   * Publish execution event to SSE
   */
  private async publishExecutionEvent(
    execution: WorkflowExecution,
    action: string,
    additionalData?: any,
  ): Promise<void> {
    try {
      const eventData: WorkflowExecutionEventData = {
        executionId: execution.id,
        workflowId: execution.workflowId,
        userId: execution.metadata?.userId || 0,
        status: execution.status,
        timestamp: Date.now(),
        progress: additionalData?.progress || this.extractProgressFromMetadata(execution),
        result: execution.result,
        error: execution.error,
        metadata: {
          ...execution.metadata,
          action,
          duration: execution.duration,
          retryCount: execution.retryCount,
        },
      };

      await this.workflowSSEService.publishExecutionEvent(eventData);

      this.logger.debug(
        `Published execution event: ${action} for execution ${execution.id}`,
      );
    } catch (error) {
      this.logger.error(`Failed to publish execution event: ${error.message}`);
      // Don't throw error to avoid breaking the main execution flow
    }
  }

  /**
   * Extract progress information from execution metadata
   */
  private extractProgressFromMetadata(execution: WorkflowExecution): any {
    if (execution.metadata?.progress) {
      return execution.metadata.progress;
    }

    // Default progress calculation if not available
    if (execution.status === ExecutionStatus.COMPLETED) {
      return {
        completedNodes: 100,
        totalNodes: 100,
        percentage: 100,
      };
    }

    if (execution.status === ExecutionStatus.RUNNING) {
      return {
        completedNodes: 0,
        totalNodes: 100,
        percentage: 0,
      };
    }

    return undefined;
  }
}
