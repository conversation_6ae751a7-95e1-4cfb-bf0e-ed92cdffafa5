import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsPositive } from 'class-validator';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo URL tải lên ảnh bìa
 */
export class CoverImageUploadDto {
  @ApiProperty({
    description: 'Loại hình ảnh',
    enum: ImageTypeEnum,
    example: 'image/jpeg',
  })
  @IsNotEmpty({ message: 'Loại hình ảnh không được để trống' })
  @IsEnum(ImageTypeEnum, { message: 'Loại hình ảnh không hợp lệ' })
  imageType: ImageTypeEnum;

  @ApiProperty({
    description: 'Kích thước tối đa của file (bytes)',
    example: 5242880, // 5MB
  })
  @IsNotEmpty({ message: '<PERSON><PERSON><PERSON> thước tối đa không được để trống' })
  @IsNumber({}, { message: '<PERSON><PERSON>ch thước tối đa phải là số' })
  @IsPositive({ message: 'Kích thước tối đa phải là số dương' })
  @Type(() => Number)
  maxSize: number;
}

/**
 * DTO cho phản hồi URL tải lên ảnh bìa
 */
export class CoverImageUploadResponseDto {
  @ApiProperty({
    description: 'URL tạm thời để tải lên ảnh bìa',
    example: 'https://example.com/presigned-url',
  })
  uploadUrl: string;

  @ApiProperty({
    description: 'Khóa S3 của ảnh bìa',
    example: 'cover-images/user-123/cover-1234567890.jpg',
  })
  coverImageKey: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của URL (giây)',
    example: 300,
  })
  expiresIn: number;
}


