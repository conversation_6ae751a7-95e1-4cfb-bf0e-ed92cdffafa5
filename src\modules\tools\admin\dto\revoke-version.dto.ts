import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc thu hồi version nâng cao
 */
export class RevokeVersionDto {
  @ApiProperty({
    description: 'Thông báo lý do thu hồi version',
    example: 'Version có lỗi nghiêm trọng và cần được thu hồi ngay lập tức',
  })
  @IsString()
  @MaxLength(1000)
  message: string;

  @ApiProperty({
    description: 'Thời hạn thu hồi version (ISO 8601 format)',
    example: '2024-12-31T23:59:59.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  revokeDeadline?: string;

  @ApiProperty({
    description: '<PERSON><PERSON> chú bổ sung cho việc thu hồi version',
    example: 'Người dùng cần cập nhật lên version mới nhất',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  notes?: string;
}
