import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { GoogleDocsIntegrationService } from '../services/google-docs-integration.service';
import { GoogleDocsApi } from '../api/google-docs.api';
import {
  GoogleDocsAuthUrlDto,
  GoogleDocsAuthUrlResponseDto,
  GoogleDocsOAuthCallbackDto,
  CreateGoogleDocsIntegrationDto,
  UpdateGoogleDocsIntegrationDto,
  GoogleDocsIntegrationResponseDto,
  GoogleDocsIntegrationsListResponseDto,
  GoogleDocsIntegrationQueryDto,
  GoogleDocumentsListQueryDto,
  GoogleDocumentsListResponseDto,
  GoogleDocumentInfoQueryDto,
  GoogleDocumentInfoResponseDto,
  GoogleDocumentReadQueryDto,
  GoogleDocumentContentResponseDto,
  GoogleDocumentCreateDto,
  GoogleDocumentUpdateDto,
  GoogleDocumentCreateResponseDto,
} from '../dto/google-docs';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';

/**
 * Controller xử lý các API liên quan đến Google Docs integration
 */
@ApiTags(SWAGGER_API_TAGS.USER_INTEGRATION)
@ApiExtraModels(
  ApiResponseDto,
  GoogleDocsIntegrationResponseDto,
  GoogleDocsIntegrationsListResponseDto,
  GoogleDocsAuthUrlResponseDto,
  GoogleDocumentsListResponseDto,
  GoogleDocumentInfoResponseDto,
  GoogleDocumentContentResponseDto,
  GoogleDocumentCreateResponseDto,
  PaginatedResult
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/integration/google-docs')
export class GoogleDocsIntegrationController {
  constructor(
    private readonly googleDocsIntegrationService: GoogleDocsIntegrationService,
    private readonly googleDocsApi: GoogleDocsApi,
  ) {}

  /**
   * Tạo URL xác thực OAuth cho Google Docs
   */
  @Post('auth-url')
  @ApiOperation({
    summary: 'Tạo URL xác thực OAuth cho Google Docs',
    description: 'Tạo URL để người dùng xác thực với Google Docs',
  })
  @ApiResponse({
    status: 200,
    description: 'URL xác thực được tạo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(GoogleDocsAuthUrlResponseDto) },
          },
        },
      ],
    },
  })
  async generateAuthUrl(
    @CurrentUser() user: JwtPayload,
    @Body() authDto: GoogleDocsAuthUrlDto,
  ): Promise<ApiResponseDto<GoogleDocsAuthUrlResponseDto>> {
    const result = await this.googleDocsIntegrationService.generateAuthUrl(user.id, authDto);
    return ApiResponseDto.success(result, 'URL xác thực được tạo thành công');
  }

  /**
   * Xử lý OAuth callback
   */
  @Post('oauth-callback')
  @ApiOperation({
    summary: 'Xử lý OAuth callback từ Google',
    description: 'Xử lý callback sau khi người dùng xác thực với Google Docs',
  })
  @ApiResponse({
    status: 200,
    description: 'OAuth callback được xử lý thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(GoogleDocsIntegrationResponseDto) },
          },
        },
      ],
    },
  })
  async handleOAuthCallback(
    @CurrentUser() user: JwtPayload,
    @Body() callbackDto: GoogleDocsOAuthCallbackDto,
  ): Promise<ApiResponseDto<GoogleDocsIntegrationResponseDto>> {
    const result = await this.googleDocsIntegrationService.handleOAuthCallback(user.id, callbackDto);
    return ApiResponseDto.success(result, 'Tích hợp Google Docs được tạo thành công');
  }

  /**
   * Tạo Google Docs integration
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo Google Docs integration',
    description: 'Tạo mới một tích hợp Google Docs',
  })
  @ApiResponse({
    status: 201,
    description: 'Integration được tạo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(GoogleDocsIntegrationResponseDto) },
          },
        },
      ],
    },
  })
  async createIntegration(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateGoogleDocsIntegrationDto,
  ): Promise<ApiResponseDto<GoogleDocsIntegrationResponseDto>> {
    const result = await this.googleDocsIntegrationService.createIntegration(user.id, createDto);
    return ApiResponseDto.success(result, 'Tích hợp Google Docs được tạo thành công');
  }

  /**
   * Lấy danh sách Google Docs integrations
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách Google Docs integrations',
    description: 'Lấy danh sách tất cả tích hợp Google Docs của người dùng',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách integrations được lấy thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(GoogleDocsIntegrationsListResponseDto) },
          },
        },
      ],
    },
  })
  async getIntegrations(
    @CurrentUser() user: JwtPayload,
    @Query() query: GoogleDocsIntegrationQueryDto,
  ): Promise<ApiResponseDto<GoogleDocsIntegrationsListResponseDto>> {
    // Implementation would call integration service to get list
    // For now, return empty list
    const result: GoogleDocsIntegrationsListResponseDto = {
      items: [],
      total: 0,
      page: query.page || 1,
      limit: query.limit || 10,
    };
    return ApiResponseDto.success(result, 'Danh sách integrations được lấy thành công');
  }

  /**
   * Cập nhật Google Docs integration
   */
  @Put(':integrationId')
  @ApiOperation({
    summary: 'Cập nhật Google Docs integration',
    description: 'Cập nhật thông tin tích hợp Google Docs',
  })
  @ApiResponse({
    status: 200,
    description: 'Integration được cập nhật thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(GoogleDocsIntegrationResponseDto) },
          },
        },
      ],
    },
  })
  async updateIntegration(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId', ParseUUIDPipe) integrationId: string,
    @Body() updateDto: UpdateGoogleDocsIntegrationDto,
  ): Promise<ApiResponseDto<GoogleDocsIntegrationResponseDto>> {
    const result = await this.googleDocsIntegrationService.updateIntegration(
      user.id,
      integrationId,
      updateDto,
    );
    return ApiResponseDto.success(result, 'Tích hợp Google Docs được cập nhật thành công');
  }

  /**
   * Xóa Google Docs integration
   */
  @Delete(':integrationId')
  @ApiOperation({
    summary: 'Xóa Google Docs integration',
    description: 'Xóa tích hợp Google Docs',
  })
  @ApiResponse({
    status: 200,
    description: 'Integration được xóa thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'object' },
          },
        },
      ],
    },
  })
  async deleteIntegration(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId', ParseUUIDPipe) integrationId: string,
  ): Promise<ApiResponseDto<{}>> {
    // Implementation would call integration service to delete
    return ApiResponseDto.success({}, 'Tích hợp Google Docs được xóa thành công');
  }

  /**
   * Làm mới access token
   */
  @Post(':integrationId/refresh-token')
  @ApiOperation({
    summary: 'Làm mới access token',
    description: 'Làm mới access token cho Google Docs integration',
  })
  @ApiResponse({
    status: 200,
    description: 'Token được làm mới thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                accessToken: { type: 'string' },
                expiresAt: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async refreshToken(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId', ParseUUIDPipe) integrationId: string,
  ): Promise<ApiResponseDto<{ accessToken: string; expiresAt?: number }>> {
    const result = await this.googleDocsIntegrationService.refreshToken(user.id, integrationId);
    return ApiResponseDto.success(result, 'Token được làm mới thành công');
  }

  /**
   * Thu hồi quyền truy cập
   */
  @Delete(':integrationId/revoke-access')
  @ApiOperation({
    summary: 'Thu hồi quyền truy cập',
    description: 'Thu hồi quyền truy cập Google Docs và xóa integration',
  })
  @ApiResponse({
    status: 200,
    description: 'Quyền truy cập được thu hồi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                revoked: { type: 'boolean' },
              },
            },
          },
        },
      ],
    },
  })
  async revokeAccess(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId', ParseUUIDPipe) integrationId: string,
  ): Promise<ApiResponseDto<{ revoked: boolean }>> {
    const revoked = await this.googleDocsIntegrationService.revokeAccess(user.id, integrationId);
    return ApiResponseDto.success({ revoked }, 'Quyền truy cập được thu hồi thành công');
  }

  /**
   * Lấy danh sách documents
   */
  @Get(':integrationId/documents')
  @ApiOperation({
    summary: 'Lấy danh sách documents',
    description: 'Lấy danh sách documents từ Google Drive',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách documents được lấy thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(GoogleDocumentsListResponseDto) },
          },
        },
      ],
    },
  })
  async getDocuments(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId', ParseUUIDPipe) integrationId: string,
    @Query() query: GoogleDocumentsListQueryDto,
  ): Promise<ApiResponseDto<GoogleDocumentsListResponseDto>> {
    const accessToken = await this.googleDocsIntegrationService.getValidAccessToken(user.id, integrationId);
    const result = await this.googleDocsApi.getDocumentsList(accessToken, query);
    return ApiResponseDto.success(result, 'Danh sách documents được lấy thành công');
  }

  /**
   * Lấy thông tin document
   */
  @Get(':integrationId/documents/:documentId')
  @ApiOperation({
    summary: 'Lấy thông tin document',
    description: 'Lấy thông tin chi tiết của một document',
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin document được lấy thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(GoogleDocumentInfoResponseDto) },
          },
        },
      ],
    },
  })
  async getDocumentInfo(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId', ParseUUIDPipe) integrationId: string,
    @Param('documentId') documentId: string,
    @Query() query: Omit<GoogleDocumentInfoQueryDto, 'documentId'>,
  ): Promise<ApiResponseDto<GoogleDocumentInfoResponseDto>> {
    const accessToken = await this.googleDocsIntegrationService.getValidAccessToken(user.id, integrationId);
    const fullQuery: GoogleDocumentInfoQueryDto = { ...query, documentId };
    const result = await this.googleDocsApi.getDocumentInfo(accessToken, fullQuery);
    return ApiResponseDto.success(result, 'Thông tin document được lấy thành công');
  }

  /**
   * Đọc nội dung document
   */
  @Get(':integrationId/documents/:documentId/content')
  @ApiOperation({
    summary: 'Đọc nội dung document',
    description: 'Đọc nội dung của một document',
  })
  @ApiResponse({
    status: 200,
    description: 'Nội dung document được đọc thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(GoogleDocumentContentResponseDto) },
          },
        },
      ],
    },
  })
  async readDocumentContent(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId', ParseUUIDPipe) integrationId: string,
    @Param('documentId') documentId: string,
    @Query() query: Omit<GoogleDocumentReadQueryDto, 'documentId'>,
  ): Promise<ApiResponseDto<GoogleDocumentContentResponseDto>> {
    const accessToken = await this.googleDocsIntegrationService.getValidAccessToken(user.id, integrationId);
    const fullQuery: GoogleDocumentReadQueryDto = { ...query, documentId };
    const result = await this.googleDocsApi.readDocumentContent(accessToken, fullQuery);

    // Update usage stats
    await this.googleDocsIntegrationService.updateUsageStats(
      user.id,
      integrationId,
      'read',
      1,
    );

    return ApiResponseDto.success(result, 'Nội dung document được đọc thành công');
  }

  /**
   * Tạo document mới
   */
  @Post(':integrationId/documents')
  @ApiOperation({
    summary: 'Tạo document mới',
    description: 'Tạo một document mới trên Google Docs',
  })
  @ApiResponse({
    status: 201,
    description: 'Document được tạo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(GoogleDocumentCreateResponseDto) },
          },
        },
      ],
    },
  })
  async createDocument(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId', ParseUUIDPipe) integrationId: string,
    @Body() createDto: GoogleDocumentCreateDto,
  ): Promise<ApiResponseDto<GoogleDocumentCreateResponseDto>> {
    const accessToken = await this.googleDocsIntegrationService.getValidAccessToken(user.id, integrationId);
    const result = await this.googleDocsApi.createDocument(accessToken, createDto);

    // Update usage stats
    await this.googleDocsIntegrationService.updateUsageStats(
      user.id,
      integrationId,
      'create',
      1,
    );

    return ApiResponseDto.success(result, 'Document được tạo thành công');
  }

  /**
   * Cập nhật nội dung document
   */
  @Put(':integrationId/documents/:documentId/content')
  @ApiOperation({
    summary: 'Cập nhật nội dung document',
    description: 'Cập nhật nội dung của một document',
  })
  @ApiResponse({
    status: 200,
    description: 'Nội dung document được cập nhật thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                updatedRange: { type: 'string' },
              },
            },
          },
        },
      ],
    },
  })
  async updateDocumentContent(
    @CurrentUser() user: JwtPayload,
    @Param('integrationId', ParseUUIDPipe) integrationId: string,
    @Param('documentId') documentId: string,
    @Body() updateDto: Omit<GoogleDocumentUpdateDto, 'documentId'>,
  ): Promise<ApiResponseDto<{ success: boolean; updatedRange?: string }>> {
    const accessToken = await this.googleDocsIntegrationService.getValidAccessToken(user.id, integrationId);
    const fullUpdateDto: GoogleDocumentUpdateDto = { ...updateDto, documentId };
    const result = await this.googleDocsApi.updateDocumentContent(accessToken, fullUpdateDto);

    // Update usage stats
    await this.googleDocsIntegrationService.updateUsageStats(
      user.id,
      integrationId,
      'update',
      1,
    );

    return ApiResponseDto.success(result, 'Nội dung document được cập nhật thành công');
  }
}
