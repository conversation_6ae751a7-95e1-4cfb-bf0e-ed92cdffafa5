import { AppException } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { FacebookBusinessConfig } from '@config';
import { AdAccount, Business, FacebookAdsApi } from 'facebook-nodejs-business-sdk';
import {
  FACEBOOK_BUSINESS_ERROR_CODES,
  createFacebookBusinessException,
  validateFacebookBusinessResponse,
  validateFacebookBusinessParams,
} from '../exceptions/facebook-business.exception';
import {
  FacebookAdAccount,
  FacebookBusinessAccount,
} from '../interfaces/facebook-business.interface';

/**
 * Service chính để tương tác với Facebook Business API
 */
@Injectable()
export class FacebookBusinessApiService {
  private readonly logger = new Logger(FacebookBusinessApiService.name);
  private config: FacebookBusinessConfig;

  constructor(private readonly configService: ConfigService) {
    this.initializeConfig();
  }

  /**
   * Khởi tạo cấu hình từ config service
   */
  private initializeConfig(): void {
    this.config = this.configService.get<FacebookBusinessConfig>('facebookBusiness')!;

    if (!this.config.appId || !this.config.appSecret) {
      this.logger.warn('Facebook Business API configuration is incomplete');
    }
  }

  /**
   * Tạo Facebook API instance với access token
   * @param accessToken Access token
   * @returns Facebook API instance
   */
  private createFacebookApiInstance(accessToken: string): typeof FacebookAdsApi {
    try {
      const api = FacebookAdsApi.init(accessToken);
      api.setDebug(this.config.debug);
      return api;
    } catch (error) {
      this.logger.error(`Failed to create Facebook API instance: ${error.message}`);
      throw new AppException(
        FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_ACCESS_TOKEN,
        'Không thể tạo Facebook API instance',
      );
    }
  }

  /**
   * Lấy thông tin Business Account
   * @param accessToken Access token
   * @param businessId ID của business account
   * @returns Thông tin business account
   */
  async getBusinessAccount(accessToken: string, businessId?: string): Promise<FacebookBusinessAccount> {
    try {
      validateFacebookBusinessParams({ accessToken }, ['accessToken']);

      const id = businessId || this.config.defaultBusinessAccountId;

      if (!id) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_MISSING_REQUIRED_FIELDS,
          'Business Account ID is required',
        );
      }

      this.logger.log(`Getting business account info for ID: ${id}`);

      // Create API instance with access token
      this.createFacebookApiInstance(accessToken);

      const business = new Business(id);
      const businessData = await business.get([
        'id',
        'name',
        'timezone_name',
        'primary_page',
        'created_time',
        'updated_time',
        'verification_status',
        'business_type',
      ]);

      validateFacebookBusinessResponse(businessData, ['id', 'name']);

      const result: FacebookBusinessAccount = {
        id: businessData.id,
        name: businessData.name,
        timezone_name: businessData.timezone_name,
        primary_page: businessData.primary_page,
        created_time: businessData.created_time,
        updated_time: businessData.updated_time,
        verification_status: businessData.verification_status,
        business_type: businessData.business_type,
      };

      this.logger.log(`Successfully retrieved business account: ${result.name}`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting business account: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy thông tin Business Account',
        { businessId },
      );
    }
  }

  /**
   * Lấy danh sách Ad Accounts thuộc Business
   * @param accessToken Access token
   * @param businessId ID của business account
   * @returns Danh sách ad accounts
   */
  async getAdAccounts(accessToken: string, businessId?: string): Promise<FacebookAdAccount[]> {
    try {
      validateFacebookBusinessParams({ accessToken }, ['accessToken']);

      const id = businessId || this.config.defaultBusinessAccountId;

      if (!id) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_MISSING_REQUIRED_FIELDS,
          'Business Account ID is required',
        );
      }

      this.logger.log(`Getting ad accounts for business ID: ${id}`);

      // Create API instance with access token
      this.createFacebookApiInstance(accessToken);

      const business = new Business(id);
      const adAccounts = await business.getOwnedAdAccounts([
        'id',
        'name',
        'account_status',
        'currency',
        'timezone_id',
        'timezone_name',
        'business',
        'balance',
        'amount_spent',
        'spend_cap',
        'capabilities',
        'created_time',
      ]);

      const result: FacebookAdAccount[] = adAccounts.map((account: any) => ({
        id: account.id,
        name: account.name,
        account_status: account.account_status,
        currency: account.currency,
        timezone_id: account.timezone_id,
        timezone_name: account.timezone_name,
        business: account.business,
        balance: account.balance,
        amount_spent: account.amount_spent,
        spend_cap: account.spend_cap,
        capabilities: account.capabilities,
        created_time: account.created_time,
      }));

      this.logger.log(`Successfully retrieved ${result.length} ad accounts`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting ad accounts: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy danh sách Ad Accounts',
        { businessId },
      );
    }
  }

  /**
   * Lấy thông tin Ad Account cụ thể
   * @param accessToken Access token
   * @param adAccountId ID của ad account
   * @returns Thông tin ad account
   */
  async getAdAccount(accessToken: string, adAccountId?: string): Promise<FacebookAdAccount> {
    try {
      validateFacebookBusinessParams({ accessToken }, ['accessToken']);

      const id = adAccountId || this.config.defaultAdAccountId;

      if (!id) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_MISSING_REQUIRED_FIELDS,
          'Ad Account ID is required',
        );
      }

      this.logger.log(`Getting ad account info for ID: ${id}`);

      // Create API instance with access token
      this.createFacebookApiInstance(accessToken);

      // Ensure ID has 'act_' prefix
      const accountId = id.startsWith('act_') ? id : `act_${id}`;

      const adAccount = new AdAccount(accountId);
      const accountData = await adAccount.get([
        'id',
        'name',
        'account_status',
        'currency',
        'timezone_id',
        'timezone_name',
        'business',
        'balance',
        'amount_spent',
        'spend_cap',
        'capabilities',
        'created_time',
      ]);

      validateFacebookBusinessResponse(accountData, ['id', 'name']);

      const result: FacebookAdAccount = {
        id: accountData.id,
        name: accountData.name,
        account_status: accountData.account_status,
        currency: accountData.currency,
        timezone_id: accountData.timezone_id,
        timezone_name: accountData.timezone_name,
        business: accountData.business,
        balance: accountData.balance,
        amount_spent: accountData.amount_spent,
        spend_cap: accountData.spend_cap,
        capabilities: accountData.capabilities,
        created_time: accountData.created_time,
      };

      this.logger.log(`Successfully retrieved ad account: ${result.name}`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting ad account: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy thông tin Ad Account',
        { adAccountId },
      );
    }
  }

  /**
   * Kiểm tra kết nối Facebook Business API
   * @param accessToken Access token để test
   * @returns True nếu kết nối thành công
   */
  async testConnection(accessToken: string): Promise<boolean> {
    try {
      validateFacebookBusinessParams({ accessToken }, ['accessToken']);

      // Create API instance with access token
      const api = this.createFacebookApiInstance(accessToken);

      // Test by getting current user info
      const response = await api.call('GET', '/me', {
        fields: 'id,name',
      });

      if (response && response.id) {
        this.logger.log('Facebook Business API connection test successful');
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Facebook Business API connection test failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Lấy cấu hình hiện tại
   * @returns Facebook Business config
   */
  getConfig(): FacebookBusinessConfig {
    return { ...this.config };
  }

  /**
   * Thực hiện API call với retry logic
   * @param accessToken Access token
   * @param method HTTP method
   * @param endpoint API endpoint
   * @param params Parameters
   * @param retries Number of retries
   * @returns API response
   */
  async makeApiCall(
    accessToken: string,
    method: string,
    endpoint: string,
    params: Record<string, unknown> = {},
    retries: number = 3,
  ): Promise<any> {
    let lastError: any;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        this.logger.debug(`Making API call: ${method} ${endpoint} (attempt ${attempt + 1})`);

        // Create API instance with access token
        const api = this.createFacebookApiInstance(accessToken);
        const response = await api.call(method, endpoint, params);

        this.logger.debug(`API call successful: ${method} ${endpoint}`);
        return response;
      } catch (error) {
        lastError = error;

        this.logger.warn(
          `API call failed (attempt ${attempt + 1}/${retries + 1}): ${error.message}`,
        );

        // Don't retry on certain errors
        const errorCode = error?.response?.data?.error?.code;
        if (errorCode === 190 || errorCode === 200) {
          // Invalid token or permission errors - don't retry
          break;
        }

        // Wait before retry (exponential backoff)
        if (attempt < retries) {
          const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw createFacebookBusinessException(
      lastError,
      `API call failed after ${retries + 1} attempts`,
      { method, endpoint, params },
    );
  }

  /**
   * Validate required configuration
   * @throws AppException if configuration is invalid
   */
  validateConfig(): void {
    const requiredFields = ['appId', 'appSecret', 'accessToken'];

    for (const field of requiredFields) {
      if (!this.config[field as keyof FacebookBusinessConfig]) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_CONFIGURATION_ERROR,
          `Missing required configuration: ${field}`,
        );
      }
    }
  }

  /**
   * Get AdAccount instance
   * @param accessToken Access token
   * @param adAccountId Ad Account ID
   * @returns AdAccount instance
   */
  getAdAccountInstance(accessToken: string, adAccountId?: string): any {
    validateFacebookBusinessParams({ accessToken }, ['accessToken']);

    const id = adAccountId || this.config.defaultAdAccountId;

    if (!id) {
      throw new AppException(
        FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_MISSING_REQUIRED_FIELDS,
        'Ad Account ID is required',
      );
    }

    // Create API instance with access token
    this.createFacebookApiInstance(accessToken);

    // Ensure ID has 'act_' prefix
    const accountId = id.startsWith('act_') ? id : `act_${id}`;
    return new AdAccount(accountId);
  }
}
