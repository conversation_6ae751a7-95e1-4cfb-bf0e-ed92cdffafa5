import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
  Min,
  ValidateNested
} from 'class-validator';
import {
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum,
  FeatureEnum
} from '../../../constants/model-capabilities.enum';
import { ModelPricingDto } from './create-model-registry.dto';

/**
 * DTO cho việc cập nhật model registry
 */
export class UpdateModelRegistryDto {

  /**
   * Tên mẫu đại diện của model
   */
  @ApiPropertyOptional({
    description: 'Tên mẫu đại diện của model (phải unique và hợp lệ với provider)',
    example: 'gpt-4-turbo',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  modelBaseId?: string;

  /**
   * <PERSON><PERSON><PERSON> loại dữ liệu đầu vào hỗ trợ cho base model
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu vào hỗ trợ cho base model',
    enum: InputModalityEnum,
    isArray: true,
    example: [InputModalityEnum.TEXT, InputModalityEnum.IMAGE],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(InputModalityEnum, { each: true })
  inputModalitiesBase?: InputModalityEnum[];

  /**
   * Các loại dữ liệu đầu vào hỗ trợ cho fine-tuned model
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu vào hỗ trợ cho fine-tuned model',
    enum: InputModalityEnum,
    isArray: true,
    example: [InputModalityEnum.TEXT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(InputModalityEnum, { each: true })
  inputModalitiesFineTune?: InputModalityEnum[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ cho base model
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu ra hỗ trợ cho base model',
    enum: OutputModalityEnum,
    isArray: true,
    example: [OutputModalityEnum.TEXT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(OutputModalityEnum, { each: true })
  outputModalitiesBase?: OutputModalityEnum[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ cho fine-tuned model
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu ra hỗ trợ cho fine-tuned model',
    enum: OutputModalityEnum,
    isArray: true,
    example: [OutputModalityEnum.TEXT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(OutputModalityEnum, { each: true })
  outputModalitiesFineTune?: OutputModalityEnum[];

  /**
   * Các tham số sampling hỗ trợ cho base model
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling hỗ trợ cho base model',
    enum: SamplingParameterEnum,
    isArray: true,
    example: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SamplingParameterEnum, { each: true })
  samplingParametersBase?: SamplingParameterEnum[];

  /**
   * Các tham số sampling hỗ trợ cho fine-tuned model
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling hỗ trợ cho fine-tuned model',
    enum: SamplingParameterEnum,
    isArray: true,
    example: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SamplingParameterEnum, { each: true })
  samplingParametersFineTune?: SamplingParameterEnum[];

  /**
   * Các feature đặc biệt hỗ trợ cho base model
   */
  @ApiPropertyOptional({
    description: 'Các feature đặc biệt hỗ trợ cho base model',
    enum: FeatureEnum,
    isArray: true,
    example: [FeatureEnum.TOOL_CALL],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(FeatureEnum, { each: true })
  featuresBase?: FeatureEnum[];

  /**
   * Các feature đặc biệt hỗ trợ cho fine-tuned model
   */
  @ApiPropertyOptional({
    description: 'Các feature đặc biệt hỗ trợ cho fine-tuned model',
    enum: FeatureEnum,
    isArray: true,
    example: [FeatureEnum.TOOL_CALL],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(FeatureEnum, { each: true })
  featuresFineTune?: FeatureEnum[];

  /**
   * Giá cơ bản cho model (input/output rate)
   */
  @ApiPropertyOptional({
    description: 'Giá cơ bản cho model',
    type: ModelPricingDto,
    example: { inputRate: 1, outputRate: 2 },
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ModelPricingDto)
  basePricing?: ModelPricingDto;

  /**
   * Giá fine-tune cho model (input/output rate)
   */
  @ApiPropertyOptional({
    description: 'Giá fine-tune cho model',
    type: ModelPricingDto,
    example: { inputRate: 2, outputRate: 4 },
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ModelPricingDto)
  fineTunePricing?: ModelPricingDto;

  /**
   * Giá training cho model
   */
  @ApiPropertyOptional({
    description: 'Giá training cho model',
    example: 0,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  trainingPricing?: number;

  /**
   * Có hỗ trợ fine-tuning không
   */
  @ApiPropertyOptional({
    description: 'Có hỗ trợ fine-tuning không',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  fineTune?: boolean;

  /**
   * Số tokens tối đa có thể sinh ra
   */
  @ApiPropertyOptional({
    description: 'Số tokens tối đa có thể sinh ra',
    example: 1000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxTokens?: number;

  /**
   * Độ dài context tối đa
   */
  @ApiPropertyOptional({
    description: 'Độ dài context tối đa',
    example: 1000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  contexWindow?: number;
}
