/**
 * Enum định nghĩa context của fine-tuning job
 */
export enum FineTuneContextEnum {
  /**
   * Context của user
   */
  USER = 'USER',

  /**
   * Context của admin
   */
  ADMIN = 'ADMIN',
}

/**
 * Enum định nghĩa loại model
 */
export enum ModelTypeEnum {
  /**
   * System model
   */
  SYSTEM = 'SYSTEM',

  /**
   * User model
   */
  USER = 'USER',
}

/**
 * Enum định nghĩa các tên job trong queue fine-tuning
 */
export enum FineTuneJobName {
  /**
   * Job fine-tune với upload data trong Worker
   */
  FINE_TUNE_UPLOAD_DATA = 'fine-tune-upload-data',

  /**
   * Job fine-tune với file đã upload sẵn
   */
  FINE_TUNE_PROCESS = 'fine-tune-process',

  /**
   * Job monitor fine-tuning status
   */
  FINE_TUNE_MONITOR = 'fine-tune-monitor',
}
