import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Unique,
  BeforeUpdate,
  Index
} from 'typeorm';
import {
  IsString,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsObject,
  MaxLength,
  MinLength,
  IsUUID,
  ValidateNested
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Entity đại diện cho bảng workflows trong cơ sở dữ liệu
 * Bảng chính chứa định nghĩa của các workflow
 * Enhanced với proper indexes theo database schema và validation
 */
@Entity('workflows')
@Unique(['userId', 'name'])
@Index(['userId', 'isActive'])
@Index(['createdAt'])
@Index(['updatedAt'])
export class Workflow {
  /**
   * ID định danh duy nhất cho một workflow
   */
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  id: string;

  /**
   * Kh<PERSON>a ngoại, liên kết đến người dùng sở hữu workflow
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  @IsOptional()
  @IsNumber()
  userId: number | null;

  /**
   * Khóa ngoại, liên kết đến nhân viên chịu trách nhiệm cho workflow
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: true })
  @IsOptional()
  @IsNumber()
  employeeId: number | null;

  /**
   * Tên của workflow, phải là duy nhất cho mỗi người dùng
   */
  @Column({ type: 'varchar', length: 255 })
  @IsString()
  @MinLength(1, { message: 'Workflow name cannot be empty' })
  @MaxLength(255, { message: 'Workflow name cannot exceed 255 characters' })
  name: string;

  /**
   * Trạng thái kích hoạt của workflow (true = đang chạy, false = đã tắt)
   */
  @Column({ name: 'is_active', type: 'boolean', default: false, nullable: false })
  @IsBoolean()
  isActive: boolean;

  /**
   * Thời gian tạo workflow, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  @IsNumber()
  createdAt: number;

  /**
   * Thời gian cập nhật workflow lần cuối, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  @IsNumber()
  updatedAt: number;

  /**
   * Lược đồ Workflow Tổng thể - lưu trữ định nghĩa cấu trúc workflow
   * Enhanced với validation để ensure proper structure
   */
  @Column({
    name: 'definition',
    type: 'jsonb',
    default: () => "'{}'::jsonb",
    nullable: false
  })
  @IsObject()
  @ValidateNested()
  definition: Record<string, any>;

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = Date.now();
  }
}
