import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsBoolean,
  IsNumber,
  IsEnum,
  Min,
  Max
} from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto';

/**
 * Sort direction enum
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

/**
 * Sort fields enum for workflows
 */
export enum WorkflowSortField {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  IS_ACTIVE = 'isActive'
}

/**
 * DTO for querying workflows with pagination, search, and filtering
 * Follows existing pagination patterns from the codebase
 */
export class QueryWorkflowDto extends QueryDto {

  /**
   * Filter by active status
   */
  @ApiPropertyOptional({
    description: 'Filter by active status',
    example: true
  })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isActive?: boolean;

  /**
   * Sort field
   */
  @ApiPropertyOptional({
    description: 'Sort field',
    enum: WorkflowSortField,
    example: WorkflowSortField.CREATED_AT,
    default: WorkflowSortField.CREATED_AT
  })
  @IsOptional()
  @IsEnum(WorkflowSortField)
  sortBy?: WorkflowSortField = WorkflowSortField.CREATED_AT;

  /**
   * Sort direction
   */
  @ApiPropertyOptional({
    description: 'Sort direction',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
