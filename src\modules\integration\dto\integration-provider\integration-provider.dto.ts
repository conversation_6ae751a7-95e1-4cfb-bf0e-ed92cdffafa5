import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsObject, IsNumber } from 'class-validator';
import { ProviderEnum } from '../../constants/provider.enum';

/**
 * DTO cho việc tạo Integration Provider
 */
export class CreateIntegrationProviderDto {
  /**
   * Loại provider
   */
  @ApiProperty({
    description: 'Loại provider',
    enum: ProviderEnum,
    example: ProviderEnum.FACEBOOK_PAGE,
  })
  @IsEnum(ProviderEnum, { message: 'type phải là một giá trị hợp lệ trong ProviderEnum' })
  type: ProviderEnum;

  /**
   * Schema MCP (tùy chọn)
   */
  @ApiPropertyOptional({
    description: 'Schema MCP dưới dạng JSON',
    type: Object
  })
  @IsOptional()
  @IsObject({ message: 'mcpSchema phải là object' })
  mcpSchema?: Record<string, any>;

  /**
   * ID người tạo
   */
  @ApiPropertyOptional({
    description: 'ID người tạo bản ghi',
    example: 1,
  })
  @IsOptional()
  @IsNumber({}, { message: 'createdBy phải là số' })
  createdBy?: number;
}

/**
 * DTO cho việc cập nhật Integration Provider
 */
export class UpdateIntegrationProviderDto {
  /**
   * Schema MCP (tùy chọn)
   */
  @ApiPropertyOptional({
    description: 'Schema MCP dưới dạng JSON',
    type: Object
  })
  @IsOptional()
  @IsObject({ message: 'mcpSchema phải là object' })
  mcpSchema?: Record<string, any>;

  /**
   * ID người cập nhật
   */
  @ApiPropertyOptional({
    description: 'ID người cập nhật bản ghi',
    example: 1,
  })
  @IsOptional()
  @IsNumber({}, { message: 'updatedBy phải là số' })
  updatedBy?: number;
}

/**
 * DTO cho response Integration Provider
 */
export class IntegrationProviderResponseDto {
  /**
   * ID của provider
   */
  @ApiProperty({
    description: 'ID của provider',
    example: 1,
  })
  id: number;

  /**
   * Loại provider
   */
  @ApiProperty({
    description: 'Loại provider',
    enum: ProviderEnum,
    example: ProviderEnum.FACEBOOK_PAGE,
  })
  type: ProviderEnum;

  /**
   * Schema MCP
   */
  @ApiPropertyOptional({
    description: 'Schema MCP dưới dạng JSON',
    type: Object
  })
  mcpSchema: Record<string, any> | null;

  /**
   * Thời gian tạo (timestamp milliseconds)
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp milliseconds)',
    example: 1703980800000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (timestamp milliseconds)
   */
  @ApiPropertyOptional({
    description: 'Thời gian cập nhật (timestamp milliseconds)',
    example: 1703980800000,
    nullable: true,
  })
  updatedAt: number | null;

  /**
   * ID người tạo
   */
  @ApiPropertyOptional({
    description: 'ID người tạo bản ghi',
    example: 1,
    nullable: true,
  })
  createdBy: number | null;

  /**
   * ID người cập nhật
   */
  @ApiPropertyOptional({
    description: 'ID người cập nhật bản ghi',
    example: 1,
    nullable: true,
  })
  updatedBy: number | null;
}

/**
 * DTO cho query Integration Provider
 */
export class IntegrationProviderQueryDto {
  /**
   * Trang hiện tại
   */
  @ApiPropertyOptional({
    description: 'Trang hiện tại',
    example: 1,
    default: 1,
  })
  @IsOptional()
  @IsNumber({}, { message: 'page phải là số' })
  page?: number = 1;

  /**
   * Số lượng items trên một trang
   */
  @ApiPropertyOptional({
    description: 'Số lượng items trên một trang',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @IsNumber({}, { message: 'limit phải là số' })
  limit?: number = 10;

  /**
   * Loại provider để filter
   */
  @ApiPropertyOptional({
    description: 'Loại provider để filter',
    enum: ProviderEnum,
    example: ProviderEnum.FACEBOOK_PAGE,
  })
  @IsOptional()
  @IsEnum(ProviderEnum, { message: 'type phải là một giá trị hợp lệ trong ProviderEnum' })
  type?: ProviderEnum;
}
