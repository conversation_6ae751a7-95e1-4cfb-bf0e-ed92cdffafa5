import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Queue } from 'bullmq';
import { QueueName, EmailMarketingJobName } from '@/shared/queue/queue.constants';

import { AppException } from '@/common/exceptions/app.exception';
import { MARKETING_ADMIN_ERROR_CODES } from '../exceptions/marketing-admin.exception';
import { Integration } from '@/modules/integration/entities/integration.entity';
import { KeyPairEncryptionService } from '@/shared/services/encryption';
import { AdminEmailCampaignRepository } from '../repositories/admin-email-campaign.repository';
import { AdminEmailCampaignStatsRepository } from '../repositories/admin-email-campaign-stats.repository';
import { AdminAudienceRepository } from '../repositories/admin-audience.repository';
import { AdminSegmentRepository } from '../repositories/admin-segment.repository';
import { UserAudienceRepository } from '../../user/repositories/user-audience.repository';
import { AdminEmailCampaign } from '../entities/admin-email-campaign.entity';
import { AdminEmailCampaignStats } from '../entities/admin-email-campaign-stats.entity';
import { AdminAudience } from '../entities/admin-audience.entity';
import { AdminSegment } from '../entities/admin-segment.entity';
import { UserAudience } from '../../user/entities/user-audience.entity';
import { AdminTemplateEmail } from '../entities/admin-template-email.entity';
import { User } from '@/modules/user/entities/user.entity';
import { UserRepository } from '@/modules/user/repositories/user.repository';
import { AdminEmailCampaignStatus } from '../types/admin-email-campaign.types';
import {
  CreateAdminEmailCampaignDto,
  CreateAdminEmailCampaignResponseDto,
  UpdateAdminEmailCampaignDto,
  UpdateAdminEmailCampaignResponseDto,
  AdminEmailCampaignQueryDto,
  AdminEmailCampaignDetailDto,
  AdminEmailCampaignItemDto,
  AdminEmailCampaignStatsDto,
  AdminEmailCampaignOverallStatsDto,
  BulkDeleteAdminEmailCampaignResponseDto,
} from '../dto/admin-email-campaign';
import { PaginatedResult, PaginationMeta } from '@/common/response';
import {
  AdminEmailCampaignJobDto,
  BatchAdminEmailCampaignJobDto,
  AdminEmailRecipientDto
} from '../dto/admin-email-campaign-job.dto';
import { FindManyOptions, FindOptionsWhere, Between, Like, In, Or } from 'typeorm';
import { PauseAdminEmailCampaignResponseDto, ResumeAdminEmailCampaignResponseDto } from '../dto/admin-email-campaign/pause-admin-email-campaign.dto';

/**
 * Service xử lý logic business cho admin email campaign
 */
@Injectable()
export class AdminEmailCampaignService {
  private readonly logger = new Logger(AdminEmailCampaignService.name);

  constructor(
    private readonly adminEmailCampaignRepository: AdminEmailCampaignRepository,
    private readonly adminEmailCampaignStatsRepository: AdminEmailCampaignStatsRepository,
    private readonly adminAudienceRepository: AdminAudienceRepository,
    private readonly adminSegmentRepository: AdminSegmentRepository,
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly userRepository: UserRepository,
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    @InjectRepository(AdminTemplateEmail)
    private readonly adminTemplateEmailRepository: Repository<AdminTemplateEmail>,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
    @InjectQueue(QueueName.EMAIL_MARKETING)
    private readonly emailMarketingQueue: Queue,
  ) {}

  /**
   * Tạo mới admin email campaign
   * @param createDto Dữ liệu tạo campaign
   * @param employeeId ID của nhân viên tạo
   * @returns Campaign đã tạo
   */
  async create(
    createDto: CreateAdminEmailCampaignDto,
    employeeId: number
  ): Promise<CreateAdminEmailCampaignResponseDto> {
    this.logger.debug(`Tạo admin email campaign: ${createDto.name}`);

    // Validate dữ liệu đầu vào
    await this.validateCreateData(createDto);

    const currentTime = Math.floor(Date.now() / 1000);

    // Tính tổng số recipients
    const totalRecipients = await this.calculateTotalRecipients(createDto);

    // Xác định trạng thái campaign
    let campaignStatus: AdminEmailCampaignStatus;

    if (createDto.status) {
      // Nếu có truyền status thì sử dụng status đó
      campaignStatus = createDto.status;
    } else if (createDto.scheduledAt) {
      // Nếu có scheduledAt nhưng không có status, tự động set thành SCHEDULED
      campaignStatus = AdminEmailCampaignStatus.SCHEDULED;
    } else {
      // Mặc định là SENDING - gửi ngay lập tức
      campaignStatus = AdminEmailCampaignStatus.SENDING;
    }

    // Tạo email list từ tất cả các nguồn
    const emailList = await this.generateEmailListFromAllSources(createDto);

    // Resolve content từ template nếu có templateId
    let resolvedContent = createDto.content;
    let resolvedSubject = createDto.subject;
    if (createDto.templateId) {
      const templateData = await this.resolveTemplateContent(createDto.templateId, createDto.templateVariables);
      resolvedContent = templateData.content;
      resolvedSubject = templateData.subject;
    }

    // Lấy cấu hình email server từ integrationId nếu có
    let emailServerConfig: any = undefined;
    if (createDto.integrationId) {
      try {
        emailServerConfig = await this.getEmailServerConfigFromIntegration(createDto.integrationId);
        this.logger.debug(`Đã lấy cấu hình email server từ integration ${createDto.integrationId}`);
      } catch (error) {
        this.logger.error(`Lỗi khi lấy cấu hình email server từ integration ${createDto.integrationId}: ${error.message}`);
        throw error;
      }
    }

    // Tạo campaign
    const campaignData: Partial<AdminEmailCampaign> = {
      ...createDto,
      subject: resolvedSubject, // Sử dụng subject từ template nếu có
      content: resolvedContent, // Sử dụng content từ template nếu có
      emailList, // Ghi đè emailList với danh sách đã tạo
      emailServerConfig, // Lưu cấu hình email server đã giải mã
      status: campaignStatus,
      totalRecipients,
      createdBy: employeeId,
      createdAt: currentTime,
    };

    const savedCampaign = await this.adminEmailCampaignRepository.create(campaignData);

    // Tạo thống kê ban đầu
    await this.createInitialStats(savedCampaign.id);

    this.logger.log(`Đã tạo admin email campaign với ID: ${savedCampaign.id}, status: ${campaignStatus}`);

    // Nếu status là SCHEDULED hoặc SENDING, tự động thực thi campaign
    if (campaignStatus === 'SCHEDULED' || campaignStatus === 'SENDING') {
      this.logger.log(`Campaign ${savedCampaign.id} có status ${campaignStatus}, tiến hành tạo jobs...`);

      // Kiểm tra có email list để gửi
      if (!savedCampaign.emailList || savedCampaign.emailList.length === 0) {
        this.logger.warn(`Campaign ${savedCampaign.id} không có email list để gửi`);
        return {
          id: savedCampaign.id,
          name: savedCampaign.name,
          subject: savedCampaign.subject,
          status: savedCampaign.status,
          totalRecipients: savedCampaign.totalRecipients,
          jobCount: 0,
          scheduledAt: savedCampaign.scheduledAt || undefined,
          createdAt: savedCampaign.createdAt,
          createdBy: savedCampaign.createdBy,
        };
      }

      // Tạo jobs và đẩy vào queue
      const jobIds = await this.createEmailJobs(savedCampaign, savedCampaign.emailList);

      return {
        id: savedCampaign.id,
        name: savedCampaign.name,
        subject: savedCampaign.subject,
        status: campaignStatus, // Sử dụng status đã xác định
        totalRecipients: savedCampaign.totalRecipients,
        jobCount: jobIds.length,
        jobIds,
        scheduledAt: savedCampaign.scheduledAt || undefined,
        createdAt: savedCampaign.createdAt,
        createdBy: savedCampaign.createdBy,
      };
    }

    // Nếu status là DRAFT hoặc các status khác, chỉ trả về thông tin campaign
    return {
      id: savedCampaign.id,
      name: savedCampaign.name,
      subject: savedCampaign.subject,
      status: savedCampaign.status,
      totalRecipients: savedCampaign.totalRecipients,
      jobCount: 0, // DRAFT không có job
      scheduledAt: savedCampaign.scheduledAt || undefined,
      createdAt: savedCampaign.createdAt,
      createdBy: savedCampaign.createdBy,
    };
  }

  /**
   * Cập nhật admin email campaign
   * @param id ID của campaign
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns Campaign đã cập nhật
   */
  async update(
    id: number,
    updateDto: UpdateAdminEmailCampaignDto,
    employeeId: number
  ): Promise<UpdateAdminEmailCampaignResponseDto> {
    this.logger.debug(`Cập nhật admin email campaign ID: ${id}`);

    const campaign = await this.adminEmailCampaignRepository.findById(id);
    if (!campaign) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_NOT_FOUND, `Campaign với ID ${id} không tồn tại`);
    }

    // Kiểm tra quyền cập nhật (chỉ người tạo hoặc admin có thể cập nhật)
    if (campaign.createdBy !== employeeId) {
      // TODO: Thêm logic kiểm tra quyền admin
    }

    // Kiểm tra trạng thái có thể cập nhật
    if (['SENDING', 'COMPLETED'].includes(campaign.status)) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.CANNOT_UPDATE_SENDING_CAMPAIGN);
    }

    const currentTime = Math.floor(Date.now() / 1000);

    // Validate template exists if templateId provided
    if (updateDto.templateId) {
      const template = await this.adminTemplateEmailRepository.findOne({
        where: { id: updateDto.templateId }
      });
      if (!template) {
        throw new AppException(MARKETING_ADMIN_ERROR_CODES.TEMPLATE_NOT_FOUND, `Template với ID ${updateDto.templateId} không tồn tại`);
      }
    }

    // Resolve content từ template nếu có templateId
    let resolvedContent = updateDto.content;
    let resolvedSubject = updateDto.subject;
    if (updateDto.templateId) {
      const templateData = await this.resolveTemplateContent(updateDto.templateId, updateDto.templateVariables);
      resolvedContent = templateData.content;
      resolvedSubject = templateData.subject;
    }

    // Cập nhật dữ liệu
    Object.assign(campaign, updateDto, {
      subject: resolvedSubject || campaign.subject, // Chỉ cập nhật nếu có giá trị mới
      content: resolvedContent || campaign.content, // Chỉ cập nhật nếu có giá trị mới
      updatedBy: employeeId,
      updatedAt: currentTime,
    });

    // Tính lại tổng recipients nếu có thay đổi
    if (updateDto.emailList || updateDto.segmentId !== undefined ||
        updateDto.audienceIds !== undefined || updateDto.userIds !== undefined) {
      campaign.totalRecipients = await this.calculateTotalRecipients(updateDto);
    }

    const savedCampaign = await this.adminEmailCampaignRepository.save(campaign);

    return {
      id: savedCampaign.id,
      name: savedCampaign.name,
      subject: savedCampaign.subject,
      status: savedCampaign.status,
      totalRecipients: savedCampaign.totalRecipients,
      scheduledAt: savedCampaign.scheduledAt || undefined,
      updatedAt: savedCampaign.updatedAt || Date.now(),
      updatedBy: savedCampaign.updatedBy || employeeId,
    };
  }

  /**
   * Lấy danh sách admin email campaign với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách campaign với phân trang
   */
  async findAll(queryDto: AdminEmailCampaignQueryDto): Promise<PaginatedResult<AdminEmailCampaignItemDto>> {
    this.logger.debug('Lấy danh sách admin email campaign với phân trang');

    const { page = 1, limit = 20, sortBy = 'createdAt', sortDirection = 'DESC' } = queryDto;
    const offset = (page - 1) * limit;

    // Tạo điều kiện where
    const where = this.buildWhereConditions(queryDto);

    // Tạo options cho query
    const options: FindManyOptions<AdminEmailCampaign> = {
      where,
      order: { [sortBy]: sortDirection },
      skip: offset,
      take: limit,
    };

    const [campaigns, total] = await this.adminEmailCampaignRepository.findAndCount(options);

    // Map sang DTO
    const items = await Promise.all(
      campaigns.map(campaign => this.mapToItemDto(campaign))
    );

    const meta: PaginationMeta = {
      totalItems: total,
      itemCount: items.length,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
    };

    return {
      items,
      meta,
    };
  }

  /**
   * Lấy chi tiết admin email campaign
   * @param id ID của campaign
   * @returns Chi tiết campaign
   */
  async findById(id: number): Promise<AdminEmailCampaignDetailDto> {
    this.logger.debug(`Lấy chi tiết admin email campaign ID: ${id}`);

    const campaign = await this.adminEmailCampaignRepository.findById(id);
    if (!campaign) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_NOT_FOUND, `Campaign với ID ${id} không tồn tại`);
    }

    return this.mapToDetailDto(campaign);
  }

  /**
   * Lấy thống kê campaign
   * @param campaignId ID của campaign
   * @returns Thống kê campaign
   */
  async getStats(campaignId: number): Promise<AdminEmailCampaignStatsDto> {
    this.logger.debug(`Lấy thống kê admin email campaign ID: ${campaignId}`);

    const stats = await this.adminEmailCampaignStatsRepository.findByCampaignId(campaignId);
    if (!stats) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_STATS_NOT_FOUND, `Thống kê cho campaign ${campaignId} không tồn tại`);
    }

    return this.mapToStatsDto(stats);
  }

  /**
   * Lấy tổng thống kê tất cả campaign
   * @returns Tổng thống kê
   */
  async getOverallStats(): Promise<AdminEmailCampaignOverallStatsDto> {
    this.logger.debug('Lấy tổng thống kê admin email campaign');

    const totalStats = await this.adminEmailCampaignStatsRepository.getTotalStats();
    
    // Đếm số campaign theo trạng thái
    const totalCampaigns = await this.adminEmailCampaignRepository.repository.count();
    const activeCampaigns = await this.adminEmailCampaignRepository.countByStatus(AdminEmailCampaignStatus.SENDING);
    const completedCampaigns = await this.adminEmailCampaignRepository.countByStatus(AdminEmailCampaignStatus.COMPLETED);
    const failedCampaigns = await this.adminEmailCampaignRepository.countByStatus(AdminEmailCampaignStatus.FAILED);

    return {
      ...totalStats,
      totalCampaigns,
      activeCampaigns,
      completedCampaigns,
      failedCampaigns,
    };
  }

  /**
   * Xóa admin email campaign
   * @param id ID của campaign
   * @param employeeId ID của nhân viên xóa
   */
  async delete(id: number, employeeId: number): Promise<void> {
    this.logger.debug(`Xóa admin email campaign ID: ${id}`);

    const campaign = await this.adminEmailCampaignRepository.findById(id);
    if (!campaign) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_NOT_FOUND, `Campaign với ID ${id} không tồn tại`);
    }

    // Kiểm tra quyền xóa
    if (campaign.createdBy !== employeeId) {
      // TODO: Thêm logic kiểm tra quyền admin
    }

    // Kiểm tra trạng thái có thể xóa
    if (campaign.status === AdminEmailCampaignStatus.SENDING) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.CANNOT_UPDATE_SENDING_CAMPAIGN, 'Không thể xóa campaign đang gửi');
    }

    // Xóa thống kê trước
    await this.adminEmailCampaignStatsRepository.deleteByCampaignId(id);

    // Xóa campaign
    await this.adminEmailCampaignRepository.delete(id);

    this.logger.log(`Đã xóa admin email campaign ID: ${id}`);
  }

  /**
   * Xóa nhiều admin email campaign
   * @param ids Danh sách ID campaign
   * @param employeeId ID của nhân viên xóa
   * @returns Kết quả xóa
   */
  async bulkDelete(ids: number[], employeeId: number): Promise<BulkDeleteAdminEmailCampaignResponseDto> {
    this.logger.debug(`Xóa nhiều admin email campaign: ${ids.join(', ')}`);

    const failedIds: number[] = [];
    let deletedCount = 0;

    for (const id of ids) {
      try {
        await this.delete(id, employeeId);
        deletedCount++;
      } catch (error) {
        this.logger.warn(`Không thể xóa campaign ${id}: ${error.message}`);
        failedIds.push(id);
      }
    }

    return {
      deletedCount,
      failedCount: failedIds.length,
      failedIds,
      message: `Đã xóa ${deletedCount}/${ids.length} campaign`,
    };
  }

  // Private helper methods

  private async validateCreateData(createDto: CreateAdminEmailCampaignDto): Promise<void> {
    // Validate có ít nhất một nguồn email
    const hasEmailSource = createDto.segmentId ||
                          createDto.audienceIds?.length ||
                          createDto.userIds?.length ||
                          createDto.emailList?.length;

    if (!hasEmailSource) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.NO_EMAIL_SOURCE_PROVIDED);
    }

    // Validate có templateId hoặc (content và subject)
    const hasTemplate = createDto.templateId;
    const hasDirectContent = createDto.content && createDto.subject;

    if (!hasTemplate && !hasDirectContent) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.NO_CONTENT_PROVIDED, 'Phải cung cấp templateId hoặc (content + subject)');
    }

    // Validate template exists if templateId provided
    if (createDto.templateId) {
      const template = await this.adminTemplateEmailRepository.findOne({
        where: { id: createDto.templateId }
      });
      if (!template) {
        throw new AppException(MARKETING_ADMIN_ERROR_CODES.TEMPLATE_NOT_FOUND, `Template với ID ${createDto.templateId} không tồn tại`);
      }
    }

    // Validate segment exists if segmentId provided
    if (createDto.segmentId) {
      const segment = await this.adminSegmentRepository.findOne({
        where: { id: createDto.segmentId }
      });
      if (!segment) {
        throw new AppException(MARKETING_ADMIN_ERROR_CODES.SEGMENT_NOT_FOUND, `Segment với ID ${createDto.segmentId} không tồn tại`);
      }
    }

    // Validate integration exists if integrationId provided
    if (createDto.integrationId) {
      // TODO: Validate integration exists (cần inject IntegrationRepository)
    }

    // Validate user exists if userIds provided
    if (createDto.userIds?.length) {
      const users = await this.userRepository.findByIds(createDto.userIds);
      const foundUserIds = users.map(user => user.id);
      const missingUserIds = createDto.userIds.filter(id => !foundUserIds.includes(id));

      if (missingUserIds.length > 0) {
        throw new AppException(MARKETING_ADMIN_ERROR_CODES.MARKETING_USER_NOT_FOUND, `User với ID ${missingUserIds.join(', ')} không tồn tại`);
      }
    }

    // Validate audience exists if audienceIds provided
    if (createDto.audienceIds?.length) {
      // TODO: Validate admin audiences exist
    }

    // Validate email list format if provided
    if (createDto.emailList?.length) {
      const invalidEmails = createDto.emailList.filter(email => !this.isValidEmail(email));
      if (invalidEmails.length > 0) {
        throw new AppException(MARKETING_ADMIN_ERROR_CODES.INVALID_EMAIL_FORMAT, `Email không hợp lệ: ${invalidEmails.join(', ')}`);
      }
    }

    // Validate scheduled time is in the future
    if (createDto.scheduledAt && createDto.scheduledAt <= Math.floor(Date.now() / 1000)) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.INVALID_SCHEDULED_TIME);
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private async calculateTotalRecipients(data: CreateAdminEmailCampaignDto | UpdateAdminEmailCampaignDto): Promise<number> {
    let allEmails: string[] = [];

    // Lấy email từ segmentId
    if ('segmentId' in data && data.segmentId) {
      const segmentEmails = await this.getEmailsFromSegment(data.segmentId);
      allEmails.push(...segmentEmails);
    }

    // Lấy email từ audienceIds
    if ('audienceIds' in data && data.audienceIds?.length) {
      const audienceEmails = await this.getEmailsFromAudienceIds(data.audienceIds);
      allEmails.push(...audienceEmails);
    }



    // Lấy email từ userIds
    if ('userIds' in data && data.userIds?.length) {
      const userEmails = await this.getEmailsFromUserIds(data.userIds);
      allEmails.push(...userEmails);
    }

    // Lấy email từ emailList
    if (data.emailList?.length) {
      allEmails.push(...data.emailList);
    }

    // Remove duplicates and count
    const uniqueEmails = [...new Set(allEmails)].filter(email => email && this.isValidEmail(email));
    return uniqueEmails.length;
  }

  /**
   * Tạo danh sách email từ tất cả các nguồn
   * @param createDto Dữ liệu tạo campaign
   * @returns Danh sách email
   */
  private async generateEmailListFromAllSources(createDto: CreateAdminEmailCampaignDto): Promise<string[]> {
    let allEmails: string[] = [];

    // Lấy email từ segmentId
    if (createDto.segmentId) {
      const segmentEmails = await this.getEmailsFromSegment(createDto.segmentId);
      allEmails.push(...segmentEmails);
    }

    // Lấy email từ audienceIds
    if (createDto.audienceIds?.length) {
      const audienceEmails = await this.getEmailsFromAudienceIds(createDto.audienceIds);
      allEmails.push(...audienceEmails);
    }



    // Lấy email từ userIds
    if (createDto.userIds?.length) {
      const userEmails = await this.getEmailsFromUserIds(createDto.userIds);
      allEmails.push(...userEmails);
    }

    // Lấy email từ emailList
    if (createDto.emailList?.length) {
      allEmails.push(...createDto.emailList);
    }

    // Remove duplicates and filter out invalid emails
    const uniqueEmails = [...new Set(allEmails)].filter(email => email && this.isValidEmail(email));

    if (uniqueEmails.length === 0) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.NO_VALID_EMAILS);
    }

    return uniqueEmails;
  }

  /**
   * Lấy email từ segment ID
   */
  private async getEmailsFromSegment(segmentId: number): Promise<string[]> {
    // TODO: Implement logic lấy audience từ segment criteria
    // Cần inject AdminSegmentService để sử dụng getAudiencesInSegment
    // Tạm thời trả về empty array
    return [];
  }

  /**
   * Lấy email từ danh sách audience IDs
   */
  private async getEmailsFromAudienceIds(audienceIds: number[]): Promise<string[]> {
    if (!audienceIds.length) return [];

    const audiences = await this.adminAudienceRepository.find({
      where: { id: In(audienceIds) }
    });

    return audiences
      .filter(audience => audience.email && audience.email.trim())
      .map(audience => audience.email.trim());
  }

  /**
   * Lấy email từ danh sách user IDs
   */
  private async getEmailsFromUserIds(userIds: number[]): Promise<string[]> {
    if (!userIds.length) return [];

    const users = await this.userRepository.findByIds(userIds);
    return users
      .filter(user => user.email && user.email.trim())
      .map(user => user.email.trim());
  }

  /**
   * Lấy và giải mã cấu hình email server từ integrationId
   */
  private async getEmailServerConfigFromIntegration(integrationId: string): Promise<any> {
    try {
      const integration = await this.integrationRepository.findOne({
        where: { id: integrationId }
      });

      if (!integration) {
        throw new AppException(MARKETING_ADMIN_ERROR_CODES.INTEGRATION_NOT_FOUND, `Integration với ID ${integrationId} không tồn tại`);
      }

      const metadata = (integration.metadata || {}) as any;
      let encryptedConfig: any = {};

      // Giải mã encryptedConfig nếu có
      if (integration.encryptedConfig && integration.secretKey) {
        try {
          const decryptedData = this.keyPairEncryptionService.decrypt(
            integration.encryptedConfig,
            integration.secretKey
          );
          encryptedConfig = JSON.parse(decryptedData.decryptedData);
        } catch (error) {
          this.logger.warn(`Không thể giải mã encryptedConfig cho integration ${integrationId}: ${error.message}`);
          throw new AppException(MARKETING_ADMIN_ERROR_CODES.INTEGRATION_DECRYPT_FAILED, 'Không thể giải mã cấu hình email server');
        }
      }

      // Trả về cấu hình email server đã giải mã
      return {
        host: metadata?.host,
        port: metadata?.port || 587,
        username: encryptedConfig?.username,
        password: encryptedConfig?.password,
        useSsl: metadata?.useSsl || false,
        serverName: metadata?.serverName || 'Email Server',
        senderName: metadata?.senderName,
        senderEmail: metadata?.senderEmail,
        replyTo: metadata?.replyTo,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy cấu hình email server từ integration ${integrationId}: ${error.message}`);
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.INTEGRATION_CONFIG_ERROR, 'Lỗi khi lấy cấu hình email server');
    }
  }

  private async createInitialStats(campaignId: number): Promise<void> {
    const currentTime = Math.floor(Date.now() / 1000);
    
    await this.adminEmailCampaignStatsRepository.create({
      campaignId,
      sentCount: 0,
      deliveredCount: 0,
      openCount: 0,
      clickCount: 0,
      bounceCount: 0,
      softBounceCount: 0,
      hardBounceCount: 0,
      unsubscribeCount: 0,
      spamComplaintCount: 0,
      failedCount: 0,
      openRate: 0,
      clickRate: 0,
      bounceRate: 0,
      unsubscribeRate: 0,
      spamComplaintRate: 0,
      lastUpdatedAt: currentTime,
      createdAt: currentTime,
    });
  }

  private buildWhereConditions(queryDto: AdminEmailCampaignQueryDto): FindOptionsWhere<AdminEmailCampaign> | FindOptionsWhere<AdminEmailCampaign>[] {
    const where: FindOptionsWhere<AdminEmailCampaign> = {};

    // Implement search logic with OR conditions
    if (queryDto.search) {
      const searchConditions: FindOptionsWhere<AdminEmailCampaign>[] = [
        { name: Like(`%${queryDto.search}%`) },
        { subject: Like(`%${queryDto.search}%`) }
      ];

      // If there are other filters, combine them with search
      if (queryDto.status || queryDto.createdBy) {
        return searchConditions.map(searchCondition => ({
          ...searchCondition,
          ...(queryDto.status && { status: queryDto.status as AdminEmailCampaignStatus }),
          ...(queryDto.createdBy && { createdBy: queryDto.createdBy }),
          ...(queryDto.createdFrom && queryDto.createdTo && {
            createdAt: Between(queryDto.createdFrom, queryDto.createdTo)
          }),
          ...(queryDto.scheduledFrom && queryDto.scheduledTo && {
            scheduledAt: Between(queryDto.scheduledFrom, queryDto.scheduledTo)
          }),
        }));
      }

      return searchConditions;
    }

    if (queryDto.status) {
      where.status = queryDto.status as AdminEmailCampaignStatus;
    }

    if (queryDto.createdBy) {
      where.createdBy = queryDto.createdBy;
    }

    if (queryDto.createdFrom && queryDto.createdTo) {
      where.createdAt = Between(queryDto.createdFrom, queryDto.createdTo);
    }

    if (queryDto.scheduledFrom && queryDto.scheduledTo) {
      where.scheduledAt = Between(queryDto.scheduledFrom, queryDto.scheduledTo);
    }

    return where;
  }

  private async mapToItemDto(campaign: AdminEmailCampaign): Promise<AdminEmailCampaignItemDto> {
    // Get basic stats
    const stats = await this.adminEmailCampaignStatsRepository.findByCampaignId(campaign.id);

    return {
      id: campaign.id,
      name: campaign.name,
      subject: campaign.subject,
      status: campaign.status,
      totalRecipients: campaign.totalRecipients,
      segment: campaign.segment || undefined,
      scheduledAt: campaign.scheduledAt || undefined,
      startedAt: campaign.startedAt || undefined,
      completedAt: campaign.completedAt || undefined,
      createdAt: campaign.createdAt,
      createdBy: campaign.createdBy,
      stats: stats ? {
        sentCount: stats.sentCount,
        openCount: stats.openCount,
        clickCount: stats.clickCount,
        openRate: stats.openRate,
        clickRate: stats.clickRate,
      } : undefined,
    };
  }

  private mapToDetailDto(campaign: AdminEmailCampaign): AdminEmailCampaignDetailDto {
    return {
      id: campaign.id,
      name: campaign.name,
      subject: campaign.subject,
      segment: campaign.segment || undefined,
      senderName: campaign.senderName || undefined,
      senderEmail: campaign.senderEmail || undefined,
      replyTo: campaign.replyTo || undefined,
      content: campaign.content || undefined,

      emailList: campaign.emailList || undefined,
      templateVariables: campaign.templateVariables || undefined,
      scheduledAt: campaign.scheduledAt || undefined,
      startedAt: campaign.startedAt || undefined,
      completedAt: campaign.completedAt || undefined,
      status: campaign.status,
      totalRecipients: campaign.totalRecipients,
      jobIds: campaign.jobIds || undefined,
      emailServerConfig: campaign.emailServerConfig || undefined,
      notes: campaign.notes || undefined,
      createdBy: campaign.createdBy,
      updatedBy: campaign.updatedBy || undefined,
      createdAt: campaign.createdAt,
      updatedAt: campaign.updatedAt || undefined,
    };
  }

  private mapToStatsDto(stats: AdminEmailCampaignStats): AdminEmailCampaignStatsDto {
    return {
      campaignId: stats.campaignId,
      sentCount: stats.sentCount,
      deliveredCount: stats.deliveredCount,
      openCount: stats.openCount,
      clickCount: stats.clickCount,
      bounceCount: stats.bounceCount,
      softBounceCount: stats.softBounceCount,
      hardBounceCount: stats.hardBounceCount,
      unsubscribeCount: stats.unsubscribeCount,
      spamComplaintCount: stats.spamComplaintCount,
      failedCount: stats.failedCount,
      openRate: stats.openRate,
      clickRate: stats.clickRate,
      bounceRate: stats.bounceRate,
      unsubscribeRate: stats.unsubscribeRate,
      spamComplaintRate: stats.spamComplaintRate,
      deviceStats: stats.deviceStats || undefined,
      emailClientStats: stats.emailClientStats || undefined,
      geoStats: stats.geoStats || undefined,
      lastUpdatedAt: stats.lastUpdatedAt,
    };
  }

  /**
   * @deprecated Sử dụng method create() với targetType thay thế
   * Gửi email campaign đến Admin Audience
   * @param createDto Dữ liệu tạo campaign
   * @param employeeId ID của nhân viên tạo
   * @returns Campaign đã tạo
   */
  async sendToAdminAudience(
    createDto: CreateAdminEmailCampaignDto,
    employeeId: number
  ): Promise<CreateAdminEmailCampaignResponseDto> {
    this.logger.debug(`Gửi email campaign đến Admin Audience: ${createDto.name}`);

    // Validate dữ liệu đầu vào
    await this.validateCreateData(createDto);

    // Lấy danh sách email từ audienceIds hoặc tất cả admin audience
    let emailList: string[] = [];
    if (createDto.audienceIds && createDto.audienceIds.length > 0) {
      // Gửi đến audience cụ thể theo IDs
      const adminAudiences = await this.adminAudienceRepository.find({
        where: { id: In(createDto.audienceIds) }
      });
      emailList = adminAudiences
        .filter((audience: AdminAudience) => audience.email && audience.email.trim())
        .map((audience: AdminAudience) => audience.email.trim());
    } else {
      // Gửi đến tất cả Admin Audience
      const adminAudiences = await this.adminAudienceRepository.find();
      emailList = adminAudiences
        .filter((audience: AdminAudience) => audience.email && audience.email.trim())
        .map((audience: AdminAudience) => audience.email.trim());
    }

    if (emailList.length === 0) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.NO_VALID_EMAILS, 'Không có email hợp lệ trong danh sách Admin Audience');
    }

    if (emailList.length === 0) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.NO_VALID_EMAILS, 'Không có email hợp lệ trong danh sách Admin Audience');
    }

    // Tạo campaign với danh sách email
    const campaignDto = {
      ...createDto,
      emailList,
      audienceIds: undefined, // Xóa audienceIds vì đã chuyển thành emailList
    };

    // Tạo campaign (logic tự động thực thi đã được tích hợp trong method create)
    return this.create(campaignDto, employeeId);
  }

  /**
   * @deprecated Sử dụng method create() với targetType thay thế
   * Gửi email campaign đến User Audience theo User ID
   * @param createDto Dữ liệu tạo campaign
   * @param userId ID của user
   * @param employeeId ID của nhân viên tạo
   * @returns Campaign đã tạo
   */
  async sendToUserAudience(
    createDto: CreateAdminEmailCampaignDto,
    userId: number,
    employeeId: number
  ): Promise<CreateAdminEmailCampaignResponseDto> {
    this.logger.debug(`Gửi email campaign đến User Audience của user ${userId}: ${createDto.name}`);

    // Validate dữ liệu đầu vào
    await this.validateCreateData(createDto);

    // Lấy danh sách email từ User Audience
    let emailList: string[] = [];

    // Gửi đến tất cả User Audience của user
    const userAudiences = await this.userAudienceRepository.find({
      where: { userId }
    });

    if (userAudiences.length === 0) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.AUDIENCE_NOT_FOUND, `Không tìm thấy User Audience nào của user ${userId} để gửi email`);
    }

    emailList = userAudiences
      .filter((audience: UserAudience) => audience.email && audience.email.trim())
      .map((audience: UserAudience) => audience.email.trim());

    if (emailList.length === 0) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.NO_VALID_EMAILS, `Không có email hợp lệ trong danh sách User Audience của user ${userId}`);
    }

    // Tạo campaign với danh sách email
    const campaignDto = {
      ...createDto,
      emailList,
    };

    // Tạo campaign (logic tự động thực thi đã được tích hợp trong method create)
    return this.create(campaignDto, employeeId);
  }

  /**
   * @deprecated Sử dụng method create() với targetType thay thế
   * Gửi email campaign đến Admin Segment
   * @param createDto Dữ liệu tạo campaign
   * @param segmentId ID của segment
   * @param employeeId ID của nhân viên tạo
   * @returns Campaign đã tạo
   */
  async sendToAdminSegment(
    createDto: CreateAdminEmailCampaignDto,
    segmentId: number,
    employeeId: number
  ): Promise<CreateAdminEmailCampaignResponseDto> {
    this.logger.debug(`Gửi email campaign đến Admin Segment ${segmentId}: ${createDto.name}`);

    // Validate dữ liệu đầu vào
    await this.validateCreateData(createDto);

    // Kiểm tra segment tồn tại
    const segment = await this.adminSegmentRepository.findOne({
      where: { id: segmentId }
    });

    if (!segment) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.ADMIN_SEGMENT_NOT_FOUND, `Admin Segment với ID ${segmentId} không tồn tại`);
    }

    // Lấy danh sách audience trong segment dựa trên criteria
    // Hiện tại implement đơn giản, có thể cần phức tạp hơn tùy theo cấu trúc segment
    const adminAudiences = await this.getAudiencesBySegmentCriteria(segment.criteria);

    if (adminAudiences.length === 0) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.AUDIENCE_NOT_FOUND, `Không tìm thấy Admin Audience nào trong segment ${segmentId} để gửi email`);
    }

    // Tạo danh sách email từ Admin Audience
    const emailList = adminAudiences
      .filter((audience: AdminAudience) => audience.email && audience.email.trim())
      .map((audience: AdminAudience) => audience.email.trim());

    if (emailList.length === 0) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.NO_VALID_EMAILS, `Không có email hợp lệ trong Admin Segment ${segmentId}`);
    }

    // Tạo campaign với danh sách email và segment ID
    const campaignDto = {
      ...createDto,
      emailList,
      segmentId,
      audienceIds: undefined, // Xóa audienceIds vì đã chuyển thành emailList
    };

    // Tạo campaign (logic tự động thực thi đã được tích hợp trong method create)
    return this.create(campaignDto, employeeId);
  }

  /**
   * @deprecated Sử dụng method create() với targetType thay thế
   * Gửi email campaign đến danh sách email tùy chỉnh
   * @param createDto Dữ liệu tạo campaign
   * @param employeeId ID của nhân viên tạo
   * @returns Campaign đã tạo
   */
  async sendToCustomEmails(
    createDto: CreateAdminEmailCampaignDto,
    employeeId: number
  ): Promise<CreateAdminEmailCampaignResponseDto> {
    this.logger.debug(`Gửi email campaign đến danh sách email tùy chỉnh: ${createDto.name}`);

    // Validate dữ liệu đầu vào
    await this.validateCreateData(createDto);

    // Kiểm tra có danh sách email
    if (!createDto.emailList || createDto.emailList.length === 0) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.EMAIL_LIST_REQUIRED_FOR_CUSTOM);
    }

    // Validate email format
    const invalidEmails = createDto.emailList.filter(email => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return !emailRegex.test(email);
    });

    if (invalidEmails.length > 0) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.INVALID_EMAIL_FORMAT, `Các email không hợp lệ: ${invalidEmails.join(', ')}`);
    }

    // Tạo campaign với danh sách email tùy chỉnh (logic tự động thực thi đã được tích hợp trong method create)
    return this.create(createDto, employeeId);
  }

  /**
   * Lấy danh sách audience dựa trên criteria của segment
   * @param criteria Tiêu chí lọc
   * @returns Danh sách audience
   */
  private async getAudiencesBySegmentCriteria(criteria: any): Promise<AdminAudience[]> {
    // Implementation đơn giản - trả về tất cả admin audience
    // Có thể cần phức tạp hơn tùy theo cấu trúc criteria
    if (!criteria) {
      return this.adminAudienceRepository.find();
    }

    // TODO: Implement logic lọc audience dựa trên criteria
    // Hiện tại trả về tất cả audience
    return this.adminAudienceRepository.find();
  }

  /**
   * Tạo jobs và đẩy vào queue để gửi email campaign
   * @param campaign Campaign đã được tạo
   * @param emailList Danh sách email cần gửi
   * @returns Danh sách job IDs
   */
  async createEmailJobs(
    campaign: AdminEmailCampaign,
    emailList: string[]
  ): Promise<string[]> {
    this.logger.debug(`Tạo email jobs cho campaign ${campaign.id} với ${emailList.length} emails`);

    const now = Date.now();
    const jobIds: string[] = [];

    // Sử dụng cấu hình email server đã được lưu trong campaign
    const emailServerConfig = campaign.emailServerConfig;

    // Tạo recipients cho batch job
    const recipients: AdminEmailRecipientDto[] = emailList.map(email => ({
      email,
      customData: {}, // Có thể mở rộng để thêm custom data cho từng email
    }));

    // Tạo batch job data với cấu hình email server đã giải mã
    const batchJobData: BatchAdminEmailCampaignJobDto = {
      campaignId: campaign.id,
      subject: campaign.subject,
      htmlContent: campaign.content?.html || undefined,
      textContent: campaign.content?.text || undefined,
      templateVariables: campaign.templateVariables || {},
      recipients,
      emailServerConfig: emailServerConfig as any,
      senderName: emailServerConfig?.senderName || undefined,
      senderEmail: emailServerConfig?.senderEmail || undefined,
      replyTo: emailServerConfig?.replyTo || undefined,
      createdAt: now,
    };

    // Tính delay nếu có scheduled time
    const delay = campaign.scheduledAt ? Math.max(0, campaign.scheduledAt * 1000 - now) : 0;

    // Thêm batch job vào queue
    const job = await this.emailMarketingQueue.add(
      EmailMarketingJobName.SEND_BATCH_ADMIN_EMAIL,
      batchJobData,
      {
        delay,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 100,
        removeOnFail: 50,
      }
    );

    jobIds.push(job.id!.toString());

    // Cập nhật campaign với job IDs
    await this.adminEmailCampaignRepository.updateJobIds(campaign.id, jobIds);

    // Cập nhật trạng thái và thời gian bắt đầu nếu cần
    if (delay === 0) {
      await this.adminEmailCampaignRepository.updateStartedAt(campaign.id, Math.floor(Date.now() / 1000));
    } else {
      await this.adminEmailCampaignRepository.updateStatus(campaign.id, AdminEmailCampaignStatus.SCHEDULED, 0); // employeeId = 0 for system update
    }

    this.logger.log(`Đã tạo ${jobIds.length} jobs cho campaign ${campaign.id}`);
    return jobIds;
  }

  /**
   * @deprecated Sử dụng method create() với auto-execution thay thế
   * Thực thi gửi email campaign (tạo jobs và đẩy vào queue)
   * @param campaignId ID của campaign
   * @param employeeId ID của nhân viên thực thi
   * @returns Thông tin campaign đã được cập nhật
   */
  async executeCampaign(
    campaignId: number,
    employeeId: number
  ): Promise<CreateAdminEmailCampaignResponseDto> {
    this.logger.debug(`Thực thi email campaign ${campaignId}`);

    const campaign = await this.adminEmailCampaignRepository.findById(campaignId);
    if (!campaign) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_NOT_FOUND, `Campaign với ID ${campaignId} không tồn tại`);
    }

    // Kiểm tra trạng thái có thể thực thi
    if (!['DRAFT', 'SCHEDULED'].includes(campaign.status)) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.CANNOT_UPDATE_SENDING_CAMPAIGN, `Không thể thực thi campaign với trạng thái ${campaign.status}`);
    }

    // Kiểm tra có email list
    if (!campaign.emailList || campaign.emailList.length === 0) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.NO_VALID_EMAILS, 'Campaign không có danh sách email để gửi');
    }

    // Tạo jobs và đẩy vào queue
    const jobIds = await this.createEmailJobs(campaign, campaign.emailList);

    // Trả về thông tin campaign đã cập nhật
    return {
      id: campaign.id,
      name: campaign.name,
      subject: campaign.subject,
      status: campaign.scheduledAt ? 'SCHEDULED' : 'SENDING',
      totalRecipients: campaign.totalRecipients,
      jobCount: jobIds.length,
      scheduledAt: campaign.scheduledAt || undefined,
      createdAt: campaign.createdAt,
      createdBy: campaign.createdBy,
    };
  }

  /**
   * Resolve content từ template và thay thế variables
   */
  private async resolveTemplateContent(
    templateId: number,
    templateVariables?: Record<string, any>
  ): Promise<{ subject: string; content: any }> {
    const template = await this.adminTemplateEmailRepository.findOne({
      where: { id: templateId }
    });

    if (!template) {
      throw new AppException(MARKETING_ADMIN_ERROR_CODES.TEMPLATE_NOT_FOUND, `Template với ID ${templateId} không tồn tại`);
    }

    let resolvedSubject = template.subject;
    let resolvedContent = template.content;

    // Thay thế variables nếu có
    if (templateVariables && Object.keys(templateVariables).length > 0) {
      for (const [key, value] of Object.entries(templateVariables)) {
        const placeholder = `{{${key}}}`;
        // Thay thế trong subject
        resolvedSubject = resolvedSubject.replace(new RegExp(placeholder, 'g'), String(value));
        // Thay thế trong content (string)
        resolvedContent = resolvedContent.replace(new RegExp(placeholder, 'g'), String(value));
      }
    }

    // Chuyển đổi content string thành object format để tương thích với EmailCampaignContent
    const contentObject = {
      html: resolvedContent,
      text: resolvedContent.replace(/<[^>]*>/g, '') // Strip HTML tags for text version
    };

    return {
      subject: resolvedSubject,
      content: contentObject
    };
  }

  /**
   * Tạm dừng admin email campaign
   * @param campaignId ID của campaign
   * @param employeeId ID của employee thực hiện
   * @returns Thông tin campaign đã tạm dừng
   */
  async pauseCampaign(
    campaignId: number,
    employeeId: number
  ): Promise<PauseAdminEmailCampaignResponseDto> {
    this.logger.debug(`Tạm dừng admin email campaign ID: ${campaignId}`);

    // Tìm campaign
    const campaign = await this.adminEmailCampaignRepository.findById(campaignId);
    if (!campaign) {
      throw new AppException(
        MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_NOT_FOUND,
        `Campaign với ID ${campaignId} không tồn tại`
      );
    }

    // Kiểm tra trạng thái có thể tạm dừng
    if (!['SENDING', 'SCHEDULED'].includes(campaign.status)) {
      throw new AppException(
        MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_INVALID_STATUS_FOR_PAUSE,
        'Chỉ có thể tạm dừng chiến dịch đang gửi (SENDING) hoặc đã lên lịch (SCHEDULED)'
      );
    }

    const previousStatus = campaign.status;
    const currentTime = Math.floor(Date.now() / 1000);

    try {
      // Hủy tất cả jobs đang chờ trong queue
      let canceledJobsCount = 0;
      if (campaign.jobIds && campaign.jobIds.length > 0) {
        for (const jobId of campaign.jobIds) {
          try {
            const job = await this.emailMarketingQueue.getJob(jobId);
            if (job) {
              const jobState = await job.getState();
              if (['waiting', 'delayed'].includes(jobState)) {
                await job.remove();
                canceledJobsCount++;
              }
            }
          } catch (error) {
            this.logger.warn(`Không thể hủy job ${jobId}: ${error.message}`);
          }
        }
      }

      // Cập nhật trạng thái campaign
      await this.adminEmailCampaignRepository.updateStatus(
        campaignId,
        AdminEmailCampaignStatus.PAUSED,
        employeeId
      );

      this.logger.log(`Đã tạm dừng admin email campaign ${campaignId}, hủy ${canceledJobsCount} jobs`);

      return {
        campaignId,
        campaignName: campaign.name,
        previousStatus,
        currentStatus: AdminEmailCampaignStatus.PAUSED,
        canceledJobsCount,
        pausedAt: currentTime,
        message: 'Chiến dịch đã được tạm dừng thành công'
      };

    } catch (error) {
      this.logger.error(`Lỗi khi tạm dừng campaign ${campaignId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_CANNOT_UPDATE,
        'Không thể tạm dừng campaign'
      );
    }
  }

  /**
   * Tiếp tục admin email campaign đã tạm dừng
   * @param campaignId ID của campaign
   * @param employeeId ID của employee thực hiện
   * @returns Thông tin campaign đã tiếp tục
   */
  async resumeCampaign(
    campaignId: number,
    employeeId: number
  ): Promise<ResumeAdminEmailCampaignResponseDto> {
    this.logger.debug(`Tiếp tục admin email campaign ID: ${campaignId}`);

    // Tìm campaign
    const campaign = await this.adminEmailCampaignRepository.findById(campaignId);
    if (!campaign) {
      throw new AppException(
        MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_NOT_FOUND,
        `Campaign với ID ${campaignId} không tồn tại`
      );
    }

    // Kiểm tra quyền truy cập
    if (campaign.createdBy !== employeeId) {
      throw new AppException(
        MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_ACCESS_DENIED,
        'Bạn không có quyền truy cập campaign này'
      );
    }

    // Kiểm tra trạng thái có thể tiếp tục
    if (campaign.status !== AdminEmailCampaignStatus.PAUSED) {
      throw new AppException(
        MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_INVALID_STATUS_FOR_RESUME,
        'Chỉ có thể tiếp tục chiến dịch đã tạm dừng (PAUSED)'
      );
    }

    const currentTime = Math.floor(Date.now() / 1000);

    try {
      // Xác định trạng thái mới dựa trên scheduledAt
      let newStatus: AdminEmailCampaignStatus;
      if (campaign.scheduledAt && campaign.scheduledAt > currentTime) {
        newStatus = AdminEmailCampaignStatus.SCHEDULED;
      } else {
        newStatus = AdminEmailCampaignStatus.SENDING;
      }

      // Tạo lại jobs cho campaign
      let recreatedJobsCount = 0;
      if (campaign.emailList && campaign.emailList.length > 0) {
        const jobIds = await this.createEmailJobs(campaign, campaign.emailList);
        recreatedJobsCount = jobIds.length;

        // Cập nhật jobIds trong campaign
        await this.adminEmailCampaignRepository.repository.update(campaignId, {
          jobIds,
          updatedBy: employeeId,
          updatedAt: currentTime
        });
      }

      // Cập nhật trạng thái campaign
      await this.adminEmailCampaignRepository.updateStatus(
        campaignId,
        newStatus,
        employeeId
      );

      this.logger.log(`Đã tiếp tục admin email campaign ${campaignId}, tạo ${recreatedJobsCount} jobs mới`);

      return {
        campaignId,
        campaignName: campaign.name,
        previousStatus: AdminEmailCampaignStatus.PAUSED,
        currentStatus: newStatus,
        recreatedJobsCount,
        resumedAt: currentTime,
        message: 'Chiến dịch đã được tiếp tục thành công'
      };

    } catch (error) {
      this.logger.error(`Lỗi khi tiếp tục campaign ${campaignId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_CANNOT_UPDATE,
        'Không thể tiếp tục campaign'
      );
    }
  }

  /**
   * Cập nhật trạng thái campaign dựa trên queue status
   * @param employeeId ID của employee thực hiện cập nhật
   * @returns Kết quả cập nhật
   */
  async syncCampaignStatus(employeeId: number): Promise<{
    totalCampaignsChecked: number;
    updatedCampaigns: Array<{
      campaignId: number;
      campaignName: string;
      previousStatus: string;
      currentStatus: string;
      reason: string;
    }>;
    summary: {
      scheduledToFailed: number;
      sendingToCompleted: number;
      sendingToFailed: number;
    };
  }> {
    this.logger.debug('Bắt đầu sync trạng thái campaign dựa trên queue status');

    const currentTime = Math.floor(Date.now() / 1000);
    const updatedCampaigns: any[] = [];
    const summary = {
      scheduledToFailed: 0,
      sendingToCompleted: 0,
      sendingToFailed: 0
    };

    try {
      // Lấy tất cả campaign có trạng thái SCHEDULED hoặc SENDING
      const campaigns = await this.adminEmailCampaignRepository.repository.find({
        where: [
          { status: AdminEmailCampaignStatus.SCHEDULED },
          { status: AdminEmailCampaignStatus.SENDING }
        ]
      });

      this.logger.debug(`Tìm thấy ${campaigns.length} campaign cần kiểm tra`);

      for (const campaign of campaigns) {
        let shouldUpdate = false;
        let newStatus: AdminEmailCampaignStatus | null = null;
        let reason = '';

        // Kiểm tra campaign SCHEDULED
        if (campaign.status === AdminEmailCampaignStatus.SCHEDULED) {
          // Nếu đã quá thời gian lên lịch
          if (campaign.scheduledAt && campaign.scheduledAt < currentTime) {
            // Kiểm tra xem còn job nào trong queue không
            const hasActiveJobs = await this.checkActiveJobs(campaign.jobIds);

            if (!hasActiveJobs) {
              newStatus = AdminEmailCampaignStatus.FAILED;
              reason = 'Quá thời gian lên lịch và không còn job trong queue';
              shouldUpdate = true;
              summary.scheduledToFailed++;
            }
          }
        }

        // Kiểm tra campaign SENDING
        if (campaign.status === AdminEmailCampaignStatus.SENDING) {
          const jobStatus = await this.checkJobsStatus(campaign.jobIds);

          // Nếu tất cả job đã hoàn thành thành công
          if (jobStatus.allCompleted && jobStatus.totalJobs > 0) {
            newStatus = AdminEmailCampaignStatus.COMPLETED;
            reason = 'Tất cả job đã hoàn thành thành công';
            shouldUpdate = true;
            summary.sendingToCompleted++;

            // Cập nhật thời gian hoàn thành
            await this.adminEmailCampaignRepository.repository.update(campaign.id, {
              completedAt: currentTime,
              updatedAt: currentTime,
              updatedBy: employeeId
            });
          }
          // Nếu tất cả job đã thất bại hoặc không còn job nào
          else if (jobStatus.allFailed || (!jobStatus.hasActiveJobs && jobStatus.totalJobs === 0)) {
            newStatus = AdminEmailCampaignStatus.FAILED;
            reason = jobStatus.allFailed
              ? 'Tất cả job đã thất bại'
              : 'Không còn job nào trong queue';
            shouldUpdate = true;
            summary.sendingToFailed++;
          }
        }

        // Cập nhật trạng thái nếu cần
        if (shouldUpdate && newStatus) {
          await this.adminEmailCampaignRepository.updateStatus(
            campaign.id,
            newStatus,
            employeeId
          );

          updatedCampaigns.push({
            campaignId: campaign.id,
            campaignName: campaign.name,
            previousStatus: campaign.status,
            currentStatus: newStatus,
            reason
          });

          this.logger.log(`Cập nhật campaign ${campaign.id} từ ${campaign.status} thành ${newStatus}: ${reason}`);
        }
      }

      const result = {
        totalCampaignsChecked: campaigns.length,
        updatedCampaigns,
        summary
      };

      this.logger.log(`Hoàn thành sync trạng thái: kiểm tra ${campaigns.length} campaign, cập nhật ${updatedCampaigns.length} campaign`);
      return result;

    } catch (error) {
      this.logger.error(`Lỗi khi sync trạng thái campaign: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETING_ADMIN_ERROR_CODES.CAMPAIGN_CANNOT_UPDATE,
        'Không thể sync trạng thái campaign'
      );
    }
  }

  /**
   * Kiểm tra xem còn job nào đang active trong queue không
   * @param jobIds Danh sách job IDs
   * @returns true nếu còn job active
   */
  private async checkActiveJobs(jobIds: string[] | null): Promise<boolean> {
    if (!jobIds || jobIds.length === 0) {
      return false;
    }

    try {
      for (const jobId of jobIds) {
        const job = await this.emailMarketingQueue.getJob(jobId);
        if (job) {
          const state = await job.getState();
          if (['waiting', 'active', 'delayed'].includes(state)) {
            return true;
          }
        }
      }
      return false;
    } catch (error) {
      this.logger.warn(`Lỗi khi kiểm tra active jobs: ${error.message}`);
      return false;
    }
  }

  /**
   * Kiểm tra trạng thái tất cả job của campaign
   * @param jobIds Danh sách job IDs
   * @returns Thông tin trạng thái job
   */
  private async checkJobsStatus(jobIds: string[] | null): Promise<{
    totalJobs: number;
    completedJobs: number;
    failedJobs: number;
    activeJobs: number;
    allCompleted: boolean;
    allFailed: boolean;
    hasActiveJobs: boolean;
  }> {
    const result = {
      totalJobs: 0,
      completedJobs: 0,
      failedJobs: 0,
      activeJobs: 0,
      allCompleted: false,
      allFailed: false,
      hasActiveJobs: false
    };

    if (!jobIds || jobIds.length === 0) {
      return result;
    }

    result.totalJobs = jobIds.length;

    try {
      for (const jobId of jobIds) {
        const job = await this.emailMarketingQueue.getJob(jobId);
        if (job) {
          const state = await job.getState();

          switch (state) {
            case 'completed':
              result.completedJobs++;
              break;
            case 'failed':
              result.failedJobs++;
              break;
            case 'waiting':
            case 'active':
            case 'delayed':
              result.activeJobs++;
              result.hasActiveJobs = true;
              break;
          }
        } else {
          // Job không tồn tại, coi như failed
          result.failedJobs++;
        }
      }

      result.allCompleted = result.completedJobs === result.totalJobs;
      result.allFailed = result.failedJobs === result.totalJobs;

      return result;
    } catch (error) {
      this.logger.warn(`Lỗi khi kiểm tra trạng thái jobs: ${error.message}`);
      return result;
    }
  }
}
