import { Injectable } from '@nestjs/common';
import { AdminAudienceHasTagRepository, AdminAudienceRepository } from '@modules/marketing/admin/repositories';
import { AdminAudienceCustomFieldRepository } from '@modules/marketing/admin/repositories';
import { AdminTagRepository } from '../repositories/admin-tag.repository';
import { AdminSegmentRepository } from '../repositories/admin-segment.repository';
import { In, Like, FindOptionsWhere, Or, Not, IsNull } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import {
  CreateAudienceDto,
  UpdateAudienceDto,
  AudienceResponseDto,
  UpdateAudienceResponseDto,
  CustomFieldResponseDto,
  AudienceQueryDto,
  CreateAvatarUploadUrlDto,
  AvatarUploadUrlResponseDto,
  MergeAudienceDto,
  MergeAudienceResponseDto,
} from '../dto/audience';
import { AttachTagsToAudiencesDto, DetachTagsFromAudiencesDto, AudienceTagResponseDto } from '../dto/audience-tag';
import { TagResponseDto } from '../dto/tag';
import { PaginatedResult, PaginationMeta } from '@/common/response';
import { AdminAudience, AdminAudienceCustomField, AdminAudienceHasTag, AdminTag, AdminSegment } from '../entities';
import { AppException, ErrorCode } from '@/common';
import { BulkDeleteResponseDto } from '@/modules/marketing/common/dto';
import { MARKETING_ERROR_CODES } from '@/modules/marketing/errors/marketing-error.code';
import { S3Service } from '@/shared/services/s3.service';
import { CdnService } from '@/shared/services/cdn.service';
import { generateS3Key, CategoryFolderEnum } from '@/shared/utils/generators/s3-key-generator.util';
import { TimeIntervalEnum, FileSizeEnum } from '@/shared/utils';


/**
 * Service xử lý logic liên quan đến audience
 */
@Injectable()
export class AdminAudienceService {
  constructor(
    private readonly adminAudienceRepository: AdminAudienceRepository,
    private readonly adminAudienceCustomFieldRepository: AdminAudienceCustomFieldRepository,
    private readonly adminTagRepository: AdminTagRepository,
    private readonly adminSegmentRepository: AdminSegmentRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly adminAudienceHasTagRepository: AdminAudienceHasTagRepository,
  ) {}

  /**
   * Tạo audience mới
   * @param employeeId ID của employee
   * @param createAudienceDto Dữ liệu tạo audience
   * @returns Audience đã tạo
   */
  @Transactional()
  async create(
    createAudienceDto: CreateAudienceDto,
  ): Promise<AudienceResponseDto> {
    // Validate tags nếu có
    if (createAudienceDto.tagIds && createAudienceDto.tagIds.length > 0) {
      const existingTags = await this.adminTagRepository.find({
        where: {
          id: In(createAudienceDto.tagIds),
        },
      });

      if (existingTags.length !== createAudienceDto.tagIds.length) {
        const existingTagIds = existingTags.map(tag => tag.id);
        const missingTagIds = createAudienceDto.tagIds.filter(id => !existingTagIds.includes(id));
        throw new AppException(
          MARKETING_ERROR_CODES.TAG_NOT_FOUND,
          `Không tìm thấy tag với ID: ${missingTagIds.join(', ')}`
        );
      }
    }

    // Tạo audience mới
    const audience = new AdminAudience();
    audience.name = createAudienceDto.name || '';
    audience.email = createAudienceDto.email;
    audience.countryCode = createAudienceDto.countryCode || null;
    audience.phoneNumber = createAudienceDto.phoneNumber || null;
    audience.createdAt = Math.floor(Date.now() / 1000);
    audience.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu audience
    const savedAudience = await this.adminAudienceRepository.save(audience);

    // Lưu tag
    if (createAudienceDto.tagIds && createAudienceDto.tagIds.length > 0) {
      const audienceHasTags = createAudienceDto.tagIds.map(tagId => {
        const audienceHasTag = new AdminAudienceHasTag();
        audienceHasTag.audienceId = (savedAudience as AdminAudience).id;
        audienceHasTag.tagId = tagId;
        return audienceHasTag;
      });
      await this.adminAudienceHasTagRepository.save(audienceHasTags);
    }



    // Tạo các trường tùy chỉnh nếu có
    let customFields: AdminAudienceCustomField[] = [];
    if (
      createAudienceDto.customFields &&
      createAudienceDto.customFields.length > 0
    ) {
      customFields = createAudienceDto.customFields.map((field) => {
        const customField = new AdminAudienceCustomField();
        customField.audienceId = (savedAudience as AdminAudience).id;
        customField.fieldName = field.fieldName;
        customField.fieldValue = field.fieldValue;
        customField.fieldType = field.fieldType;
        customField.createdAt = Math.floor(Date.now() / 1000);
        customField.updatedAt = Math.floor(Date.now() / 1000);
        return customField;
      });

      customFields =
        await this.adminAudienceCustomFieldRepository.save(customFields);
    }

    // Lấy các tag nếu có
    let tags: AdminTag[] = [];
    if (createAudienceDto.tagIds && createAudienceDto.tagIds.length > 0) {
      tags = await this.adminTagRepository.find({
        where: {
          id: In(createAudienceDto.tagIds),
        },
      });
    }

    // Đảm bảo savedAudience là một đối tượng AdminAudience, không phải mảng
    return this.mapToDto(savedAudience as AdminAudience, customFields, tags);
  }

  /**
   * Cập nhật audience
   * @param id ID của audience
   * @param updateAudienceDto Dữ liệu cập nhật audience
   * @returns Audience đã cập nhật (bao gồm thông tin avatar upload nếu có)
   */
  @Transactional()
  async update(
    id: number,
    updateAudienceDto: UpdateAudienceDto,
  ): Promise<UpdateAudienceResponseDto> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND, `Audience với ID ${id} không tồn tại`);
    }

    // Cập nhật thông tin audience
    if (updateAudienceDto.name !== undefined) {
      audience.name = updateAudienceDto.name || '';
    }

    if (updateAudienceDto.email) {
      audience.email = updateAudienceDto.email;
    }

    if (updateAudienceDto.countryCode !== undefined) {
      audience.countryCode = updateAudienceDto.countryCode;
    }

    if (updateAudienceDto.phoneNumber !== undefined) {
      audience.phoneNumber = updateAudienceDto.phoneNumber || null;
    }

    audience.updatedAt = Math.floor(Date.now() / 1000);

    // Xử lý avatar nếu có yêu cầu upload
    let avatarS3Key: string | undefined;
    if (updateAudienceDto.avatarMediaType) {
      // Tạo S3 key cho avatar
      avatarS3Key = generateS3Key({
        baseFolder: 'marketing',
        categoryFolder: CategoryFolderEnum.CUSTOMER_AVATAR,
        useTimeFolder: true,
        prefix: `admin`,
        fileName: `avatar.${updateAudienceDto.avatarMediaType.split('/')[1]}`,
      });

      // Lưu S3 key vào database
      audience.avatar = avatarS3Key;
    }

    // Lưu audience
    const updatedAudience = await this.adminAudienceRepository.save(audience);

    // Cập nhật các trường tùy chỉnh nếu có
    let customFields: AdminAudienceCustomField[] = [];
    if (
      updateAudienceDto.customFields &&
      updateAudienceDto.customFields.length > 0
    ) {
      // Xóa các trường tùy chỉnh cũ
      await this.adminAudienceCustomFieldRepository.delete({ audienceId: id });

      // Tạo các trường tùy chỉnh mới
      customFields = updateAudienceDto.customFields.map((field) => {
        const customField = new AdminAudienceCustomField();
        customField.audienceId = id;
        customField.fieldName = field.fieldName;
        customField.fieldValue = field.fieldValue;
        customField.fieldType = field.fieldType;
        customField.createdAt = Math.floor(Date.now() / 1000);
        customField.updatedAt = Math.floor(Date.now() / 1000);
        return customField;
      });

      const savedCustomFields =
        await this.adminAudienceCustomFieldRepository.save(customFields);
      customFields = Array.isArray(savedCustomFields)
        ? savedCustomFields
        : [savedCustomFields];
    } else {
      // Lấy các trường tùy chỉnh hiện tại
      customFields = await this.adminAudienceCustomFieldRepository.find({
        where: { audienceId: id },
      });
    }

    // Cập nhật tags nếu có
    let tags: AdminTag[] = [];
    if (updateAudienceDto.tagIds !== undefined) {
      // Xóa tất cả tag relationships cũ
      await this.adminAudienceHasTagRepository.delete({ audienceId: id });

      // Thêm tag relationships mới nếu có
      if (updateAudienceDto.tagIds.length > 0) {
        // Validate tags tồn tại
        tags = await this.adminTagRepository.find({
          where: {
            id: In(updateAudienceDto.tagIds),
          },
        });

        if (tags.length !== updateAudienceDto.tagIds.length) {
          const foundIds = tags.map(t => t.id);
          const missingIds = updateAudienceDto.tagIds.filter(id => !foundIds.includes(id));
          throw new AppException(
            MARKETING_ERROR_CODES.TAG_NOT_FOUND,
            `Không tìm thấy tags với ID: ${missingIds.join(', ')}`
          );
        }

        // Tạo các relationships mới
        const audienceTagRelations = updateAudienceDto.tagIds.map(tagId => {
          const relation = new AdminAudienceHasTag();
          relation.audienceId = id;
          relation.tagId = tagId;
          return relation;
        });

        await this.adminAudienceHasTagRepository.save(audienceTagRelations);
      }
    } else {
      // Nếu không có tagIds trong request, lấy tags hiện tại
      const audienceHasTags = await this.adminAudienceHasTagRepository.find({
        where: { audienceId: id },
      });

      if (audienceHasTags.length > 0) {
        const tagIds = audienceHasTags.map(aht => aht.tagId);
        tags = await this.adminTagRepository.find({
          where: { id: In(tagIds) },
        });
      }
    }

    // Tạo response DTO cơ bản
    const baseResponse = this.mapToDto(updatedAudience as AdminAudience, customFields, tags);

    // Tạo response với thông tin avatar upload nếu có
    const response = new UpdateAudienceResponseDto();
    Object.assign(response, baseResponse);

    // Xử lý avatar upload nếu có yêu cầu
    if (updateAudienceDto.avatarMediaType && avatarS3Key) {
      try {
        // Tạo presigned URL với S3 key đã được lưu vào database
        const uploadUrl = await this.s3Service.createPresignedWithID(
          avatarS3Key,
          TimeIntervalEnum.ONE_HOUR, // URL hết hạn sau 1 giờ
          updateAudienceDto.avatarMediaType as any, // Cast to MediaType
          FileSizeEnum.FIVE_MB, // Giới hạn 5MB cho avatar
        );

        // Tính thời gian hết hạn
        const expiresAt = Math.floor(Date.now() / 1000) + (60 * 60); // 1 giờ

        // Thêm thông tin avatar upload vào response
        response.avatarUploadUrl = uploadUrl;
        response.avatarS3Key = avatarS3Key;
        response.avatarUploadExpiresAt = expiresAt;
      } catch (error) {
        // Log lỗi nhưng không làm fail toàn bộ request
        console.error('Error creating avatar upload URL:', error);
      }
    }

    return response;
  }

  /**
   * Lấy danh sách audience của employee với phân trang và filter
   * @param employeeId ID của employee
   * @param query Tham số truy vấn
   * @returns Danh sách audience với phân trang
   */
  async findAll(
    query: AudienceQueryDto,
  ): Promise<PaginatedResult<AudienceResponseDto>> {
    const {
      page = 1,
      limit = 10,
      search,
      name,
      email,
      phone,
      hasPhoneNumber,
      tagId,
      segmentId,
      customFieldName,
      customFieldValue,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện cơ bản
    let where: FindOptionsWhere<AdminAudience> | FindOptionsWhere<AdminAudience>[] = {};

    // Thêm điều kiện tìm kiếm tổng hợp (search trong name, email, phone)
    if (search) {
      where = [
        { name: Like(`%${search}%`) },
        { email: Like(`%${search}%`) },
        { phoneNumber: Like(`%${search}%`) }
      ];
    } else {
      // Thêm điều kiện tìm kiếm theo tên
      if (name) {
        (where as FindOptionsWhere<AdminAudience>).name = Like(`%${name}%`);
      }

      // Thêm điều kiện tìm kiếm theo email
      if (email) {
        (where as FindOptionsWhere<AdminAudience>).email = Like(`%${email}%`);
      }

      // Thêm điều kiện tìm kiếm theo số điện thoại (backward compatibility)
      if (phone) {
        (where as FindOptionsWhere<AdminAudience>).phoneNumber = Like(`%${phone}%`);
      }
    }

    // Thêm điều kiện filter theo việc có số điện thoại hay không
    if (hasPhoneNumber !== undefined) {
      if (Array.isArray(where)) {
        if (hasPhoneNumber) {
          // Có số điện thoại: phoneNumber IS NOT NULL AND phoneNumber != ''
          where = where.map(w => ({
            ...w,
            phoneNumber: Not(Or(IsNull(), Like('')))
          }));
        } else {
          // Không có số điện thoại: phoneNumber IS NULL OR phoneNumber = ''
          where = where.map(w => ({
            ...w,
            phoneNumber: Or(IsNull(), Like(''))
          }));
        }
      } else {
        if (hasPhoneNumber) {
          // Có số điện thoại: phoneNumber IS NOT NULL AND phoneNumber != ''
          (where as FindOptionsWhere<AdminAudience>).phoneNumber = Not(Or(IsNull(), Like('')));
        } else {
          // Không có số điện thoại: phoneNumber IS NULL OR phoneNumber = ''
          (where as FindOptionsWhere<AdminAudience>).phoneNumber = Or(IsNull(), Like(''));
        }
      }
    }

    // Nếu có filter theo tagId, cần lấy audience IDs từ bảng liên kết trước
    let filteredAudienceIds: number[] | undefined;
    if (tagId) {
      const audienceHasTagsForFilter = await this.adminAudienceHasTagRepository.find({
        where: { tagId },
      });
      filteredAudienceIds = audienceHasTagsForFilter.map(aht => aht.audienceId);

      // Nếu không có audience nào có tag này, trả về kết quả rỗng
      if (filteredAudienceIds.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: limit,
            totalPages: 0,
            currentPage: page,
          },
        };
      }

      // Thêm điều kiện filter theo audience IDs
      if (Array.isArray(where)) {
        where = where.map(w => ({ ...w, id: In(filteredAudienceIds!) }));
      } else {
        where = { ...where, id: In(filteredAudienceIds!) };
      }
    }

    // Nếu có filter theo segmentId, cần lọc audience theo segment criteria
    if (segmentId) {
      const segment = await this.adminSegmentRepository.findOne({
        where: { id: segmentId },
      });

      if (!segment) {
        // Nếu segment không tồn tại, trả về kết quả rỗng
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: limit,
            totalPages: 0,
            currentPage: page,
          },
        };
      }

      // Sử dụng query builder để lọc audience theo segment criteria
      const queryBuilder = this.adminAudienceRepository.createQueryBuilder('audience');

      // Áp dụng segment criteria
      this.applyCriteriaToQuery(queryBuilder, segment.criteria);

      // Áp dụng các điều kiện where hiện tại
      if (Array.isArray(where)) {
        // Nếu where là array (OR conditions), cần xử lý phức tạp hơn
        if (where.length > 0) {
          const orConditions = where.map((condition, index) => {
            const conditions: string[] = [];
            const params: any = {};

            Object.entries(condition).forEach(([key, value]) => {
              if (key === 'id' && value && typeof value === 'object' && 'value' in value) {
                // Handle In() operator
                conditions.push(`audience.${key} IN (:...ids${index})`);
                params[`ids${index}`] = (value as any).value;
              } else if (key === 'phoneNumber' && value && typeof value === 'object') {
                // Handle complex phone number conditions
                if ('operator' in value && value.operator === 'not') {
                  conditions.push(`(audience.${key} IS NOT NULL AND audience.${key} != '')`);
                } else {
                  conditions.push(`(audience.${key} IS NULL OR audience.${key} = '')`);
                }
              } else if (typeof value === 'object' && 'value' in value) {
                // Handle Like() operator
                conditions.push(`audience.${key} LIKE :${key}${index}`);
                params[`${key}${index}`] = (value as any).value;
              } else {
                conditions.push(`audience.${key} = :${key}${index}`);
                params[`${key}${index}`] = value;
              }
            });

            queryBuilder.setParameters(params);
            return conditions.join(' AND ');
          });

          if (orConditions.length > 0) {
            queryBuilder.andWhere(`(${orConditions.join(' OR ')})`);
          }
        }
      } else {
        // Nếu where là object đơn giản
        Object.entries(where).forEach(([key, value]) => {
          if (key === 'id' && value && typeof value === 'object' && 'value' in value) {
            queryBuilder.andWhere(`audience.${key} IN (:...ids)`, { ids: (value as any).value });
          } else if (key === 'phoneNumber' && value && typeof value === 'object') {
            if ('operator' in value && value.operator === 'not') {
              queryBuilder.andWhere(`(audience.${key} IS NOT NULL AND audience.${key} != '')`);
            } else {
              queryBuilder.andWhere(`(audience.${key} IS NULL OR audience.${key} = '')`);
            }
          } else if (typeof value === 'object' && 'value' in value) {
            queryBuilder.andWhere(`audience.${key} LIKE :${key}`, { [key]: (value as any).value });
          } else {
            queryBuilder.andWhere(`audience.${key} = :${key}`, { [key]: value });
          }
        });
      }

      // Lấy audience IDs từ query builder
      const segmentAudiences = await queryBuilder.select('audience.id').getMany();
      const segmentAudienceIds = segmentAudiences.map(a => a.id);

      if (segmentAudienceIds.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: limit,
            totalPages: 0,
            currentPage: page,
          },
        };
      }

      // Cập nhật where condition với segment audience IDs
      if (filteredAudienceIds) {
        // Nếu đã có filter từ tagId, lấy giao của hai tập hợp
        const intersectionIds = filteredAudienceIds.filter(id => segmentAudienceIds.includes(id));
        if (intersectionIds.length === 0) {
          return {
            items: [],
            meta: {
              totalItems: 0,
              itemCount: 0,
              itemsPerPage: limit,
              totalPages: 0,
              currentPage: page,
            },
          };
        }
        where = { id: In(intersectionIds) };
      } else {
        where = { id: In(segmentAudienceIds) };
      }
    }

    // Đếm tổng số audience
    const total = await this.adminAudienceRepository.count({ where });

    // Lấy danh sách audience với phân trang và sắp xếp
    const audiences = await this.adminAudienceRepository.find({
      where,
      order: { [sortBy]: sortDirection },
      skip: offset,
      take: limit,
    });

    // Lấy danh sách ID của audience
    const audienceIds = audiences.map((a) => a.id);

    // Lấy tất cả các trường tùy chỉnh của các audience
    let customFieldsQuery: FindOptionsWhere<AdminAudienceCustomField> = {
      audienceId: In(audienceIds),
    };

    // Thêm điều kiện tìm kiếm theo tên trường tùy chỉnh
    if (customFieldName) {
      customFieldsQuery.fieldName = Like(`%${customFieldName}%`);
    }

    // Thêm điều kiện tìm kiếm theo giá trị trường tùy chỉnh
    if (customFieldValue) {
      customFieldsQuery.fieldValue = Like(`%${customFieldValue}%`);
    }

    const customFields = await this.adminAudienceCustomFieldRepository.find({
      where: customFieldsQuery,
    });

    // Lấy tất cả tags của các audience
    const audienceHasTags = await this.adminAudienceHasTagRepository.find({
      where: { audienceId: In(audienceIds) },
    });

    // Lấy tất cả tag IDs
    const tagIds = [...new Set(audienceHasTags.map(aht => aht.tagId))];

    // Lấy thông tin chi tiết của các tags
    let allTags: AdminTag[] = [];
    if (tagIds.length > 0) {
      allTags = await this.adminTagRepository.find({
        where: { id: In(tagIds) },
      });
    }

    // Tạo map để dễ dàng tìm tags theo audience ID
    const audienceTagsMap = new Map<number, AdminTag[]>();
    for (const audienceHasTag of audienceHasTags) {
      const tag = allTags.find(t => t.id === audienceHasTag.tagId);
      if (tag) {
        if (!audienceTagsMap.has(audienceHasTag.audienceId)) {
          audienceTagsMap.set(audienceHasTag.audienceId, []);
        }
        audienceTagsMap.get(audienceHasTag.audienceId)!.push(tag);
      }
    }

    // Chuyển đổi kết quả thành DTO
    const items: AudienceResponseDto[] = [];
    for (const audience of audiences) {
      const audienceCustomFields = customFields.filter(
        (cf) => cf.audienceId === audience.id,
      );
      const audienceTags = audienceTagsMap.get(audience.id) || [];
      items.push(this.mapToDto(audience, audienceCustomFields, audienceTags));
    }

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta: PaginationMeta = {
      totalItems: total,
      itemCount: items.length,
      itemsPerPage: limit,
      totalPages,
      currentPage: page,
    };

    return {
      items,
      meta,
    };
  }

  /**
   * Lấy audience theo ID
   * @param employeeId ID của employee
   * @param id ID của audience
   * @returns Audience
   */
  async findOne(id: number): Promise<AudienceResponseDto> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND, `Audience với ID ${id} không tồn tại`);
    }

    // Lấy các trường tùy chỉnh
    const customFields = await this.adminAudienceCustomFieldRepository.find({
      where: { audienceId: id },
    });

    // Lấy các tag của audience
    const audienceHasTags = await this.adminAudienceHasTagRepository.find({
      where: { audienceId: id },
    });

    // Lấy thông tin chi tiết của các tags
    let tags: AdminTag[] = [];
    if (audienceHasTags.length > 0) {
      const tagIds = audienceHasTags.map(aht => aht.tagId);
      tags = await this.adminTagRepository.find({
        where: { id: In(tagIds) },
      });
    }

    return this.mapToDto(audience, customFields, tags);
  }

  /**
   * Xóa audience
   * @param employeeId ID của employee
   * @param id ID của audience
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async remove(id: number): Promise<boolean> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND);
    }

    // Xóa các trường tùy chỉnh
    await this.adminAudienceCustomFieldRepository.delete({ audienceId: id });

    // Xóa các liên kết với tags
    await this.adminAudienceHasTagRepository.deleteByAudienceId(id);

    // Xóa audience
    await this.adminAudienceRepository.remove(audience);

    return true;
  }

  /**
   * Xóa nhiều audience
   * @param ids Danh sách ID audience cần xóa
   * @returns Kết quả xóa nhiều
   */
  @Transactional()
  async bulkDelete(ids: number[]): Promise<BulkDeleteResponseDto> {
    const deletedIds: number[] = [];
    const failedIds: number[] = [];

    for (const id of ids) {
      try {
        const audience = await this.adminAudienceRepository.findOne({ where: { id } });
        if (!audience) {
          failedIds.push(id);
          continue;
        }

        // Xóa các trường tùy chỉnh
        await this.adminAudienceCustomFieldRepository.delete({ audienceId: id });

        // Xóa các liên kết với tags
        await this.adminAudienceHasTagRepository.deleteByAudienceId(id);

        // Xóa audience
        await this.adminAudienceRepository.remove(audience);
        deletedIds.push(id);
      } catch (error) {
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message = failedCount > 0
      ? `Đã xóa ${deletedCount} audience thành công, ${failedCount} audience không thể xóa`
      : `Đã xóa ${deletedCount} audience thành công`;

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param audience Audience entity
   * @param customFields Các trường tùy chỉnh
   * @param tags Các tag
   * @returns Audience DTO
   */
  private mapToDto(
    audience: AdminAudience,
    customFields: AdminAudienceCustomField[],
    tags: AdminTag[],
  ): AudienceResponseDto {
    const dto = new AudienceResponseDto();
    dto.id = audience.id;
    dto.employeeId = 0; // TODO: Cần thêm employeeId vào AdminAudience entity
    dto.name = audience.name;
    dto.email = audience.email;
    dto.countryCode = audience.countryCode;
    dto.phoneNumber = audience.phoneNumber;

    // Chuyển đổi avatar S3 key thành CDN URL
    let avatarUrl: string | null = audience.avatar;
    if (audience.avatar) {
      try {
        const cdnUrl = this.cdnService.generateUrlView(audience.avatar, TimeIntervalEnum.ONE_DAY);
        avatarUrl = cdnUrl || audience.avatar; // Fallback to original key if CDN fails
      } catch (error) {
        console.warn(`Không thể tạo URL CDN cho avatar audience ${audience.id}: ${error.message}`);
        avatarUrl = audience.avatar; // Keep original key as fallback
      }
    }
    dto.avatar = avatarUrl;

    dto.createdAt = audience.createdAt;
    dto.updatedAt = audience.updatedAt;

    // Chuyển đổi các trường tùy chỉnh
    dto.customFields = customFields.map((field) => {
      const fieldDto = new CustomFieldResponseDto();
      fieldDto.id = field.id;
      fieldDto.audienceId = field.audienceId;
      fieldDto.fieldName = field.fieldName;
      fieldDto.fieldValue = field.fieldValue;
      fieldDto.fieldType = field.fieldType as any;
      fieldDto.createdAt = field.createdAt;
      fieldDto.updatedAt = field.updatedAt;
      return fieldDto;
    });

    // Chuyển đổi các tag
    dto.tags = tags.map((tag) => {
      const tagDto = new TagResponseDto();
      tagDto.id = tag.id;
      tagDto.name = tag.name;
      tagDto.color = tag.color;
      tagDto.createdBy = tag.createdBy;
      tagDto.updatedBy = tag.updatedBy;
      tagDto.createdAt = tag.createdAt;
      tagDto.updatedAt = tag.updatedAt;
      return tagDto;
    });

    return dto;
  }

  /**
   * Tạo presigned URL để upload avatar
   * @param id ID của audience
   * @param createAvatarUploadUrlDto Dữ liệu tạo URL upload
   * @returns Thông tin URL upload
   */
  async createAvatarUploadUrl(
    id: number,
    createAvatarUploadUrlDto: CreateAvatarUploadUrlDto,
  ): Promise<AvatarUploadUrlResponseDto> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND, `Audience với ID ${id} không tồn tại`);
    }

    // Tạo S3 key cho avatar
    const s3Key = generateS3Key({
      baseFolder: 'marketing',
      categoryFolder: CategoryFolderEnum.CUSTOMER_AVATAR,
      useTimeFolder: true,
      prefix: `admin`,
      fileName: `avatar.${createAvatarUploadUrlDto.mediaType.split('/')[1]}`,
    });

    // Tạo presigned URL
    const uploadUrl = await this.s3Service.createPresignedWithID(
      s3Key,
      TimeIntervalEnum.ONE_HOUR, // URL hết hạn sau 1 giờ
      createAvatarUploadUrlDto.mediaType as any, // Cast to MediaType
      FileSizeEnum.FIVE_MB, // Giới hạn 5MB cho avatar
    );

    // Tính thời gian hết hạn
    const expiresAt = Math.floor(Date.now() / 1000) + (60 * 60); // 1 giờ

    return {
      uploadUrl,
      s3Key,
      expiresAt,
    };
  }



  /**
   * Xóa avatar của audience
   * @param id ID của audience
   * @returns Audience đã cập nhật
   */
  async removeAvatar(id: number): Promise<AudienceResponseDto> {
    // Kiểm tra audience tồn tại
    const audience = await this.adminAudienceRepository.findOne({
      where: { id },
    });

    if (!audience) {
      throw new AppException(ErrorCode.AUDIENCE_NOT_FOUND, `Audience với ID ${id} không tồn tại`);
    }

    // Xóa avatar
    audience.avatar = null;
    audience.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu audience
    const updatedAudience = await this.adminAudienceRepository.save(audience);

    // Lấy các trường tùy chỉnh
    const customFields = await this.adminAudienceCustomFieldRepository.find({
      where: { audienceId: id },
    });

    // Lấy các tag của audience
    const audienceHasTags = await this.adminAudienceHasTagRepository.find({
      where: { audienceId: id },
    });

    // Lấy thông tin chi tiết của các tags
    let tags: AdminTag[] = [];
    if (audienceHasTags.length > 0) {
      const tagIds = audienceHasTags.map(aht => aht.tagId);
      tags = await this.adminTagRepository.find({
        where: { id: In(tagIds) },
      });
    }

    return this.mapToDto(updatedAudience as AdminAudience, customFields, tags);
  }

  /**
   * Merge nhiều audience thành một audience mới
   * @param mergeDto Thông tin merge audience
   * @returns Kết quả merge audience
   */
  @Transactional()
  async mergeAudiences(mergeDto: MergeAudienceDto): Promise<MergeAudienceResponseDto> {
    // Validate các audience tồn tại
    const audiences = await this.adminAudienceRepository.find({
      where: { id: In(mergeDto.audienceIds) },
    });

    if (audiences.length !== mergeDto.audienceIds.length) {
      const foundIds = audiences.map(a => a.id);
      const missingIds = mergeDto.audienceIds.filter(id => !foundIds.includes(id));
      throw new AppException(
        ErrorCode.AUDIENCE_NOT_FOUND,
        `Không tìm thấy audience với ID: ${missingIds.join(', ')}`
      );
    }

    // Tạo audience mới
    const newAudience = new AdminAudience();
    newAudience.name = mergeDto.newAudienceName || `Merged Audience - ${new Date().toISOString()}`;
    newAudience.email = mergeDto.newAudienceEmail || '';
    // TODO: Cập nhật merge logic để sử dụng countryCode và phoneNumber
    newAudience.countryCode = null;
    newAudience.phoneNumber = mergeDto.newAudiencePhone || null;
    newAudience.createdAt = Math.floor(Date.now() / 1000);
    newAudience.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu audience mới
    const savedNewAudience = await this.adminAudienceRepository.save(newAudience);

    // Thu thập tất cả tags từ các audience cũ
    const allAudienceHasTags = await this.adminAudienceHasTagRepository.find({
      where: { audienceId: In(mergeDto.audienceIds) },
    });

    // Sử dụng custom fields từ frontend thay vì thu thập từ audience cũ
    const customFieldsToCreate: any[] = [];
    if (mergeDto.customFields && mergeDto.customFields.length > 0) {
      mergeDto.customFields.forEach(cf => {
        customFieldsToCreate.push({
          audienceId: savedNewAudience.id,
          fieldName: cf.fieldName,
          fieldValue: cf.fieldValue,
          fieldType: cf.fieldType,
          createdAt: Math.floor(Date.now() / 1000),
          updatedAt: Math.floor(Date.now() / 1000),
        });
      });
    }

    // Lưu custom fields cho audience mới
    if (customFieldsToCreate.length > 0) {
      const newCustomFields = customFieldsToCreate.map(cf => {
        const customField = new AdminAudienceCustomField();
        customField.audienceId = cf.audienceId;
        customField.fieldName = cf.fieldName;
        customField.fieldValue = cf.fieldValue;
        customField.fieldType = cf.fieldType;
        customField.createdAt = cf.createdAt;
        customField.updatedAt = cf.updatedAt;
        return customField;
      });
      await this.adminAudienceCustomFieldRepository.save(newCustomFields);
    }

    // Merge tags (loại bỏ trùng lặp theo tagId)
    const uniqueTagIds = new Set<number>();
    allAudienceHasTags.forEach(aht => {
      uniqueTagIds.add(aht.tagId);
    });

    // Lưu tags cho audience mới
    if (uniqueTagIds.size > 0) {
      // Validate tags tồn tại (để đảm bảo data consistency)
      const tagIdsArray = Array.from(uniqueTagIds);
      const existingTags = await this.adminTagRepository.find({
        where: {
          id: In(tagIdsArray),
        },
      });

      if (existingTags.length !== tagIdsArray.length) {
        const existingTagIds = existingTags.map(tag => tag.id);
        const missingTagIds = tagIdsArray.filter(id => !existingTagIds.includes(id));
        throw new AppException(
          MARKETING_ERROR_CODES.TAG_NOT_FOUND,
          `Không tìm thấy tag với ID: ${missingTagIds.join(', ')} khi merge audience`
        );
      }

      const newAudienceHasTags = tagIdsArray.map(tagId => {
        const audienceHasTag = new AdminAudienceHasTag();
        audienceHasTag.audienceId = savedNewAudience.id;
        audienceHasTag.tagId = tagId;
        return audienceHasTag;
      });
      await this.adminAudienceHasTagRepository.save(newAudienceHasTags);
    }

    // Xóa các audience cũ
    const deletedIds: number[] = [];
    for (const audienceId of mergeDto.audienceIds) {
      try {
        // Xóa custom fields
        await this.adminAudienceCustomFieldRepository.delete({ audienceId });

        // Xóa audience has tags
        await this.adminAudienceHasTagRepository.deleteByAudienceId(audienceId);

        // Xóa audience
        const audienceToDelete = audiences.find(a => a.id === audienceId);
        if (audienceToDelete) {
          await this.adminAudienceRepository.remove(audienceToDelete);
          deletedIds.push(audienceId);
        }
      } catch (error) {
        // Log lỗi nhưng không dừng quá trình
        console.warn(`Không thể xóa audience ${audienceId}:`, error);
      }
    }

    return {
      newAudience: {
        id: savedNewAudience.id,
        name: savedNewAudience.name,
        email: savedNewAudience.email,
        phone: savedNewAudience.phoneNumber || undefined,
        createdAt: savedNewAudience.createdAt,
        updatedAt: savedNewAudience.updatedAt,
      },
      mergedAudienceIds: deletedIds,
      mergedCount: deletedIds.length,
      message: `Đã merge ${deletedIds.length} audience thành công`,
    };
  }

  /**
   * Gắn nhiều tag với nhiều audience
   * @param attachDto Dữ liệu gắn tag với audience
   * @returns Kết quả gắn
   */
  @Transactional()
  async attachTagsToAudiences(attachDto: AttachTagsToAudiencesDto): Promise<AudienceTagResponseDto> {
    // Kiểm tra audiences tồn tại
    const audiences = await this.adminAudienceRepository.find({
      where: { id: attachDto.audienceIds.map(id => ({ id })) as any },
    });

    if (audiences.length !== attachDto.audienceIds.length) {
      const foundIds = audiences.map(a => a.id);
      const missingIds = attachDto.audienceIds.filter(id => !foundIds.includes(id));
      throw new AppException(MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND, `Không tìm thấy audiences với ID: ${missingIds.join(', ')}`);
    }

    // Kiểm tra tags tồn tại
    const tags = await this.adminTagRepository.find({
      where: { id: attachDto.tagIds.map(id => ({ id })) as any },
    });

    if (tags.length !== attachDto.tagIds.length) {
      const foundIds = tags.map(t => t.id);
      const missingIds = attachDto.tagIds.filter(id => !foundIds.includes(id));
      throw new AppException(MARKETING_ERROR_CODES.TAG_NOT_FOUND, `Không tìm thấy tags với ID: ${missingIds.join(', ')}`);
    }

    // Tạo các mối quan hệ audience-tag
    const audienceTagRelations: AdminAudienceHasTag[] = [];
    for (const audienceId of attachDto.audienceIds) {
      for (const tagId of attachDto.tagIds) {
        // Kiểm tra xem mối quan hệ đã tồn tại chưa
        const existing = await this.adminAudienceHasTagRepository.findOne({
          where: { audienceId, tagId },
        });

        if (!existing) {
          const relation = new AdminAudienceHasTag();
          relation.audienceId = audienceId;
          relation.tagId = tagId;
          audienceTagRelations.push(relation);
        }
      }
    }

    // Lưu các mối quan hệ mới
    if (audienceTagRelations.length > 0) {
      await this.adminAudienceHasTagRepository.save(audienceTagRelations);
    }

    const response = new AudienceTagResponseDto();
    response.affectedCount = audienceTagRelations.length;
    response.message = `Đã gắn ${audienceTagRelations.length} mối quan hệ audience-tag thành công`;

    return response;
  }

  /**
   * Bỏ gắn nhiều tag với nhiều audience
   * @param detachDto Dữ liệu bỏ gắn tag với audience
   * @returns Kết quả bỏ gắn
   */
  @Transactional()
  async detachTagsFromAudiences(detachDto: DetachTagsFromAudiencesDto): Promise<AudienceTagResponseDto> {
    // Xóa các mối quan hệ audience-tag
    let totalDeleted = 0;
    for (const audienceId of detachDto.audienceIds) {
      for (const tagId of detachDto.tagIds) {
        const deleted = await this.adminAudienceHasTagRepository.delete({
          audienceId,
          tagId,
        });
        totalDeleted += deleted;
      }
    }

    const response = new AudienceTagResponseDto();
    response.affectedCount = totalDeleted;
    response.message = `Đã bỏ gắn ${totalDeleted} mối quan hệ audience-tag thành công`;

    return response;
  }

  /**
   * Áp dụng segment criteria vào query builder
   * @param queryBuilder Query builder để áp dụng criteria
   * @param criteria Criteria của segment
   */
  private applyCriteriaToQuery(queryBuilder: any, criteria: any): void {
    if (!criteria) {
      return;
    }

    // Xử lý cấu trúc criteria
    const { conditionType, conditions, groups } = criteria;

    // Xử lý các điều kiện
    if (conditions && Array.isArray(conditions)) {
      const conditionClauses: string[] = [];
      const params: any = {};

      conditions.forEach((condition: any, index: number) => {
        const { field, operator, value } = condition;
        const paramKey = `condition_${index}`;

        let whereClause = '';

        if (field === 'tag') {
          // Xử lý điều kiện tag
          whereClause = this.applyTagCondition(operator, value, paramKey, params);
        } else if (field === 'email') {
          // Xử lý điều kiện email
          whereClause = this.applyFieldCondition('email', operator, value, paramKey, params);
        } else if (field === 'name') {
          // Xử lý điều kiện name
          whereClause = this.applyFieldCondition('name', operator, value, paramKey, params);
        } else if (field === 'phoneNumber' || field === 'phone') {
          // Xử lý điều kiện phone
          whereClause = this.applyFieldCondition('phoneNumber', operator, value, paramKey, params);
        }

        if (whereClause) {
          conditionClauses.push(whereClause);
        }
      });

      // Áp dụng các điều kiện với logic AND/OR
      if (conditionClauses.length > 0) {
        const logicOperator = conditionType === 'AND' ? ' AND ' : ' OR ';
        const combinedCondition = conditionClauses.join(logicOperator);
        queryBuilder.andWhere(`(${combinedCondition})`, params);
      }
    }

    // Xử lý các nhóm điều kiện con (recursive)
    if (groups && Array.isArray(groups)) {
      groups.forEach((group: any) => {
        this.applyCriteriaToQuery(queryBuilder, group);
      });
    }
  }

  /**
   * Áp dụng điều kiện tag
   */
  private applyTagCondition(operator: string, value: any, paramKey: string, params: any): string {
    switch (operator) {
      case 'hasTag':
      case 'equals':
      case 'eq':
        params[paramKey] = value;
        return `EXISTS (
          SELECT 1 FROM admin_audience_has_tags aht
          WHERE aht.audience_id = audience.id AND aht.tag_id = :${paramKey}
        )`;

      case 'hasAnyTag':
      case 'in':
        if (Array.isArray(value) && value.length > 0) {
          params[paramKey] = value;
          return `EXISTS (
            SELECT 1 FROM admin_audience_has_tags aht
            WHERE aht.audience_id = audience.id AND aht.tag_id IN (:...${paramKey})
          )`;
        }
        break;

      case 'notHasTag':
      case 'notEquals':
      case 'ne':
        params[paramKey] = value;
        return `NOT EXISTS (
          SELECT 1 FROM admin_audience_has_tags aht
          WHERE aht.audience_id = audience.id AND aht.tag_id = :${paramKey}
        )`;
    }
    return '';
  }

  /**
   * Áp dụng điều kiện cho các trường thông thường
   */
  private applyFieldCondition(field: string, operator: string, value: any, paramKey: string, params: any): string {
    switch (operator) {
      case 'equals':
      case 'eq':
        params[paramKey] = value;
        return `audience.${field} = :${paramKey}`;

      case 'notEquals':
      case 'ne':
        params[paramKey] = value;
        return `audience.${field} != :${paramKey}`;

      case 'contains':
      case 'like':
        params[paramKey] = `%${value}%`;
        return `audience.${field} LIKE :${paramKey}`;

      case 'notContains':
      case 'notLike':
        params[paramKey] = `%${value}%`;
        return `audience.${field} NOT LIKE :${paramKey}`;

      case 'startsWith':
        params[paramKey] = `${value}%`;
        return `audience.${field} LIKE :${paramKey}`;

      case 'endsWith':
        params[paramKey] = `%${value}`;
        return `audience.${field} LIKE :${paramKey}`;

      case 'isEmpty':
        return `(audience.${field} IS NULL OR audience.${field} = '')`;

      case 'isNotEmpty':
        return `(audience.${field} IS NOT NULL AND audience.${field} != '')`;

      case 'in':
        if (Array.isArray(value) && value.length > 0) {
          params[paramKey] = value;
          return `audience.${field} IN (:...${paramKey})`;
        }
        break;

      case 'notIn':
        if (Array.isArray(value) && value.length > 0) {
          params[paramKey] = value;
          return `audience.${field} NOT IN (:...${paramKey})`;
        }
        break;
    }
    return '';
  }
}
