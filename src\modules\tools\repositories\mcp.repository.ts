import { Injectable } from '@nestjs/common';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Mcp } from '../entities/mcp.entity';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho MCP
 */
@Injectable()
export class McpRepository {
  constructor(
    @InjectRepository(Mcp)
    private readonly repository: Repository<Mcp>,
  ) {}

  // Wrapper methods để expose Repository methods
  create(entityLike: Partial<Mcp>): Mcp {
    return this.repository.create(entityLike);
  }

  async save(entity: Mcp): Promise<Mcp> {
    return this.repository.save(entity);
  }

  async find(options?: any): Promise<Mcp[]> {
    return this.repository.find(options);
  }

  async findOne(options: any): Promise<Mcp | null> {
    return this.repository.findOne(options);
  }

  async update(criteria: any, partialEntity: any): Promise<any> {
    return this.repository.update(criteria, partialEntity);
  }

  async delete(criteria: any): Promise<any> {
    return this.repository.delete(criteria);
  }

  async count(options?: any): Promise<number> {
    return this.repository.count(options);
  }

  get manager() {
    return this.repository.manager;
  }

  /**
   * Tạo base query với các điều kiện cơ bản
   */
  private createBaseQuery(): SelectQueryBuilder<Mcp> {
    return this.repository.createQueryBuilder('mcp');
  }

  /**
   * Tìm MCP theo ID
   */
  async findById(id: string, userId: number): Promise<Mcp | null> {
    return this.createBaseQuery()
      .where('mcp.id = :id', { id })
      .andWhere('mcp.userId = :userId', { userId })
      .getOne();
  }

  /**
   * Tìm MCP theo server name
   */
  async findByServerName(nameServer: string): Promise<Mcp | null> {
    return this.createBaseQuery()
      .where('mcp.nameServer = :nameServer', { nameServer })
      .getOne();
  }

  /**
   * Tìm MCP theo server name và user
   */
  async findByUserAndServerName(nameServer: string, userId: number): Promise<Mcp | null> {
    return this.createBaseQuery()
      .where('mcp.nameServer = :nameServer', { nameServer })
      .andWhere('mcp.userId = :userId', { userId })
      .getOne();
  }

  /**
   * Lấy danh sách MCPs với phân trang
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 20,
    search?: string,
    transport?: string,
    userId?: number,
    employeeId?: number
  ): Promise<PaginatedResult<Mcp>> {
    const query = this.createBaseQuery();

    // Apply search
    if (search) {
      query.andWhere(
        '(mcp.nameServer ILIKE :search OR mcp.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply transport filter
    if (transport) {
      query.andWhere("mcp.config->>'transport' = :transport", { transport });
    }

    // Apply userId filter
    if (userId) {
      query.andWhere('mcp.userId = :userId', { userId });
    }

    // Apply employeeId filter
    if (employeeId) {
      query.andWhere('mcp.employeeId IS NOT NULL');
    }

    // Apply sorting
    query.orderBy('mcp.createdAt', 'DESC');

    // Apply pagination
    const offset = (page - 1) * limit;
    query.skip(offset).take(limit);

    const [items, total] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy tất cả MCPs (không phân trang)
   */
  async findAll(): Promise<Mcp[]> {
    return this.createBaseQuery()
      .orderBy('mcp.nameServer', 'ASC')
      .getMany();
  }

  /**
   * Đếm tổng số MCPs
   */
  async countAll(): Promise<number> {
    return this.createBaseQuery().getCount();
  }

  /**
   * Đếm số lượng MCPs theo transport type
   */
  async countByTransport(transport: string): Promise<number> {
    return this.createBaseQuery()
      .andWhere("mcp.config->>'transport' = :transport", { transport })
      .getCount();
  }

  /**
   * Kiểm tra tên server đã tồn tại chưa
   */
  async existsByServerName(nameServer: string, excludeId?: string): Promise<boolean> {
    const query = this.createBaseQuery()
      .where('mcp.nameServer = :nameServer', { nameServer });

    if (excludeId) {
      query.andWhere('mcp.id != :excludeId', { excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Cập nhật config của MCP
   */
  async updateConfig(id: string, config: Record<string, any>): Promise<void> {
    await this.repository.createQueryBuilder()
      .update(Mcp)
      .set({
        config: config,
        updatedAt: Date.now(),
      })
      .where('id = :id', { id })
      .execute();
  }

  /**
   * Lấy MCPs theo transport type
   */
  async findByTransport(transport: 'http' | 'sse'): Promise<Mcp[]> {
    return this.createBaseQuery()
      .andWhere("mcp.config->>'transport' = :transport", { transport })
      .orderBy('mcp.nameServer', 'ASC')
      .getMany();
  }

  /**
   * Tìm MCPs theo URL
   */
  async findByUrl(url: string): Promise<Mcp[]> {
    return this.createBaseQuery()
      .andWhere("mcp.config->>'url' = :url", { url })
      .orderBy('mcp.nameServer', 'ASC')
      .getMany();
  }

  /**
   * Lấy MCPs được tạo gần đây
   */
  async findRecent(limit: number = 10): Promise<Mcp[]> {
    return this.createBaseQuery()
      .orderBy('mcp.createdAt', 'DESC')
      .limit(limit)
      .getMany();
  }

  /**
   * Lấy MCPs theo user tạo
   */
  async findByUserId(userId: number): Promise<Mcp[]> {
    return this.createBaseQuery()
      .where('mcp.userId = :userId', { userId })
      .orderBy('mcp.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Lấy MCPs theo employee tạo
   */
  async findByEmployeeId(employeeId: number): Promise<Mcp[]> {
    return this.createBaseQuery()
      .where('mcp.employeeId = :employeeId', { employeeId })
      .orderBy('mcp.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Bulk update headers
   */
  async bulkUpdateHeaders(ids: string[], headers: string): Promise<number> {
    const result = await this.repository.createQueryBuilder()
      .update(Mcp)
      .set({
        headers: headers,
        updatedAt: Date.now(),
      })
      .where('id IN (:...ids)', { ids })
      .execute();

    return result.affected || 0;
  }

  /**
   * Kiểm tra MCP có đang được sử dụng bởi agent nào không
   */
  async isUsedByAgents(id: string): Promise<boolean> {
    const count = await this.manager
      .createQueryBuilder()
      .select('COUNT(*) as count')
      .from('agents_mcp', 'am')
      .where('am.mcpId = :id', { id })
      .getRawOne();

    return parseInt(count.count) > 0;
  }
}
