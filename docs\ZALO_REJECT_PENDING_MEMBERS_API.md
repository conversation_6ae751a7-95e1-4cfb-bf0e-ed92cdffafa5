# Zalo Reject Pending Members API Documentation

## Tổng quan

API này cho phép từ chối các thành viên chờ duyệt vào nhóm chat Zalo GMF.

## Đi<PERSON><PERSON> kiện sử dụng

- **Quyền cần thiết**: OA phải được cấp quyền quản lý thông tin nhóm
- **Vai trò**: OA phải là admin của nhóm
- **Trạng thái**: Các user ID phải đang trong danh sách chờ duyệt
- **Giới hạn**: Tối đa 100 thành viên mỗi lần từ chối

## Endpoint

### Từ chối thành viên chờ duyệt

```
POST /v1/zalo-group-management/{integrationId}/{groupId}/pending-members/reject
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| integrationId | string | Yes | ID của Integration Zalo OA (UUID format) |
| groupId | string | Yes | ID của nhóm chat Zalo |

#### Request Body

```json
{
  "memberUserIds": [
    "8756287263669629130",
    "1234567890123456789"
  ]
}
```

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| memberUserIds | string[] | Yes | Danh sách user ID bị từ chối (1-100 items) |

#### Response

**Success (200)**
```json
{
  "success": true,
  "message": "Từ chối thành viên chờ duyệt thành công",
  "data": {
    "error": 0,
    "message": "Success",
    "rejectedCount": 2
  }
}
```

**Error (400) - Validation Error**
```json
{
  "success": false,
  "message": "Không thể từ chối quá 100 thành viên cùng lúc",
  "code": 1001
}
```

**Error (404) - Group Not Found**
```json
{
  "success": false,
  "message": "Không tìm thấy nhóm chat",
  "code": 1004
}
```

**Error (403) - Permission Denied**
```json
{
  "success": false,
  "message": "OA không có quyền quản lý nhóm này",
  "code": 1003
}
```

## Validation Rules

### memberUserIds
- **Required**: Bắt buộc
- **Type**: Array of strings
- **Min Items**: 1
- **Max Items**: 100
- **Item Format**: Zalo User ID (string)

## Error Codes

| Code | Message | Description |
|------|---------|-------------|
| 0 | Success | Thành công |
| 1001 | Validation Error | Dữ liệu đầu vào không hợp lệ |
| 1003 | Permission Denied | Không có quyền thực hiện |
| 1004 | Not Found | Không tìm thấy resource |
| 9999 | Internal Error | Lỗi hệ thống |

## Workflow

### 1. Kiểm tra quyền
- Xác thực JWT token
- Kiểm tra quyền truy cập Integration
- Xác minh OA có quyền quản lý nhóm

### 2. Validation
- Kiểm tra format của groupId
- Validate danh sách memberUserIds
- Kiểm tra giới hạn số lượng (≤ 100)

### 3. Gọi Zalo API
- Endpoint: `https://openapi.zalo.me/v3.0/oa/group/rejectpendinginvite`
- Method: POST
- Headers: `access_token`, `Content-Type: application/json`

### 4. Xử lý response
- Parse response từ Zalo
- Transform data theo format API
- Return kết quả cho client

## Example Usage

### cURL
```bash
curl -X POST \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4/pending-members/reject' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "memberUserIds": [
      "8756287263669629130",
      "1234567890123456789"
    ]
  }'
```

### JavaScript
```javascript
const response = await fetch('/v1/zalo-group-management/integration-id/group-id/pending-members/reject', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    memberUserIds: ['8756287263669629130', '1234567890123456789']
  })
});

const result = await response.json();
if (result.success) {
  console.log(`Đã từ chối ${result.data.rejectedCount} thành viên`);
}
```

## Related APIs

- **GET** `/pending-members` - Lấy danh sách thành viên chờ duyệt
- **POST** `/pending-members/accept` - Duyệt thành viên chờ duyệt
- **POST** `/members/remove` - Xóa thành viên khỏi nhóm

## Notes

1. **Batch Processing**: API hỗ trợ từ chối nhiều thành viên cùng lúc (tối đa 100)
2. **Idempotent**: Có thể gọi lại API với cùng dữ liệu mà không gây side effect
3. **Real-time**: Thay đổi có hiệu lực ngay lập tức trên Zalo
4. **Audit**: Tất cả thao tác được log để audit
