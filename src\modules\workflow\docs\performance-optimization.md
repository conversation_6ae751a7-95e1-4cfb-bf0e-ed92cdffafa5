# Workflow Performance Optimization Guide

## 📋 Overview

This document outlines the performance optimization strategies implemented in the Workflow module to ensure fast, scalable, and efficient operations.

## 🚀 Performance Enhancements

### 1. Redis Caching Strategy

#### Cache Layers

- **L1 Cache**: In-memory application cache for frequently accessed data
- **L2 Cache**: Redis distributed cache for shared data across instances
- **L3 Cache**: Database query result caching

#### Cache Patterns

```typescript
// Workflow caching
await cacheService.cacheWorkflow(workflow, userId);
const cachedWorkflow = await cacheService.getCachedWorkflow(workflowId);

// List caching with query parameters
await cacheService.cacheWorkflowList(userId, workflows, queryParams);
const cachedList = await cacheService.getCachedWorkflowList(userId, queryParams);

// Statistics caching
await cacheService.cacheStatistics('execution_stats_123', statistics, 600);
const cachedStats = await cacheService.getCachedStatistics('execution_stats_123');
```

#### Cache TTL Strategy

| Data Type | TTL | Reason |
|-----------|-----|--------|
| Workflows | 1 hour | Moderate update frequency |
| Workflow Lists | 30 minutes | Frequent pagination requests |
| Executions | 30 minutes | Real-time status updates |
| Node Definitions | 2 hours | Rarely changes |
| Statistics | 10 minutes | Aggregated data, acceptable delay |

#### Cache Invalidation

```typescript
// Automatic invalidation on updates
await queryOptimizer.invalidateWorkflowRelatedCache(workflowId, userId);
await queryOptimizer.invalidateExecutionRelatedCache(executionId, userId, workflowId);

// Manual cache clearing
await cacheService.clearAllCache();
```

### 2. Database Query Optimization

#### Indexing Strategy

```sql
-- User workflow queries (most common)
CREATE INDEX "IDX_workflow_user_id_updated_at" 
ON "workflows" ("user_id", "updated_at" DESC);

-- Execution queries with JSONB metadata
CREATE INDEX "IDX_workflow_execution_user_id_started_at" 
ON "workflow_executions" ((metadata->>'userId'), "started_at" DESC);

-- Full-text search indexes
CREATE INDEX "IDX_workflow_name_gin" 
ON "workflows" USING gin(to_tsvector('english', "name"));

-- Array operations for tags
CREATE INDEX "IDX_workflow_tags_gin" 
ON "workflows" USING gin("tags");
```

#### Query Optimization Patterns

```typescript
// Optimized workflow queries with caching
const { workflows, total, fromCache } = await queryOptimizer.findWorkflowsOptimized(
  userId,
  {
    where: { isActive: true, search: 'automation' },
    take: 20,
    skip: 0,
  }
);

// Single workflow with cache-first strategy
const { workflow, fromCache } = await queryOptimizer.findWorkflowByIdOptimized(
  workflowId,
  userId
);
```

#### Pagination Optimization

- **Cursor-based pagination** for large datasets
- **Limit/offset optimization** with proper indexing
- **Count query optimization** using estimated counts for large tables

### 3. Response Compression

#### Compression Middleware

```typescript
// Intelligent compression based on content type and size
@Injectable()
export class WorkflowCompressionMiddleware implements NestMiddleware {
  // Compress JSON responses > 1KB
  // Use different compression levels based on endpoint
  // Skip compression for SSE streams
}
```

#### Compression Strategy

| Endpoint Type | Compression Level | Threshold |
|---------------|-------------------|-----------|
| Workflow Lists | High (8) | 512 bytes |
| Single Workflows | Medium (6) | 2KB |
| Real-time Data | Low (4) | 1KB |
| Statistics | High (8) | 512 bytes |

### 4. Connection Pooling

#### Database Connection Optimization

```typescript
// TypeORM connection pool configuration
{
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  // Optimized pool settings
  extra: {
    max: 20,           // Maximum connections
    min: 5,            // Minimum connections
    acquire: 30000,    // 30 seconds acquire timeout
    idle: 10000,       // 10 seconds idle timeout
    evict: 1000,       // 1 second eviction interval
  }
}
```

#### Redis Connection Optimization

```typescript
// Redis connection with optimized settings
const redis = new Redis({
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT),
  // Performance optimizations
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  lazyConnect: true,
  keepAlive: 30000,
  // Connection pooling
  family: 4,
  connectTimeout: 10000,
  commandTimeout: 5000,
});
```

## 📊 Performance Monitoring

### 1. Response Time Tracking

```typescript
// Automatic response time logging
const startTime = Date.now();
const result = await service.findWorkflows(userId, query);
const duration = Date.now() - startTime;

logger.debug(`Query completed in ${duration}ms (${fromCache ? 'cached' : 'database'})`);
```

### 2. Cache Hit Rate Monitoring

```typescript
// Cache performance metrics
const cacheStats = {
  hits: cacheHits,
  misses: cacheMisses,
  hitRate: (cacheHits / (cacheHits + cacheMisses)) * 100,
  avgResponseTime: totalResponseTime / requestCount,
};
```

### 3. Database Query Performance

```typescript
// Query performance tracking
const queryMetrics = {
  query: 'findWorkflowsOptimized',
  duration: 150,
  rowsReturned: 25,
  cacheUsed: true,
  indexesUsed: ['IDX_workflow_user_id_updated_at'],
};
```

## 🎯 Performance Targets

### Response Time Targets

| Operation | Target | Acceptable | Notes |
|-----------|--------|------------|-------|
| Get Workflow List | < 200ms | < 500ms | With caching |
| Get Single Workflow | < 100ms | < 300ms | Cache hit preferred |
| Create Workflow | < 500ms | < 1000ms | Database write |
| Update Workflow | < 300ms | < 800ms | With cache invalidation |
| Execute Workflow | < 200ms | < 500ms | Queue job creation |
| Get Statistics | < 150ms | < 400ms | Cached aggregations |

### Throughput Targets

| Endpoint | Target RPS | Peak RPS | Notes |
|----------|------------|----------|-------|
| Workflow CRUD | 100 RPS | 200 RPS | Per instance |
| Execution Queries | 150 RPS | 300 RPS | Read-heavy |
| Node Testing | 50 RPS | 100 RPS | CPU intensive |
| SSE Connections | 500 concurrent | 1000 concurrent | Per instance |

### Cache Performance Targets

| Cache Type | Hit Rate Target | TTL | Invalidation Strategy |
|------------|-----------------|-----|----------------------|
| Workflow Cache | > 80% | 1 hour | On update/delete |
| List Cache | > 70% | 30 minutes | On any workflow change |
| Statistics Cache | > 90% | 10 minutes | Time-based |
| Node Definitions | > 95% | 2 hours | Manual/deployment |

## 🔧 Optimization Techniques

### 1. Lazy Loading

```typescript
// Load related data only when needed
const workflow = await workflowRepository.findOne(id);
// Nodes and edges loaded separately if needed
if (includeNodes) {
  workflow.nodes = await workflowNodeRepository.findByWorkflowId(id);
}
```

### 2. Batch Operations

```typescript
// Batch cache invalidation
await Promise.all([
  cacheService.invalidateWorkflowCache(workflowId),
  cacheService.invalidateUserWorkflowListCache(userId),
  cacheService.invalidateStatistics(`stats_${userId}`),
]);
```

### 3. Selective Field Loading

```typescript
// Load only required fields
const workflows = await workflowRepository
  .createQueryBuilder('workflow')
  .select(['workflow.id', 'workflow.name', 'workflow.status'])
  .where('workflow.userId = :userId', { userId })
  .getMany();
```

### 4. Parallel Processing

```typescript
// Parallel data fetching
const [workflows, statistics, nodeDefinitions] = await Promise.all([
  workflowService.findAll(userId, query),
  statisticsService.getOverview(userId),
  nodeDefinitionService.findAll(),
]);
```

## 📈 Performance Testing

### Load Testing Scenarios

1. **Normal Load**: 50 concurrent users, 30-minute duration
2. **Peak Load**: 200 concurrent users, 15-minute duration
3. **Stress Test**: 500 concurrent users, 10-minute duration
4. **Endurance Test**: 100 concurrent users, 2-hour duration

### Performance Benchmarks

```bash
# Example load test with Artillery
artillery run --config load-test-config.yml workflow-load-test.yml

# Expected results:
# - 95th percentile response time < 500ms
# - Error rate < 1%
# - Cache hit rate > 80%
# - Database connection pool utilization < 80%
```

## 🚨 Performance Alerts

### Monitoring Thresholds

- **Response Time**: Alert if 95th percentile > 1000ms
- **Error Rate**: Alert if > 2% over 5 minutes
- **Cache Hit Rate**: Alert if < 60% over 10 minutes
- **Database Connections**: Alert if > 90% pool utilization
- **Memory Usage**: Alert if > 85% heap usage
- **CPU Usage**: Alert if > 80% for 5 minutes

### Performance Degradation Response

1. **Immediate**: Check cache hit rates and database performance
2. **Short-term**: Scale horizontally if needed
3. **Medium-term**: Optimize slow queries and cache strategies
4. **Long-term**: Review architecture and data patterns

## 🔄 Continuous Optimization

### Regular Performance Reviews

- **Weekly**: Review performance metrics and cache hit rates
- **Monthly**: Analyze slow queries and optimization opportunities
- **Quarterly**: Review architecture and scaling strategies

### Performance Improvement Process

1. **Identify**: Use monitoring to find bottlenecks
2. **Analyze**: Profile code and database queries
3. **Optimize**: Implement caching, indexing, or code improvements
4. **Test**: Validate improvements with load testing
5. **Monitor**: Track performance impact in production

This performance optimization strategy ensures the Workflow module can handle high loads while maintaining fast response times and efficient resource utilization.
