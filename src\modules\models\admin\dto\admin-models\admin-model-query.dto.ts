import { QueryDto } from '@/common/dto';
import { ProviderLlmEnum } from '@/modules/models/constants';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export enum AdminModelType {
    SYSTEM = 'SYSTEM',
    ADMIN = 'ADMIN',
    ALL = 'ALL',
}

/**
 * DTO cho query danh sách model của admin
 */
export class AdminModelQueryDto extends QueryDto {
    @ApiProperty({
        description: 'Nhà cung cấp model',
        enum: ProviderLlmEnum,
        example: ProviderLlmEnum.OPENAI,
        required: true,
    })
    @IsEnum(ProviderLlmEnum)
    @IsNotEmpty()
    provider: ProviderLlmEnum = ProviderLlmEnum.OPENAI;
}
