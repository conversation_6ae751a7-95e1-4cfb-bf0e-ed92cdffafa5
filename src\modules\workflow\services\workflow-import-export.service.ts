import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { WorkflowRepository } from '../repositories/workflow.repository';
import { Workflow } from '../entities/workflow.entity';
import { WorkflowValidationService } from './workflow-validation.service';
import { UpdateWorkflowDefinitionDto } from '../dto/definition';

/**
 * Interface cho workflow export data
 */
export interface WorkflowExportData {
  workflow: {
    name: string;
    description?: string;
    definition: any;
    metadata: {
      exportedAt: number;
      exportedBy: number;
      version: string;
      schemaVersion: string;
    };
  };
}

/**
 * Interface cho import options
 */
export interface ImportOptions {
  overwriteExisting?: boolean;
  validateOnly?: boolean;
  preserveIds?: boolean;
  namePrefix?: string;
  nameSuffix?: string;
}

/**
 * Interface cho import result
 */
export interface ImportResult {
  success: boolean;
  workflowId?: string;
  workflow?: Workflow;
  validation?: any;
  errors?: string[];
  warnings?: string[];
}

/**
 * Service để handle workflow import/export functionality
 * Following existing patterns và integrating với validation service
 */
@Injectable()
export class WorkflowImportExportService {
  private readonly logger = new Logger(WorkflowImportExportService.name);

  constructor(
    private readonly workflowRepository: WorkflowRepository,
    private readonly validationService: WorkflowValidationService,
  ) {}

  /**
   * Export workflow to JSON format
   * @param workflowId - ID của workflow to export
   * @param userId - ID của user performing export
   * @returns Exported workflow data
   */
  async exportWorkflow(workflowId: string, userId: number): Promise<WorkflowExportData> {
    this.logger.log(`Exporting workflow: ${workflowId}`);

    // 1. Find workflow
    const workflow = await this.workflowRepository.findOne({
      where: { id: workflowId }
    });

    if (!workflow) {
      throw new BadRequestException(`Workflow with ID '${workflowId}' not found`);
    }

    // 2. Check permission (basic check)
    if (workflow.userId !== userId) {
      // TODO: Implement proper permission checking
      throw new BadRequestException('You do not have permission to export this workflow');
    }

    // 3. Create export data
    const exportData: WorkflowExportData = {
      workflow: {
        name: workflow.name,
        description: workflow.definition.metadata?.description,
        definition: workflow.definition,
        metadata: {
          exportedAt: Date.now(),
          exportedBy: userId,
          version: workflow.definition.metadata?.version || '1.0.0',
          schemaVersion: workflow.definition.metadata?.schemaVersion || '1.0'
        }
      }
    };

    this.logger.log(`Workflow exported successfully: ${workflowId}`);

    return exportData;
  }

  /**
   * Export multiple workflows
   * @param workflowIds - Array of workflow IDs to export
   * @param userId - ID của user performing export
   * @returns Array of exported workflow data
   */
  async exportWorkflows(workflowIds: string[], userId: number): Promise<WorkflowExportData[]> {
    this.logger.log(`Exporting ${workflowIds.length} workflows`);

    const exportPromises = workflowIds.map(id => this.exportWorkflow(id, userId));
    const results = await Promise.allSettled(exportPromises);

    const successfulExports: WorkflowExportData[] = [];
    const errors: string[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successfulExports.push(result.value);
      } else {
        errors.push(`Failed to export workflow ${workflowIds[index]}: ${result.reason.message}`);
      }
    });

    if (errors.length > 0) {
      this.logger.warn(`Export completed with errors: ${errors.join(', ')}`);
    }

    this.logger.log(`Exported ${successfulExports.length}/${workflowIds.length} workflows successfully`);

    return successfulExports;
  }

  /**
   * Import workflow from JSON data
   * @param importData - Workflow data to import
   * @param userId - ID của user performing import
   * @param options - Import options
   * @returns Import result
   */
  async importWorkflow(
    importData: WorkflowExportData,
    userId: number,
    options: ImportOptions = {}
  ): Promise<ImportResult> {
    this.logger.log(`Importing workflow: ${importData.workflow.name}`);

    try {
      // 1. Validate import data structure
      if (!this.validateImportData(importData)) {
        return {
          success: false,
          errors: ['Invalid import data structure']
        };
      }

      // 2. Validate workflow definition
      const validation = await this.validationService.validateWorkflowDefinition(
        importData.workflow.definition
      );

      if (!validation.isValid && !options.validateOnly) {
        return {
          success: false,
          validation,
          errors: validation.errors.map(e => e.message),
          warnings: validation.warnings.map(w => w.message)
        };
      }

      // 3. If validate only, return validation result
      if (options.validateOnly) {
        return {
          success: validation.isValid,
          validation,
          errors: validation.errors.map(e => e.message),
          warnings: validation.warnings.map(w => w.message)
        };
      }

      // 4. Prepare workflow name
      let workflowName = importData.workflow.name;
      if (options.namePrefix) {
        workflowName = `${options.namePrefix}${workflowName}`;
      }
      if (options.nameSuffix) {
        workflowName = `${workflowName}${options.nameSuffix}`;
      }

      // 5. Check for existing workflow với same name
      const existingWorkflow = await this.workflowRepository.findOne({
        where: { name: workflowName, userId }
      });

      if (existingWorkflow && !options.overwriteExisting) {
        // Generate unique name
        workflowName = await this.generateUniqueWorkflowName(workflowName, userId);
      }

      // 6. Process definition (regenerate IDs if needed)
      let processedDefinition = importData.workflow.definition;
      if (!options.preserveIds) {
        processedDefinition = this.regenerateDefinitionIds(processedDefinition);
      }

      // 7. Create or update workflow
      let workflow: Workflow;
      
      if (existingWorkflow && options.overwriteExisting) {
        // Update existing workflow
        existingWorkflow.definition = processedDefinition;
        existingWorkflow.updatedAt = Date.now();
        workflow = await this.workflowRepository.save(existingWorkflow);
        
        // Update definition directly
        await this.workflowRepository.updateWorkflowDefinition(
          existingWorkflow.id,
          processedDefinition
        );
      } else {
        // Create new workflow
        workflow = this.workflowRepository.create({
          name: workflowName,
          userId,
          isActive: false,
          definition: processedDefinition,
          createdAt: Date.now(),
          updatedAt: Date.now()
        });
        
        workflow = await this.workflowRepository.save(workflow);
        
        // Update definition directly
        await this.workflowRepository.updateWorkflowDefinition(
          workflow.id,
          processedDefinition
        );
      }

      this.logger.log(`Workflow imported successfully: ${workflow.id}`);

      return {
        success: true,
        workflowId: workflow.id,
        workflow,
        validation,
        warnings: validation.warnings.map(w => w.message)
      };

    } catch (error) {
      this.logger.error(`Failed to import workflow: ${error.message}`, error.stack);
      
      return {
        success: false,
        errors: [`Import failed: ${error.message}`]
      };
    }
  }

  /**
   * Import multiple workflows
   * @param importDataArray - Array of workflow data to import
   * @param userId - ID của user performing import
   * @param options - Import options
   * @returns Array of import results
   */
  async importWorkflows(
    importDataArray: WorkflowExportData[],
    userId: number,
    options: ImportOptions = {}
  ): Promise<ImportResult[]> {
    this.logger.log(`Importing ${importDataArray.length} workflows`);

    const results: ImportResult[] = [];

    for (const [index, importData] of importDataArray.entries()) {
      try {
        // Add index to name if multiple workflows
        const workflowOptions = { ...options };
        if (importDataArray.length > 1 && !options.nameSuffix) {
          workflowOptions.nameSuffix = ` (${index + 1})`;
        }

        const result = await this.importWorkflow(importData, userId, workflowOptions);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          errors: [`Failed to import workflow ${index + 1}: ${error.message}`]
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    this.logger.log(`Imported ${successCount}/${importDataArray.length} workflows successfully`);

    return results;
  }

  /**
   * Create workflow template
   * @param workflowId - ID của workflow to create template from
   * @param templateName - Name for the template
   * @param userId - ID của user creating template
   * @returns Template data
   */
  async createWorkflowTemplate(
    workflowId: string,
    templateName: string,
    userId: number
  ): Promise<WorkflowExportData> {
    this.logger.log(`Creating template from workflow: ${workflowId}`);

    const exportData = await this.exportWorkflow(workflowId, userId);

    // Modify export data for template
    exportData.workflow.name = templateName;
    (exportData.workflow.metadata as any) = {
      ...exportData.workflow.metadata,
      templateCreatedAt: Date.now(),
      templateCreatedBy: userId,
      originalWorkflowId: workflowId
    };

    // Clear runtime-specific data
    const definition = exportData.workflow.definition;
    if (definition.metadata) {
      delete definition.metadata.lastModified;
      delete definition.metadata.author;
      definition.metadata.version = '1.0.0';
    }

    this.logger.log(`Template created successfully: ${templateName}`);

    return exportData;
  }

  /**
   * Validate import data structure
   * @param importData - Data to validate
   * @returns true if valid
   */
  private validateImportData(importData: any): boolean {
    if (!importData || typeof importData !== 'object') {
      return false;
    }

    if (!importData.workflow || typeof importData.workflow !== 'object') {
      return false;
    }

    const workflow = importData.workflow;
    
    if (!workflow.name || typeof workflow.name !== 'string') {
      return false;
    }

    if (!workflow.definition || typeof workflow.definition !== 'object') {
      return false;
    }

    if (!workflow.definition.nodes || !Array.isArray(workflow.definition.nodes)) {
      return false;
    }

    return true;
  }

  /**
   * Generate unique workflow name
   * @param baseName - Base name for workflow
   * @param userId - User ID
   * @returns Unique workflow name
   */
  private async generateUniqueWorkflowName(baseName: string, userId: number): Promise<string> {
    let counter = 1;
    let uniqueName = `${baseName} (${counter})`;

    while (await this.workflowRepository.findOne({ where: { name: uniqueName, userId } })) {
      counter++;
      uniqueName = `${baseName} (${counter})`;
    }

    return uniqueName;
  }

  /**
   * Regenerate IDs trong workflow definition
   * @param definition - Workflow definition
   * @returns Definition với new IDs
   */
  private regenerateDefinitionIds(definition: any): any {
    const idMapping = new Map<string, string>();
    
    // Generate new node IDs
    const newDefinition = { ...definition };
    
    if (newDefinition.nodes) {
      newDefinition.nodes = newDefinition.nodes.map((node: any) => {
        const newId = this.generateNodeId();
        idMapping.set(node.id, newId);
        
        return {
          ...node,
          id: newId
        };
      });
    }

    // Update edge references với new node IDs
    if (newDefinition.edges) {
      newDefinition.edges = newDefinition.edges.map((edge: any) => {
        const newSourceId = idMapping.get(edge.sourceNodeId) || edge.sourceNodeId;
        const newTargetId = idMapping.get(edge.targetNodeId) || edge.targetNodeId;
        
        return {
          ...edge,
          id: this.generateEdgeId(),
          sourceNodeId: newSourceId,
          targetNodeId: newTargetId
        };
      });
    }

    return newDefinition;
  }

  /**
   * Generate unique node ID
   * @returns New node ID
   */
  private generateNodeId(): string {
    return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique edge ID
   * @returns New edge ID
   */
  private generateEdgeId(): string {
    return `edge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
