import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { KnowledgeFileUserService } from '../services';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import {
  BatchCreateFilesDto,
  BatchCreateFilesResponseDto,
  DeleteFilesDto,
  FileResponseDto,
  QueryFileDto,
  QueryUnassignedFileDto,
} from '../dto';
// Removed RAG processing - temporarily disabled
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { KNOWLEDGE_FILE_ERROR_CODES } from '../exceptions/error-codes';
import { ErrorCode } from '@common/exceptions';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';

@ApiTags(SWAGGER_API_TAGS.USER_KNOWLEDGE_FILES)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  FileResponseDto,
  BatchCreateFilesResponseDto,
  BatchCreateFilesDto,
  DeleteFilesDto,
  QueryFileDto,
  QueryUnassignedFileDto
)
@Controller('user/knowledge-files')
export class KnowledgeFileUserController {
  constructor(
    private readonly knowledgeFileUserService: KnowledgeFileUserService,
    private readonly ragFileProcessingService: RagFileProcessingService,
  ) {}

  /**
   * Thêm nhiều file tri thức
   */
  @ApiOperation({
    summary: 'Thêm nhiều file tri thức',
    description: `Tạo nhiều file tri thức cùng lúc (tối đa 5 file) và trả về presigned URLs để upload file.

**Quy trình tự động:**
1. Gọi API này để tạo file records và nhận presigned URLs
2. Upload file lên S3 sử dụng presigned URLs
3. Hệ thống tự động xử lý với RAG API sau khi upload (không cần confirm)

**Tham số RAG:**
- \`chunkSize\`: Kích thước chunk cho RAG processing (mặc định: 4000)
- \`chunkOverlap\`: Độ chồng lấp giữa các chunk (mặc định: 100)

**Lưu ý:**
- Cần truyền MIME type (không phải URL) cho từng file
- File sẽ tự động được xử lý với RAG API sau khi upload thành công
- Status file sẽ tự động chuyển từ DRAFT sang APPROVED`
  })
  @ApiBody({
    type: BatchCreateFilesDto,
    description: 'Danh sách thông tin các file cần tạo',
    examples: {
      'single': {
        summary: 'Tạo một file tri thức',
        description: 'Tạo một file tri thức với thông tin cơ bản',
        value: {
          files: [
            {
              name: 'Tài liệu hướng dẫn của sơn sói.pdf',
              mime: 'application/pdf',
              storage: 1024000
            }
          ],
          chunkSize: 4000,
          chunkOverlap: 100
        }
      },
      'multiple': {
        summary: 'Tạo nhiều file tri thức',
        description: 'Tạo nhiều file tri thức cùng lúc với các loại file khác nhau',
        value: {
          files: [
            {
              name: 'Tài liệu hướng dẫn của sơn sói.pdf',
              mime: 'application/pdf',
              storage: 1024000
            },
            {
              name: 'Báo cáo tháng.docx',
              mime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              storage: 512000
            },
            {
              name: 'Dữ liệu cấu hình.json',
              mime: 'application/json',
              storage: 256000
            }
          ],
          chunkSize: 6000,
          chunkOverlap: 150
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Đã tạo files tri thức thành công.',
    schema: ApiResponseDto.getSchema(BatchCreateFilesResponseDto),
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Post('batch')
  @HttpCode(HttpStatus.CREATED)
  async batchCreateFiles(
    @Body() batchCreateFilesDto: BatchCreateFilesDto,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.knowledgeFileUserService.batchCreateFiles(
      batchCreateFilesDto,
      userId,
    );

    return ApiResponseDto.created<BatchCreateFilesResponseDto>(result, 'Đã tạo files tri thức thành công.');
  }

  /**
   * Lấy danh sách files tri thức
   */
  @ApiOperation({
    summary: 'Lấy danh sách files tri thức',
    description: 'Lấy danh sách files tri thức với khả năng tìm kiếm, lọc và phân trang'
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Tên file cần tìm kiếm',
    example: 'hướng dẫn'
  })
  @ApiQuery({
    name: 'extensions',
    required: false,
    type: String,
    description: 'Lọc theo định dạng file (phân cách bằng dấu phẩy)',
    example: 'pdf,docx,txt'
  })
  @ApiQuery({
    name: 'vectorStoreId',
    required: false,
    type: String,
    description: 'ID của vector store để lọc file',
    example: 'vs_123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Số trang (bắt đầu từ 1)',
    example: 1,
    minimum: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Số lượng kết quả trên một trang',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Trường sắp xếp',
    enum: ['name', 'createdAt', 'storage'],
    example: 'createdAt'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    description: 'Hướng sắp xếp',
    enum: ['ASC', 'DESC'],
    example: 'DESC'
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Lọc theo trạng thái file',
    enum: ['PENDING', 'APPROVED', 'REJECTED', 'DELETED'],
    example: 'APPROVED'
  })
  @ApiQuery({
    name: 'isForSale',
    required: false,
    type: Boolean,
    description: 'Lọc theo trạng thái đăng bán (true: đang bán, false: không bán)',
    example: true
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách file thành công.',
    schema: ApiResponseDto.getPaginatedSchema(FileResponseDto)
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Get()
  @HttpCode(HttpStatus.OK)
  async getFiles(@Query() queryDto: QueryFileDto, @CurrentUser('id') userId: number) {
    const result = await this.knowledgeFileUserService.getFiles(
      queryDto,
      userId,
    );
    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách file thành công.',
    );
  }


  /**
   * Xóa nhiều file tri thức
   */
  @ApiOperation({
    summary: 'Xóa nhiều file tri thức',
    description: 'Xóa nhiều file tri thức cùng lúc theo danh sách ID'
  })
  @ApiBody({
    type: DeleteFilesDto,
    description: 'Danh sách ID của các file cần xóa',
    examples: {
      'single': {
        summary: 'Xóa một file',
        description: 'Xóa một file duy nhất',
        value: {
          fileIds: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890']
        }
      },
      'multiple': {
        summary: 'Xóa nhiều file',
        description: 'Xóa nhiều file cùng lúc',
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            'b2c3d4e5-f6a7-8901-bcde-f01234567890'
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa file thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        message: { type: 'string', example: 'Đã xóa 2 file thành công.' },
        result: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            deletedCount: { type: 'number', example: 2 },
            failedItems: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890' },
                  reason: { type: 'string', example: 'File đã bị xóa trước đó' },
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Delete('batch')
  @HttpCode(HttpStatus.OK)
  async batchDeleteFiles(@Body() deleteFilesDto: DeleteFilesDto, @CurrentUser('id') userId: number) {
    const result = await this.knowledgeFileUserService.deleteFiles(deleteFilesDto.fileIds, userId);
    return ApiResponseDto.deleted(result, `Đã xóa ${result.deletedCount} file thành công.`);
  }

  /**
   * Lấy danh sách file tri thức chưa gắn với vector store cụ thể
   */
  @ApiOperation({
    summary: 'Lấy danh sách file tri thức chưa gắn với vector store cụ thể',
    description: 'Lấy danh sách file tri thức chưa được gắn với vector store cụ thể. Yêu cầu truyền vectorStoreId để lấy các file chưa gắn với vector store đó (nhưng có thể đã gắn với vector store khác).'
  })
  @ApiQuery({
    name: 'vectorStoreId',
    required: true,
    type: String,
    description: 'ID của vector store để lấy các file chưa gắn',
    example: 'vs_123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Số trang (bắt đầu từ 1)',
    example: 1,
    minimum: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Số lượng kết quả trên một trang',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Tên file cần tìm kiếm',
    example: 'hướng dẫn'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách file chưa gắn với vector store cụ thể thành công.',
    schema: ApiResponseDto.getPaginatedSchema(FileResponseDto),
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Get('unassigned')
  @HttpCode(HttpStatus.OK)
  async getUnassignedFiles(
    @Query() queryDto: QueryUnassignedFileDto,
    @CurrentUser('id') userId: number
  ) {
    const result = await this.knowledgeFileUserService.getUnassignedFiles(
      queryDto,
      userId,
    );
    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách file chưa gắn với vector store cụ thể thành công.',
    );
  }

  /**
   * Gửi file tri thức để duyệt
   */
  @ApiOperation({
    summary: 'Gửi file tri thức để duyệt',
    description: 'Gửi file tri thức để admin duyệt. File phải ở trạng thái nháp mới có thể gửi duyệt.'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của file cần gửi duyệt',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'File đã được gửi duyệt thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        message: { type: 'string', example: 'File đã được gửi duyệt thành công.' },
        result: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            message: { type: 'string', example: 'File đã được gửi duyệt thành công' },
          },
        },
      },
    },
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_INVALID_STATUS,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_STATUS_CHANGE_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @Put(':id/submit')
  @HttpCode(HttpStatus.OK)
  async submitForApproval(@Param('id') id: string, @CurrentUser('id') userId: number) {
    const result = await this.knowledgeFileUserService.submitForApproval(id, userId);
    return ApiResponseDto.success(result, 'File đã được gửi duyệt thành công.');
  }
}
