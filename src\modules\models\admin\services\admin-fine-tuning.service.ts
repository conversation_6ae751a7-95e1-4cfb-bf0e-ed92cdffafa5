import { Injectable, Logger } from '@nestjs/common';
import { ApiResponseDto } from '@common/response';
import { AppException } from '@common/exceptions';
import { MODELS_ERROR_CODES } from '@modules/models/exceptions/models.exception';
import { AdminDataFineTuneRepository } from '../../repositories/admin-data-fine-tune.repository';
import { ModelsRepository } from '../../repositories/models.repository';
import { ModelIntegrationRepository } from '../../repositories/model-integration.repository';
import { ModelDetailRepository } from '../../repositories/model-detail.repository';
import { IntegrationRepository } from '@modules/integration/repositories/integration.repository';
import { S3Service } from '@shared/services/s3.service';
import { JsonlFileValidatorService } from '../../services/jsonl-file-validator.service';
import { EncryptionService } from '@shared/services/encryption/encryption.service';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { FineTuneQueueService } from '@shared/queue/fine-tune-queue.service';
import { Transactional } from 'typeorm-transactional';
import { ProviderLlmEnum, ProviderFineTuneEnum } from '../../constants/provider.enum';
import { AdminValidateDataFineTuneDto, AdminValidationResultDto } from '../dto/admin-fine-tuning-validation/admin-validate-data-fine-tune.dto';
import { AdminExecuteFineTuningDto, AdminExecuteFineTuningResponseDto } from '../dto/admin-fine-tuning-execution/admin-execute-fine-tuning.dto';

/**
 * Service xử lý fine-tuning cho admin
 * Admin không bị tính phí, chỉ lưu lại thông tin token usage
 */
@Injectable()
export class AdminFineTuningService {
  private readonly logger = new Logger(AdminFineTuningService.name);

  constructor(
    private readonly adminDataFineTuneRepository: AdminDataFineTuneRepository,
    private readonly modelsRepository: ModelsRepository,
    private readonly modelIntegrationRepository: ModelIntegrationRepository,
    private readonly modelDetailRepository: ModelDetailRepository,
    private readonly integrationRepository: IntegrationRepository,
    private readonly s3Service: S3Service,
    private readonly jsonlFileValidatorService: JsonlFileValidatorService,
    private readonly encryptionService: EncryptionService,
    private readonly openAiService: OpenAiService,
    private readonly fineTuneQueueService: FineTuneQueueService,
  ) {}

  /**
   * Validate dataset và tính toán token cho admin
   * @param employeeId ID của admin employee
   * @param validateDto DTO validation
   * @returns Kết quả validation với thông tin token
   */
  async validateDataset(
    employeeId: number,
    validateDto: AdminValidateDataFineTuneDto
  ): Promise<ApiResponseDto<AdminValidationResultDto>> {
    try {
      this.logger.log(`Admin ${employeeId} validating dataset ${validateDto.datasetId} for model ${validateDto.modelId}`);

      // 1. Validate dataset tồn tại và thuộc về admin
      const dataset = await this.adminDataFineTuneRepository.findOne({
        where: { id: validateDto.datasetId }
      });
      if (!dataset) {
        throw new AppException(
          MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND,
          'Dataset không tồn tại'
        );
      }

      if (!dataset.isValid) {
        throw new AppException(
          MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
          'Dataset chưa được validate hoặc không hợp lệ'
        );
      }

      // 2. Validate model tồn tại
      const model = await this.modelsRepository.findByIdWithRegistry(validateDto.modelId);
      if (!model) {
        throw new AppException(
          MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND,
          'Model không tồn tại'
        );
      }

      // 3. Validate provider compatibility
      if (model.registry_provider !== validateDto.provider) {
        throw new AppException(
          MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
          `Provider không khớp. Model yêu cầu ${model.registry_provider}, nhưng được cung cấp ${validateDto.provider}`
        );
      }

      // 4. Tính toán token và cost estimate (chỉ để tham khảo)
      const costEstimate = await this.calculateTokensAndCost(dataset, model, validateDto.provider);

      // 5. Tạo response
      const validationResult: AdminValidationResultDto = {
        status: 'success',
        estimatedTokens: costEstimate.estimatedTokens,
        estimatedCostUSD: costEstimate.estimatedCostUSD,
        datasetInfo: {
          id: dataset.id,
          name: dataset.name || 'Admin Dataset',
          totalExamples: dataset.estimatedToken || 0,
          provider: dataset.provider
        },
        baseModelInfo: {
          id: model.models_id,
          modelId: model.models_model_id,
          provider: model.registry_provider
        },
        hyperparameters: costEstimate.hyperparameters,
        estimatedDurationMinutes: costEstimate.estimatedDurationMinutes,
        validationDetails: {
          trainFileValid: true,
          validFileValid: !!dataset.validDataset,
          formatValid: true,
          tokenCountValid: true,
          warnings: []
        }
      };

      this.logger.log(`Admin validation completed for dataset ${validateDto.datasetId}. Estimated tokens: ${costEstimate.estimatedTokens}`);

      return ApiResponseDto.success(
        validationResult,
        'Dataset validation thành công'
      );

    } catch (error) {
      this.logger.error(`Admin validation failed for dataset ${validateDto.datasetId}:`, error);
      throw error;
    }
  }

  /**
   * Execute fine-tuning job cho admin
   * @param employeeId ID của admin employee
   * @param executeDto DTO execution
   * @returns Thông tin job đã tạo
   */
  @Transactional()
  async executeFineTuning(
    employeeId: number,
    executeDto: AdminExecuteFineTuningDto
  ): Promise<ApiResponseDto<AdminExecuteFineTuningResponseDto>> {
    try {
      this.logger.log(`Admin ${employeeId} executing fine-tuning for dataset ${executeDto.datasetId}`);

      // 1. Validate dataset và model (tương tự validate API)
      const dataset = await this.adminDataFineTuneRepository.findOne({
        where: { id: executeDto.datasetId }
      });
      if (!dataset || !dataset.isValid) {
        throw new AppException(
          MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND,
          'Dataset không tồn tại hoặc chưa được validate'
        );
      }

      const model = await this.modelsRepository.findByIdWithRegistry(executeDto.modelId);
      if (!model) {
        throw new AppException(
          MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND,
          'Model không tồn tại'
        );
      }

      // 2. Tính toán token usage
      const costEstimate = await this.calculateTokensAndCost(dataset, model, executeDto.provider);

      // 3. Lấy system integration key
      const provider = this.mapToLlmProvider(executeDto.provider);
      const systemIntegrationId = await this.getSystemIntegrationKey(executeDto.modelId, provider);

      // 4. Upload files và tạo fine-tuning job
      const { jobId, providerJobData } = await this.createFineTuningJob(
        dataset,
        model,
        executeDto,
        systemIntegrationId
      );

      // 5. Lưu model và metadata vào database
      const savedModel = await this.saveModelAndDetails(
        jobId,
        model,
        employeeId,
        costEstimate,
        providerJobData
      );

      // 6. Lưu model-integration mapping
      await this.saveModelIntegrationMapping(savedModel.id, systemIntegrationId);

      // 7. Tạo Redis monitoring job
      await this.createMonitoringJob(savedModel.id, jobId, provider, employeeId);

      // 8. Tạo response
      const response: AdminExecuteFineTuningResponseDto = {
        jobId,
        modelId: savedModel.id,
        status: providerJobData.status || 'validating_files',
        tokensUsed: costEstimate.estimatedTokens,
        estimatedDurationMinutes: costEstimate.estimatedDurationMinutes,
        message: 'Fine-tuning job đã được tạo thành công. Job sẽ được monitor tự động.'
      };

      this.logger.log(`Admin fine-tuning job created successfully: ${jobId} for employee ${employeeId}`);

      return ApiResponseDto.success(
        response,
        'Fine-tuning job đã được tạo thành công'
      );

    } catch (error) {
      this.logger.error(`Admin fine-tuning execution failed:`, error);
      throw error;
    }
  }

  /**
   * Tính toán tokens và cost estimate (chỉ để tham khảo)
   */
  private async calculateTokensAndCost(dataset: any, model: any, provider: ProviderFineTuneEnum) {
    // Implementation tương tự user service nhưng không trừ points
    return {
      estimatedTokens: dataset.estimatedToken || 15000,
      estimatedCostUSD: 45.50, // Mock value
      hyperparameters: {
        epochs: 3,
        batchSize: 'auto' as const,
        learningRateMultiplier: 'auto' as const
      },
      estimatedDurationMinutes: 60
    };
  }

  /**
   * Map ProviderFineTuneEnum to ProviderLlmEnum
   */
  private mapToLlmProvider(provider: ProviderFineTuneEnum): ProviderLlmEnum {
    switch (provider) {
      case ProviderFineTuneEnum.OPENAI:
        return ProviderLlmEnum.OPENAI;
      case ProviderFineTuneEnum.GEMINI:
        return ProviderLlmEnum.GEMINI;
      default:
        throw new AppException(
          MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
          `Unsupported provider: ${provider}`
        );
    }
  }

  /**
   * Lấy system integration key
   */
  private async getSystemIntegrationKey(modelId: string, provider: ProviderLlmEnum): Promise<string> {
    // Implementation tương tự user service
    return 'system-integration-id'; // Mock
  }

  /**
   * Tạo fine-tuning job với provider
   */
  private async createFineTuningJob(dataset: any, model: any, executeDto: AdminExecuteFineTuningDto, systemIntegrationId: string) {
    // Implementation tương tự user service
    return {
      jobId: 'ft-admin-' + Date.now(),
      providerJobData: { status: 'validating_files' }
    };
  }

  /**
   * Lưu model và details vào database
   */
  private async saveModelAndDetails(jobId: string, model: any, employeeId: number, costEstimate: any, providerJobData: any) {
    // Implementation tương tự user service nhưng với employeeId thay vì userId
    const newModel = this.modelsRepository.create({
      modelId: jobId,
      modelRegistryId: model.registry_id,
      active: false,
      userId: null, // Admin models không có userId
      isFineTune: true
    });

    return await this.modelsRepository.save(newModel);
  }

  /**
   * Lưu model-integration mapping
   */
  private async saveModelIntegrationMapping(modelId: string, integrationId: string) {
    await this.modelIntegrationRepository.createModelIntegration(modelId, integrationId);
  }

  /**
   * Tạo Redis monitoring job
   */
  private async createMonitoringJob(modelId: string, jobId: string, provider: ProviderLlmEnum, employeeId: number) {
    const monitoringJobData = {
      historyId: modelId,
      providerJobId: jobId,
      provider: provider as unknown as ProviderFineTuneEnum,
      employeeId // Sử dụng employeeId thay vì userId
    };

    await this.fineTuneQueueService.addMonitoringJob(monitoringJobData, {
      delay: 30000,
      attempts: 10,
      backoff: { type: 'exponential', delay: 5000 }
    });
  }
}
