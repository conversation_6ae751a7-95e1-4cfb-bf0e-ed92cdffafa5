import { ConfigType } from '@/config';
import { CategoryFolderEnum, generateS3Key } from '@/shared/utils';
import { AppException, ErrorCode } from '@common/exceptions';
import { ConfigService } from '@config/config.service';
import { FacebookConfig } from '@config/interfaces';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { S3Service } from '../../s3.service';
import {
  FacebookPageInfo,
  FacebookPictureResponse,
} from '../interfaces/facebook.interface';
import {
  PageDetailsResponse,
  UpdatePageDetailsRequest,
  UpdatePageDetailsResponse,
  CreatePostRequest,
  CreatePostResponse,
  GetCommentsResponse,
  GetInsightsResponse,
  GetInsightsParams,
  GetPostsParams,
  GetPostsResponse,
  GetPageDetailsParams,
} from '../interfaces/facebook-page.interface';

/**
 * Service để xử lý các API liên quan đến Facebook Page
 */
@Injectable()
export class FacebookPageService {
  private readonly logger = new Logger(FacebookPageService.name);
  private readonly facebookConfig: FacebookConfig;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly s3Service: S3Service,
  ) {
    this.facebookConfig = this.configService.getConfig<FacebookConfig>(
      ConfigType.Facebook,
    );
  }

  /**
   * Lấy avatar của trang Facebook, upload lên S3 và trả về key
   * @param pageId ID của trang Facebook
   * @param accessToken Access token của trang hoặc người dùng có quyền truy cập trang
   * @param keyOld
   * @returns S3 key của avatar đã upload
   */
  async getPageAvatarAndUploadToS3(
    pageId: string,
    accessToken: string,
    keyOld?: string,
  ): Promise<{ key: string }> {
    try {
      this.logger.log(`Bắt đầu lấy avatar của trang Facebook ${pageId}`);

      // Lấy trực tiếp ảnh avatar của trang Facebook với kích thước lớn
      const pictureResponse = await firstValueFrom(
        this.httpService.get<FacebookPictureResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/picture`,
          {
            params: {
              access_token: accessToken,
              type: 'large',
              redirect: 'false',
            },
          },
        ),
      );

      // Tải avatar từ URL
      const response = await firstValueFrom(
        this.httpService.get(pictureResponse.data.data.url, {
          responseType: 'arraybuffer',
        }),
      );

      // Tạo buffer từ dữ liệu nhận được
      const buffer = Buffer.from(response.data);

      // Xác định content type từ header hoặc mặc định là image/jpeg
      const contentType = response.headers['content-type'] || 'image/jpeg';

      // Tạo S3 key cho avatar
      const key = generateS3Key({
        baseFolder: 'facebook',
        categoryFolder: CategoryFolderEnum.PROFILE,
        fileName: `page-${pageId}-avatar.jpg`,
        useTimeFolder: true,
      });

      // Upload avatar lên S3
      await this.s3Service.uploadFile(keyOld || key, buffer, contentType);

      this.logger.log(
        `Đã upload avatar của trang Facebook ${pageId} lên S3 với key: ${key}`,
      );

      return { key };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy và upload avatar của trang Facebook ${pageId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy và upload avatar của trang Facebook',
        { pageId },
      );
    }
  }

  /**
   * Lấy danh sách trang Facebook được gán cho người dùng
   * @param accessToken Access token của người dùng
   * @param fields Các trường cần lấy, mặc định là 'id,name,access_token,category,tasks,picture'
   * @returns Danh sách trang Facebook được gán
   */
  async getUserAssignedPages(
    accessToken: string,
    fields: string = 'id,name,access_token,category,tasks,picture',
  ): Promise<FacebookPageInfo[]> {
    this.logger.log(`[DEBUG] getUserAssignedPages started`);
    this.logger.log(`[DEBUG] Input parameters:`, {
      hasToken: !!accessToken,
      tokenLength: accessToken?.length,
      tokenPrefix: accessToken ? `${accessToken.substring(0, 20)}...` : 'null',
      fields,
    });

    try {
      // Thử endpoint /me/accounts trước (endpoint chính thức cho pages)
      const accountsUrl = `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/me/accounts`;

      this.logger.log(`[DEBUG] Gọi Facebook API /me/accounts:`, {
        url: accountsUrl,
        params: {
          access_token: accessToken
            ? `${accessToken.substring(0, 20)}...`
            : 'null',
          fields,
        },
      });

      const response = await firstValueFrom(
        this.httpService.get<{ data: FacebookPageInfo[] }>(accountsUrl, {
          params: {
            access_token: accessToken,
            fields,
          },
        }),
      );

      const pages = response.data.data || [];
      this.logger.log(
        `[DEBUG] getUserAssignedPages completed successfully with ${pages.length} pages`,
      );

      return pages;
    } catch (error) {
      // Kiểm tra lỗi cụ thể từ Facebook
      if (error.response?.status === 400) {
        const errorData = error.response.data;
        this.logger.error(
          `[DEBUG] Facebook API 400 Error Details for getUserAssignedPages:`,
          errorData,
        );

        if (errorData?.error?.message) {
          const facebookError = errorData.error;

          // Xử lý lỗi rate limiting từ Facebook (code 368)
          if (facebookError.code === 368) {
            throw new AppException(
              ErrorCode.RATE_LIMIT_EXCEEDED,
              'Facebook đang giới hạn tần suất truy cập. Vui lòng thử lại sau ít phút.',
            );
          }

          throw new AppException(
            ErrorCode.EXTERNAL_SERVICE_ERROR,
            `Facebook API Error (${facebookError.code}): ${facebookError.message}`,
          );
        }
      }

      this.logger.error(
        `Error getting assigned Facebook pages: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách trang Facebook được gán',
      );
    }
  }

  /**
   * Đăng ký webhook cho ứng dụng Facebook
   * @param pageId ID của trang Facebook
   * @param pageAccessToken Access token của trang
   * @param fields Các trường cần đăng ký nhận webhook (messages, messaging_postbacks, message_deliveries, etc.)
   * @returns Kết quả đăng ký webhook
   */
  async subscribeApp(
    pageId: string,
    pageAccessToken: string,
    fields: string[] = [
      'messages',
      'messaging_postbacks',
      'message_deliveries',
    ],
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Bắt đầu đăng ký webhook cho trang Facebook ${pageId}`);

      if (!this.facebookConfig?.appId) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu cấu hình Facebook App ID',
        );
      }

      // Gọi API để đăng ký webhook
      const response = await firstValueFrom(
        this.httpService.post(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/subscribed_apps`,
          {},
          {
            params: {
              access_token: pageAccessToken,
              subscribed_fields: fields.join(','),
            },
          },
        ),
      );

      if (response.data && response.data.success) {
        this.logger.log(
          `Đã đăng ký webhook thành công cho trang Facebook ${pageId}`,
        );
        return { success: true };
      } else {
        throw new Error('Facebook API không trả về kết quả thành công');
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi đăng ký webhook cho trang Facebook ${pageId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi đăng ký webhook cho trang Facebook',
        { pageId },
      );
    }
  }

  /**
   * Hủy đăng ký webhook cho ứng dụng Facebook
   * @param pageId ID của trang Facebook
   * @param pageAccessToken Access token của trang
   * @returns Kết quả hủy đăng ký webhook
   */
  async unsubscribeApp(
    pageId: string,
    pageAccessToken: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(
        `Bắt đầu hủy đăng ký webhook cho trang Facebook ${pageId}`,
      );

      if (!this.facebookConfig?.appId) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu cấu hình Facebook App ID',
        );
      }

      // Gọi API để hủy đăng ký webhook
      const response = await firstValueFrom(
        this.httpService.delete(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/subscribed_apps`,
          {
            params: {
              access_token: pageAccessToken,
              app_id: this.facebookConfig.appId,
            },
          },
        ),
      );

      if (response.data && response.data.success) {
        this.logger.log(
          `Đã hủy đăng ký webhook thành công cho trang Facebook ${pageId}`,
        );
        return { success: true };
      } else {
        throw new Error('Facebook API không trả về kết quả thành công');
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi hủy đăng ký webhook cho trang Facebook ${pageId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi hủy đăng ký webhook cho trang Facebook',
        { pageId },
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của trang Facebook
   * @param pageId ID của trang Facebook
   * @param accessToken Access token của trang hoặc user
   * @param params Tham số để chỉ định các trường cần lấy
   * @returns Thông tin chi tiết của trang
   */
  async getPageDetails(
    pageId: string,
    accessToken: string,
    params: GetPageDetailsParams,
  ): Promise<PageDetailsResponse> {
    try {
      this.logger.log(`Bắt đầu lấy thông tin chi tiết trang Facebook ${pageId}`);

      const response = await firstValueFrom(
        this.httpService.get<PageDetailsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}`,
          {
            params: {
              access_token: accessToken,
              fields: params.fields,
            },
          },
        ),
      );

      this.logger.log(
        `Đã lấy thông tin chi tiết trang Facebook ${pageId} thành công`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thông tin chi tiết trang Facebook ${pageId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin chi tiết trang Facebook',
        { pageId },
      );
    }
  }

  /**
   * Cập nhật thông tin trang Facebook
   * @param pageId ID của trang Facebook
   * @param updateData Dữ liệu cần cập nhật
   * @returns Kết quả cập nhật
   */
  async updatePageDetails(
    pageId: string,
    updateData: UpdatePageDetailsRequest,
  ): Promise<UpdatePageDetailsResponse> {
    try {
      this.logger.log(`Bắt đầu cập nhật thông tin trang Facebook ${pageId}`);

      const response = await firstValueFrom(
        this.httpService.post<UpdatePageDetailsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}`,
          updateData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(
        `Đã cập nhật thông tin trang Facebook ${pageId} thành công`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật thông tin trang Facebook ${pageId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật thông tin trang Facebook',
        { pageId },
      );
    }
  }

  /**
   * Tạo bài viết mới trên trang Facebook
   * @param pageId ID của trang Facebook
   * @param postData Dữ liệu bài viết
   * @returns ID của bài viết mới
   */
  async createPost(
    pageId: string,
    postData: CreatePostRequest,
  ): Promise<CreatePostResponse> {
    try {
      this.logger.log(`Bắt đầu tạo bài viết mới cho trang Facebook ${pageId}`);

      const response = await firstValueFrom(
        this.httpService.post<CreatePostResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/feed`,
          postData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(
        `Đã tạo bài viết mới cho trang Facebook ${pageId} thành công. Post ID: ${response.data.id}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo bài viết cho trang Facebook ${pageId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo bài viết cho trang Facebook',
        { pageId },
      );
    }
  }

  /**
   * Lấy danh sách bài viết của trang Facebook
   * @param pageId ID của trang Facebook
   * @param accessToken Access token của trang
   * @param params Tham số lọc và phân trang
   * @returns Danh sách bài viết
   */
  async getPosts(
    pageId: string,
    accessToken: string,
    params?: GetPostsParams,
  ): Promise<GetPostsResponse> {
    try {
      this.logger.log(`Bắt đầu lấy danh sách bài viết trang Facebook ${pageId}`);

      const queryParams: any = {
        access_token: accessToken,
      };

      if (params?.fields) {
        queryParams.fields = params.fields;
      }
      if (params?.limit) {
        queryParams.limit = params.limit;
      }
      if (params?.since) {
        queryParams.since = params.since;
      }
      if (params?.until) {
        queryParams.until = params.until;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetPostsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/posts`,
          {
            params: queryParams,
          },
        ),
      );

      this.logger.log(
        `Đã lấy danh sách bài viết trang Facebook ${pageId} thành công. Số lượng: ${response.data.data.length}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách bài viết trang Facebook ${pageId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách bài viết trang Facebook',
        { pageId },
      );
    }
  }

  /**
   * Lấy danh sách bình luận của một bài viết
   * @param postId ID của bài viết
   * @param accessToken Access token của trang
   * @param fields Các trường cần lấy
   * @returns Danh sách bình luận
   */
  async getPostComments(
    postId: string,
    accessToken: string,
    fields?: string,
  ): Promise<GetCommentsResponse> {
    try {
      this.logger.log(`Bắt đầu lấy danh sách bình luận bài viết ${postId}`);

      const queryParams: any = {
        access_token: accessToken,
      };

      if (fields) {
        queryParams.fields = fields;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetCommentsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${postId}/comments`,
          {
            params: queryParams,
          },
        ),
      );

      this.logger.log(
        `Đã lấy danh sách bình luận bài viết ${postId} thành công. Số lượng: ${response.data.data.length}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách bình luận bài viết ${postId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách bình luận bài viết',
        { postId },
      );
    }
  }

  /**
   * Lấy insights (phân tích dữ liệu) của trang Facebook
   * @param pageId ID của trang Facebook
   * @param accessToken Access token của trang
   * @param params Tham số để chỉ định metrics và khoảng thời gian
   * @returns Dữ liệu insights
   */
  async getPageInsights(
    pageId: string,
    accessToken: string,
    params: GetInsightsParams,
  ): Promise<GetInsightsResponse> {
    try {
      this.logger.log(`Bắt đầu lấy insights trang Facebook ${pageId}`);

      const queryParams: any = {
        access_token: accessToken,
        metric: params.metric,
      };

      if (params.period) {
        queryParams.period = params.period;
      }
      if (params.since) {
        queryParams.since = params.since;
      }
      if (params.until) {
        queryParams.until = params.until;
      }
      if (params.breakdown) {
        queryParams.breakdown = params.breakdown;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetInsightsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/insights`,
          {
            params: queryParams,
          },
        ),
      );

      this.logger.log(
        `Đã lấy insights trang Facebook ${pageId} thành công. Số metrics: ${response.data.data.length}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy insights trang Facebook ${pageId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy insights trang Facebook',
        { pageId },
      );
    }
  }

  /**
   * Lấy insights của một bài viết cụ thể
   * @param postId ID của bài viết
   * @param accessToken Access token của trang
   * @param metrics Danh sách metrics cần lấy
   * @returns Dữ liệu insights của bài viết
   */
  async getPostInsights(
    postId: string,
    accessToken: string,
    metrics?: string,
  ): Promise<GetInsightsResponse> {
    try {
      this.logger.log(`Bắt đầu lấy insights bài viết ${postId}`);

      const queryParams: any = {
        access_token: accessToken,
      };

      if (metrics) {
        queryParams.metric = metrics;
      } else {
        // Default metrics cho post insights
        queryParams.metric = 'post_impressions,post_engaged_users,post_clicks,post_reactions_by_type_total';
      }

      const response = await firstValueFrom(
        this.httpService.get<GetInsightsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${postId}/insights`,
          {
            params: queryParams,
          },
        ),
      );

      this.logger.log(
        `Đã lấy insights bài viết ${postId} thành công. Số metrics: ${response.data.data.length}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy insights bài viết ${postId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy insights bài viết',
        { postId },
      );
    }
  }

  /**
   * Xóa một bài viết trên trang Facebook
   * @param postId ID của bài viết
   * @param accessToken Access token của trang
   * @returns Kết quả xóa bài viết
   */
  async deletePost(
    postId: string,
    accessToken: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Bắt đầu xóa bài viết ${postId}`);

      const response = await firstValueFrom(
        this.httpService.delete(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${postId}`,
          {
            params: {
              access_token: accessToken,
            },
          },
        ),
      );

      if (response.data && response.data.success) {
        this.logger.log(`Đã xóa bài viết ${postId} thành công`);
        return { success: true };
      } else {
        throw new Error('Facebook API không trả về kết quả thành công');
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa bài viết ${postId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa bài viết Facebook',
        { postId },
      );
    }
  }

  /**
   * Cập nhật một bài viết trên trang Facebook
   * @param postId ID của bài viết
   * @param accessToken Access token của trang
   * @param updateData Dữ liệu cần cập nhật
   * @returns Kết quả cập nhật
   */
  async updatePost(
    postId: string,
    accessToken: string,
    updateData: { message?: string; is_published?: boolean },
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Bắt đầu cập nhật bài viết ${postId}`);

      const requestData = {
        ...updateData,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${postId}`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      if (response.data && response.data.success) {
        this.logger.log(`Đã cập nhật bài viết ${postId} thành công`);
        return { success: true };
      } else {
        throw new Error('Facebook API không trả về kết quả thành công');
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật bài viết ${postId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật bài viết Facebook',
        { postId },
      );
    }
  }

  /**
   * Trả lời một bình luận trên bài viết
   * @param commentId ID của bình luận gốc
   * @param accessToken Access token của trang
   * @param message Nội dung trả lời
   * @returns ID của bình luận trả lời
   */
  async replyToComment(
    commentId: string,
    accessToken: string,
    message: string,
  ): Promise<{ id: string }> {
    try {
      this.logger.log(`Bắt đầu trả lời bình luận ${commentId}`);

      const response = await firstValueFrom(
        this.httpService.post(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${commentId}/comments`,
          {
            message,
            access_token: accessToken,
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(
        `Đã trả lời bình luận ${commentId} thành công. Reply ID: ${response.data.id}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi trả lời bình luận ${commentId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi trả lời bình luận Facebook',
        { commentId },
      );
    }
  }
}
