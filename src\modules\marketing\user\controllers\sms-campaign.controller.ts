import { Controller, Post, Body, UseGuards, Get, Query, Delete, Param, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { SmsCampaignService } from '../services/sms-campaign.service';
import { CreateSmsCampaignDto } from '../dto/sms-campaign/create-sms-campaign.dto';
import { CreateSmsCampaignResponseDto, SmsCampaignItemDto, SmsCampaignQueryDto, SmsCampaignOverviewDto } from '../dto/sms-campaign/sms-campaign-response.dto';
import { SmsCampaignOverviewFilterDto } from '../dto/sms-campaign/sms-campaign-overview-filter.dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { BulkDeleteSmsCampaignDto, BulkDeleteResponseDto } from '@/modules/marketing/common/dto/bulk-delete.dto';

/**
 * Controller xử lý tất cả API liên quan đến SMS marketing campaigns
 * Bao gồm tạo, quản lý, thống kê SMS campaigns
 */
@ApiTags(SWAGGER_API_TAGS.USER_SMS_CAMPAIGN)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('marketing/sms-campaigns')
export class SmsCampaignController {
  constructor(private readonly smsCampaignService: SmsCampaignService) {}

  /**
   * Tạo SMS campaign với template và đẩy jobs vào queue
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo SMS campaign với template',
    description: `Tạo chiến dịch SMS marketing sử dụng template với các tùy chọn:
    - Sử dụng template SMS có sẵn với nội dung được định sẵn
    - Chọn loại chiến dịch: OTP (tin nhắn xác thực) hoặc ADS (tin nhắn quảng cáo)
    - Tự động thay thế các biến trong template
    - Hỗ trợ personalization với custom fields
    - Lên lịch gửi hoặc gửi ngay lập tức
    - Chọn segment, audience cụ thể hoặc danh sách số điện thoại
    - Tự động tạo jobs và đẩy vào queue để worker xử lý`,
  })
  @ApiResponse({
    status: 201,
    description: 'SMS campaign với template đã được tạo thành công và jobs đã được đẩy vào queue',
    type: CreateSmsCampaignResponseDto,
    schema: {
      example: {
        success: true,
        message: 'SMS campaign với template đã được tạo thành công với 150 recipients. Campaign sẽ được xử lý bởi worker.',
        data: {
          campaignId: 124,
          jobCount: 1,
          jobIds: ['job_2'],
          status: 'SENDING',
          scheduledAt: 1703980800,
          totalRecipients: 150,
          campaignType: 'ADS'
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Validation failed',
        errors: [
          'Template ID không được để trống',
          'SMS integration ID không hợp lệ'
        ]
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Template SMS, segment hoặc audience không tồn tại',
    schema: {
      example: {
        success: false,
        message: 'Template SMS với ID 15 không tồn tại',
        errorCode: 'TEMPLATE_NOT_FOUND'
      }
    }
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.TEMPLATE_NOT_FOUND,
    MARKETING_ERROR_CODES.SEGMENT_NOT_FOUND,
    MARKETING_ERROR_CODES.AUDIENCE_NOT_FOUND
  )
  async createSmsCampaign(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateSmsCampaignDto,
  ): Promise<AppApiResponse<CreateSmsCampaignResponseDto>> {
    const result = await this.smsCampaignService.createSmsCampaignWithTemplate(user.id, createDto);
    return wrapResponse(
      result,
      `SMS campaign với template đã được tạo thành công với ${result.totalRecipients} recipients. Campaign sẽ được xử lý bởi worker.`
    );
  }



  /**
   * Lấy danh sách chiến dịch SMS có phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách chiến dịch SMS',
    description: `Lấy danh sách các chiến dịch SMS với các tùy chọn:
    - Phân trang với page và limit
    - Tìm kiếm theo tên campaign
    - Filter theo trạng thái (DRAFT, SCHEDULED, SENDING, SENT, FAILED)
    - Sắp xếp theo ngày tạo (mới nhất trước)
    - Thống kê cơ bản: số SMS gửi, thành công, thất bại cho mỗi campaign`,
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách chiến dịch SMS với phân trang',
    schema: {
      example: {
        success: true,
        message: 'Danh sách chiến dịch SMS',
        data: {
          items: [
            {
              id: 123,
              name: 'Chiến dịch SMS khuyến mãi Black Friday',
              description: 'Chiến dịch SMS marketing cho sự kiện Black Friday',
              campaignType: 'ADS',
              status: 'SENT',
              totalRecipients: 150,
              sentCount: 145,
              failedCount: 5,
              successRate: 96.7,
              scheduledAt: 1703980800,
              startedAt: 1703980800,
              completedAt: 1703981200,
              createdAt: 1703980000,
              updatedAt: 1703981200
            }
          ],
          pagination: {
            page: 1,
            limit: 20,
            total: 25,
            totalPages: 2
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập',
    schema: {
      example: {
        success: false,
        message: 'Token không hợp lệ hoặc đã hết hạn',
        errorCode: 'UNAUTHORIZED'
      }
    }
  })
  async getCampaigns(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: SmsCampaignQueryDto,
  ): Promise<AppApiResponse<PaginatedResult<SmsCampaignItemDto>>> {
    const result = await this.smsCampaignService.getCampaigns(user.id, queryDto);
    return wrapResponse(result, 'Danh sách chiến dịch SMS');
  }

  /**
   * Lấy thống kê tổng quan SMS campaign
   */
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy thống kê tổng quan SMS campaign',
    description: `Trả về dashboard tổng quan với thống kê toàn bộ SMS marketing:
    - Tổng số campaign theo từng trạng thái
    - Tổng số SMS đã gửi thành công và thất bại
    - Tỷ lệ thành công tổng thể
    - Số campaign đang hoạt động
    - Tổng chi phí và chi phí trung bình mỗi tin nhắn
    - Thống kê hiệu suất chung

    **Filter options:**
    - campaignIds: Danh sách ID campaigns cần thống kê (ví dụ: ?campaignIds=1,2,3)
    - startTime & endTime: Khoảng thời gian filter (Unix timestamp)
    - filterByCreatedAt: Filter theo thời gian tạo (mặc định: true)
    - filterByCompletedAt: Filter theo thời gian hoàn thành (mặc định: false)`,
  })
  @ApiResponse({
    status: 200,
    description: 'Thống kê tổng quan SMS marketing',
    type: SmsCampaignOverviewDto,
    schema: {
      example: {
        success: true,
        message: 'Thống kê tổng quan SMS marketing',
        data: {
          totalCampaigns: 25,
          draftCampaigns: 3,
          scheduledCampaigns: 2,
          sendingCampaigns: 1,
          sentCampaigns: 18,
          failedCampaigns: 1,
          totalSent: 5420,
          totalFailed: 125,
          overallSuccessRate: 97.7,
          activeProviders: 3,
          totalCost: 162600,
          averageCostPerMessage: 30
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu filter không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Validation failed',
        errors: {
          campaignIds: 'each value in campaignIds must be a number',
          startTime: 'startTime must not be less than 0'
        }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Không có quyền truy cập',
    schema: {
      example: {
        success: false,
        message: 'Token không hợp lệ hoặc đã hết hạn',
        errorCode: 'UNAUTHORIZED'
      }
    }
  })
  async getOverview(
    @CurrentUser() user: JwtPayload,
    @Query() filter: SmsCampaignOverviewFilterDto,
  ): Promise<AppApiResponse<SmsCampaignOverviewDto>> {
    const result = await this.smsCampaignService.getOverview(user.id, filter);
    return wrapResponse(result, 'Thống kê tổng quan SMS marketing');
  }

  /**
   * Xóa nhiều SMS campaign
   */
  @Delete('bulk')
  @ApiOperation({
    summary: 'Xóa nhiều SMS campaign',
    description: `Xóa nhiều chiến dịch SMS marketing cùng lúc:
    - Chỉ xóa được campaign thuộc về user hiện tại
    - Tự động hủy job trong queue cho campaign đang chạy (SENDING) hoặc đã lên lịch (SCHEDULED)
    - Trả về danh sách campaign đã xóa thành công và thất bại
    - Lưu ý: Không thể khôi phục sau khi xóa`,
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa nhiều SMS campaign',
    type: BulkDeleteResponseDto,
    schema: {
      example: {
        success: true,
        message: 'Đã xóa 2 SMS campaign thành công, 1 campaign không thể xóa',
        data: {
          deletedCount: 2,
          failedCount: 1,
          deletedIds: [123, 124],
          failedIds: [125],
          message: 'Đã xóa 2 SMS campaign thành công, 1 campaign không thể xóa'
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Validation failed',
        errors: [
          'Danh sách ID không được để trống',
          'ID phải là số'
        ]
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async bulkDeleteCampaigns(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteSmsCampaignDto,
  ): Promise<AppApiResponse<BulkDeleteResponseDto>> {
    const result = await this.smsCampaignService.bulkDeleteCampaigns(user.id, bulkDeleteDto.ids);
    return wrapResponse(result, result.message);
  }

  /**
   * Xóa SMS campaign
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa SMS campaign',
    description: 'Xóa chiến dịch SMS marketing. Tự động hủy job trong queue cho campaign đang chạy (SENDING) hoặc đã lên lịch (SCHEDULED).',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của SMS campaign',
    example: 123,
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa SMS campaign thành công',
    schema: {
      example: {
        success: true,
        message: 'SMS campaign đã được xóa thành công',
        data: null
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'SMS campaign không tồn tại',
    schema: {
      example: {
        success: false,
        message: 'Campaign với ID 123 không tồn tại',
        errorCode: 'CAMPAIGN_NOT_FOUND'
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async deleteCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) campaignId: number
  ): Promise<AppApiResponse<null>> {
    await this.smsCampaignService.deleteCampaign(user.id, campaignId);
    return wrapResponse(null, 'SMS campaign đã được xóa thành công');
  }

  /**
   * Retry SMS campaign đã failed
   */
  @Post(':id/retry')
  @ApiOperation({
    summary: 'Retry SMS campaign đã failed',
    description: 'Thử lại chiến dịch SMS đã thất bại. Reset trạng thái về PENDING và tạo job mới trong queue.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của SMS campaign cần retry',
    example: 13,
  })
  @ApiResponse({
    status: 200,
    description: 'Retry SMS campaign thành công',
    schema: {
      example: {
        success: true,
        message: 'SMS campaign đã được retry thành công',
        data: {
          campaignId: 13,
          jobCount: 1,
          jobIds: ['26'],
          status: 'PENDING',
          totalRecipients: 1
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'SMS campaign không tồn tại',
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign không ở trạng thái FAILED',
  })
  async retryCampaign(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) campaignId: number
  ): Promise<AppApiResponse<CreateSmsCampaignResponseDto>> {
    const result = await this.smsCampaignService.retryCampaign(user.id, campaignId);
    return wrapResponse(result, 'SMS campaign đã được retry thành công');
  }
}
