import { Injectable, Logger } from '@nestjs/common';
import Ajv, { JSONSchemaType, ValidateFunction, ErrorObject } from 'ajv';
import addFormats from 'ajv-formats';
import {
  WorkflowDefinitionSchema,
  WorkflowSchemaValidators
} from '../schemas/workflow-definition.schema';

/**
 * Workflow definition interface for validation
 */
export interface WorkflowDefinitionInput {
  nodes: WorkflowNodeInput[];
  edges?: WorkflowEdgeInput[];
  metadata?: Record<string, unknown>;
}

/**
 * Workflow node interface for validation
 */
export interface WorkflowNodeInput {
  id: string;
  type: string;
  name: string;
  description?: string;
  position: { x: number; y: number };
  size?: { width: number; height: number };
  inputs?: Record<string, unknown>;
  outputs?: Record<string, unknown>;
  config?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

/**
 * Workflow edge interface for validation
 */
export interface WorkflowEdgeInput {
  id: string;
  sourceNodeId: string;
  sourcePort?: string;
  targetNodeId: string;
  targetPort?: string;
  edgeType?: string;
  condition?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

/**
 * Validation error interface
 */
export interface ValidationError {
  code: string;
  message: string;
  path?: string;
  severity: 'error' | 'warning';
  details?: Record<string, unknown>;
}

/**
 * Validation warning interface
 */
export interface ValidationWarning {
  code: string;
  message: string;
  path?: string;
  suggestion?: string;
}

/**
 * Service để validate workflow definitions
 * Following existing validation patterns và integrating với WK-002 executor framework
 */
@Injectable()
export class WorkflowValidationService {
  private readonly logger = new Logger(WorkflowValidationService.name);
  private readonly ajv: Ajv;
  private readonly schemaValidator: ValidateFunction;

  constructor() {
    // Initialize AJV with proper configuration
    this.ajv = new Ajv({
      allErrors: true,
      removeAdditional: false,
      useDefaults: true,
      coerceTypes: true,
      strict: false, // Allow additional properties for extensibility
    });

    // Add format validators
    addFormats(this.ajv);
    
    // Add custom formats
    this.addCustomFormats();

    // Compile workflow definition schema
    this.schemaValidator = this.ajv.compile(WorkflowDefinitionSchema);
  }

  /**
   * Validate complete workflow definition
   * @param definition - Workflow definition to validate
   * @returns Validation result with errors and warnings
   */
  async validateWorkflowDefinition(definition: WorkflowDefinitionInput): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // 1. JSON Schema validation
      const schemaValid = this.schemaValidator(definition);
      if (!schemaValid) {
        result.isValid = false;
        result.errors.push(...this.formatAjvErrors(this.schemaValidator.errors || []));
      }

      // 2. Business logic validation
      if (definition.nodes && definition.edges) {
        const businessValidation = await this.validateBusinessLogic(definition);
        result.errors.push(...businessValidation.errors);
        result.warnings.push(...businessValidation.warnings);
        
        if (businessValidation.errors.length > 0) {
          result.isValid = false;
        }
      }

      // 3. Node type validation (integration với WK-002)
      if (definition.nodes) {
        const nodeValidation = await this.validateNodeTypes(definition.nodes);
        result.errors.push(...nodeValidation.errors);
        result.warnings.push(...nodeValidation.warnings);
        
        if (nodeValidation.errors.length > 0) {
          result.isValid = false;
        }
      }

      // 4. Performance validation
      const performanceValidation = this.validatePerformance(definition);
      result.warnings.push(...performanceValidation.warnings);

    } catch (error) {
      this.logger.error('Error during workflow validation:', error);
      result.isValid = false;
      result.errors.push({
        code: 'VALIDATION_ERROR',
        message: `Validation failed: ${error.message}`,
        severity: 'error'
      });
    }

    return result;
  }

  /**
   * Validate individual node configuration
   * @param node - Node to validate
   * @returns Validation result
   */
  async validateNode(node: WorkflowNodeInput): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // Validate node structure
      if (!node.id || !node.type || !node.name) {
        result.isValid = false;
        result.errors.push({
          code: 'MISSING_REQUIRED_FIELDS',
          message: 'Node must have id, type, and name',
          severity: 'error'
        });
      }

      // Validate node type format
      if (node.type && !/^[a-z]+(\.[a-z]+)*$/.test(node.type)) {
        result.isValid = false;
        result.errors.push({
          code: 'INVALID_NODE_TYPE_FORMAT',
          message: 'Node type must follow pattern: category.subcategory.action',
          path: 'type',
          severity: 'error'
        });
      }

      // Validate position
      if (node.position) {
        if (typeof node.position.x !== 'number' || typeof node.position.y !== 'number') {
          result.isValid = false;
          result.errors.push({
            code: 'INVALID_POSITION',
            message: 'Node position must have numeric x and y coordinates',
            path: 'position',
            severity: 'error'
          });
        }
      }

      // Validate node inputs based on type (integration với WK-002)
      if (node.inputs) {
        const inputValidation = await this.validateNodeInputs(node.type, node.inputs);
        result.errors.push(...inputValidation.errors);
        result.warnings.push(...inputValidation.warnings);
        
        if (inputValidation.errors.length > 0) {
          result.isValid = false;
        }
      }

    } catch (error) {
      this.logger.error('Error validating node:', error);
      result.isValid = false;
      result.errors.push({
        code: 'NODE_VALIDATION_ERROR',
        message: `Node validation failed: ${error.message}`,
        severity: 'error'
      });
    }

    return result;
  }

  /**
   * Validate workflow edge
   * @param edge - Edge to validate
   * @param nodes - Available nodes for reference validation
   * @returns Validation result
   */
  validateEdge(edge: WorkflowEdgeInput, nodes: WorkflowNodeInput[]): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // Validate required fields
      if (!edge.id || !edge.sourceNodeId || !edge.targetNodeId) {
        result.isValid = false;
        result.errors.push({
          code: 'MISSING_EDGE_FIELDS',
          message: 'Edge must have id, sourceNodeId, and targetNodeId',
          severity: 'error'
        });
      }

      // Validate node references
      const nodeIds = nodes.map(node => node.id);
      if (edge.sourceNodeId && !nodeIds.includes(edge.sourceNodeId)) {
        result.isValid = false;
        result.errors.push({
          code: 'INVALID_SOURCE_NODE',
          message: `Source node '${edge.sourceNodeId}' does not exist`,
          path: 'sourceNodeId',
          severity: 'error'
        });
      }

      if (edge.targetNodeId && !nodeIds.includes(edge.targetNodeId)) {
        result.isValid = false;
        result.errors.push({
          code: 'INVALID_TARGET_NODE',
          message: `Target node '${edge.targetNodeId}' does not exist`,
          path: 'targetNodeId',
          severity: 'error'
        });
      }

      // Validate self-connection
      if (edge.sourceNodeId === edge.targetNodeId) {
        result.isValid = false;
        result.errors.push({
          code: 'SELF_CONNECTION',
          message: 'Node cannot connect to itself',
          severity: 'error'
        });
      }

      // Validate conditional edge
      if (edge.edgeType === 'conditional' && !edge.condition) {
        result.warnings.push({
          code: 'MISSING_CONDITION',
          message: 'Conditional edge should have condition defined',
          suggestion: 'Add condition configuration for conditional edge'
        });
      }

    } catch (error) {
      this.logger.error('Error validating edge:', error);
      result.isValid = false;
      result.errors.push({
        code: 'EDGE_VALIDATION_ERROR',
        message: `Edge validation failed: ${error.message}`,
        severity: 'error'
      });
    }

    return result;
  }

  /**
   * Validate business logic rules
   * @param definition - Workflow definition
   * @returns Validation result
   */
  private async validateBusinessLogic(definition: any): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    const { nodes, edges } = definition;

    // Validate unique node IDs
    if (!WorkflowSchemaValidators.validateUniqueNodeIds(nodes)) {
      result.isValid = false;
      result.errors.push({
        code: 'DUPLICATE_NODE_IDS',
        message: 'All node IDs must be unique within workflow',
        severity: 'error'
      });
    }

    // Validate edge references
    if (!WorkflowSchemaValidators.validateEdgeReferences(nodes, edges)) {
      result.isValid = false;
      result.errors.push({
        code: 'INVALID_EDGE_REFERENCES',
        message: 'All edges must reference existing nodes',
        severity: 'error'
      });
    }

    // Validate no circular dependencies
    if (!WorkflowSchemaValidators.validateNoCycles(edges)) {
      result.isValid = false;
      result.errors.push({
        code: 'CIRCULAR_DEPENDENCY',
        message: 'Workflow contains circular dependencies',
        severity: 'error'
      });
    }

    // Validate has start node
    if (!WorkflowSchemaValidators.validateHasStartNode(nodes, edges)) {
      result.warnings.push({
        code: 'NO_START_NODE',
        message: 'Workflow should have at least one start node (node with no incoming edges)',
        suggestion: 'Add a trigger or start node to begin workflow execution'
      });
    }

    // Validate has end node
    if (!WorkflowSchemaValidators.validateHasEndNode(nodes, edges)) {
      result.warnings.push({
        code: 'NO_END_NODE',
        message: 'Workflow should have at least one end node (node with no outgoing edges)',
        suggestion: 'Add an end node or action to complete workflow execution'
      });
    }

    return result;
  }

  /**
   * Validate node types against WK-002 executor registry
   * @param nodes - Array of nodes to validate
   * @returns Validation result
   */
  private async validateNodeTypes(nodes: any[]): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // This would integrate với WK-002 NodeExecutorRegistry
    // For now, validate against known node type patterns
    const validNodeTypePatterns = [
      /^system\./,
      /^google\./,
      /^facebook\./,
      /^zalo\./,
      /^webhook\./,
      /^http\./,
      /^email\./,
      /^sms\./
    ];

    for (const node of nodes) {
      const isValidType = validNodeTypePatterns.some(pattern => 
        pattern.test(node.type)
      );

      if (!isValidType) {
        result.warnings.push({
          code: 'UNKNOWN_NODE_TYPE',
          message: `Node type '${node.type}' is not recognized`,
          path: `nodes[${node.id}].type`,
          suggestion: 'Verify node type is supported by the executor framework'
        });
      }
    }

    return result;
  }

  /**
   * Validate node inputs based on node type
   * @param nodeType - Type of node
   * @param inputs - Node inputs to validate
   * @returns Validation result
   */
  private async validateNodeInputs(nodeType: string, inputs: any): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // This would integrate với WK-002 executor input schemas
    // For now, basic validation
    if (typeof inputs !== 'object') {
      result.isValid = false;
      result.errors.push({
        code: 'INVALID_INPUTS_TYPE',
        message: 'Node inputs must be an object',
        severity: 'error'
      });
    }

    return result;
  }

  /**
   * Validate workflow performance characteristics
   * @param definition - Workflow definition
   * @returns Validation result with warnings
   */
  private validatePerformance(definition: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    const { nodes, edges } = definition;

    // Check workflow complexity
    if (nodes.length > 100) {
      result.warnings.push({
        code: 'LARGE_WORKFLOW',
        message: `Workflow has ${nodes.length} nodes, which may impact performance`,
        suggestion: 'Consider breaking large workflows into smaller sub-workflows'
      });
    }

    if (edges.length > 200) {
      result.warnings.push({
        code: 'MANY_CONNECTIONS',
        message: `Workflow has ${edges.length} edges, which may impact performance`,
        suggestion: 'Review workflow complexity and consider simplification'
      });
    }

    // Check for potential bottlenecks
    const nodeConnections = new Map<string, number>();
    edges.forEach(edge => {
      nodeConnections.set(edge.targetNodeId, (nodeConnections.get(edge.targetNodeId) || 0) + 1);
    });

    for (const [nodeId, connectionCount] of nodeConnections.entries()) {
      if (connectionCount > 10) {
        result.warnings.push({
          code: 'HIGH_FAN_IN',
          message: `Node '${nodeId}' has ${connectionCount} incoming connections`,
          suggestion: 'Consider using intermediate nodes to reduce complexity'
        });
      }
    }

    return result;
  }

  /**
   * Format AJV validation errors
   * @param errors - AJV errors
   * @returns Formatted validation errors
   */
  private formatAjvErrors(errors: any[]): ValidationError[] {
    return errors.map(error => ({
      code: `SCHEMA_${error.keyword?.toUpperCase() || 'ERROR'}`,
      message: this.formatAjvErrorMessage(error),
      path: error.instancePath || error.schemaPath,
      severity: 'error' as const,
      details: error
    }));
  }

  /**
   * Format AJV error message
   * @param error - AJV error object
   * @returns Formatted error message
   */
  private formatAjvErrorMessage(error: any): string {
    const path = error.instancePath || 'root';
    
    switch (error.keyword) {
      case 'required':
        return `Missing required property: ${error.params.missingProperty}`;
      case 'type':
        return `Property '${path}' should be ${error.params.type}`;
      case 'format':
        return `Property '${path}' should match format '${error.params.format}'`;
      case 'pattern':
        return `Property '${path}' should match pattern`;
      case 'minimum':
        return `Property '${path}' should be >= ${error.params.limit}`;
      case 'maximum':
        return `Property '${path}' should be <= ${error.params.limit}`;
      case 'minItems':
        return `Array '${path}' should have at least ${error.params.limit} items`;
      case 'maxItems':
        return `Array '${path}' should have at most ${error.params.limit} items`;
      case 'enum':
        return `Property '${path}' should be one of: ${error.params.allowedValues?.join(', ')}`;
      default:
        return error.message || 'Validation failed';
    }
  }

  /**
   * Add custom format validators
   */
  private addCustomFormats(): void {
    // Node ID format
    this.ajv.addFormat('nodeId', {
      type: 'string',
      validate: (data: string) => /^[a-zA-Z0-9_-]+$/.test(data)
    });

    // Node type format
    this.ajv.addFormat('nodeType', {
      type: 'string',
      validate: (data: string) => /^[a-z]+(\.[a-z]+)*$/.test(data)
    });

    // Color format
    this.ajv.addFormat('color', {
      type: 'string',
      validate: (data: string) => /^#[0-9A-Fa-f]{6}$/.test(data)
    });
  }
}
