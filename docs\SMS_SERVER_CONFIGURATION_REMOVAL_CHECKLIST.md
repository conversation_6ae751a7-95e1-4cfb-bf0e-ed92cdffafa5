# SMS Server Configuration Removal Checklist

## Pre-Removal Verification ✅

### Data Migration Status
- [ ] All SMS campaigns migrated to use Integration entity
- [ ] All SmsServerConfiguration data migrated to Integration
- [ ] No active references to sms_server_id in campaigns
- [ ] Integration entity working properly for SMS

### Verification Queries
```sql
-- Check unmigrated user campaigns
SELECT COUNT(*) FROM sms_campaign_user 
WHERE sms_integration_config IS NULL AND sms_server_id IS NOT NULL;
-- Should return 0

-- Check unmigrated admin campaigns  
SELECT COUNT(*) FROM sms_campaign_admin 
WHERE sms_integration_id IS NULL AND sms_server_id IS NOT NULL;
-- Should return 0

-- Check SMS integrations exist
SELECT COUNT(*) FROM integration i
JOIN integration_providers ip ON i.type_id = ip.id
WHERE ip.type IN ('SMS_FPT', 'SMS_TWILIO', 'SMS_VONAGE');
-- Should return > 0
```

## Phase 1: Code Updates 🔄

### Update Services
- [ ] Update `admin-twilio-sms.service.ts`
  - [ ] Replace SmsServerConfiguration import with Integration
  - [ ] Replace SmsServerConfigurationRepository with IntegrationRepository
  - [ ] Update method calls and data handling
  
- [ ] Update `user-twilio-sms.service.ts`
  - [ ] Replace SmsServerConfiguration import with Integration
  - [ ] Replace SmsServerConfigurationRepository with IntegrationRepository
  - [ ] Update method calls and data handling

### Remove Imports
- [ ] Search and remove all `SmsServerConfiguration` imports
- [ ] Search and remove all `SmsServerConfigurationRepository` imports
- [ ] Update any remaining service dependencies

### Test Code Changes
- [ ] Application compiles without errors
- [ ] No TypeScript errors
- [ ] SMS functionality works with Integration

## Phase 2: Remove Files 🗑️

### Entity & Repository
- [ ] Remove `src/modules/integration/entities/sms-server-configuration.entity.ts`
- [ ] Remove `src/modules/integration/repositories/sms-server-configuration.repository.ts`

### Services
- [ ] Remove `src/modules/integration/admin/services/sms-server-configuration-admin.service.ts`
- [ ] Remove `src/modules/integration/user/services/sms-server-configuration-user.service.ts`

### Controllers
- [ ] Remove `src/modules/integration/admin/controllers/sms-server-configuration-admin.controller.ts`
- [ ] Remove `src/modules/integration/user/controllers/sms-server-configuration-user.controller.ts`

### DTOs
- [ ] Remove `src/modules/integration/user/dto/sms/update-sms-server.dto.ts`
- [ ] Remove `src/modules/integration/admin/dto/sms-server-admin-response.dto.ts`

## Phase 3: Update Modules 📦

### Integration Module
- [ ] Remove SmsServerConfiguration from entities export
- [ ] Remove SmsServerConfigurationRepository from imports
- [ ] Remove SmsServerConfigurationRepository from providers
- [ ] Remove related services from providers
- [ ] Remove related controllers from controllers array

### Marketing Module  
- [ ] Remove SmsServerConfigurationRepository import
- [ ] Update any remaining dependencies

### Test Module Updates
- [ ] Application starts without errors
- [ ] No missing dependency errors
- [ ] All modules load correctly

## Phase 4: Database Cleanup 🗄️

### Pre-Database Cleanup
- [ ] Create full database backup
- [ ] Verify no active foreign key constraints
- [ ] Confirm all data is safely migrated

### Run Database Migration
- [ ] Execute: `./scripts/remove-sms-server-configuration.sh`
- [ ] Verify backup table created: `sms_server_configurations_backup`
- [ ] Confirm original table dropped
- [ ] Check unused columns removed from other tables

### Post-Database Verification
```sql
-- Verify table is gone
SELECT * FROM information_schema.tables 
WHERE table_name = 'sms_server_configurations';
-- Should return no rows

-- Verify backup exists
SELECT COUNT(*) FROM sms_server_configurations_backup;
-- Should return count of backed up records

-- Verify SMS functionality still works
SELECT COUNT(*) FROM integration i
JOIN integration_providers ip ON i.type_id = ip.id
WHERE ip.type LIKE 'SMS_%';
-- Should return active SMS integrations
```

## Phase 5: Final Testing 🧪

### Functional Testing
- [ ] SMS campaigns can be created
- [ ] SMS campaigns can be sent
- [ ] SMS integration management works
- [ ] No errors in application logs
- [ ] All SMS providers work (FPT, Twilio, etc.)

### Integration Testing
- [ ] Admin SMS functionality works
- [ ] User SMS functionality works
- [ ] Campaign creation with Integration works
- [ ] SMS sending through Integration works

### Performance Testing
- [ ] No performance degradation
- [ ] Database queries optimized
- [ ] No memory leaks from removed code

## Phase 6: Documentation & Cleanup 📚

### Update Documentation
- [ ] Update API documentation
- [ ] Update database schema documentation
- [ ] Update integration guides
- [ ] Remove outdated SmsServerConfiguration references

### Code Cleanup
- [ ] Remove commented out code
- [ ] Clean up unused imports
- [ ] Update code comments
- [ ] Run linting and formatting

### Remove Migration Files
- [ ] Remove old migration files (after confirming success):
  - [ ] `database/migrations/add-integration-provider-id-to-sms-server-configurations.sql`
  - [ ] `database/migrations/migrate-sms-server-to-integration.sql`
  - [ ] `scripts/run-sms-server-configuration-migration.sh`
  - [ ] `scripts/run-sms-server-configuration-migration.ps1`

## Rollback Plan 🔄

### If Issues Occur:
1. **Stop application immediately**
2. **Restore database from backup**:
   ```bash
   psql -h $DB_HOST -d $DB_NAME -U $DB_USER < backup_before_sms_cleanup_YYYYMMDD_HHMMSS.sql
   ```
3. **Restore code from git**:
   ```bash
   git checkout HEAD~1  # or specific commit
   ```
4. **Restart application**
5. **Investigate issues**

### Emergency Contacts
- [ ] Database admin contact ready
- [ ] DevOps team notified
- [ ] Backup verification completed

## Success Criteria ✅

### Technical Success
- [ ] Application runs without SmsServerConfiguration
- [ ] All SMS functionality works through Integration
- [ ] No compilation or runtime errors
- [ ] Database cleaned up successfully
- [ ] Performance maintained or improved

### Business Success
- [ ] SMS campaigns work normally
- [ ] Users can manage SMS integrations
- [ ] No service disruption
- [ ] All SMS providers functional

## Final Sign-off 📝

- [ ] **Developer**: Code changes reviewed and tested
- [ ] **QA**: Functional testing completed
- [ ] **DevOps**: Database migration verified
- [ ] **Product**: Business functionality confirmed
- [ ] **Security**: No security issues introduced

**Completion Date**: ___________
**Completed By**: ___________
**Notes**: ___________
