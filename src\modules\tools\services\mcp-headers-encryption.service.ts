import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@config/config.service';
import * as crypto from 'crypto';

/**
 * Service xử lý mã hóa và giải mã headers cho MCP systems
 * Sử dụng AES-256-GCM để đảm bảo t<PERSON>h bảo mật và toàn vẹn dữ liệu
 */
@Injectable()
export class McpHeadersEncryptionService {
  private readonly logger = new Logger(McpHeadersEncryptionService.name);
  private readonly algorithm = 'aes-256-gcm';
  private readonly ivLength = 16; // 128 bits
  private readonly tagLength = 16; // 128 bits
  private readonly keyLength = 32; // 256 bits

  constructor(private readonly configService: ConfigService) {}

  /**
   * Mã hóa headers cho user MCP
   * @param headers Headers cần mã hóa (JSON string hoặc object)
   * @returns Chuỗi đã mã hóa (base64)
   */
  encryptUserHeaders(headers: string | Record<string, any>): string {
    const encryptionKey = this.configService.getMcpHeadersEncryption().userEncryptionKey;
    return this.encrypt(headers, encryptionKey);
  }

  /**
   * Giải mã headers cho user MCP
   * @param encryptedHeaders Chuỗi đã mã hóa (base64)
   * @returns Headers đã giải mã (JSON string)
   */
  decryptUserHeaders(encryptedHeaders: string): string {
    const encryptionKey = this.configService.getMcpHeadersEncryption().userEncryptionKey;
    return this.decrypt(encryptedHeaders, encryptionKey);
  }

  /**
   * Mã hóa headers cho admin MCP
   * @param headers Headers cần mã hóa (JSON string hoặc object)
   * @returns Chuỗi đã mã hóa (base64)
   */
  encryptAdminHeaders(headers: string | Record<string, any>): string {
    const encryptionKey = this.configService.getMcpHeadersEncryption().adminEncryptionKey;
    return this.encrypt(headers, encryptionKey);
  }

  /**
   * Giải mã headers cho admin MCP
   * @param encryptedHeaders Chuỗi đã mã hóa (base64)
   * @returns Headers đã giải mã (JSON string)
   */
  decryptAdminHeaders(encryptedHeaders: string): string {
    const encryptionKey = this.configService.getMcpHeadersEncryption().adminEncryptionKey;
    return this.decrypt(encryptedHeaders, encryptionKey);
  }

  /**
   * Mã hóa dữ liệu sử dụng AES-256-GCM
   * @param data Dữ liệu cần mã hóa
   * @param encryptionKey Khóa mã hóa (32 bytes)
   * @returns Chuỗi đã mã hóa (base64)
   */
  private encrypt(data: string | Record<string, any>, encryptionKey: string): string {
    try {
      // Chuyển đổi data thành string nếu cần
      const plaintext = typeof data === 'string' ? data : JSON.stringify(data);
      
      // Tạo key từ encryption key
      const key = this.deriveKey(encryptionKey);
      
      // Tạo IV ngẫu nhiên
      const iv = crypto.randomBytes(this.ivLength);
      
      // Tạo cipher
      const cipher = crypto.createCipheriv(this.algorithm, key, iv);
      cipher.setAAD(Buffer.from('mcp-headers')); // Additional Authenticated Data
      
      // Mã hóa
      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      // Lấy authentication tag
      const tag = cipher.getAuthTag();
      
      // Kết hợp IV + tag + encrypted data
      const result = Buffer.concat([
        iv,
        tag,
        Buffer.from(encrypted, 'hex')
      ]);
      
      return result.toString('base64');
    } catch (error) {
      this.logger.error('Lỗi khi mã hóa headers:', error);
      throw new Error('Không thể mã hóa headers');
    }
  }

  /**
   * Giải mã dữ liệu sử dụng AES-256-GCM
   * @param encryptedData Dữ liệu đã mã hóa (base64)
   * @param encryptionKey Khóa mã hóa (32 bytes)
   * @returns Dữ liệu đã giải mã
   */
  private decrypt(encryptedData: string, encryptionKey: string): string {
    try {
      // Decode base64
      const buffer = Buffer.from(encryptedData, 'base64');
      
      // Tách IV, tag và encrypted data
      const iv = buffer.subarray(0, this.ivLength);
      const tag = buffer.subarray(this.ivLength, this.ivLength + this.tagLength);
      const encrypted = buffer.subarray(this.ivLength + this.tagLength);
      
      // Tạo key từ encryption key
      const key = this.deriveKey(encryptionKey);
      
      // Tạo decipher
      const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
      decipher.setAAD(Buffer.from('mcp-headers')); // Additional Authenticated Data
      decipher.setAuthTag(tag);
      
      // Giải mã
      let decrypted = decipher.update(encrypted, undefined, 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      this.logger.error('Lỗi khi giải mã headers:', error);
      throw new Error('Không thể giải mã headers');
    }
  }

  /**
   * Tạo key từ encryption key sử dụng PBKDF2
   * @param encryptionKey Khóa gốc
   * @returns Key đã derive (32 bytes)
   */
  private deriveKey(encryptionKey: string): Buffer {
    const salt = Buffer.from('mcp-headers-salt', 'utf8'); // Salt cố định cho consistency
    return crypto.pbkdf2Sync(encryptionKey, salt, 10000, this.keyLength, 'sha256');
  }

  /**
   * Kiểm tra tính hợp lệ của headers đã mã hóa
   * @param encryptedHeaders Headers đã mã hóa
   * @param isAdmin Có phải admin không
   * @returns true nếu hợp lệ
   */
  validateEncryptedHeaders(encryptedHeaders: string, isAdmin: boolean = false): boolean {
    try {
      if (isAdmin) {
        this.decryptAdminHeaders(encryptedHeaders);
      } else {
        this.decryptUserHeaders(encryptedHeaders);
      }
      return true;
    } catch (error) {
      this.logger.warn('Headers không hợp lệ:', error.message);
      return false;
    }
  }

  /**
   * Parse headers từ chuỗi JSON đã giải mã
   * @param headersString Chuỗi JSON headers
   * @returns Object headers hoặc null nếu không hợp lệ
   */
  parseHeaders(headersString: string): Record<string, string> | null {
    try {
      const parsed = JSON.parse(headersString);
      if (typeof parsed === 'object' && parsed !== null) {
        return parsed;
      }
      return null;
    } catch (error) {
      this.logger.warn('Không thể parse headers:', error.message);
      return null;
    }
  }

  /**
   * Mã hóa và validate headers trước khi lưu
   * @param headers Headers object
   * @param isAdmin Có phải admin không
   * @returns Headers đã mã hóa hoặc null nếu không hợp lệ
   */
  encryptAndValidateHeaders(
    headers: Record<string, any>, 
    isAdmin: boolean = false
  ): string | null {
    try {
      // Validate headers format
      if (!headers || typeof headers !== 'object') {
        return null;
      }

      // Mã hóa
      const encrypted = isAdmin 
        ? this.encryptAdminHeaders(headers)
        : this.encryptUserHeaders(headers);

      // Validate bằng cách thử giải mã
      const decrypted = isAdmin
        ? this.decryptAdminHeaders(encrypted)
        : this.decryptUserHeaders(encrypted);

      const parsed = this.parseHeaders(decrypted);
      if (!parsed) {
        return null;
      }

      return encrypted;
    } catch (error) {
      this.logger.error('Lỗi khi mã hóa và validate headers:', error);
      return null;
    }
  }

  /**
   * Giải mã và parse headers để sử dụng
   * @param encryptedHeaders Headers đã mã hóa
   * @param isAdmin Có phải admin không
   * @returns Headers object hoặc null nếu không hợp lệ
   */
  decryptAndParseHeaders(
    encryptedHeaders: string, 
    isAdmin: boolean = false
  ): Record<string, string> | null {
    try {
      const decrypted = isAdmin
        ? this.decryptAdminHeaders(encryptedHeaders)
        : this.decryptUserHeaders(encryptedHeaders);

      return this.parseHeaders(decrypted);
    } catch (error) {
      this.logger.error('Lỗi khi giải mã và parse headers:', error);
      return null;
    }
  }
}
