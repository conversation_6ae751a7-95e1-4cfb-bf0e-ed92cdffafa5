import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsUUID } from 'class-validator';
import { QueryDto } from '@common/dto';
import { ProviderLlmEnum } from '@/modules/models/constants';

/**
 * DTO cho việc thay thế models của type agent
 */
export class ReplaceModelsForTypeAgentDto {
  /**
   * Có áp dụng cho tất cả model không
   */
  @ApiPropertyOptional({
    description: 'Có áp dụng cho tất cả model không. Nếu true thì bỏ qua modelRegistryIds',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'isAllModel phải là boolean' })
  isAllModel?: boolean;

  /**
   * Danh sách model registry IDs mới (thay thế toàn bộ)
   */
  @ApiPropertyOptional({
    description: 'Danh sách model registry IDs mới để thay thế toàn bộ models của type agent. Bỏ qua nếu isAllModel = true',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi modelRegistryId phải là UUID hợp lệ' })
  modelRegistryIds?: string[];
}

/**
 * DTO cho việc xóa models khỏi type agent
 */
export class RemoveModelsFromTypeAgentDto {
  /**
   * Danh sách model registry IDs cần xóa
   */
  @ApiProperty({
    description: 'Danh sách model registry IDs cần xóa khỏi type agent',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true, message: 'Mỗi modelRegistryId phải là UUID hợp lệ' })
  modelRegistryIds: string[];
}

/**
 * DTO cho query parameters của models listing
 */
export class TypeAgentModelsQueryDto extends QueryDto {
  // Kế thừa tất cả fields từ QueryDto: page, limit, search, sortBy, sortDirection
  // Có thể thêm các fields specific cho models nếu cần
}

/**
 * DTO cho thông tin chi tiết model
 */
export class TypeAgentModelItemDto {
  /**
   * ID của model registry
   */
  @ApiProperty({
    description: 'ID của model registry',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  /**
   * Pattern tên model
   */
  @ApiProperty({
    description: 'Pattern tên model (model_name_pattern)',
    example: 'gpt-4*',
  })
  name: string;

  /**
   * Model ID (giống với name)
   */
  @ApiProperty({
    description: 'Model ID pattern',
    example: 'gpt-4*',
  })
  modelId: string;

  /**
   * Provider của model
   */
  @ApiProperty({
    description: 'Provider của model',
    example: 'OPENAI',
    enum: ProviderLlmEnum,
  })
  provider: ProviderLlmEnum;

  /**
   * Trạng thái của model (derived)
   */
  @ApiProperty({
    description: 'Trạng thái của model (ACTIVE nếu chưa xóa, INACTIVE nếu đã xóa)',
    example: 'ACTIVE',
    enum: ['ACTIVE', 'INACTIVE'],
  })
  status: string;

  /**
   * Ngày tạo
   */
  @ApiProperty({
    description: 'Ngày tạo model registry (timestamp)',
    example: 1682506892000,
  })
  createdAt: number;
}
