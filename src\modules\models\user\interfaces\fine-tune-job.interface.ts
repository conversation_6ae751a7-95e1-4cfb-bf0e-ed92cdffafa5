import { ProviderFineTuneEnum } from '@modules/models/constants/provider.enum';
import { FineTuneContextEnum, ModelTypeEnum } from '@modules/models/constants/fine-tune.enum';

/**
 * Interface cho training data
 */
export interface TrainingDataInterface {
  /**
   * Dữ liệu training ở định dạng JSONL
   */
  trainData: string;

  /**
   * Dữ liệu validation ở định dạng JSONL (tùy chọn)
   */
  validationData?: string;

  /**
   * Tên file training
   */
  trainFileName?: string;

  /**
   * Tên file validation
   */
  validationFileName?: string;
}

/**
 * Interface cho dữ liệu job fine-tuning
 */
export interface FineTuneJobData {
  // === REQUIRED FIELDS ===
  /**
   * ID của fine-tune history record
   */
  historyId: string;

  /**
   * ID của user model fine-tune record
   */
  userModelFineTuneId: string;

  /**
   * Context của job (USER hoặc ADMIN)
   */
  context: FineTuneContextEnum;

  /**
   * Provider AI (OPENAI hoặc GOOGLE)
   */
  provider: ProviderFineTuneEnum;

  /**
   * Loại model (SYSTEM hoặc USER)
   */
  modelType: ModelTypeEnum;

  /**
   * Model cơ sở (ví dụ: 'gpt-3.5-turbo', 'gemini-pro')
   */
  baseModel: string;

  /**
   * Timestamp tạo job
   */
  timestamp: number;

  // === CONDITIONAL FIELDS ===
  /**
   * ID của user (bắt buộc cho USER context)
   */
  userId?: number;

  /**
   * ID của employee (bắt buộc cho ADMIN context)
   */
  employeeId?: number;

  /**
   * ID của user key (bắt buộc cho USER model)
   */
  userKeyId?: string;

  /**
   * Số R-Points cần hoàn trả nếu job thất bại (bắt buộc cho USER + SYSTEM model)
   */
  pointsToRefund?: number;

  // === TRAINING DATA (Chọn 1 trong 2) ===
  /**
   * Option 1: Upload data trong Worker
   */
  trainingData?: TrainingDataInterface;

  /**
   * Option 2: File đã upload sẵn - Training file ID
   */
  trainingFileId?: string;

  /**
   * Option 2: File đã upload sẵn - Validation file ID
   */
  validationFileId?: string;

  // === OPTIONAL FIELDS ===
  /**
   * Siêu tham số cho quá trình fine-tuning
   */
  hyperparameters?: {
    /**
     * Số epoch để huấn luyện
     */
    nEpochs?: number;

    /**
     * Kích thước batch
     */
    batchSize?: number | 'auto';

    /**
     * Hệ số tốc độ học
     */
    learningRateMultiplier?: number | 'auto';
  };

  /**
   * Metadata bổ sung
   */
  metadata?: Record<string, any>;

  /**
   * ID của job từ provider (để trống '', sẽ được cập nhật trong Worker)
   */
  providerJobId: string;
}
