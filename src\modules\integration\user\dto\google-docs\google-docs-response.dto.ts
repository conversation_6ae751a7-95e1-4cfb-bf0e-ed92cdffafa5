import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO response cho Google Docs integration
 */
export class GoogleDocsIntegrationResponseDto {
  /**
   * ID của integration
   */
  @ApiProperty({
    description: 'ID của integration',
    example: 'uuid-string',
  })
  @Expose()
  id: string;

  /**
   * Tên của integration
   */
  @ApiProperty({
    description: 'Tên của integration',
    example: 'My Google Docs Integration',
  })
  @Expose()
  integrationName: string;

  /**
   * ID của provider type
   */
  @ApiProperty({
    description: 'ID của provider type',
    example: 1,
  })
  @Expose()
  typeId: number;

  /**
   * ID của user sở hữu integration
   */
  @ApiProperty({
    description: 'ID của user sở hữu integration',
    example: 123,
  })
  @Expose()
  userId: number;

  /**
   * Trạng thái kết nối
   */
  @ApiProperty({
    description: 'Trạng thái kết nối',
    example: 'connected',
    enum: ['connected', 'disconnected', 'expired'],
  })
  @Expose()
  status: 'connected' | 'disconnected' | 'expired';

  /**
   * Thời gian hết hạn của access token
   */
  @ApiPropertyOptional({
    description: 'Thời gian hết hạn của access token (timestamp)',
    example: 1640995200000,
  })
  @Expose()
  expiresAt?: number;

  /**
   * Scope được cấp quyền
   */
  @ApiPropertyOptional({
    description: 'Scope được cấp quyền',
    example: 'https://www.googleapis.com/auth/documents',
  })
  @Expose()
  scope?: string;

  /**
   * Thời gian tạo integration
   */
  @ApiProperty({
    description: 'Thời gian tạo integration',
    example: 1640995200000,
  })
  @Expose()
  createdAt: number;

  /**
   * Thông tin metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Thông tin metadata bổ sung',
    type: Object,
  })
  @Expose()
  metadata?: Record<string, any>;
}

/**
 * DTO response cho danh sách Google Docs integrations
 */
export class GoogleDocsIntegrationsListResponseDto {
  /**
   * Danh sách integrations
   */
  @ApiProperty({
    description: 'Danh sách integrations',
    type: [GoogleDocsIntegrationResponseDto],
  })
  items: GoogleDocsIntegrationResponseDto[];

  /**
   * Tổng số integrations
   */
  @ApiProperty({
    description: 'Tổng số integrations',
    example: 10,
  })
  total: number;

  /**
   * Trang hiện tại
   */
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  page: number;

  /**
   * Số lượng items per page
   */
  @ApiProperty({
    description: 'Số lượng items per page',
    example: 10,
  })
  limit: number;
}

/**
 * DTO response cho thông tin document
 */
export class GoogleDocumentInfoResponseDto {
  /**
   * ID của document
   */
  @ApiProperty({
    description: 'ID của document',
    example: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
  })
  documentId: string;

  /**
   * Tiêu đề của document
   */
  @ApiProperty({
    description: 'Tiêu đề của document',
    example: 'My Document',
  })
  title: string;

  /**
   * URL để xem document
   */
  @ApiProperty({
    description: 'URL để xem document',
    example: 'https://docs.google.com/document/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit',
  })
  webViewLink: string;

  /**
   * Thời gian tạo document
   */
  @ApiProperty({
    description: 'Thời gian tạo document',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdTime: string;

  /**
   * Thời gian sửa đổi cuối cùng
   */
  @ApiProperty({
    description: 'Thời gian sửa đổi cuối cùng',
    example: '2024-01-15T15:45:00.000Z',
  })
  modifiedTime: string;

  /**
   * Thông tin về người tạo
   */
  @ApiPropertyOptional({
    description: 'Thông tin về người tạo',
    type: Object,
  })
  createdBy?: {
    displayName: string;
    emailAddress: string;
  };

  /**
   * Thông tin về người sửa đổi cuối cùng
   */
  @ApiPropertyOptional({
    description: 'Thông tin về người sửa đổi cuối cùng',
    type: Object,
  })
  lastModifiedBy?: {
    displayName: string;
    emailAddress: string;
  };
}

/**
 * DTO response cho danh sách documents
 */
export class GoogleDocumentsListResponseDto {
  /**
   * Danh sách documents
   */
  @ApiProperty({
    description: 'Danh sách documents',
    type: [GoogleDocumentInfoResponseDto],
  })
  documents: GoogleDocumentInfoResponseDto[];

  /**
   * Token để lấy trang tiếp theo
   */
  @ApiPropertyOptional({
    description: 'Token để lấy trang tiếp theo',
    example: 'next-page-token',
  })
  nextPageToken?: string;
}

/**
 * DTO response cho nội dung document
 */
export class GoogleDocumentContentResponseDto {
  /**
   * ID của document
   */
  @ApiProperty({
    description: 'ID của document',
    example: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
  })
  documentId: string;

  /**
   * Tiêu đề của document
   */
  @ApiProperty({
    description: 'Tiêu đề của document',
    example: 'My Document',
  })
  title: string;

  /**
   * Nội dung text của document
   */
  @ApiProperty({
    description: 'Nội dung text của document',
    example: 'This is the content of my document...',
  })
  content: string;

  /**
   * Số từ trong document
   */
  @ApiProperty({
    description: 'Số từ trong document',
    example: 1250,
  })
  wordCount: number;

  /**
   * Số ký tự trong document
   */
  @ApiProperty({
    description: 'Số ký tự trong document',
    example: 7500,
  })
  characterCount: number;
}

/**
 * DTO response cho việc tạo document
 */
export class GoogleDocumentCreateResponseDto {
  /**
   * ID của document được tạo
   */
  @ApiProperty({
    description: 'ID của document được tạo',
    example: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
  })
  documentId: string;

  /**
   * Tiêu đề của document
   */
  @ApiProperty({
    description: 'Tiêu đề của document',
    example: 'New Document',
  })
  title: string;

  /**
   * URL để xem document
   */
  @ApiProperty({
    description: 'URL để xem document',
    example: 'https://docs.google.com/document/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit',
  })
  webViewLink: string;

  /**
   * Thời gian tạo document
   */
  @ApiProperty({
    description: 'Thời gian tạo document',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdTime: string;
}
