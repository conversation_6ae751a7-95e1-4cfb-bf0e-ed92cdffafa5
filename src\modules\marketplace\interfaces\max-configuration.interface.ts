/**
 * Interface cho cấu hình giới hạn flash sale
 */
export interface MaxConfiguration {
  /**
   * Số lượng tối đa mỗi user có thể mua trong toàn bộ flash sale
   * null = không giới hạn
   */
  maxPerUser: number | null;

  /**
   * Tổng số lượng inventory cho flash sale
   * null = sử dụng toàn bộ inventory của sản phẩm
   */
  totalInventory: number | null;

  /**
   * Giới hạn số lượng mỗi đơn hàng
   * null = không giới hạn
   */
  purchaseLimitPerOrder: number | null;

  /**
   * Giới hạn mua lặp trong khung thời gian ngắn
   * null = không giới hạn theo thời gian
   */
  timeWindowLimit: {
    /** Số lượng cho phép trong khung thời gian */
    qty: number;
    /** Khung thời gian tính bằng phút */
    windowMinutes: number;
  } | null;
}

/**
 * Default max configuration khi user không cung cấp
 */
export const DEFAULT_MAX_CONFIGURATION: MaxConfiguration = {
  maxPerUser: null,
  totalInventory: null,
  purchaseLimitPerOrder: null,
  timeWindowLimit: null
};
