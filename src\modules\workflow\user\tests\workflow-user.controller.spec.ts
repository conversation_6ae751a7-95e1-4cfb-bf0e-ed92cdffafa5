import { Test, TestingModule } from '@nestjs/testing';
import { WorkflowUserController } from '../controllers/workflow-user.controller';
import { WorkflowUserService } from '../services/workflow-user.service';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  ToggleWorkflowStatusDto,
  WorkflowQueryDto,
  WorkflowListItemDto,
  WorkflowDetailDto,
  WorkflowStatisticsDto,
} from '../dto';
import { WorkflowSortBy } from '../dto/workflow-query.dto';
import { SortDirection } from '@common/dto/query.dto';

describe('WorkflowUserController', () => {
  let controller: WorkflowUserController;
  let workflowUserService: jest.Mocked<WorkflowUserService>;

  const mockUser: JwtPayload = {
    sub: 1,
    id: 1,
    email: '<EMAIL>',
    role: 'user',
  } as JwtPayload;

  const mockWorkflowListItem: WorkflowListItemDto = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'Test Workflow',
    isActive: true,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    nodeCount: 0,
    edgeCount: 0,
  };

  const mockWorkflowDetail: WorkflowDetailDto = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    userId: 1,
    employeeId: undefined,
    name: 'Test Workflow',
    isActive: true,
    definition: { nodes: [], edges: [] },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  };

  const mockWorkflowStatistics: WorkflowStatisticsDto = {
    totalWorkflows: 10,
    activeWorkflows: 7,
    inactiveWorkflows: 3,
  };

  beforeEach(async () => {
    const mockWorkflowUserService = {
      getWorkflows: jest.fn(),
      getWorkflowById: jest.fn(),
      createWorkflow: jest.fn(),
      updateWorkflow: jest.fn(),
      deleteWorkflow: jest.fn(),
      toggleWorkflowStatus: jest.fn(),
      searchWorkflows: jest.fn(),
      getWorkflowStatistics: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkflowUserController],
      providers: [
        {
          provide: WorkflowUserService,
          useValue: mockWorkflowUserService,
        },
      ],
    }).compile();

    controller = module.get<WorkflowUserController>(WorkflowUserController);
    workflowUserService = module.get(WorkflowUserService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getWorkflows', () => {
    it('should return paginated workflows', async () => {
      const queryDto: WorkflowQueryDto = {
        page: 1,
        limit: 10,
        sortBy: WorkflowSortBy.UPDATED_AT,
        sortDirection: SortDirection.DESC,
      };

      const mockPaginatedResult: PaginatedResult<WorkflowListItemDto> = {
        items: [mockWorkflowListItem],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
          hasItems: true
        },


      };

      workflowUserService.getWorkflows.mockResolvedValue(mockPaginatedResult);

      const result = await controller.getWorkflows(queryDto, mockUser);

      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(0);
      expect(result.result).toEqual(mockPaginatedResult);
      expect(result.message).toBe('Lấy danh sách workflows thành công');
      expect(workflowUserService.getWorkflows).toHaveBeenCalledWith(mockUser.id, queryDto);
    });
  });

  describe('getWorkflowById', () => {
    it('should return workflow detail', async () => {
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      workflowUserService.getWorkflowById.mockResolvedValue(mockWorkflowDetail);

      const result = await controller.getWorkflowById(workflowId, mockUser);

      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(0);
      expect(result.result).toEqual(mockWorkflowDetail);
      expect(result.message).toBe('Lấy chi tiết workflow thành công');
      expect(workflowUserService.getWorkflowById).toHaveBeenCalledWith(mockUser.id, workflowId);
    });
  });

  describe('createWorkflow', () => {
    it('should create workflow successfully', async () => {
      const createDto: CreateWorkflowDto = {
        name: 'New Workflow',
        isActive: false,
        definition: { nodes: [], edges: [] },
      };

      workflowUserService.createWorkflow.mockResolvedValue(mockWorkflowDetail);

      const result = await controller.createWorkflow(createDto, mockUser);

      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(0);
      expect(result.result).toEqual(mockWorkflowDetail);
      expect(result.message).toBe('Tạo workflow thành công');
      expect(workflowUserService.createWorkflow).toHaveBeenCalledWith(mockUser.id, createDto);
    });
  });

  describe('updateWorkflow', () => {
    it('should update workflow successfully', async () => {
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      const updateDto: UpdateWorkflowDto = {
        name: 'Updated Workflow',
        isActive: true,
      };

      const updatedWorkflow = { ...mockWorkflowDetail, ...updateDto };
      workflowUserService.updateWorkflow.mockResolvedValue(updatedWorkflow);

      const result = await controller.updateWorkflow(workflowId, updateDto, mockUser);

      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(0);
      expect(result.result).toEqual(updatedWorkflow);
      expect(result.message).toBe('Cập nhật workflow thành công');
      expect(workflowUserService.updateWorkflow).toHaveBeenCalledWith(
        mockUser.id,
        workflowId,
        updateDto,
      );
    });
  });

  describe('deleteWorkflow', () => {
    it('should delete workflow successfully', async () => {
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      workflowUserService.deleteWorkflow.mockResolvedValue();

      const result = await controller.deleteWorkflow(workflowId, mockUser);

      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(0);
      expect(result.result).toEqual({ success: true });
      expect(result.message).toBe('Xóa workflow thành công');
      expect(workflowUserService.deleteWorkflow).toHaveBeenCalledWith(mockUser.id, workflowId);
    });
  });

  describe('toggleWorkflowStatus', () => {
    it('should toggle workflow status successfully', async () => {
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      const toggleDto: ToggleWorkflowStatusDto = {
        isActive: false,
      };

      const updatedWorkflow = { ...mockWorkflowDetail, isActive: false };
      workflowUserService.toggleWorkflowStatus.mockResolvedValue(updatedWorkflow);

      const result = await controller.toggleWorkflowStatus(workflowId, toggleDto, mockUser);

      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(0);
      expect(result.result).toEqual(updatedWorkflow);
      expect(result.message).toBe('Cập nhật trạng thái workflow thành công');
      expect(workflowUserService.toggleWorkflowStatus).toHaveBeenCalledWith(
        mockUser.id,
        workflowId,
        toggleDto.isActive,
      );
    });
  });

  describe('searchWorkflows', () => {
    it('should search workflows successfully', async () => {
      const searchQuery = 'test';
      workflowUserService.searchWorkflows.mockResolvedValue([mockWorkflowListItem]);

      const result = await controller.searchWorkflows(searchQuery, mockUser);

      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(0);
      expect(result.result).toEqual([mockWorkflowListItem]);
      expect(result.message).toBe('Tìm kiếm workflows thành công');
      expect(workflowUserService.searchWorkflows).toHaveBeenCalledWith(mockUser.id, searchQuery);
    });
  });

  describe('getWorkflowStatistics', () => {
    it('should return workflow statistics', async () => {
      workflowUserService.getWorkflowStatistics.mockResolvedValue(mockWorkflowStatistics);

      const result = await controller.getWorkflowStatistics(mockUser);

      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(0);
      expect(result.result).toEqual(mockWorkflowStatistics);
      expect(result.message).toBe('Lấy thống kê workflows thành công');
      expect(workflowUserService.getWorkflowStatistics).toHaveBeenCalledWith(mockUser.id);
    });
  });

  describe('Error Handling', () => {
    it('should propagate service errors', async () => {
      const queryDto: WorkflowQueryDto = {
        page: 1,
        limit: 10,
        sortBy: WorkflowSortBy.UPDATED_AT,
        sortDirection: SortDirection.DESC,
      };

      const error = new Error('Service error');
      workflowUserService.getWorkflows.mockRejectedValue(error);

      await expect(controller.getWorkflows(queryDto, mockUser)).rejects.toThrow(error);
    });
  });

  describe('Validation', () => {
    it('should validate UUID parameters', async () => {
      // This test would be handled by NestJS validation pipes
      // The ParseUUIDPipe would throw an error for invalid UUIDs
      const invalidId = 'invalid-uuid';
      
      // In a real scenario, this would be caught by the ParseUUIDPipe
      // and return a 400 Bad Request before reaching the controller method
      expect(invalidId).not.toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    it('should validate DTO properties', async () => {
      // This test would be handled by class-validator decorators
      // Invalid DTOs would be rejected before reaching the controller method
      const invalidCreateDto = {
        name: '', // Should fail MinLength validation
        isActive: 'not-boolean', // Should fail IsBoolean validation
      };

      expect(invalidCreateDto.name).toBe('');
      expect(typeof invalidCreateDto.isActive).not.toBe('boolean');
    });
  });
});
