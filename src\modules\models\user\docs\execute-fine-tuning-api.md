# API Execute Fine-Tuning

## <PERSON><PERSON> tả
API này cho phép thực hiện fine-tuning job với dataset và model đã được validate. API sẽ:
1. Ki<PERSON>m tra dataset và model
2. <PERSON><PERSON><PERSON> to<PERSON> và trừ chi phí từ points của user
3. Upload files lên provider (OpenAI/Gemini)
4. Tạo fine-tuning job
5. Tạo model record mới trong database
6. Tạo monitoring job trên Redis

## Endpoint
```
POST /user/data-fine-tune/execute
```

## Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Request Body
```json
{
  "datasetId": "550e8400-e29b-41d4-a716-************",
  "modelId": "550e8400-e29b-41d4-a716-************",
  "epochs": 3,
  "batchSize": "auto",
  "learningRateMultiplier": "auto",
  "suffix": "my-custom-model"
}
```

### Request Body Schema
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| datasetId | string (UUID) | Yes | ID của dataset fine-tune đã validate |
| modelId | string (UUID) | Yes | ID của base model để fine-tune |
| epochs | number | No | Số epoch (1-100, default: 3) |
| batchSize | number \| "auto" | No | Kích thước batch (default: "auto") |
| learningRateMultiplier | number \| "auto" | No | Hệ số tốc độ học (default: "auto") |
| suffix | string | No | Suffix cho tên model (chỉ OpenAI, max 40 chars) |

## Response

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "modelId": "550e8400-e29b-41d4-a716-************",
    "jobId": "ft-abc123def456",
    "provider": "OPENAI",
    "costDeducted": 450000,
    "remainingBalance": 1550000,
    "status": "running",
    "message": "Fine-tuning job đã được tạo thành công"
  },
  "message": "Tạo fine-tuning job thành công"
}
```

### Error Responses

#### 400 - Bad Request
```json
{
  "success": false,
  "error": {
    "code": 12021,
    "message": "Số dư không đủ. Cần 450000 points, hiện có 200000 points"
  }
}
```

#### 404 - Not Found
```json
{
  "success": false,
  "error": {
    "code": 5001,
    "message": "Không tìm thấy dataset fine tune"
  }
}
```

#### 400 - Dataset Not Validated
```json
{
  "success": false,
  "error": {
    "code": 5002,
    "message": "Dataset chưa được validate. Vui lòng validate trước khi thực hiện fine-tuning"
  }
}
```

## Luồng xử lý

### 1. Validation Phase
- ✅ Kiểm tra dataset tồn tại và thuộc về user
- ✅ Kiểm tra dataset đã được validate (`isValid = true`)
- ✅ Kiểm tra model tồn tại
- ✅ Kiểm tra provider của model khớp với dataset

### 2. Cost Calculation & Payment
- ✅ Tính toán chi phí: `totalTrainingTokens × training_pricing`
- ✅ Kiểm tra số dư points của user
- ✅ Trừ points từ tài khoản user

### 3. System Integration & Provider Integration
- ✅ Lấy system integration key đầu tiên (employee IS NOT NULL)
- ✅ Decrypt API key từ system integration
- ✅ Upload training file lên provider
- ✅ Upload validation file (nếu có)
- ✅ Tạo fine-tuning job với hyperparameters

### 4. Database Operations
- ✅ Tạo record mới trong `models` table
- ✅ Tạo record trong `model_detail` table với metadata
- ✅ Link model với model_detail
- ✅ Tạo mapping trong `model_integration` table

### 5. Integration Mapping & Monitoring Setup
- ✅ Lưu model-integration mapping để track system key usage
- ✅ Tạo Redis job để monitor progress
- ✅ Log thông tin để tracking

## Supported Providers

### OpenAI
- ✅ File upload via OpenAI API
- ✅ Fine-tuning job creation
- ✅ Hyperparameters: epochs, batchSize, learningRateMultiplier
- ✅ Suffix support

### Gemini
- ❌ Chưa được implement (coming soon)

## Error Handling

### Rollback Mechanism
Nếu có lỗi xảy ra sau khi trừ points:
- Points sẽ được hoàn trả tự động
- Model records sẽ được cleanup
- Provider files sẽ được xóa (nếu đã upload)

### Common Errors
1. **Insufficient Points**: Số dư không đủ để thực hiện fine-tuning
2. **Dataset Not Found**: Dataset không tồn tại hoặc không thuộc về user
3. **Dataset Not Validated**: Dataset chưa được validate
4. **Provider Mismatch**: Provider của model và dataset không khớp
5. **API Key Missing**: Không tìm thấy API key cho provider
6. **Upload Failed**: Lỗi khi upload file lên provider
7. **Job Creation Failed**: Lỗi khi tạo fine-tuning job

## Ví dụ sử dụng

### cURL
```bash
curl -X POST "https://api.example.com/user/data-fine-tune/execute" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "datasetId": "550e8400-e29b-41d4-a716-************",
    "modelId": "550e8400-e29b-41d4-a716-************",
    "epochs": 5,
    "batchSize": 4,
    "learningRateMultiplier": 0.1,
    "suffix": "customer-support"
  }'
```

### JavaScript
```javascript
const response = await fetch('/user/data-fine-tune/execute', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    datasetId: '550e8400-e29b-41d4-a716-************',
    modelId: '550e8400-e29b-41d4-a716-************',
    epochs: 3,
    batchSize: 'auto',
    learningRateMultiplier: 'auto'
  })
});

const result = await response.json();
console.log('Fine-tuning job created:', result.data.jobId);
```

## Lưu ý quan trọng

1. **Prerequisites**: Dataset phải được validate trước khi execute
2. **Cost**: Chi phí sẽ được trừ ngay lập tức và không hoàn lại nếu job thành công
3. **Monitoring**: Sử dụng API khác để theo dõi progress của job
4. **Completion**: Model sẽ được activate tự động khi fine-tuning hoàn thành
5. **Failure Handling**: Nếu job thất bại, points sẽ được hoàn trả tự động
