import { Entity, PrimaryColumn, Column, Check, Index } from 'typeorm';

/**
 * Enum định nghĩa các category của node theo SQL schema chuẩn
 */
export enum NodeCategory {
  SYSTEM = 'system',
  GOOGLE_SHEETS = 'google_sheets',
  GOOGLE_DOCS = 'google_docs',
  GOOGLE_GMAIL = 'google_gmail',
  GOOGLE_ADS = 'google_ads',
  GOOGLE_DRIVE = 'google_drive',
  GOOGLE_CALENDAR = 'google_calendar',
  FACEBOOK_PAGE = 'facebook_page',
  FACEBOOK_ADS = 'facebook_ads',
  FACEBOOK_MESSENGER = 'facebook_messenger',
  INSTAGRAM = 'instagram',
  ZALO_OA = 'zalo_oa',
  ZALO_ZNS = 'zalo_zns',
  ZALO = 'zalo',
}

/**
 * Entity đại diện cho bảng node_definitions trong cơ sở dữ liệu
 * Registry cho tất cả các loại node có sẵn trong hệ thống (192 types)
 */
@Entity('node_definitions')
@Index('idx_node_definitions_category', ['category'])
@Index('idx_node_definitions_version', ['version'])
@Check('chk_node_definitions_category', `category IN ('system', 'google_sheets', 'google_docs', 'google_gmail', 'google_ads', 'google_drive', 'google_calendar', 'facebook_page', 'facebook_ads', 'facebook_messenger', 'instagram', 'zalo_oa', 'zalo_zns', 'zalo')`)
export class NodeDefinition {
  /**
   * Loại node, đóng vai trò là khóa chính (e.g., "google.sheet.getRows")
   */
  @PrimaryColumn({ type: 'varchar', length: 100 })
  type: string;

  /**
   * Tên hiển thị của node
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * Mô tả chức năng của node
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Danh mục của node (system, google_sheet, facebook_page, etc.)
   */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
    default: NodeCategory.SYSTEM,
  })
  category: NodeCategory;

  /**
   * JSON Schema định nghĩa cấu trúc form cấu hình cho node
   */
  @Column({ name: 'input_schema', type: 'jsonb', nullable: false })
  inputSchema: Record<string, any>;

  /**
   * JSON Schema định nghĩa cấu trúc các đầu ra của node
   */
  @Column({ name: 'output_schema', type: 'jsonb', nullable: false })
  outputSchema: Record<string, any>;

  /**
   * Phiên bản của node definition
   */
  @Column({ type: 'varchar', length: 50, nullable: false })
  version: string;
}
