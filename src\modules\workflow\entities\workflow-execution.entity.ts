import { Entity, PrimaryGeneratedColumn, Column, Index, Check, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ExecutionStatus } from '../constants';

/**
 * Entity đại diện cho bảng workflow_executions trong cơ sở dữ liệu
 * Ghi lại lịch sử mỗi lần một workflow được chạy
 */
@Entity('workflow_executions')
@Index('idx_workflow_executions_workflow_id', ['workflowId'])
@Index('idx_workflow_executions_status', ['status'])
@Index('idx_workflow_executions_started_at', ['startedAt'])
@Check(`status IN ('queued', 'running', 'completed', 'failed', 'paused', 'cancelled')`)
export class WorkflowExecution {
  /**
   * ID định danh duy nhất cho một lần thực thi
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Kh<PERSON>a ngoại, liên kết đến workflow đã được thực thi
   */
  @Column({ name: 'workflow_id', type: 'uuid', nullable: false })
  workflowId: string;

  /**
   * Trạng thái hiện tại của lần thực thi (e.g., "queued", "running", "completed", "failed", "paused")
   */
  @Column({
    name: 'status',
    type: 'varchar',
    length: 50,
    nullable: false
  })
  status: ExecutionStatus;

  /**
   * Sự kiện kích hoạt workflow (e.g., payload từ webhook, trigger manual)
   */
  @Column({ name: 'trigger_event', type: 'jsonb', nullable: true })
  triggerEvent: Record<string, any> | null;

  /**
   * Thời điểm bắt đầu thực thi, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({
    name: 'started_at',
    type: 'bigint',
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  startedAt: number;

  /**
   * Thời điểm kết thúc thực thi, lưu dưới dạng Unix timestamp (milliseconds)
   */
  @Column({ name: 'finished_at', type: 'bigint', nullable: true })
  finishedAt: number | null;

  /**
   * Thời gian thực thi (milliseconds), được tính tự động
   */
  @Column({ name: 'duration', type: 'bigint', nullable: true })
  duration: number | null;

  /**
   * Kết quả thực thi workflow (output data, error messages, etc.)
   */
  @Column({ name: 'result', type: 'jsonb', nullable: true })
  result: Record<string, any> | null;

  /**
   * Thông tin lỗi nếu thực thi thất bại
   */
  @Column({ name: 'error', type: 'jsonb', nullable: true })
  error: {
    message: string;
    code?: string;
    stack?: string;
    nodeId?: string;
    timestamp?: number;
  } | null;

  /**
   * Metadata bổ sung cho execution
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: {
    priority?: number;
    source?: string;
    userId?: number;
    jobId?: string;
    retryCount?: number;
    maxRetries?: number;
    tags?: string[];
    isRetry?: boolean;
    originalExecutionId?: string;
    retryReason?: string;
    cancelReason?: string;
    cancelledAt?: number;
    pausedAt?: number;
    resumedAt?: number;
    actualStartedAt?: number;
    [key: string]: any;
  } | null;

  /**
   * Số lần retry đã thực hiện
   */
  @Column({ name: 'retry_count', type: 'int', default: 0 })
  retryCount: number;

  /**
   * Số lần retry tối đa
   */
  @Column({ name: 'max_retries', type: 'int', default: 3 })
  maxRetries: number;

  /**
   * Thời điểm retry tiếp theo (nếu có)
   */
  @Column({ name: 'next_retry_at', type: 'bigint', nullable: true })
  nextRetryAt: number | null;

  /**
   * ID của execution cha (nếu đây là retry)
   */
  @Column({ name: 'parent_execution_id', type: 'uuid', nullable: true })
  parentExecutionId: string | null;

  /**
   * Thời điểm tạo record
   */
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  /**
   * Thời điểm cập nhật record lần cuối
   */
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  /**
   * Tính toán duration từ startedAt và finishedAt
   */
  calculateDuration(): number | null {
    if (this.startedAt && this.finishedAt) {
      return this.finishedAt - this.startedAt;
    }
    return null;
  }

  /**
   * Kiểm tra xem execution có đang chạy không
   */
  isRunning(): boolean {
    return this.status === ExecutionStatus.RUNNING;
  }

  /**
   * Kiểm tra xem execution có hoàn thành không
   */
  isCompleted(): boolean {
    return [ExecutionStatus.COMPLETED, ExecutionStatus.FAILED, ExecutionStatus.CANCELLED].includes(this.status);
  }

  /**
   * Kiểm tra xem execution có thể retry không
   */
  canRetry(): boolean {
    return this.status === ExecutionStatus.FAILED && this.retryCount < this.maxRetries;
  }

  /**
   * Cập nhật duration khi finish
   */
  finish(status: ExecutionStatus.COMPLETED | ExecutionStatus.FAILED | ExecutionStatus.CANCELLED, result?: any, error?: any): void {
    this.status = status;
    this.finishedAt = Date.now();
    this.duration = this.calculateDuration();

    if (result) {
      this.result = result;
    }

    if (error) {
      this.error = {
        message: error.message || 'Unknown error',
        code: error.code,
        stack: error.stack,
        nodeId: error.nodeId,
        timestamp: Date.now(),
      };
    }
  }
}
