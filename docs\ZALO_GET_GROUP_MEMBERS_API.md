# Zalo Get Group Members API Documentation

## Tổng quan

API này lấy danh sách thành viên nhóm chat Zalo trực tiếp từ Zalo API với phân trang, trả về format `ApiResponseDto<PaginatedResult>` để tương thích với hệ thống.

## Đi<PERSON><PERSON> kiện sử dụng

- **<PERSON>uy<PERSON>n cần thiết**: OA phải được cấp quyền quản lý thông tin nhóm
- **Thành viên**: OA phải là thành viên của nhóm
- **Phân trang**: Hỗ trợ offset và count theo Zalo API

## Endpoint

### L<PERSON>y danh sách thành viên nhóm

```
GET /v1/zalo-group-management/{integrationId}/{groupId}/members
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| integrationId | string | Yes | ID của Integration Zalo OA (UUID format) |
| groupId | string | Yes | ID của nhóm chat Zalo |

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| page | integer | No | 1 | Số trang hiện tại (≥ 1) |
| limit | integer | No | 10 | Số lượng bản ghi trên mỗi trang (1-50) |
| search | string | No | - | Từ khóa tìm kiếm |
| sortBy | string | No | createdAt | Trường cần sắp xếp |
| sortDirection | enum | No | DESC | Hướng sắp xếp (ASC/DESC) |
| memberUserId | string | No | - | User ID của thành viên (để tìm kiếm) |
| role | enum | No | - | Vai trò thành viên (member/admin) |
| status | enum | No | - | Trạng thái thành viên (active/pending/etc) |

#### Headers

```
Authorization: Bearer {JWT_TOKEN}
```

#### Response

**Success Response (200 OK):**

```json
{
  "success": true,
  "message": "Lấy danh sách thành viên thành công",
  "data": {
    "items": [
      {
        "id": "8756287263669629130",
        "userId": "8756287263669629130",
        "oaId": null,
        "name": "Hoàng Trường Phước",
        "avatar": "https://s120-ava-talk.zadn.vn/8/0/d/7/2/120/59fc092d056c759a71fd16d3bc508458.jpg",
        "isOA": false,
        "role": "member",
        "status": "active",
        "joinedAt": "2024-01-15T10:30:00Z"
      },
      {
        "id": "607812198688816074",
        "userId": null,
        "oaId": "607812198688816074",
        "name": "Chủ Trại Mèo",
        "avatar": "https://s120-ava-talk.zadn.vn/d/e/f/d/7/120/380ffb601008ba5c58852a80cb635b79.jpg",
        "isOA": true,
        "role": "admin",
        "status": "active",
        "joinedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "meta": {
      "totalItems": 25,
      "itemCount": 2,
      "itemsPerPage": 5,
      "totalPages": 5,
      "currentPage": 1
    }
  }
}
```

**Error Response (400 Bad Request):**

```json
{
  "success": false,
  "message": "Count phải từ 1 đến 50",
  "error": "VALIDATION_ERROR"
}
```

**Error Response (403 Forbidden):**

```json
{
  "success": false,
  "message": "OA không có quyền quản lý thông tin nhóm",
  "error": "PERMISSION_DENIED"
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| items | array | Danh sách thành viên nhóm |
| meta | object | Thông tin phân trang |

**Member Object Fields:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | ID duy nhất (user_id hoặc oa_id) |
| userId | string\|null | User ID (nếu là user thường) |
| oaId | string\|null | OA ID (nếu là Official Account) |
| name | string | Tên thành viên trong nhóm |
| avatar | string | URL hình đại diện |
| isOA | boolean | Có phải là Official Account không |
| role | string | Vai trò (member/admin) |
| status | string | Trạng thái (active/pending/etc) |
| joinedAt | string | Thời gian tham gia (ISO 8601) |

**Meta Object Fields:**

| Field | Type | Description |
|-------|------|-------------|
| totalItems | integer | Tổng số thành viên trong nhóm |
| itemCount | integer | Số lượng thành viên trả về |
| itemsPerPage | integer | Số lượng thành viên trên mỗi trang |
| totalPages | integer | Tổng số trang |
| currentPage | integer | Trang hiện tại |

## Sử dụng

### 1. Lấy trang đầu tiên (mặc định)

```bash
curl -X GET \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-************/f414c8f76fa586fbdfb4/members' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

### 2. Lấy với phân trang tùy chỉnh

```bash
curl -X GET \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-************/f414c8f76fa586fbdfb4/members?page=2&limit=20' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

### 3. Tìm kiếm thành viên cụ thể

```bash
curl -X GET \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-************/f414c8f76fa586fbdfb4/members?memberUserId=8756287263669629130' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

## Error Handling

### Lỗi thường gặp

1. **Tham số không hợp lệ**
```json
{
  "success": false,
  "message": "Offset phải >= 0",
  "error": "VALIDATION_ERROR"
}
```

2. **Không có quyền truy cập**
```json
{
  "success": false,
  "message": "OA không có quyền quản lý thông tin nhóm",
  "error": "PERMISSION_DENIED"
}
```

3. **Nhóm không tồn tại**
```json
{
  "success": false,
  "message": "Nhóm không tồn tại hoặc OA không phải thành viên",
  "error": "GROUP_NOT_FOUND"
}
```

## Code Example (JavaScript)

```javascript
class ZaloGroupMembersClient {
  constructor(apiBaseUrl, authToken) {
    this.apiBaseUrl = apiBaseUrl;
    this.authToken = authToken;
  }

  async getGroupMembers(integrationId, groupId, options = {}) {
    const { page = 1, limit = 10, search, sortBy, sortDirection, memberUserId, role, status } = options;

    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());

    if (search) params.append('search', search);
    if (sortBy) params.append('sortBy', sortBy);
    if (sortDirection) params.append('sortDirection', sortDirection);
    if (memberUserId) params.append('memberUserId', memberUserId);
    if (role) params.append('role', role);
    if (status) params.append('status', status);

    const response = await fetch(
      `${this.apiBaseUrl}/v1/zalo-group-management/${integrationId}/${groupId}/members?${params}`,
      {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      }
    );
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data;
  }

  async getAllMembers(integrationId, groupId) {
    const allMembers = [];
    let page = 1;
    const limit = 50; // Lấy tối đa mỗi lần

    while (true) {
      const result = await this.getGroupMembers(integrationId, groupId, { page, limit });

      allMembers.push(...result.items);

      // Kiểm tra xem còn trang nào không
      if (result.meta.currentPage >= result.meta.totalPages) {
        break;
      }

      page++;
    }

    return {
      items: allMembers,
      total: allMembers.length,
    };
  }

  async getAdminMembers(integrationId, groupId) {
    // Lấy tất cả thành viên và lọc admin
    const allMembers = await this.getAllMembers(integrationId, groupId);
    
    return {
      items: allMembers.items.filter(member => member.role === 'admin'),
      total: allMembers.items.filter(member => member.role === 'admin').length,
    };
  }

  async getMembersPaginated(integrationId, groupId, page = 1, limit = 10) {
    const result = await this.getGroupMembers(integrationId, groupId, { page, limit });

    return {
      items: result.items,
      pagination: {
        page: page,
        limit: limit,
        total: result.meta.totalItems,
        totalPages: result.meta.totalPages,
        hasNext: page < result.meta.totalPages,
        hasPrev: page > 1,
      },
    };
  }
}

// Sử dụng
const client = new ZaloGroupMembersClient(
  'https://api.example.com',
  'your-jwt-token'
);

// Lấy thành viên với phân trang
client.getGroupMembers('integration-id', 'group-id', {
  page: 1,
  limit: 10
}).then(result => {
  console.log(`Found ${result.items.length} members out of ${result.meta.totalItems} total`);
  result.items.forEach(member => {
    console.log(`- ${member.name} (${member.isOA ? 'OA' : 'User'})`);
  });
});

// Lấy tất cả thành viên
client.getAllMembers('integration-id', 'group-id')
  .then(result => {
    console.log(`Total members: ${result.total}`);
  });

// Lấy chỉ admin
client.getAdminMembers('integration-id', 'group-id')
  .then(result => {
    console.log(`Admin members: ${result.total}`);
  });
```

## Workflow tích hợp

### 1. Workflow cơ bản
```
1. Gọi API với page=1, limit=10
   GET /members?page=1&limit=10

2. Xử lý kết quả
   - items: danh sách thành viên
   - meta: thông tin phân trang

3. Lặp lại với trang tiếp theo nếu cần
   GET /members?page=2&limit=10
```

### 2. Pagination workflow
```javascript
async function loadMembersWithPagination(integrationId, groupId) {
  let currentPage = 1;
  const limit = 20;
  
  while (true) {
    const result = await client.getMembersPaginated(
      integrationId, 
      groupId, 
      currentPage, 
      limit
    );
    
    // Xử lý dữ liệu trang hiện tại
    processMembers(result.items);
    
    // Kiểm tra có trang tiếp theo không
    if (!result.pagination.hasNext) {
      break;
    }
    
    currentPage++;
  }
}
```

## So sánh với Database API

| Aspect | Zalo API | Database API |
|--------|----------|--------------|
| **Data Source** | Trực tiếp từ Zalo | Local database |
| **Real-time** | ✅ Luôn cập nhật | ❌ Cần sync |
| **Performance** | ⚠️ Phụ thuộc Zalo | ✅ Nhanh |
| **Offline** | ❌ Cần internet | ✅ Hoạt động offline |
| **Pagination** | page/limit (chuyển đổi sang offset/count) | page/limit |
| **Filtering** | ⚠️ Hạn chế | ✅ Linh hoạt |
| **Rate Limit** | ⚠️ Có giới hạn | ✅ Không giới hạn |

## Best Practices

1. **Caching**: Cache kết quả trong thời gian ngắn để giảm API calls
2. **Batch Loading**: Sử dụng count=50 để giảm số lần gọi API
3. **Error Handling**: Xử lý timeout và rate limiting
4. **Pagination**: Sử dụng page/limit chuẩn (tự động chuyển đổi sang offset/count cho Zalo API)
5. **Member Type**: Kiểm tra `isOA` để phân biệt OA và user thường
6. **Real-time**: Kết hợp với webhook để cập nhật real-time
7. **Fallback**: Có plan B khi Zalo API không khả dụng
