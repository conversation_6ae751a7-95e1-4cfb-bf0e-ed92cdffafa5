import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Transactional } from 'typeorm-transactional';
import { UserRegisteredEvent } from '../events/user-registered.event';
import { UserToolService } from '../user/services/user-tool.service';

/**
 * Listener xử lý event khi có người dùng mới đăng ký
 * Tự động clone các tool công khai cho người dùng mới
 */
@Injectable()
export class UserRegisteredListener {
  private readonly logger = new Logger(UserRegisteredListener.name);

  constructor(
    private readonly userToolService: UserToolService,
  ) {}

  /**
   * Xử lý event khi có người dùng mới đăng ký
   * Tự động clone tất cả tool công khai cho người dùng
   * @param event Event chứa thông tin người dùng mới
   */
  @OnEvent('user.registered')
  @Transactional()
  async handleUserRegistered(event: UserRegisteredEvent): Promise<void> {
    try {
      this.logger.log(`Bắt đầu clone tools công khai cho user ${event.userId}`);

      // Gọi service để clone tất cả tool công khai
      const clonedCount = await this.userToolService.cloneAllPublicToolsForNewUser(
        event.userId,
        event.namePrefix
      );

      this.logger.log(`Đã clone thành công ${clonedCount} tools cho user ${event.userId}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi clone tools cho user ${event.userId}: ${error.message}`,
        error.stack
      );
      // Không throw error để không ảnh hưởng đến quá trình đăng ký
      // Chỉ log lỗi để theo dõi
    }
  }
}
