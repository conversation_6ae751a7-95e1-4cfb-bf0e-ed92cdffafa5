import { Controller, Get, Query, Res, Session, Post, Body, Param, UseGuards } from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { GooglePickerService, GooglePickerConfig, GoogleTokens, GoogleUserInfo } from '../services/google-picker.service';
import { AppException } from '@common/exceptions';
import { GOOGLE_ERROR_CODES } from '@shared/services/google/exceptions/google.exception';
import { JwtUserGuard } from '@/modules/auth/guards';

import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto } from '@/common/response';

/**
 * DTO cho callback request
 */
export class GoogleCallbackDto {
  code: string;
  state: string;
  endpointCallback: string;
  error?: string;
}

/**
 * DTO cho file download request
 */
export class DownloadFileDto {
  fileId: string;
}

/**
 * Controller x<PERSON> lý Google Picker APIs
 * Hỗ trợ OAuth authentication và file operations
 */
@ApiTags(SWAGGER_API_TAGS.GOOGLE_PICKER)
@Controller('chat/google-picker')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class GooglePickerController {
  constructor(
    private readonly googlePickerService: GooglePickerService,
  ) { }

  /**
   * Lấy URL để user click vào và xác thực với Google
   */
  @Get('auth-url')
  @ApiOperation({
    summary: 'Lấy Google OAuth authorization URL',
    description: 'Tạo URL để user click vào và xác thực với Google để sử dụng Picker'
  })
  @ApiParam({
    name: 'endpointCallback',
    description: 'Callback endpoint path',
    example: '/api/chat/google-picker/callback',
    required: true
  })
  @ApiResponse({
    status: 200,
    description: 'Authorization URL được tạo thành công',
    schema: {
      type: 'object',
      properties: {
        url: { type: 'string', description: 'Google OAuth authorization URL' }
      }
    }
  })
  @ApiResponse({ status: 500, description: 'Lỗi server khi tạo authorization URL' })
  getAuthUrl(
    @Query() endpointCallback: string,
    @Session() session: Record<string, any>,
  ): ApiResponseDto<{ url: string }> {
    try {
      // Tạo URL để user xác thực với Google
      const url = this.googlePickerService.getAuthorizationUrl(endpointCallback);
      session.google_auth_state = url.state;

      return ApiResponseDto.success({ url: url.url });
    } catch (error) {

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_API_CONFIGURATION_ERROR);
    }
  }

  /**
   * Xử lý callback từ Google OAuth và trả về access token + config
   */
  @Get('callback')
  @ApiOperation({
    summary: 'Xử lý Google OAuth callback',
    description: 'Xử lý callback từ Google sau khi user xác thực, đổi code lấy tokens và trả về access token + config'
  })
  @ApiQuery({ name: 'code', description: 'Authorization code từ Google', required: true })
  @ApiQuery({ name: 'state', description: 'State parameter for CSRF protection', required: true })
  @ApiQuery({ name: 'error', description: 'Error parameter nếu có lỗi', required: false })
  @ApiResponse({
    status: 200,
    description: 'Callback xử lý thành công, trả về tokens và config',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', description: 'Trạng thái thành công' },
        config: {
          type: 'object',
          properties: {
            clientId: { type: 'string', description: 'Google Client ID' },
            accessToken: { type: 'string', description: 'Access token để sử dụng' },
            appId: { type: 'string', description: 'Google App ID' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Thiếu authorization code hoặc có lỗi từ Google' })
  @ApiResponse({ status: 500, description: 'Lỗi server khi xử lý callback' })
  async handleCallback(
    @Query() query: GoogleCallbackDto,
    @Session() session: Record<string, any>,
  ): Promise<ApiResponseDto<GooglePickerConfig | null>> {
    try {
      // Kiểm tra có error từ Google không
      if (query.error) {
        return ApiResponseDto.error(`OAuth error from Google: ${query.error}`, 400);
      }

      // Kiểm tra có authorization code không
      if (!query.code) {
        return ApiResponseDto.error('Authorization code is required', 400);
      }

      // Kiểm tra state parameter để đảm bảo bảo mật (CSRF protection)
      if (!query.state) {
        return ApiResponseDto.error('State parameter is required for CSRF protection', 400);
      }

      // Validate state với session để chống CSRF attack
      const sessionState = session.google_auth_state;
      if (!sessionState || sessionState !== query.state) {
        return ApiResponseDto.error('Invalid state parameter - CSRF protection triggered', 400);
      }

      // Xóa state khỏi session sau khi validate thành công
      delete session.google_auth_state;

      // Đổi code lấy tokens
      const tokens = await this.googlePickerService.handleCallback(query.code, query.endpointCallback);

      // Lưu tokens vào session
      session.google_access_token = tokens.access_token;
      session.google_refresh_token = tokens.refresh_token;
      session.google_token_expiry = tokens.expiry_date;

      // Lấy picker config với access token
      const config = this.googlePickerService.getPickerConfig(tokens.access_token);

      // Trả về config
      return ApiResponseDto.success(config, 'Google Picker authentication successful');
    } catch (error) {
      const errorMessage = error instanceof AppException ? error.message : 'Authentication failed';
      return ApiResponseDto.error(errorMessage, 500);
    }
  }

  /**
   * Lấy config cho Google Picker và access token từ session
   */
  @Get('config')
  @ApiOperation({
    summary: 'Lấy Google Picker configuration',
    description: 'Lấy config cần thiết cho Google Picker bao gồm client ID, API key và access token'
  })
  @ApiResponse({
    status: 200,
    description: 'Picker configuration được lấy thành công',
    schema: {
      type: 'object',
      properties: {
        clientId: { type: 'string', description: 'Google Client ID' },
        apiKey: { type: 'string', description: 'Google API Key' },
        accessToken: { type: 'string', description: 'Access token từ session (nếu có)' },
        appId: { type: 'string', description: 'Google App ID (optional)' }
      }
    }
  })
  @ApiResponse({ status: 500, description: 'Lỗi server khi lấy configuration' })
  getPickerConfig(@Session() session: Record<string, any>): ApiResponseDto<GooglePickerConfig | null> {
    try {
      const accessToken = session.google_access_token;
      const config = this.googlePickerService.getPickerConfig(accessToken);
      return ApiResponseDto.success(config, 'Picker configuration retrieved successfully');
    } catch (error) {
      return ApiResponseDto.error('Failed to get picker configuration', 500);
    }
  }



  /**
   * Validate access token hiện tại
   */
  @Get('validate-token')
  @ApiOperation({
    summary: 'Validate Google access token',
    description: 'Kiểm tra access token hiện tại có còn hợp lệ không'
  })
  @ApiResponse({
    status: 200,
    description: 'Token validation result',
    schema: {
      type: 'object',
      properties: {
        valid: { type: 'boolean', description: 'Token có hợp lệ không' },
        expiry_date: { type: 'number', description: 'Thời gian hết hạn (timestamp)' },
        scope: { type: 'string', description: 'Scope của token' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Không có access token' })
  async validateToken(@Session() session: Record<string, any>): Promise<ApiResponseDto<{
    valid: boolean;
    expiry_date?: number;
    scope?: string;
  }>> {
    try {
      const accessToken = session.google_access_token;

      if (!accessToken) {
        return ApiResponseDto.success({ valid: false }, 'No access token found');
      }

      const result = await this.googlePickerService.validateAccessToken(accessToken);
      return ApiResponseDto.success(result, 'Token validation completed');
    } catch (error) {
      return ApiResponseDto.success({ valid: false }, 'Token validation failed');
    }
  }

  /**
   * Refresh access token
   */
  @Post('refresh-token')
  @ApiOperation({
    summary: 'Refresh Google access token',
    description: 'Làm mới access token sử dụng refresh token'
  })
  @ApiResponse({
    status: 200,
    description: 'Token được refresh thành công',
    schema: {
      type: 'object',
      properties: {
        access_token: { type: 'string', description: 'Access token mới' },
        expiry_date: { type: 'number', description: 'Thời gian hết hạn mới' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Không có refresh token' })
  @ApiResponse({ status: 500, description: 'Lỗi khi refresh token' })
  async refreshToken(@Session() session: Record<string, any>): Promise<ApiResponseDto<{
    access_token: string;
    expiry_date?: number;
  } | null>> {
    try {
      const refreshToken = session.google_refresh_token;

      if (!refreshToken) {
        return ApiResponseDto.error('No refresh token found', 401);
      }

      const tokens = await this.googlePickerService.refreshAccessToken(refreshToken);

      // Cập nhật session với token mới
      session.google_access_token = tokens.access_token;
      session.google_token_expiry = tokens.expiry_date;

      return ApiResponseDto.success(tokens, 'Token refreshed successfully');
    } catch (error) {
      const errorMessage = error instanceof AppException ? error.message : 'Failed to refresh token';
      return ApiResponseDto.error(errorMessage, 500);
    }
  }

  /**
   * Lấy thông tin file từ Google Drive
   */
  @Get('file/:fileId')
  @ApiOperation({
    summary: 'Lấy thông tin file từ Google Drive',
    description: 'Lấy metadata của file từ Google Drive sử dụng file ID'
  })
  @ApiParam({ name: 'fileId', description: 'Google Drive file ID' })
  @ApiResponse({
    status: 200,
    description: 'File info được lấy thành công',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', description: 'File ID' },
        name: { type: 'string', description: 'Tên file' },
        mimeType: { type: 'string', description: 'MIME type của file' },
        size: { type: 'string', description: 'Kích thước file' },
        createdTime: { type: 'string', description: 'Thời gian tạo' },
        modifiedTime: { type: 'string', description: 'Thời gian sửa đổi cuối' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Không có access token' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy file' })
  async getFileInfo(
    @Param('fileId') fileId: string,
    @Session() session: Record<string, any>
  ): Promise<ApiResponseDto<any>> {
    try {
      const accessToken = session.google_access_token;

      if (!accessToken) {
        return ApiResponseDto.error('No access token found', 401);
      }

      const fileInfo = await this.googlePickerService.getFileInfo(fileId, accessToken);
      return ApiResponseDto.success(fileInfo, 'File info retrieved successfully');
    } catch (error) {
      if (error instanceof AppException) {
        return ApiResponseDto.error(error.message, 404);
      }
      return ApiResponseDto.error('File not found', 404);
    }
  }

  /**
   * Download file từ Google Drive
   */
  @Post('download')
  @ApiOperation({
    summary: 'Download file từ Google Drive',
    description: 'Download nội dung file từ Google Drive'
  })
  @ApiBody({
    type: DownloadFileDto,
    description: 'File ID để download'
  })
  @ApiResponse({
    status: 200,
    description: 'File được download thành công',
    schema: {
      type: 'string',
      format: 'binary',
      description: 'File content as binary data'
    }
  })
  @ApiResponse({ status: 401, description: 'Không có access token' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy file' })
  async downloadFile(
    @Body() downloadDto: DownloadFileDto,
    @Session() session: Record<string, any>,
    @Res() res: Response
  ): Promise<void> {
    try {
      const accessToken = session.google_access_token;

      if (!accessToken) {
        throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
      }

      // Lấy thông tin file trước
      const fileInfo = await this.googlePickerService.getFileInfo(downloadDto.fileId, accessToken);

      // Download file content
      const fileContent = await this.googlePickerService.downloadFile(downloadDto.fileId, accessToken);

      // Set headers cho download
      res.setHeader('Content-Type', fileInfo.mimeType || 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${fileInfo.name}"`);
      res.setHeader('Content-Length', fileContent.length);

      // Send file content
      res.send(fileContent);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(GOOGLE_ERROR_CODES.GOOGLE_DRIVE_DOWNLOAD_FAILED);
    }
  }

  /**
   * Revoke access token
   */
  @Post('revoke')
  @ApiOperation({
    summary: 'Revoke Google access token',
    description: 'Thu hồi access token và xóa khỏi session'
  })
  @ApiResponse({ status: 200, description: 'Token được revoke thành công' })
  @ApiResponse({ status: 401, description: 'Không có access token' })
  async revokeToken(@Session() session: Record<string, any>): Promise<ApiResponseDto<{ success: boolean }>> {
    try {
      const accessToken = session.google_access_token;

      if (accessToken) {
        await this.googlePickerService.revokeAccessToken(accessToken);
      }

      // Xóa tokens khỏi session
      delete session.google_access_token;
      delete session.google_refresh_token;
      delete session.google_token_expiry;

      return ApiResponseDto.success({ success: true }, 'Token revoked successfully');
    } catch (error) {
      // Vẫn xóa tokens khỏi session dù có lỗi
      delete session.google_access_token;
      delete session.google_refresh_token;
      delete session.google_token_expiry;

      return ApiResponseDto.success({ success: true }, 'Token revoked (with cleanup)');
    }
  }
}
