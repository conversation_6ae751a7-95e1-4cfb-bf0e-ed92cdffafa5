import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { WorkflowDefinitionDto } from './create-workflow.dto';

/**
 * DTO cho workflow list item
 * Following agent-response.dto patterns
 */
export class WorkflowListItemDto {
  /**
   * ID của workflow
   */
  @ApiProperty({
    description: 'ID của workflow',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  /**
   * Tên của workflow
   */
  @ApiProperty({
    description: 'Tên của workflow',
    example: 'My Workflow',
  })
  name: string;

  /**
   * Trạng thái kích hoạt
   */
  @ApiProperty({
    description: 'Trạng thái kích hoạt',
    example: true,
  })
  isActive: boolean;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1640995200000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật cuối (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật cuối (Unix timestamp)',
    example: 1640995200000,
  })
  updatedAt: number;

  /**
   * Số lượng nodes trong workflow
   */
  @ApiPropertyOptional({
    description: 'Số lượng nodes trong workflow',
    example: 5,
  })
  nodeCount?: number;

  /**
   * Số lượng edges trong workflow
   */
  @ApiPropertyOptional({
    description: 'Số lượng edges trong workflow',
    example: 4,
  })
  edgeCount?: number;
}

/**
 * DTO cho workflow detail
 */
export class WorkflowDetailDto {
  /**
   * ID của workflow
   */
  @ApiProperty({
    description: 'ID của workflow',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  /**
   * ID của user sở hữu workflow
   */
  @ApiProperty({
    description: 'ID của user sở hữu workflow',
    example: 1,
  })
  userId: number;

  /**
   * ID của employee (nếu có)
   */
  @ApiPropertyOptional({
    description: 'ID của employee (nếu có)',
    example: 1,
  })
  employeeId?: number;

  /**
   * Tên của workflow
   */
  @ApiProperty({
    description: 'Tên của workflow',
    example: 'My Workflow',
  })
  name: string;

  /**
   * Trạng thái kích hoạt
   */
  @ApiProperty({
    description: 'Trạng thái kích hoạt',
    example: true,
  })
  isActive: boolean;

  /**
   * Định nghĩa workflow
   */
  @ApiProperty({
    description: 'Định nghĩa workflow',
    type: WorkflowDefinitionDto,
  })
  definition: WorkflowDefinitionDto;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1640995200000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật cuối (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật cuối (Unix timestamp)',
    example: 1640995200000,
  })
  updatedAt: number;
}

/**
 * DTO cho workflow statistics
 */
export class WorkflowStatisticsDto {
  /**
   * Tổng số workflows
   */
  @ApiProperty({
    description: 'Tổng số workflows',
    example: 10,
  })
  totalWorkflows: number;

  /**
   * Số workflows đang hoạt động
   */
  @ApiProperty({
    description: 'Số workflows đang hoạt động',
    example: 7,
  })
  activeWorkflows: number;

  /**
   * Số workflows đã tắt
   */
  @ApiProperty({
    description: 'Số workflows đã tắt',
    example: 3,
  })
  inactiveWorkflows: number;
}
