import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO for workflow definition update response
 * Only exposes necessary fields for API responses
 */
export class WorkflowDefinitionUpdateResponseDto {
  /**
   * Workflow ID
   */
  @ApiProperty({
    description: 'Workflow ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @Expose()
  id: string;

  /**
   * Workflow name
   */
  @ApiProperty({
    description: 'Workflow name',
    example: 'Customer Onboarding Flow'
  })
  @Expose()
  name: string;

  /**
   * Whether the workflow is active
   */
  @ApiProperty({
    description: 'Whether the workflow is active',
    example: true
  })
  @Expose()
  isActive: boolean;

  /**
   * Workflow definition structure
   */
  @ApiProperty({
    description: 'Updated workflow definition structure',
    example: {
      nodes: [
        { id: 'start', type: 'start', data: {} },
        { id: 'end', type: 'end', data: {} }
      ],
      edges: [
        { id: 'e1', source: 'start', target: 'end' }
      ],
      metadata: {
        version: '1.0.0',
        lastModified: {
          timestamp: 1705209600000,
          userId: 123,
          changes: 'Updated workflow definition'
        }
      }
    }
  })
  @Expose()
  definition: Record<string, any>;

  /**
   * Last update timestamp (Unix timestamp in milliseconds)
   */
  @ApiProperty({
    description: 'Last update timestamp (Unix timestamp in milliseconds)',
    example: 1705209600000
  })
  @Expose()
  @Type(() => Number)
  updatedAt: number;

  /**
   * Validation result
   */
  @ApiProperty({
    description: 'Validation result for the updated definition',
    example: {
      isValid: true,
      errors: [],
      warnings: []
    }
  })
  @Expose()
  validation: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  };
}

/**
 * DTO for workflow node operation response
 */
export class WorkflowNodeOperationResponseDto {
  /**
   * Workflow ID
   */
  @ApiProperty({
    description: 'Workflow ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @Expose()
  id: string;

  /**
   * Workflow name
   */
  @ApiProperty({
    description: 'Workflow name',
    example: 'Customer Onboarding Flow'
  })
  @Expose()
  name: string;

  /**
   * Updated workflow definition with nodes
   */
  @ApiProperty({
    description: 'Updated workflow definition',
    example: {
      nodes: [
        { id: 'node1', type: 'start', name: 'Start Node' },
        { id: 'node2', type: 'action', name: 'Process Data' }
      ],
      edges: [],
      metadata: {
        lastModified: {
          timestamp: 1705209600000,
          userId: 123,
          changes: 'Added node: node2'
        }
      }
    }
  })
  @Expose()
  definition: Record<string, any>;

  /**
   * Last update timestamp
   */
  @ApiProperty({
    description: 'Last update timestamp (Unix timestamp in milliseconds)',
    example: 1705209600000
  })
  @Expose()
  @Type(() => Number)
  updatedAt: number;

  /**
   * Number of nodes in the workflow
   */
  @ApiPropertyOptional({
    description: 'Total number of nodes in the workflow',
    example: 5
  })
  @Expose()
  nodeCount?: number;

  /**
   * Number of edges in the workflow
   */
  @ApiPropertyOptional({
    description: 'Total number of edges in the workflow',
    example: 4
  })
  @Expose()
  edgeCount?: number;
}

/**
 * DTO for workflow edge operation response
 */
export class WorkflowEdgeOperationResponseDto {
  /**
   * Workflow ID
   */
  @ApiProperty({
    description: 'Workflow ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @Expose()
  id: string;

  /**
   * Workflow name
   */
  @ApiProperty({
    description: 'Workflow name',
    example: 'Customer Onboarding Flow'
  })
  @Expose()
  name: string;

  /**
   * Updated workflow definition with edges
   */
  @ApiProperty({
    description: 'Updated workflow definition',
    example: {
      nodes: [
        { id: 'node1', type: 'start' },
        { id: 'node2', type: 'end' }
      ],
      edges: [
        { id: 'edge1', sourceNodeId: 'node1', targetNodeId: 'node2' }
      ],
      metadata: {
        lastModified: {
          timestamp: 1705209600000,
          userId: 123,
          changes: 'Added edge: edge1'
        }
      }
    }
  })
  @Expose()
  definition: Record<string, any>;

  /**
   * Last update timestamp
   */
  @ApiProperty({
    description: 'Last update timestamp (Unix timestamp in milliseconds)',
    example: 1705209600000
  })
  @Expose()
  @Type(() => Number)
  updatedAt: number;

  /**
   * Number of edges in the workflow
   */
  @ApiPropertyOptional({
    description: 'Total number of edges in the workflow',
    example: 4
  })
  @Expose()
  edgeCount?: number;
}

/**
 * DTO for validation-only response
 */
export class WorkflowValidationResponseDto {
  /**
   * Whether the workflow definition is valid
   */
  @ApiProperty({
    description: 'Whether the workflow definition is valid',
    example: true
  })
  @Expose()
  isValid: boolean;

  /**
   * Validation errors (if any)
   */
  @ApiProperty({
    description: 'List of validation errors',
    example: [],
    type: [String]
  })
  @Expose()
  errors: string[];

  /**
   * Validation warnings (if any)
   */
  @ApiProperty({
    description: 'List of validation warnings',
    example: ['Node "node1" has no outgoing connections'],
    type: [String]
  })
  @Expose()
  warnings: string[];

  /**
   * Additional validation metadata
   */
  @ApiPropertyOptional({
    description: 'Additional validation metadata',
    example: {
      nodeCount: 5,
      edgeCount: 4,
      cycleDetected: false
    }
  })
  @Expose()
  metadata?: Record<string, any>;
}
