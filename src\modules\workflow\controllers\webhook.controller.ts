import {
  Controller,
  Post,
  Body,
  Param,
  Headers,
  HttpStatus,
  Logger,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiHeader,
  ApiBody,
} from '@nestjs/swagger';
import { ApiResponseDto } from '@/common/response';
import { WorkflowTriggerService } from '../services/workflow-trigger.service';
import {
  WebhookTriggerResponseDto,
  FacebookWebhookDto,
  ZaloWebhookDto,
  GoogleWebhookDto,
  GenericWebhookDto,
  WebhookValidationErrorDto,
} from '../dto/webhook/webhook-trigger.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';

/**
 * Controller xử lý webhook endpoints cho workflow triggering
 * Handles webhook từ Facebook, Zalo, Google và generic webhooks
 */
@ApiTags(SWAGGER_API_TAGS.WORKFLOW)
@Controller('webhooks')
export class WebhookController {
  private readonly logger = new Logger(WebhookController.name);

  constructor(private readonly workflowTriggerService: WorkflowTriggerService) {}

  /**
   * Handle Facebook webhook
   */
  @Post('facebook/:workflowId')
  @ApiOperation({
    summary: 'Handle Facebook webhook',
    description: 'Endpoint để nhận webhook từ Facebook và trigger workflow tương ứng',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần trigger',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiHeader({
    name: 'X-Hub-Signature-256',
    description: 'Facebook webhook signature',
    required: false,
  })
  @ApiBody({
    type: FacebookWebhookDto,
    description: 'Facebook webhook payload',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Webhook processed successfully',
    schema: ApiResponseDto.getSchema(WebhookTriggerResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid webhook data',
    schema: ApiResponseDto.getSchema(WebhookValidationErrorDto),
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid webhook signature',
    schema: ApiResponseDto.getSchema(WebhookValidationErrorDto),
  })
  async handleFacebookWebhook(
    @Param('workflowId') workflowId: string,
    @Body() payload: FacebookWebhookDto,
    @Headers('X-Hub-Signature-256') signature?: string,
  ): Promise<ApiResponseDto<WebhookTriggerResponseDto>> {
    this.logger.log(`Received Facebook webhook for workflow: ${workflowId}`);
    this.logger.debug('Facebook webhook payload:', JSON.stringify(payload, null, 2));

    try {
      // TODO: Implement signature validation
      // this.validateFacebookSignature(payload, signature);

      const result = await this.workflowTriggerService.triggerWorkflow(
        workflowId,
        'webhook.facebook',
        payload,
      );

      const response: WebhookTriggerResponseDto = {
        executionId: result.executionId,
        jobId: result.jobId,
        message: 'Facebook webhook processed successfully',
        timestamp: Date.now(),
      };

      return ApiResponseDto.success(response, 'Facebook webhook processed successfully');
    } catch (error) {
      this.logger.error(`Failed to process Facebook webhook: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Handle Zalo webhook
   */
  @Post('zalo/:workflowId')
  @ApiOperation({
    summary: 'Handle Zalo webhook',
    description: 'Endpoint để nhận webhook từ Zalo OA và trigger workflow tương ứng',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần trigger',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiHeader({
    name: 'X-ZEvent-Signature',
    description: 'Zalo webhook signature',
    required: false,
  })
  @ApiHeader({
    name: 'X-ZEvent-Timestamp',
    description: 'Zalo webhook timestamp',
    required: false,
  })
  @ApiBody({
    type: ZaloWebhookDto,
    description: 'Zalo webhook payload',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Webhook processed successfully',
    schema: ApiResponseDto.getSchema(WebhookTriggerResponseDto),
  })
  async handleZaloWebhook(
    @Param('workflowId') workflowId: string,
    @Body() payload: ZaloWebhookDto,
    @Headers('X-ZEvent-Signature') signature?: string,
    @Headers('X-ZEvent-Timestamp') timestamp?: string,
  ): Promise<ApiResponseDto<WebhookTriggerResponseDto>> {
    this.logger.log(`Received Zalo webhook for workflow: ${workflowId}`);
    this.logger.debug('Zalo webhook payload:', JSON.stringify(payload, null, 2));

    try {
      // TODO: Implement signature validation
      // this.validateZaloSignature(payload, signature, timestamp);

      const result = await this.workflowTriggerService.triggerWorkflow(
        workflowId,
        'webhook.zalo',
        payload,
      );

      const response: WebhookTriggerResponseDto = {
        executionId: result.executionId,
        jobId: result.jobId,
        message: 'Zalo webhook processed successfully',
        timestamp: Date.now(),
      };

      return ApiResponseDto.success(response, 'Zalo webhook processed successfully');
    } catch (error) {
      this.logger.error(`Failed to process Zalo webhook: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Handle Google webhook
   */
  @Post('google/:workflowId')
  @ApiOperation({
    summary: 'Handle Google webhook',
    description: 'Endpoint để nhận webhook từ Google services và trigger workflow tương ứng',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần trigger',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: GoogleWebhookDto,
    description: 'Google webhook payload',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Webhook processed successfully',
    schema: ApiResponseDto.getSchema(WebhookTriggerResponseDto),
  })
  async handleGoogleWebhook(
    @Param('workflowId') workflowId: string,
    @Body() payload: GoogleWebhookDto,
  ): Promise<ApiResponseDto<WebhookTriggerResponseDto>> {
    this.logger.log(`Received Google webhook for workflow: ${workflowId}`);
    this.logger.debug('Google webhook payload:', JSON.stringify(payload, null, 2));

    try {
      // Decode Google Pub/Sub message
      const decodedData = this.decodeGooglePubSubMessage(payload);

      const result = await this.workflowTriggerService.triggerWorkflow(
        workflowId,
        'webhook.google',
        decodedData,
      );

      const response: WebhookTriggerResponseDto = {
        executionId: result.executionId,
        jobId: result.jobId,
        message: 'Google webhook processed successfully',
        timestamp: Date.now(),
      };

      return ApiResponseDto.success(response, 'Google webhook processed successfully');
    } catch (error) {
      this.logger.error(`Failed to process Google webhook: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Handle generic webhook
   */
  @Post('generic/:workflowId')
  @ApiOperation({
    summary: 'Handle generic webhook',
    description: 'Endpoint để nhận webhook từ bất kỳ service nào và trigger workflow tương ứng',
  })
  @ApiParam({
    name: 'workflowId',
    description: 'ID của workflow cần trigger',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: GenericWebhookDto,
    description: 'Generic webhook payload',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Webhook processed successfully',
    schema: ApiResponseDto.getSchema(WebhookTriggerResponseDto),
  })
  async handleGenericWebhook(
    @Param('workflowId') workflowId: string,
    @Body() payload: GenericWebhookDto,
  ): Promise<ApiResponseDto<WebhookTriggerResponseDto>> {
    this.logger.log(`Received generic webhook for workflow: ${workflowId}`);
    this.logger.debug('Generic webhook payload:', JSON.stringify(payload, null, 2));

    try {
      const result = await this.workflowTriggerService.triggerWorkflow(
        workflowId,
        'webhook.generic',
        payload,
      );

      const response: WebhookTriggerResponseDto = {
        executionId: result.executionId,
        jobId: result.jobId,
        message: 'Generic webhook processed successfully',
        timestamp: Date.now(),
      };

      return ApiResponseDto.success(response, 'Generic webhook processed successfully');
    } catch (error) {
      this.logger.error(`Failed to process generic webhook: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Decode Google Pub/Sub message
   * @private
   */
  private decodeGooglePubSubMessage(payload: GoogleWebhookDto): any {
    try {
      const decodedData = Buffer.from(payload.message.data, 'base64').toString('utf-8');
      return {
        ...JSON.parse(decodedData),
        messageId: payload.message.messageId,
        publishTime: payload.message.publishTime,
        attributes: payload.message.attributes,
        subscription: payload.subscription,
      };
    } catch (error) {
      this.logger.error(`Failed to decode Google Pub/Sub message: ${error.message}`);
      throw new BadRequestException('Invalid Google Pub/Sub message format');
    }
  }

  /**
   * Validate Facebook webhook signature
   * @private
   */
  private validateFacebookSignature(payload: any, signature?: string): void {
    // TODO: Implement Facebook signature validation
    // if (!signature) {
    //   throw new UnauthorizedException('Missing Facebook webhook signature');
    // }
    // Implementation would use Facebook app secret to validate signature
  }

  /**
   * Validate Zalo webhook signature
   * @private
   */
  private validateZaloSignature(payload: any, signature?: string, timestamp?: string): void {
    // TODO: Implement Zalo signature validation
    // if (!signature || !timestamp) {
    //   throw new UnauthorizedException('Missing Zalo webhook signature or timestamp');
    // }
    // Implementation would use Zalo app secret to validate signature
  }
}
