import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString, ArrayMinSize, ArrayMaxSize } from 'class-validator';

/**
 * DTO cho việc xóa nhiều công cụ tùy chỉnh cùng lúc
 */
export class DeleteMultipleToolsCustomDto {
  /**
   * Danh sách ID các công cụ cần xóa
   */
  @ApiProperty({
    description: 'Danh sách ID các công cụ cần xóa',
    example: ['uuid-1', 'uuid-2', 'uuid-3'],
    type: [String],
  })
  @IsArray({ message: 'Danh sách ID phải là mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 ID để xóa' })
  @ArrayMaxSize(50, { message: 'Không thể xóa quá 50 công cụ cùng lúc' })
  @IsString({ each: true, message: 'Mỗi ID phải là chuỗi' })
  toolIds: string[];
}
