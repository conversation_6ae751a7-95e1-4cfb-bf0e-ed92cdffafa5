# Phân Tích: <PERSON><PERSON><PERSON> và Ủy Quyền cho Ứng Dụng Zalo Official Account

## Tổng Quan

Tài liệu này phân tích quy trình xác thực và ủy quyền cho ứng dụng sử dụng Zalo Official Account API, dựa trên tài liệu chính thức từ Zalo For Developers.

## 1. <PERSON>ến Trúc Xác Thực

### 1.1 OAuth 2.0 Flow
Zalo Official Account API sử dụng OAuth 2.0 Authorization Code Grant để xác thực:

```
Client Application → Authorization Server → Resource Server
     ↓                        ↓                    ↓
   App ID/Secret        Authorization Code    Access Token
```

### 1.2 Các <PERSON>nh Phần Chính
- **App ID**: Định danh ứng dụng
- **App Secret**: Kh<PERSON>a b<PERSON> mật của ứng dụng
- **Authorization Code**: Mã ủy quyền tạm thời
- **Access Token**: Token truy cập API (hiệu lự<PERSON> 25 giờ)
- **Refresh Token**: Token làm mới access token

## 2. Quy Trình X<PERSON>c <PERSON>c Chi Tiết

### Bước 1: Đăng Ký Ứng Dụng
1. Truy cập Zalo For Developers
2. Tạo ứng dụng mới
3. Cấu hình Official Account API
4. Lấy App ID và App Secret

### Bước 2: Yêu Cầu Authorization Code
```http
GET https://oauth.zaloapp.com/v4/oa/permission
Parameters:
- app_id: {APP_ID}
- redirect_uri: {REDIRECT_URI}
- state: {RANDOM_STRING}
```

### Bước 3: Lấy Access Token
```http
POST https://oauth.zaloapp.com/v4/oa/access_token
Headers:
- Content-Type: application/x-www-form-urlencoded
- secret_key: {APP_SECRET}

Body:
- app_id: {APP_ID}
- grant_type: authorization_code
- code: {AUTHORIZATION_CODE}
```

**Response:**
```json
{
  "access_token": "string",
  "refresh_token": "string",
  "expires_in": 90000
}
```

### Bước 4: Sử dụng Access Token
```http
GET/POST https://openapi.zalo.me/v2.0/oa/{endpoint}
Headers:
- access_token: {ACCESS_TOKEN}
```

## 3. Refresh Token Flow

### Làm Mới Access Token
```http
POST https://oauth.zaloapp.com/v4/oa/access_token
Headers:
- Content-Type: application/x-www-form-urlencoded
- secret_key: {APP_SECRET}

Body:
- app_id: {APP_ID}
- grant_type: refresh_token
- refresh_token: {REFRESH_TOKEN}
```

## 4. Các Scope và Quyền Hạn

### 4.1 Scope Cơ Bản
- `oa.message.send`: Gửi tin nhắn
- `oa.info.basic`: Thông tin cơ bản OA
- `oa.follower.getlist`: Danh sách người theo dõi
- `oa.conversation.getlist`: Danh sách cuộc trò chuyện

### 4.2 Scope Nâng Cao
- `oa.upload.image`: Upload hình ảnh
- `oa.upload.file`: Upload file
- `oa.template.send`: Gửi template message
- `oa.broadcast.send`: Gửi tin nhắn broadcast

## 5. Bảo Mật và Best Practices

### 5.1 Bảo Mật Token
- Lưu trữ App Secret an toàn (server-side only)
- Mã hóa Access Token khi lưu trữ
- Sử dụng HTTPS cho tất cả API calls
- Implement token rotation

### 5.2 Error Handling
```javascript
// Xử lý lỗi token hết hạn
if (response.error.code === -216) {
  // Token expired, refresh token
  await refreshAccessToken();
  // Retry original request
}
```

### 5.3 Rate Limiting
- Giới hạn: 1000 requests/phút
- Implement exponential backoff
- Cache responses khi có thể

## 6. Implementation Example

### 6.1 Node.js Implementation
```javascript
class ZaloOAAuth {
  constructor(appId, appSecret, redirectUri) {
    this.appId = appId;
    this.appSecret = appSecret;
    this.redirectUri = redirectUri;
  }

  getAuthorizationUrl(state) {
    const params = new URLSearchParams({
      app_id: this.appId,
      redirect_uri: this.redirectUri,
      state: state
    });
    return `https://oauth.zaloapp.com/v4/oa/permission?${params}`;
  }

  async getAccessToken(authCode) {
    const response = await fetch('https://oauth.zaloapp.com/v4/oa/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'secret_key': this.appSecret
      },
      body: new URLSearchParams({
        app_id: this.appId,
        grant_type: 'authorization_code',
        code: authCode
      })
    });
    return response.json();
  }

  async refreshToken(refreshToken) {
    const response = await fetch('https://oauth.zaloapp.com/v4/oa/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'secret_key': this.appSecret
      },
      body: new URLSearchParams({
        app_id: this.appId,
        grant_type: 'refresh_token',
        refresh_token: refreshToken
      })
    });
    return response.json();
  }
}
```

## 7. Troubleshooting

### 7.1 Lỗi Thường Gặp
- **-201**: App không tồn tại
- **-213**: User chưa cấp quyền
- **-216**: Access token hết hạn
- **-217**: Access token không hợp lệ

### 7.2 Debug Tips
- Kiểm tra App ID/Secret
- Verify redirect URI
- Check token expiration
- Validate request format

## 8. Kết Luận

Zalo Official Account API sử dụng OAuth 2.0 standard với một số customization:
- Token có thời hạn 25 giờ
- Cần App Secret trong header
- Hỗ trợ refresh token
- Rate limiting nghiêm ngặt

### Khuyến Nghị
1. Implement proper token management
2. Use secure storage for credentials
3. Handle errors gracefully
4. Monitor API usage
5. Keep documentation updated

---

**Tài liệu tham khảo:**
- [Zalo For Developers - Official Account API](https://developers.zalo.me/docs/official-account/)
- [OAuth 2.0 RFC 6749](https://tools.ietf.org/html/rfc6749)
