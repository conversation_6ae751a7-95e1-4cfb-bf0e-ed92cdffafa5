import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { Transactional } from 'typeorm-transactional';
import { 
  UpdateProfileDto,
  ProfileResponseDto
} from '../dto/profile';
import { ProfileMapper } from '../mappers';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { Profile } from '@modules/agent/interfaces/agent-config.interface';
import { AgentValidationService } from './agent-validation.service';
import { getRequiredFeatures } from '../constants/agent-feature-mapping';
import { AgentRepository } from '../../repositories';

/**
 * Service xử lý các thao tác liên quan đến profile của agent cho người dùng
 */
@Injectable()
export class ProfileUserService {
  private readonly logger = new Logger(ProfileUserService.name);

  constructor(
    private readonly agentValidationService: AgentValidationService,
    private readonly agentRepository: AgentRepository,
  ) { }

  /**
   * L<PERSON>y thông tin profile của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin profile
   */
  async getProfile(
    agentId: string,
    userId: number,
  ): Promise<ProfileResponseDto> {
    try {
      // Validate agent ownership và Profile feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('PROFILE')
      );

      // Lấy agent data sau khi validate
      const result = await this.agentRepository.findOneByIdAndUserId(agentId, userId);

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Lấy profile từ config.profile với null check
      const profile = result.config?.profile || {};

      // Chuyển đổi Profile sang ProfileAgent để sử dụng với ProfileMapper
      const profileAgent: ProfileAgent = {
        gender: profile.gender,
        dateOfBirth: profile.dateOfBirth ? new Date(profile.dateOfBirth) : undefined,
        position: profile.position,
        education: profile.education,
        skills: profile.skills,
        personality: profile.personality,
        languages: profile.languages,
        nations: profile.nations,
      };

      // Chuyển đổi ProfileAgent sang ProfileDto
      const profileDto = ProfileMapper.toDto(profileAgent);

      // Tạo response với updatedAt
      const response: ProfileResponseDto = {
        ...profileDto,
        updatedAt: Date.now(), // Sử dụng timestamp hiện tại
      };

      this.logger.log(`Lấy profile thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy profile agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật profile của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin profile cần cập nhật
   * @returns Thông tin profile đã cập nhật
   */
  @Transactional()
  async updateProfile(
    agentId: string,
    userId: number,
    updateDto: UpdateProfileDto,
  ): Promise<ProfileResponseDto> {
    try {
      // Validate agent ownership và Profile feature
      await this.agentValidationService.validateAgentAndMultipleFeatures(
        agentId,
        userId,
        getRequiredFeatures('PROFILE')
      );

      // Lấy agent data sau khi validate
      const result = await this.agentRepository.findOneByIdAndUserId(agentId, userId);

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Lấy profile hiện tại từ config.profile
      const currentProfile = result.config?.profile || {};

      // Chuyển đổi Profile sang ProfileAgent để sử dụng với mergeProfileData
      const profileAgent: ProfileAgent = {
        gender: currentProfile.gender,
        dateOfBirth: currentProfile.dateOfBirth ? new Date(currentProfile.dateOfBirth) : undefined,
        position: currentProfile.position,
        education: currentProfile.education,
        skills: currentProfile.skills,
        personality: currentProfile.personality,
        languages: currentProfile.languages,
        nations: currentProfile.nations,
      };

      // Merge profile data
      const updatedProfileAgent = this.mergeProfileData(profileAgent, updateDto);

      // Chuyển đổi ProfileAgent thành Profile để lưu vào database
      const updatedProfile = {
        gender: updatedProfileAgent.gender,
        dateOfBirth: updatedProfileAgent.dateOfBirth instanceof Date
          ? updatedProfileAgent.dateOfBirth.getTime()
          : (typeof updatedProfileAgent.dateOfBirth === 'string'
              ? new Date(updatedProfileAgent.dateOfBirth).getTime()
              : undefined),
        position: updatedProfileAgent.position,
        education: updatedProfileAgent.education,
        skills: updatedProfileAgent.skills,
        personality: updatedProfileAgent.personality,
        languages: updatedProfileAgent.languages,
        nations: updatedProfileAgent.nations,
      };

      // Cập nhật profile trong database
      // Đảm bảo config object tồn tại
      const currentConfig = result.config || {};
      currentConfig.profile = updatedProfile;

      await this.agentRepository.createQueryBuilder()
        .update()
        .set({
          config: currentConfig
        })
        .where('id = :id', { id: agentId })
        .andWhere('user_id = :userId', { userId })
        .execute();

      // Chuyển đổi sang response DTO với profile đã cập nhật
      const profileDto = ProfileMapper.toDto(updatedProfileAgent);
      const response: ProfileResponseDto = {
        ...profileDto,
        updatedAt: Date.now(), // Sử dụng timestamp hiện tại
      };

      this.logger.log(`Cập nhật profile thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật profile agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  // ==================== PRIVATE METHODS ====================

  /**
   * Merge profile data từ DTO với profile hiện tại
   * @param currentProfile Profile hiện tại
   * @param updateDto DTO cập nhật
   * @returns ProfileAgent đã merge
   */
  private mergeProfileData(
    currentProfile: ProfileAgent, 
    updateDto: UpdateProfileDto
  ): ProfileAgent {
    const updatedProfile: ProfileAgent = { ...currentProfile };

    // Cập nhật các trường nếu có trong DTO
    if (updateDto.gender !== undefined) {
      updatedProfile.gender = updateDto.gender as any; // Will be converted by ProfileMapper
    }

    if (updateDto.dateOfBirth !== undefined) {
      updatedProfile.dateOfBirth = new Date(updateDto.dateOfBirth);
    }

    if (updateDto.position !== undefined) {
      updatedProfile.position = updateDto.position;
    }

    if (updateDto.education !== undefined) {
      updatedProfile.education = updateDto.education;
    }

    if (updateDto.skills !== undefined) {
      updatedProfile.skills = updateDto.skills;
    }

    if (updateDto.personality !== undefined) {
      updatedProfile.personality = updateDto.personality;
    }

    if (updateDto.languages !== undefined) {
      updatedProfile.languages = updateDto.languages;
    }

    if (updateDto.nations !== undefined) {
      updatedProfile.nations = updateDto.nations;
    }

    return updatedProfile;
  }
}
