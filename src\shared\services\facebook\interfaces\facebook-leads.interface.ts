/**
 * Interface cho Lead Gen Form
 */
export interface LeadGenForm {
  id: string;
  name: string;
  status: 'ACTIVE' | 'ARCHIVED' | 'DELETED' | 'PAUSED';
  locale: string;
  questions: LeadGenQuestion[];
  privacy_policy_url?: string;
  follow_up_action_url?: string;
  is_continued_flow?: boolean;
  leadgen_export_csv_url?: string;
  leads_count?: number;
  page_id: string;
  created_time: string;
  updated_time?: string;
  context_card?: LeadGenContextCard;
  creator?: {
    id: string;
    name: string;
  };
  creator_id?: string;
  cusomized_tcpa_content?: string;
  expired_leads_count?: number;
  follow_up_action_text?: string;
  is_optimized_for_quality?: boolean;
  leadgen_tos_acceptance_time?: string;
  leadgen_tos_accepted?: boolean;
  legal_content?: LeadGenLegalContent;
  organic_leads_count?: number;
  page?: {
    id: string;
    name: string;
  };
  qualifiers?: LeadGenQualifier[];
  question_page_custom_headline?: string;
  tcpa_compliance?: boolean;
  thank_you_page?: LeadGenThankYouPage;
}

/**
 * Interface cho Lead Gen Question
 */
export interface LeadGenQuestion {
  key: string;
  label: string;
  type: 'FULL_NAME' | 'EMAIL' | 'PHONE' | 'ZIP' | 'DATE_TIME' | 'MULTIPLE_CHOICE' | 'CONDITIONAL' | 'CUSTOM' | 'STORE_LOOKUP' | 'APPOINTMENT_SCHEDULING';
  options?: LeadGenQuestionOption[];
  conditional_questions_group_id?: string;
  conditional_questions_choices?: string[];
  dependent_conditional_questions?: LeadGenConditionalQuestion[];
}

/**
 * Interface cho Lead Gen Question Option
 */
export interface LeadGenQuestionOption {
  key: string;
  value: string;
}

/**
 * Interface cho Lead Gen Conditional Question
 */
export interface LeadGenConditionalQuestion {
  field_key: string;
  input_type: string;
  name: string;
  options?: LeadGenQuestionOption[];
}

/**
 * Interface cho Lead Gen Context Card
 */
export interface LeadGenContextCard {
  button_text?: string;
  content?: LeadGenContextCardContent[];
  cover_photo?: {
    id: string;
    offset_x: number;
    offset_y: number;
    source: string;
  };
  style?: 'LIST_STYLE' | 'PARAGRAPH_STYLE' | 'BULLET_POINT_STYLE';
  title?: string;
}

/**
 * Interface cho Lead Gen Context Card Content
 */
export interface LeadGenContextCardContent {
  type: 'TEXT' | 'IMAGE' | 'VIDEO';
  text?: string;
  image?: {
    id: string;
    source: string;
  };
  video?: {
    id: string;
    source: string;
  };
}

/**
 * Interface cho Lead Gen Legal Content
 */
export interface LeadGenLegalContent {
  privacy_policy?: LeadGenPrivacyPolicy;
  custom_disclaimer?: LeadGenCustomDisclaimer;
}

/**
 * Interface cho Lead Gen Privacy Policy
 */
export interface LeadGenPrivacyPolicy {
  url: string;
  link_text?: string;
}

/**
 * Interface cho Lead Gen Custom Disclaimer
 */
export interface LeadGenCustomDisclaimer {
  title?: string;
  body?: string;
  checkboxes?: LeadGenDisclaimerCheckbox[];
}

/**
 * Interface cho Lead Gen Disclaimer Checkbox
 */
export interface LeadGenDisclaimerCheckbox {
  key: string;
  text: string;
  is_required: boolean;
  is_checked_by_default?: boolean;
}

/**
 * Interface cho Lead Gen Qualifier
 */
export interface LeadGenQualifier {
  question: string;
  answers: string[];
  operator: 'EQUAL' | 'NOT_EQUAL' | 'CONTAINS' | 'NOT_CONTAINS';
}

/**
 * Interface cho Lead Gen Thank You Page
 */
export interface LeadGenThankYouPage {
  title?: string;
  body?: string;
  button_text?: string;
  button_type?: 'VIEW_WEBSITE' | 'CALL_PHONE' | 'DOWNLOAD' | 'VISIT_PAGES_FEED';
  website_url?: string;
  phone_number?: string;
  app_destination?: string;
  enable_messenger?: boolean;
}

/**
 * Interface cho Lead
 */
export interface Lead {
  id: string;
  created_time: string;
  ad_id?: string;
  ad_name?: string;
  adset_id?: string;
  adset_name?: string;
  campaign_id?: string;
  campaign_name?: string;
  form_id: string;
  is_organic?: boolean;
  field_data: LeadFieldData[];
  partner_name?: string;
  platform?: string;
  post?: {
    id: string;
    headline?: string;
  };
  retailer_item_id?: string;
}

/**
 * Interface cho Lead Field Data
 */
export interface LeadFieldData {
  name: string;
  values: string[];
}

/**
 * Interface cho yêu cầu tạo Lead Gen Form
 */
export interface CreateLeadGenFormRequest {
  name: string;
  questions: LeadGenQuestion[];
  privacy_policy_url?: string;
  follow_up_action_url?: string;
  follow_up_action_text?: string;
  is_continued_flow?: boolean;
  locale?: string;
  context_card?: LeadGenContextCard;
  cusomized_tcpa_content?: string;
  is_optimized_for_quality?: boolean;
  legal_content?: LeadGenLegalContent;
  qualifiers?: LeadGenQualifier[];
  question_page_custom_headline?: string;
  tcpa_compliance?: boolean;
  thank_you_page?: LeadGenThankYouPage;
}

/**
 * Interface cho yêu cầu cập nhật Lead Gen Form
 */
export interface UpdateLeadGenFormRequest {
  name?: string;
  status?: 'ACTIVE' | 'ARCHIVED' | 'PAUSED';
  questions?: LeadGenQuestion[];
  privacy_policy_url?: string;
  follow_up_action_url?: string;
  follow_up_action_text?: string;
  is_continued_flow?: boolean;
  context_card?: LeadGenContextCard;
  cusomized_tcpa_content?: string;
  is_optimized_for_quality?: boolean;
  legal_content?: LeadGenLegalContent;
  qualifiers?: LeadGenQualifier[];
  question_page_custom_headline?: string;
  tcpa_compliance?: boolean;
  thank_you_page?: LeadGenThankYouPage;
}

/**
 * Interface cho phản hồi danh sách Lead Gen Forms
 */
export interface GetLeadGenFormsResponse {
  data: LeadGenForm[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
    previous?: string;
  };
}

/**
 * Interface cho phản hồi danh sách Leads
 */
export interface GetLeadsResponse {
  data: Lead[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
    previous?: string;
  };
}

/**
 * Interface cho Lead Gen Form Stats
 */
export interface LeadGenFormStats {
  form_id: string;
  impressions?: number;
  clicks?: number;
  leads?: number;
  cost_per_lead?: number;
  cost_per_click?: number;
  click_through_rate?: number;
  conversion_rate?: number;
  spend?: number;
  date_start?: string;
  date_stop?: string;
}

/**
 * Interface cho Lead Export
 */
export interface LeadExport {
  export_id: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  download_url?: string;
  created_time: string;
  completed_time?: string;
  error_message?: string;
  total_leads?: number;
  exported_leads?: number;
}

/**
 * Interface cho yêu cầu export Leads
 */
export interface ExportLeadsRequest {
  form_id: string;
  date_start?: string;
  date_stop?: string;
  format?: 'CSV' | 'JSON';
  fields?: string[];
  filtering?: LeadExportFiltering[];
}

/**
 * Interface cho Lead Export Filtering
 */
export interface LeadExportFiltering {
  field: string;
  operator: 'EQUAL' | 'NOT_EQUAL' | 'CONTAINS' | 'NOT_CONTAINS' | 'GREATER_THAN' | 'LESS_THAN';
  value: string;
}

/**
 * Interface cho Lead Gen Form Test
 */
export interface LeadGenFormTest {
  form_id: string;
  test_data: LeadFieldData[];
  test_result?: {
    success: boolean;
    lead_id?: string;
    error_message?: string;
  };
}

/**
 * Interface cho Lead Gen Form Preview
 */
export interface LeadGenFormPreview {
  form_id: string;
  preview_url: string;
  mobile_preview_url?: string;
  desktop_preview_url?: string;
}

/**
 * Interface cho Lead Gen Form Analytics
 */
export interface LeadGenFormAnalytics {
  form_id: string;
  date_start: string;
  date_stop: string;
  metrics: LeadGenFormMetric[];
}

/**
 * Interface cho Lead Gen Form Metric
 */
export interface LeadGenFormMetric {
  name: string;
  value: number;
  description?: string;
  period?: string;
}

/**
 * Interface cho Lead Gen Webhook
 */
export interface LeadGenWebhook {
  object: 'page';
  entry: LeadGenWebhookEntry[];
}

/**
 * Interface cho Lead Gen Webhook Entry
 */
export interface LeadGenWebhookEntry {
  id: string; // Page ID
  time: number;
  changes: LeadGenWebhookChange[];
}

/**
 * Interface cho Lead Gen Webhook Change
 */
export interface LeadGenWebhookChange {
  field: 'leadgen';
  value: LeadGenWebhookValue;
}

/**
 * Interface cho Lead Gen Webhook Value
 */
export interface LeadGenWebhookValue {
  ad_id?: string;
  form_id: string;
  leadgen_id: string;
  created_time: number;
  page_id: string;
  adgroup_id?: string;
  campaign_id?: string;
}

/**
 * Interface cho Lead Gen Form Template
 */
export interface LeadGenFormTemplate {
  id: string;
  name: string;
  category: string;
  description?: string;
  questions: LeadGenQuestion[];
  context_card?: LeadGenContextCard;
  thank_you_page?: LeadGenThankYouPage;
  is_default?: boolean;
  created_time: string;
  updated_time?: string;
}

/**
 * Interface cho phản hồi danh sách Lead Gen Form Templates
 */
export interface GetLeadGenFormTemplatesResponse {
  data: LeadGenFormTemplate[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
    previous?: string;
  };
}
