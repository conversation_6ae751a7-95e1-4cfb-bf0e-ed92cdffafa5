/**
 * Interface cho cấu hình Google Docs integration
 */
export interface GoogleDocsConfig {
  /**
   * Access token từ Google OAuth
   */
  accessToken: string;

  /**
   * Refresh token từ Google OAuth
   */
  refreshToken: string;

  /**
   * Thời gian hết hạn của access token (timestamp)
   */
  expiresAt?: number;

  /**
   * Scope được cấp quyền
   */
  scope?: string;

  /**
   * Client ID của Google OAuth app
   */
  clientId?: string;

  /**
   * Client Secret của Google OAuth app
   */
  clientSecret?: string;
}

/**
 * Interface cho metadata của Google Docs integration
 */
export interface GoogleDocsMetadata {
  /**
   * Thông tin về user Google
   */
  userInfo?: {
    email: string;
    name: string;
    picture?: string;
  };

  /**
   * Thống kê sử dụng
   */
  usage?: {
    documentsCreated: number;
    documentsRead: number;
    documentsUpdated: number;
    lastUsedAt: number;
  };

  /**
   * Cài đặt integration
   */
  settings?: {
    defaultFolderId?: string;
    autoBackup?: boolean;
    syncEnabled?: boolean;
  };

  /**
   * Thông tin về token
   */
  tokenInfo?: {
    issuedAt: number;
    lastRefreshedAt?: number;
    refreshCount: number;
  };
}

/**
 * Interface cho payload encryption của Google Docs
 */
export interface GoogleDocsPayload {
  /**
   * Access token (sẽ được mã hóa)
   */
  accessToken: string;

  /**
   * Refresh token (sẽ được mã hóa)
   */
  refreshToken: string;

  /**
   * Client secret (sẽ được mã hóa nếu có)
   */
  clientSecret?: string;
}
