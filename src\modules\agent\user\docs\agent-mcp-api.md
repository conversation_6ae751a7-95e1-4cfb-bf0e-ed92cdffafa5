# Agent MCP API Documentation

## Overview

API endpoints để quản lý MCP (Model Context Protocol) servers cho Agent. <PERSON> phép liên kết, h<PERSON><PERSON> liên kết và quản lý MCP servers của từng agent.

## Base URL

```
/user/agents/{agentId}/mcps
```

## Authentication

Tất cả endpoints yêu cầu JWT authentication:

```
Authorization: Bearer <jwt-token>
```

## API Endpoints

### 1. L<PERSON>y danh sách MCP servers của Agent

**GET** `/user/agents/{agentId}/mcps`

Lấy tất cả MCP servers được liên kết với Agent với phân trang và tìm kiếm.

**Parameters:**
- `agentId` (path): ID của Agent

**Query Parameters:**
- `page` (query, optional): S<PERSON> trang (bắt đầu từ 1), default: 1
- `limit` (query, optional): Số lượng items per page, default: 20
- `search` (query, optional): <PERSON><PERSON><PERSON> ki<PERSON>m theo tên MCP server
- `transport` (query, optional): Lọ<PERSON> theo transport type (http, sse)

**Response:**
```json
{
  "success": true,
  "message": "Lấy danh sách MCP servers của Agent thành công",
  "data": {
    "agentId": "agent-uuid-here",
    "mcps": [
      {
        "agentId": "agent-uuid-here",
        "mcpId": "mcp-uuid-here",
        "mcpInfo": {
          "id": "mcp-uuid-here",
          "nameServer": "redai-affiliate-server",
          "description": "MCP server for affiliate management",
          "config": {
            "url": "http://redai-affiliate-server:8004/mcp",
            "transport": "http",
            "automaticSSEFallback": false
          },
          "userId": 123,
          "createdAt": 1703123456789,
          "updatedAt": 1703123456789
        }
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 20,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false,
    "hasItems": true
  }
}
```

### 2. Hủy liên kết Agent với nhiều MCP servers

**DELETE** `/user/agents/{agentId}/mcps/bulk`

Xóa liên kết hàng loạt giữa Agent và nhiều MCP servers.

**Parameters:**
- `agentId` (path): ID của Agent

**Request Body:**
```json
{
  "mcpIds": ["mcp-uuid-1", "mcp-uuid-2", "mcp-uuid-3"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Hủy liên kết hàng loạt hoàn thành",
  "data": {
    "agentId": "agent-uuid-here",
    "unlinkedMcpIds": ["mcp-uuid-1", "mcp-uuid-2"],
    "failedMcpIds": ["mcp-uuid-3"],
    "successCount": 2,
    "failedCount": 1
  }
}
```

### 3. Liên kết nhiều MCP servers với Agent (Bulk)

**POST** `/user/agents/{agentId}/mcps/bulk`

Tạo liên kết hàng loạt giữa Agent và nhiều MCP servers.

**Parameters:**
- `agentId` (path): ID của Agent

**Request Body:**
```json
{
  "mcpIds": [
    "mcp-uuid-1",
    "mcp-uuid-2",
    "mcp-uuid-3"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Liên kết hàng loạt hoàn thành",
  "data": {
    "agentId": "agent-uuid-here",
    "linkedMcpIds": [
      "mcp-uuid-1",
      "mcp-uuid-2"
    ],
    "failedMcpIds": [
      "mcp-uuid-3"
    ],
    "successCount": 2,
    "failedCount": 1
  }
}
```

### 4. Xóa tất cả liên kết MCP của Agent

**DELETE** `/user/agents/{agentId}/mcps`

Hủy tất cả liên kết giữa Agent và MCP servers.

**Parameters:**
- `agentId` (path): ID của Agent

**Response:**
```json
{
  "success": true,
  "message": "Đã xóa 3 liên kết MCP của Agent thành công",
  "data": {
    "removedCount": 3
  }
}
```

## Error Responses

### Agent không tồn tại
```json
{
  "success": false,
  "message": "Agent không tồn tại hoặc không thuộc về bạn",
  "error": {
    "code": 40050,
    "details": {
      "userId": 123,
      "agentId": "invalid-agent-id"
    }
  }
}
```

### MCP server không tồn tại
```json
{
  "success": false,
  "message": "MCP server không tồn tại hoặc không thuộc về bạn",
  "error": {
    "code": 40210,
    "details": {
      "userId": 123,
      "mcpId": "invalid-mcp-id"
    }
  }
}
```

### Liên kết đã tồn tại
```json
{
  "success": false,
  "message": "Agent đã được liên kết với MCP server này",
  "error": {
    "code": 40211,
    "details": {
      "agentId": "agent-uuid",
      "mcpId": "mcp-uuid"
    }
  }
}
```

### Liên kết không tồn tại
```json
{
  "success": false,
  "message": "Liên kết giữa Agent và MCP server không tồn tại",
  "error": {
    "code": 40212,
    "details": {
      "agentId": "agent-uuid",
      "mcpId": "mcp-uuid"
    }
  }
}
```

## Use Cases

### 1. Workflow cơ bản
1. Tạo MCP server qua `/user/mcp` API
2. Liên kết MCP với Agent qua `/user/agents/{agentId}/mcps`
3. Agent có thể sử dụng tools từ MCP server
4. Hủy liên kết khi không cần thiết

### 2. Bulk operations
1. Tạo nhiều MCP servers
2. Sử dụng bulk link để liên kết tất cả với Agent
3. Kiểm tra kết quả để xử lý các MCP thất bại

### 3. Agent cleanup
1. Sử dụng DELETE `/user/agents/{agentId}/mcps` để xóa tất cả liên kết
2. Hoặc xóa từng MCP một cách có chọn lọc

## Validation Rules

1. **Agent ownership**: Agent phải thuộc về user hiện tại
2. **MCP ownership**: MCP server phải thuộc về user hiện tại  
3. **Unique links**: Một Agent chỉ có thể liên kết với một MCP server một lần
4. **Valid UUIDs**: Tất cả IDs phải là UUID hợp lệ

## Integration với Tools Module

Các API này hoạt động cùng với Tools Module:
- MCP servers được quản lý qua `/user/mcp` APIs
- Agent-MCP links được quản lý qua `/user/agents/{agentId}/mcps` APIs
- Khi Agent thực thi, nó có thể sử dụng tools từ tất cả MCP servers được liên kết
