import { Column, Entity, PrimaryColumn, PrimaryGeneratedColumn } from 'typeorm';
import { CustomFieldDataType } from '@modules/marketing/common/enums/custom-field-data-type.enum';

/**
 * Entity đại diện cho bảng audience_admin_custom_fields trong cơ sở dữ liệu
 * Lưu thông tin các trường tùy chỉnh mà admin có thể định nghĩa động
 * Sử dụng composite primary key (fieldKey + createdBy) và có thêm id tự động tăng
 */
@Entity('audience_admin_custom_fields')
export class AdminAudienceCustomFieldDefinition {
  /**
   * ID tự động tăng cho trường tùy chỉnh
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Định danh cho trường tùy chỉnh (chữ thường, không dấu cách)
   * <PERSON><PERSON><PERSON> hợp với createdBy tạo thành composite unique key
   */
  @Column({ name: 'field_key', length: 100, unique: false })
  fieldKey: string;

  /**
   * ID của admin mà trường tùy chỉnh này thuộc về
   * Kết hợp với fieldKey tạo thành composite unique key
   */
  @Column({ name: 'created_by' })
  createdBy: number;

  /**
   * Tên hiển thị thân thiện với admin
   */
  @Column({ name: 'display_name', length: 255 })
  displayName: string;

  /**
   * Kiểu dữ liệu: text, number, boolean, date, select, object
   */
  @Column({
    name: 'data_type',
    type: 'enum',
    enum: CustomFieldDataType
  })
  dataType: CustomFieldDataType;

  /**
   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  /**
   * Danh sách tags để phân loại trường tùy chỉnh
   */
  @Column({ name: 'tags', type: 'jsonb', default: '[]' })
  tags: string[];

  /**
   * Cấu hình chi tiết cho trường tùy chỉnh (validation rules, options, etc.)
   */
  @Column({ name: 'config', type: 'jsonb', default: '{}' })
  config: Record<string, any>;
}
