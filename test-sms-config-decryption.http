### Test SMS Config Decryption

### Test 1: Tạo SMS Campaign với FPT SMS (config sẽ được giải mã)
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Test FPT SMS Decryption",
  "campaignType": "OTP",
  "description": "Test giải mã config FPT SMS",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "audienceIds": [1, 2, 3],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}

### Test 2: Tạo SMS Campaign với Twilio (config sẽ được giải mã)
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Test Twilio Decryption",
  "campaignType": "OTP",
  "description": "Test giải mã config Twilio",
  "serverId": "TWILIO_SERVER_ID_HERE",
  "audienceIds": [1, 2, 3],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}

### Test 3: Tạo SMS Campaign với Generic Provider (config sẽ được giải mã)
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Test Generic Provider Decryption",
  "campaignType": "OTP",
  "description": "Test giải mã config Generic Provider",
  "serverId": "GENERIC_SERVER_ID_HERE",
  "audienceIds": [1, 2, 3],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}

### Test 4: Test với server không tồn tại
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Test Non-existent Server",
  "campaignType": "OTP",
  "serverId": "00000000-0000-0000-0000-000000000000",
  "audienceIds": [1, 2, 3],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}

### Test 5: Test với server không phải SMS
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Test Non-SMS Server",
  "campaignType": "OTP",
  "serverId": "EMAIL_SERVER_ID_HERE",
  "audienceIds": [1, 2, 3],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}

### Test 6: Test ADS Campaign với scheduled time (config sẽ được giải mã)
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Test ADS Scheduled Decryption",
  "campaignType": "ADS",
  "description": "Test giải mã config cho ADS campaign",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "segmentIds": [1],
  "templateId": 20,
  "templateVariables": {
    "PROMOTION_CODE": "SALE50",
    "DISCOUNT": "50%"
  },
  "scheduledAt": 1735689600
}

### Test 7: Test với phoneNumbers (config sẽ được giải mã)
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Test Phone Numbers Decryption",
  "campaignType": "OTP",
  "description": "Test giải mã config với phone numbers",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "phoneNumbers": ["0901234567", "0987654321"],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}

### Test 8: Test retry campaign (config đã được giải mã từ trước)
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns/CAMPAIGN_ID_HERE/retry
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{}

### Lưu ý khi test:
# 1. Thay YOUR_JWT_TOKEN_HERE bằng JWT token thực
# 2. Thay các SERVER_ID_HERE bằng ID thực của integration
# 3. Kiểm tra logs để xem quá trình giải mã config
# 4. Verify rằng worker nhận được config đã giải mã
# 5. Kiểm tra fallback mechanism khi giải mã thất bại

### Expected Behavior:
# - Config sẽ được giải mã từ encryptedConfig và secretKey
# - Nếu giải mã thất bại, sẽ fallback về metadata
# - Worker sẽ nhận được config đã giải mã với apiKey, endpoint, etc.
# - Logs sẽ hiển thị quá trình giải mã và provider type
