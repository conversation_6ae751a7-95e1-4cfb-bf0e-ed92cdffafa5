/**
 * <PERSON><PERSON><PERSON> định nghĩa kiểu dữ liệu cho Queue Job
 */

import { SmsTypeEnum } from '@/modules/sms/enums';
import { CreateCustomerProductDto } from '@modules/business/user/dto/customer-product';
import { SyncZaloUsersToAudienceDto } from '@modules/marketing/user/dto/zalo/sync-zalo-users-to-audience.dto';
import { Platform } from '../enums';
import { ToolCallDecision } from '@/modules/chat/dto';

/**
 * Interface cho job data của bulk create customer products
 */
export interface BulkCreateCustomerProductsJobData {
  /**
   * ID của người dùng tạo sản phẩm
   */
  userId: number;

  /**
   * Danh sách sản phẩm cần tạo
   */
  products: CreateCustomerProductDto[];

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job gửi email thông thường
 */
export interface EmailJobData {
  /**
   * Địa chỉ email người nhận
   */
  to: string;

  /**
   * Tiêu đề email
   */
  subject: string;

  /**
   * Nội dung email (HTML)
   */
  content: string;

  /**
   * CC - Danh sách email nhận bản sao (optional)
   */
  cc?: string[];

  /**
   * BCC - Danh sách email nhận bản sao ẩn (optional)
   */
  bcc?: string[];

  /**
   * Địa chỉ email người gửi (optional, mặc định sẽ lấy từ cấu hình)
   */
  from?: string;

  /**
   * Tệp đính kèm (optional)
   */
  attachments?: EmailAttachment[];

  /**
   * Thời gian job được tạo
   */
  timestamp: number;
}

/**
 * Interface cho tệp đính kèm trong email
 */
export interface EmailAttachment {
  /**
   * Tên file
   */
  filename: string;

  /**
   * Nội dung file (dạng Buffer, Base64 hoặc đường dẫn)
   */
  content?: string | Buffer;

  /**
   * Đường dẫn đến file
   */
  path?: string;

  /**
   * Loại MIME của file
   */
  contentType?: string;
}

/**
 * Interface cho job gửi email theo mẫu
 */
export interface TemplateEmailJobData {
  /**
   * Địa chỉ email người nhận
   */
  to: string;

  /**
   * ID của mẫu email
   */
  templateId: string;

  /**
   * Dữ liệu được truyền vào mẫu email
   */
  data: Record<string, any>;

  /**
   * CC - Danh sách email nhận bản sao (optional)
   */
  cc?: string[];

  /**
   * BCC - Danh sách email nhận bản sao ẩn (optional)
   */
  bcc?: string[];

  /**
   * Thời gian job được tạo
   */
  timestamp: number;
}

/**
 * Interface cho job gửi SMS hệ thống
 */
export interface SmsSystemJobData {
  /**
   * Số điện thoại người nhận
   */
  phone: string;

  /**
   * Nội dung tin nhắn
   */
  message: string;

  /**
   * ID người dùng (tùy chọn)
   */
  userId?: number;

  /**
   * Loại SMS
   */
  type: SmsTypeEnum;

  /**
   * Dữ liệu bổ sung cho SMS (ví dụ: TWO_FA_CODE, USER_NAME, etc.)
   */
  data: Record<string, any>;

  /**
   * Thời gian job được tạo
   */
  timestamp: number;
}

/**
 * Interface cho job đồng bộ người dùng Zalo vào audience
 */
export interface ZaloAudienceSyncJobData {
  /**
   * ID của người dùng thực hiện đồng bộ
   */
  userId: number;

  /**
   * ID của Zalo Official Account
   */
  oaId: string;

  /**
   * Dữ liệu cấu hình đồng bộ
   */
  syncDto: SyncZaloUsersToAudienceDto;

  /**
   * ID để tracking job
   */
  syncId: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;
}

/**
 * Tùy chọn cho job
 */
export interface JobOptions {
  /**
   * Độ ưu tiên (số càng nhỏ, ưu tiên càng cao)
   */
  priority?: number;

  /**
   * Độ trễ trước khi thực hiện job (ms)
   */
  delay?: number;

  /**
   * Số lần thử lại nếu job thất bại
   */
  attempts?: number;

  /**
   * Cấu hình backoff cho retry
   */
  backoff?: {
    /**
     * Kiểu backoff: 'fixed' hoặc 'exponential'
     */
    type: 'fixed' | 'exponential';

    /**
     * Thời gian delay giữa các lần retry (ms)
     */
    delay: number;
  };

  /**
   * Xóa job sau khi hoàn thành
   */
  removeOnComplete?: boolean;

  /**
   * Xóa job nếu thất bại
   */
  removeOnFail?: boolean;

  /**
   * Timeout cho job (ms)
   */
  timeout?: number;
}

/**
 * Interface cho job data của Zalo Video Tracking
 */
export interface ZaloVideoTrackingJobData {
  /**
   * Token của video upload từ Zalo API
   */
  token: string;

  /**
   * Access token của Zalo Official Account
   */
  accessToken: string;

  /**
   * ID của user sở hữu video
   */
  userId: number;

  /**
   * ID của integration (Zalo OA)
   */
  integrationId: string;

  /**
   * ID của Official Account (để tương thích với hệ thống cũ)
   */
  oaId?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * Số lần đã check (để tránh check vô hạn)
   */
  checkCount?: number;

  /**
   * Thời gian delay giữa các lần check (milliseconds)
   */
  delayMs?: number;
}

/**
 * Interface cho job data upload GIF Zalo
 */
export interface ZaloUploadGifJobData {
  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * ID của Integration Zalo OA
   */
  integrationId: string;

  /**
   * Thông tin file GIF
   */
  fileInfo: {
    /**
     * Buffer dữ liệu file
     */
    data: Buffer;

    /**
     * Tên file gốc
     */
    filename: string;

    /**
     * MIME type
     */
    mimetype: string;

    /**
     * Kích thước file (bytes)
     */
    size: number;
  };

  /**
   * Mô tả GIF (tùy chọn)
   */
  description?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job data upload file Zalo
 */
export interface ZaloUploadFileJobData {
  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * ID của Integration Zalo OA
   */
  integrationId: string;

  /**
   * Thông tin file
   */
  fileInfo: {
    /**
     * Buffer dữ liệu file
     */
    data: Buffer;

    /**
     * Tên file gốc
     */
    filename: string;

    /**
     * MIME type
     */
    mimetype: string;

    /**
     * Kích thước file (bytes)
     */
    size: number;
  };

  /**
   * Mô tả file (tùy chọn)
   */
  description?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Dữ liệu job thực thi workflow
 */
export interface WorkflowExecutionJobData {
  /**
   * ID của workflow execution
   */
  executionId: string;

  /**
   * ID của workflow
   */
  workflowId: string;

  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * Dữ liệu trigger
   */
  triggerData: any;

  /**
   * Loại trigger (manual, webhook, schedule)
   */
  triggerType: 'manual' | 'webhook' | 'schedule';

  /**
   * Metadata bổ sung
   */
  metadata?: {
    source?: string;
    webhookId?: string;
    scheduleId?: string;
    priority?: number;
  };

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
  };
}

/**
 * ID của user thực thi
* Interface cho job data của In-App AI processing
*/
export interface InAppJobData {
  runId: string;
  threadId: string;
  agentId: string;
  user: {
    userId?: number;
    employeeId?: number;
  };
  jwt: string;
  chatWithSystem: boolean;
  alwaysApproveToolCall: boolean;
  toolCallDecision?: ToolCallDecision | undefined;
  workerAgentIds?: string[];
  platform: Platform.IN_APP;
  webSearchEnabled?: boolean;
}

/**
 * Interface cho job data đồng bộ tin nhắn Zalo
 */
export interface ZaloMessageSyncJobData {
  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * Dữ liệu trigger
   */
  triggerData: any;

  /**
   * Loại trigger (manual, webhook, schedule)
   */
  triggerType: 'manual' | 'webhook' | 'schedule';

  /**
   * Metadata bổ sung
   */
  metadata?: {
    source?: string;
    webhookId?: string;
    scheduleId?: string;
    priority?: number;
  };

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
  };


  /**
   * ID của Integration Zalo OA
   */
  integrationId: string;

  /**
   * Số lượng tin nhắn tối đa cần đồng bộ
   */
  limit: number;

  /**
   * Offset để phân trang
   */
  offset: number;

  /**
   * Chỉ đồng bộ tin nhắn từ những user-audience có zaloSocialId
   */
  onlyExistingAudience: boolean;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Dữ liệu job thực thi node đơn lẻ
 */
export interface WorkflowNodeExecutionJobData {
  /**
   * ID của workflow execution
   */
  executionId: string;

  /**
   * ID của node cần thực thi
   */
  nodeId: string;

  /**
   * Loại node
   */
  nodeType: string;

  /**
   * Cấu hình node
   */
  nodeConfig: Record<string, any>;

  /**
   * Dữ liệu đầu vào cho node
   */
  inputData: Record<string, any>;

  /**
   * Context từ các node trước đó
   */
  executionContext: Record<string, any>;

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    skipValidation?: boolean;
  };
}

/**
 * Interface cho job data của page navigation
 */
export interface PageNavigationJobData {
  /**
   * Đường dẫn trang cần chuyển đến
   */
  path: string;

  /**
   * ID người dùng (optional - nếu có thì chỉ gửi cho user cụ thể)
   */
  userId?: string;

  /**
   * Dữ liệu bổ sung để gửi kèm
   */
  data?: Record<string, any>;

  /**
   * Loại navigation
   */
  type?: 'navigate' | 'redirect' | 'replace';

  /**
   * Thông báo kèm theo
   */
  message?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;

  /**
   * Độ ưu tiên (optional)
   */
  priority?: 'low' | 'normal' | 'high';
}

