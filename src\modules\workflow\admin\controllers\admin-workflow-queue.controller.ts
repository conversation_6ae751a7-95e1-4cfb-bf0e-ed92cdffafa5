import { ApiResponseDto } from '@/common/response';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { JwtEmployeeGuard } from '@/modules/auth/guards/jwt-employee.guard';
import {
  WorkflowExecutionJobData,
  WorkflowNodeExecutionJobData,
} from '@/shared/queue/queue.types';
import { Body, Controller, Delete, Get, HttpStatus, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WorkflowQueueService } from '../../services/workflow-queue.service';

/**
 * Admin controller for workflow queue management
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_WORKFLOW)
@ApiBearerAuth()
@UseGuards(JwtEmployeeGuard)
@Controller('admin/workflow/queue')
export class AdminWorkflowQueueController {
  constructor(private readonly workflowQueueService: WorkflowQueueService) { }

  /**
   * Add workflow execution job to queue
   */
  @Post('execution')
  @ApiOperation({ summary: 'Add workflow execution job to queue' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Workflow execution job added successfully',
  })
  async addWorkflowExecutionJob(
    @Body() jobData: WorkflowExecutionJobData,
  ): Promise<ApiResponseDto<{ jobId: string | undefined }>> {
    const jobId = await this.workflowQueueService.addWorkflowExecutionJob(jobData);

    return ApiResponseDto.created(
      { jobId },
      'Workflow execution job added to queue successfully'
    );
  }

  /**
   * Add high priority workflow execution job to queue
   */
  @Post('execution/high-priority')
  @ApiOperation({ summary: 'Add high priority workflow execution job to queue' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'High priority workflow execution job added successfully',
  })
  async addHighPriorityWorkflowExecutionJob(
    @Body() jobData: WorkflowExecutionJobData,
  ): Promise<ApiResponseDto<{ jobId: string | undefined }>> {
    const jobId = await this.workflowQueueService.addHighPriorityWorkflowExecutionJob(jobData);

    return ApiResponseDto.created(
      { jobId },
      'High priority workflow execution job added to queue successfully'
    );
  }

  /**
   * Add delayed workflow execution job to queue
   */
  @Post('execution/delayed/:delayMs')
  @ApiOperation({ summary: 'Add delayed workflow execution job to queue' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Delayed workflow execution job added successfully',
  })
  async addDelayedWorkflowExecutionJob(
    @Param('delayMs') delayMs: number,
    @Body() jobData: WorkflowExecutionJobData,
  ): Promise<ApiResponseDto<{ jobId: string | undefined }>> {
    const jobId = await this.workflowQueueService.addDelayedWorkflowExecutionJob(jobData, delayMs);

    return ApiResponseDto.created(
      { jobId },
      `Delayed workflow execution job added to queue with ${delayMs}ms delay`
    );
  }

  /**
   * Add node execution job to queue
   */
  @Post('node/execution')
  @ApiOperation({ summary: 'Add node execution job to queue' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Node execution job added successfully',
  })
  async addNodeExecutionJob(
    @Body() jobData: WorkflowNodeExecutionJobData,
  ): Promise<ApiResponseDto<{ jobId: string | undefined }>> {
    const jobId = await this.workflowQueueService.addNodeExecutionJob(jobData);

    return ApiResponseDto.created(
      { jobId },
      'Node execution job added to queue successfully'
    );
  }

  // Node test endpoints removed - focusing on real execution only

  /**
   * Get workflow execution job status
   */
  @Get('execution/:jobId')
  @ApiOperation({ summary: 'Get workflow execution job status' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job status retrieved successfully',
  })
  async getWorkflowExecutionJobStatus(
    @Param('jobId') jobId: string,
  ): Promise<ApiResponseDto<any>> {
    const jobStatus = await this.workflowQueueService.getWorkflowExecutionJobStatus(jobId);

    return ApiResponseDto.success(
      jobStatus,
      'Workflow execution job status retrieved successfully'
    );
  }

  // Node test job status endpoint removed

  /**
   * Cancel workflow execution job
   */
  @Delete('execution/:jobId')
  @ApiOperation({ summary: 'Cancel workflow execution job' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Job cancelled successfully',
  })
  async cancelWorkflowExecutionJob(
    @Param('jobId') jobId: string,
  ): Promise<ApiResponseDto<{ cancelled: boolean }>> {
    const cancelled = await this.workflowQueueService.cancelWorkflowExecutionJob(jobId);

    return ApiResponseDto.success(
      { cancelled },
      cancelled ? 'Workflow execution job cancelled successfully' : 'Job not found or already completed'
    );
  }

  // Node test job cancellation endpoint removed

  /**
   * Get workflow execution queue statistics
   */
  @Get('execution/stats')
  @ApiOperation({ summary: 'Get workflow execution queue statistics' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Queue statistics retrieved successfully',
  })
  async getWorkflowExecutionQueueStats(): Promise<ApiResponseDto<any>> {
    const stats = await this.workflowQueueService.getWorkflowExecutionQueueStats();

    return ApiResponseDto.success(
      stats,
      'Workflow execution queue statistics retrieved successfully'
    );
  }
}
