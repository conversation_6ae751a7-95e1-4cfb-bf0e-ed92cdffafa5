import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ErrorCode } from '@common/exceptions';
import { ApiResponseDto } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Put,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  ProfileResponseDto,
  UpdateProfileDto,
} from '../dto/profile';
import { ProfileUserService } from '../services/profile-user.service';

/**
 * Controller xử lý các API liên quan đến profile của agent cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents/:id/profile')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ProfileUserController {
  constructor(private readonly profileUserService: ProfileUserService) { }

  // // ==================== PROFILE ENDPOINTS ====================

  /**
   * Lấy thông tin profile của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @returns Thông tin profile của agent
   */
  @Get()
  @ApiOperation({ summary: 'Lấy thông tin profile của agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin profile thành công',
    type: ProfileResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getProfile(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<ProfileResponseDto>> {
    const result = await this.profileUserService.getProfile(id, userId);
    return ApiResponseDto.success(result, 'Lấy thông tin profile thành công');
  }

  /**
   * Cập nhật profile của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param updateDto Thông tin profile cần cập nhật
   * @returns Thông tin profile đã cập nhật
   */
  @Put()
  @ApiOperation({ summary: 'Cập nhật profile của agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật profile thành công',
    type: ProfileResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateProfile(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateProfileDto,
  ): Promise<ApiResponseDto<ProfileResponseDto>> {
    const result = await this.profileUserService.updateProfile(id, userId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật profile thành công');
  }
}
