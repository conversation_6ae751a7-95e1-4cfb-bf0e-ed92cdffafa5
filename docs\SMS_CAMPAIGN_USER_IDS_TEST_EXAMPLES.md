# SMS Campaign với UserIds - Test Examples

## API Endpoint
```
POST /admin/integration/sms-campaigns/with-template
```

## Test Cases

### 1. <PERSON><PERSON><PERSON> SMS đến danh sách users cụ thể

#### Request
```json
{
  "name": "SMS Campaign Test với UserIds",
  "description": "Test gửi SMS đến users cụ thể",
  "smsIntegrationId": "uuid-integration-id",
  "templateId": 456,
  "userIds": [1, 2, 3, 4, 5],
  "templateVariables": {
    "customerName": "Kh<PERSON>ch hàng",
    "discountPercent": "50",
    "productName": "iPhone 15"
  },
  "campaignType": "ADS",
  "scheduledAt": 1703980800
}
```

#### Expected Response
```json
{
  "success": true,
  "data": {
    "campaignId": 123,
    "jobCount": 1,
    "jobIds": ["job_admin_sms_123"],
    "status": "SCHEDULED",
    "scheduledAt": 1703980800,
    "totalRecipients": 5,
    "campaignType": "ADS"
  },
  "message": "SMS campaign admin với template đã được tạo thành công. Campaign sẽ được xử lý bởi worker."
}
```

### 2. Gửi SMS ngay lập tức (không có scheduledAt)

#### Request
```json
{
  "name": "SMS Campaign Immediate với UserIds",
  "description": "Gửi SMS ngay lập tức đến users",
  "smsIntegrationId": "uuid-integration-id-2",
  "templateId": 456,
  "userIds": [10, 20, 30],
  "templateVariables": {
    "customerName": "Quý khách",
    "eventName": "Black Friday Sale"
  },
  "campaignType": "NOTIFICATION"
}
```

### 3. Kết hợp userIds với audienceIds

#### Request
```json
{
  "name": "SMS Campaign Mixed Targets",
  "description": "Gửi SMS đến cả users và audiences",
  "smsIntegrationId": "uuid-integration-id-3",
  "templateId": 456,
  "userIds": [1, 2, 3],
  "audienceIds": [101, 102, 103],
  "templateVariables": {
    "promotionCode": "SAVE20",
    "validUntil": "31/12/2024"
  },
  "campaignType": "PROMOTION",
  "scheduledAt": 1703980800
}
```

### 4. Error Cases

#### 4.1 Không có target nào được chỉ định
```json
{
  "name": "SMS Campaign No Target",
  "description": "Test case không có target",
  "smsIntegrationId": "uuid-integration-id",
  "templateId": 456,
  "templateVariables": {},
  "campaignType": "ADS"
}
```

**Expected Error Response:**
```json
{
  "success": false,
  "message": "Phải chọn segment, audience hoặc users để gửi SMS",
  "errorCode": "VALIDATION_ERROR"
}
```

#### 4.2 UserIds không tồn tại
```json
{
  "name": "SMS Campaign Invalid UserIds",
  "description": "Test với userIds không tồn tại",
  "smsIntegrationId": "uuid-integration-id",
  "templateId": 456,
  "userIds": [99999, 88888],
  "templateVariables": {},
  "campaignType": "ADS"
}
```

**Expected Error Response:**
```json
{
  "success": false,
  "message": "Không tìm thấy user nào với các ID được cung cấp",
  "errorCode": "VALIDATION_ERROR"
}
```

#### 4.3 Users không có số điện thoại hợp lệ
```json
{
  "name": "SMS Campaign Invalid Phone Users",
  "description": "Test với users không có phone hợp lệ",
  "smsIntegrationId": "uuid-integration-id",
  "templateId": 456,
  "userIds": [100, 101], // Giả sử users này không có phoneNumber hoặc countryCode
  "templateVariables": {},
  "campaignType": "ADS"
}
```

**Expected Error Response:**
```json
{
  "success": false,
  "message": "Không có user nào có số điện thoại hợp lệ trong danh sách",
  "errorCode": "VALIDATION_ERROR"
}
```

#### 4.4 SMS Integration không tồn tại
```json
{
  "name": "SMS Campaign Invalid Integration",
  "description": "Test với smsIntegrationId không tồn tại",
  "smsIntegrationId": "invalid-uuid-id",
  "templateId": 456,
  "userIds": [1, 2, 3],
  "templateVariables": {},
  "campaignType": "ADS"
}
```

**Expected Error Response:**
```json
{
  "success": false,
  "message": "SMS integration với ID invalid-uuid-id không tồn tại",
  "errorCode": "NOT_FOUND"
}
```

## cURL Examples

### 1. Basic Test với UserIds
```bash
curl -X POST "http://localhost:3000/admin/integration/sms-campaigns/with-template" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "SMS Test với UserIds",
    "description": "Test API với userIds",
    "smsServerId": 1,
    "templateId": 456,
    "userIds": [1, 2, 3],
    "templateVariables": {
      "customerName": "Test User",
      "message": "Hello World"
    },
    "campaignType": "NOTIFICATION"
  }'
```

### 2. Scheduled Campaign
```bash
curl -X POST "http://localhost:3000/admin/integration/sms-campaigns/with-template" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Scheduled SMS với UserIds",
    "description": "SMS được lên lịch",
    "smsServerId": 1,
    "templateId": 456,
    "userIds": [1, 2, 3, 4, 5],
    "templateVariables": {
      "eventName": "Flash Sale",
      "startTime": "10:00 AM"
    },
    "campaignType": "ADS",
    "scheduledAt": 1703980800
  }'
```

## Database Setup for Testing

### Tạo test users với số điện thoại hợp lệ:

```sql
-- Insert test users
INSERT INTO users (id, full_name, email, phone_number, country_code, is_active, created_at, updated_at) VALUES
(1, 'Nguyễn Văn A', '<EMAIL>', '912345678', 84, true, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),
(2, 'Trần Thị B', '<EMAIL>', '987654321', 84, true, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),
(3, 'Lê Văn C', '<EMAIL>', '901234567', 84, true, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),
(4, 'Phạm Thị D', '<EMAIL>', '976543210', 84, true, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),
(5, 'Hoàng Văn E', '<EMAIL>', '965432109', 84, true, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW()));

-- Insert test user without phone (for error testing)
INSERT INTO users (id, full_name, email, is_active, created_at, updated_at) VALUES
(100, 'User No Phone', '<EMAIL>', true, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW()));
```

### Tạo SMS Integration cho testing:

```sql
-- Insert test integration providers
INSERT INTO integration_providers (id, type, created_at, updated_at) VALUES
(1, 'SMS_FPT', EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),
(2, 'SMS_TWILIO', EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW()));

-- Insert test SMS integrations
INSERT INTO integration (id, integration_name, type_id, user_id, owned_type, employee_id, encrypted_config, secret_key, metadata, created_at) VALUES
('uuid-integration-id', 'FPT SMS Test', 1, null, 'ADMIN', 1,
 'encrypted_config_data_here', 'public_key_here',
 '{"brandName": "TEST_BRAND", "apiUrl": "https://api01.sms.fpt.net"}',
 EXTRACT(EPOCH FROM NOW()) * 1000),
('uuid-integration-id-2', 'Twilio SMS Test', 2, null, 'ADMIN', 1,
 'encrypted_twilio_config_here', 'twilio_public_key_here',
 '{"accountSid": "test_account_sid", "phoneNumber": "+**********"}',
 EXTRACT(EPOCH FROM NOW()) * 1000);
```

## Validation Checklist

- [ ] API chấp nhận `userIds` trong request body
- [ ] Validation đúng khi không có target nào được chỉ định
- [ ] Validation đúng khi userIds không tồn tại
- [ ] Validation đúng khi users không có số điện thoại hợp lệ
- [ ] Validation đúng khi smsIntegrationId không tồn tại
- [ ] Recipients được tạo đúng format từ user data
- [ ] Phone number được format đúng: `+{countryCode}{phoneNumber}`
- [ ] Custom fields được set đúng (name, email, userId)
- [ ] Campaign được tạo thành công với totalRecipients đúng
- [ ] SMS jobs được đẩy vào queue đúng cách
- [ ] API hoạt động với cả scheduled và immediate campaigns
- [ ] SMS integration được validate đúng cách
- [ ] Encryption/decryption hoạt động đúng với Integration
- [ ] Fallback strategy hoạt động khi decryption thất bại
