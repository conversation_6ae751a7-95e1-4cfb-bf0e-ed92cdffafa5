import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { FacebookBusinessApiService } from './facebook-business-api.service';
import { FacebookCustomAudience } from '../interfaces/facebook-business.interface';
import {
  FACEBOOK_BUSINESS_ERROR_CODES,
  createFacebookBusinessException,
  validateFacebookBusinessResponse,
  validateFacebookBusinessParams,
} from '../exceptions/facebook-business.exception';
import { CustomAudience } from 'facebook-nodejs-business-sdk';

/**
 * Service quản lý Facebook Custom Audiences
 */
@Injectable()
export class FacebookAudiencesService {
  private readonly logger = new Logger(FacebookAudiencesService.name);

  constructor(private readonly facebookApiService: FacebookBusinessApiService) {}

  /**
   * L<PERSON>y danh sách custom audiences
   * @param accessToken Access token
   * @param adAccountId ID của ad account
   * @param limit Số lượng audiences tối đa
   * @returns Danh sách custom audiences
   */
  async getCustomAudiences(accessToken: string, adAccountId?: string, limit: number = 25): Promise<FacebookCustomAudience[]> {
    try {
      this.logger.log(`Getting custom audiences for ad account: ${adAccountId || 'default'}`);

      const adAccount = this.facebookApiService.getAdAccountInstance(accessToken, adAccountId);
      
      const audiences = await adAccount.getCustomAudiences([
        'id',
        'name',
        'description',
        'subtype',
        'approximate_count',
        'data_source',
        'created_time',
        'updated_time',
        'delivery_status',
        'operation_status',
      ], {
        limit,
      });

      const result: FacebookCustomAudience[] = audiences.map((audience: any) => ({
        id: audience.id,
        name: audience.name,
        description: audience.description,
        subtype: audience.subtype,
        approximate_count: audience.approximate_count,
        data_source: audience.data_source,
        created_time: audience.created_time,
        updated_time: audience.updated_time,
        delivery_status: audience.delivery_status,
        operation_status: audience.operation_status,
      }));

      this.logger.log(`Successfully retrieved ${result.length} custom audiences`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting custom audiences: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy danh sách custom audiences',
        { adAccountId },
      );
    }
  }

  /**
   * Lấy thông tin custom audience cụ thể
   * @param audienceId ID của custom audience
   * @returns Thông tin custom audience
   */
  async getCustomAudience(audienceId: string): Promise<FacebookCustomAudience> {
    try {
      validateFacebookBusinessParams({ audienceId }, ['audienceId']);

      this.logger.log(`Getting custom audience info for ID: ${audienceId}`);

      const audience = new CustomAudience(audienceId);
      const audienceData = await audience.get([
        'id',
        'name',
        'description',
        'subtype',
        'approximate_count',
        'data_source',
        'created_time',
        'updated_time',
        'delivery_status',
        'operation_status',
      ]);

      validateFacebookBusinessResponse(audienceData, ['id', 'name']);

      const result: FacebookCustomAudience = {
        id: audienceData.id,
        name: audienceData.name,
        description: audienceData.description,
        subtype: audienceData.subtype,
        approximate_count: audienceData.approximate_count,
        data_source: audienceData.data_source,
        created_time: audienceData.created_time,
        updated_time: audienceData.updated_time,
        delivery_status: audienceData.delivery_status,
        operation_status: audienceData.operation_status,
      };

      this.logger.log(`Successfully retrieved custom audience: ${result.name}`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting custom audience: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy thông tin custom audience',
        { audienceId },
      );
    }
  }

  /**
   * Tạo custom audience từ customer list
   * @param adAccountId ID của ad account
   * @param audienceData Dữ liệu audience
   * @returns Custom audience đã tạo
   */
  async createCustomAudienceFromCustomerList(
    adAccountId: string,
    audienceData: {
      name: string;
      description?: string;
      customer_file_source?: string;
    },
  ): Promise<FacebookCustomAudience> {
    try {
      validateFacebookBusinessParams(audienceData, ['name']);

      this.logger.log(`Creating custom audience from customer list: ${audienceData.name}`);

      const adAccount = this.facebookApiService.getAdAccountInstance(adAccountId);

      const params = {
        name: audienceData.name,
        subtype: 'CUSTOM',
        description: audienceData.description || '',
        customer_file_source: audienceData.customer_file_source || 'USER_PROVIDED_ONLY',
      };

      const audience = await adAccount.createCustomAudience([], params);
      
      validateFacebookBusinessResponse(audience, ['id']);

      // Get full audience data
      const createdAudience = await this.getCustomAudience(audience.id);

      this.logger.log(`Successfully created custom audience: ${createdAudience.name} (ID: ${createdAudience.id})`);
      return createdAudience;
    } catch (error) {
      this.logger.error(`Error creating custom audience: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể tạo custom audience',
        { adAccountId, audienceData },
      );
    }
  }

  /**
   * Tạo lookalike audience
   * @param adAccountId ID của ad account
   * @param audienceData Dữ liệu lookalike audience
   * @returns Lookalike audience đã tạo
   */
  async createLookalikeAudience(
    adAccountId: string,
    audienceData: {
      name: string;
      origin_audience_id: string;
      target_countries: string[];
      ratio: number;
      description?: string;
    },
  ): Promise<FacebookCustomAudience> {
    try {
      validateFacebookBusinessParams(audienceData, [
        'name',
        'origin_audience_id',
        'target_countries',
        'ratio',
      ]);

      if (audienceData.ratio < 0.01 || audienceData.ratio > 0.2) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_PARAMETERS,
          'Lookalike ratio must be between 0.01 and 0.2',
        );
      }

      this.logger.log(`Creating lookalike audience: ${audienceData.name}`);

      const adAccount = this.facebookApiService.getAdAccountInstance(adAccountId);

      const params = {
        name: audienceData.name,
        subtype: 'LOOKALIKE',
        description: audienceData.description || '',
        origin_audience_id: audienceData.origin_audience_id,
        lookalike_spec: {
          ratio: audienceData.ratio,
          country: audienceData.target_countries,
        },
      };

      const audience = await adAccount.createCustomAudience([], params);
      
      validateFacebookBusinessResponse(audience, ['id']);

      // Get full audience data
      const createdAudience = await this.getCustomAudience(audience.id);

      this.logger.log(`Successfully created lookalike audience: ${createdAudience.name} (ID: ${createdAudience.id})`);
      return createdAudience;
    } catch (error) {
      this.logger.error(`Error creating lookalike audience: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể tạo lookalike audience',
        { adAccountId, audienceData },
      );
    }
  }

  /**
   * Cập nhật custom audience
   * @param audienceId ID của custom audience
   * @param updateData Dữ liệu cập nhật
   * @returns Custom audience đã cập nhật
   */
  async updateCustomAudience(
    audienceId: string,
    updateData: {
      name?: string;
      description?: string;
    },
  ): Promise<FacebookCustomAudience> {
    try {
      validateFacebookBusinessParams({ audienceId }, ['audienceId']);

      this.logger.log(`Updating custom audience: ${audienceId}`);

      const audience = new CustomAudience(audienceId);
      
      // Remove undefined values
      const params = Object.fromEntries(
        Object.entries(updateData).filter(([_, value]) => value !== undefined)
      );

      if (Object.keys(params).length === 0) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_PARAMETERS,
          'No valid update parameters provided',
        );
      }

      await audience.update([], params);

      // Get updated audience data
      const updatedAudience = await this.getCustomAudience(audienceId);

      this.logger.log(`Successfully updated custom audience: ${updatedAudience.name}`);
      return updatedAudience;
    } catch (error) {
      this.logger.error(`Error updating custom audience: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể cập nhật custom audience',
        { audienceId, updateData },
      );
    }
  }

  /**
   * Xóa custom audience
   * @param audienceId ID của custom audience
   * @returns True nếu xóa thành công
   */
  async deleteCustomAudience(audienceId: string): Promise<boolean> {
    try {
      validateFacebookBusinessParams({ audienceId }, ['audienceId']);

      this.logger.log(`Deleting custom audience: ${audienceId}`);

      const audience = new CustomAudience(audienceId);
      await audience.delete();

      this.logger.log(`Successfully deleted custom audience: ${audienceId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error deleting custom audience: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể xóa custom audience',
        { audienceId },
      );
    }
  }

  /**
   * Thêm users vào custom audience
   * @param audienceId ID của custom audience
   * @param users Danh sách users (emails, phone numbers, etc.)
   * @param schema Schema của data (EMAIL, PHONE, etc.)
   * @returns True nếu thành công
   */
  async addUsersToAudience(
    audienceId: string,
    users: string[],
    schema: string[] = ['EMAIL'],
  ): Promise<boolean> {
    try {
      validateFacebookBusinessParams({ audienceId, users }, ['audienceId', 'users']);

      if (!Array.isArray(users) || users.length === 0) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_PARAMETERS,
          'Users must be a non-empty array',
        );
      }

      this.logger.log(`Adding ${users.length} users to custom audience: ${audienceId}`);

      const audience = new CustomAudience(audienceId);

      // Hash the user data (Facebook requires hashed data)
      const crypto = require('crypto');
      const hashedUsers = users.map(user => 
        crypto.createHash('sha256').update(user.toLowerCase().trim()).digest('hex')
      );

      await audience.createUser([], {
        payload: {
          schema,
          data: hashedUsers.map(hash => [hash]),
        },
      });

      this.logger.log(`Successfully added ${users.length} users to audience ${audienceId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error adding users to audience: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể thêm users vào custom audience',
        { audienceId, userCount: users.length },
      );
    }
  }

  /**
   * Xóa users khỏi custom audience
   * @param audienceId ID của custom audience
   * @param users Danh sách users cần xóa
   * @param schema Schema của data
   * @returns True nếu thành công
   */
  async removeUsersFromAudience(
    audienceId: string,
    users: string[],
    schema: string[] = ['EMAIL'],
  ): Promise<boolean> {
    try {
      validateFacebookBusinessParams({ audienceId, users }, ['audienceId', 'users']);

      if (!Array.isArray(users) || users.length === 0) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_PARAMETERS,
          'Users must be a non-empty array',
        );
      }

      this.logger.log(`Removing ${users.length} users from custom audience: ${audienceId}`);

      const audience = new CustomAudience(audienceId);

      // Hash the user data
      const crypto = require('crypto');
      const hashedUsers = users.map(user => 
        crypto.createHash('sha256').update(user.toLowerCase().trim()).digest('hex')
      );

      await audience.deleteUser([], {
        payload: {
          schema,
          data: hashedUsers.map(hash => [hash]),
        },
      });

      this.logger.log(`Successfully removed ${users.length} users from audience ${audienceId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error removing users from audience: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể xóa users khỏi custom audience',
        { audienceId, userCount: users.length },
      );
    }
  }

  /**
   * Lấy audience size estimate
   * @param adAccountId ID của ad account
   * @param targeting Targeting spec
   * @returns Estimated audience size
   */
  async getAudienceSizeEstimate(
    adAccountId: string,
    targeting: Record<string, unknown>,
  ): Promise<{
    estimate_dau: number;
    estimate_mau: number;
    estimate_ready: boolean;
  }> {
    try {
      validateFacebookBusinessParams({ adAccountId, targeting }, ['adAccountId', 'targeting']);

      this.logger.log(`Getting audience size estimate for ad account: ${adAccountId}`);

      const adAccount = this.facebookApiService.getAdAccountInstance(adAccountId);

      const estimate = await adAccount.getReachEstimate([], {
        targeting_spec: targeting,
      });

      const result = {
        estimate_dau: estimate.estimate_dau || 0,
        estimate_mau: estimate.estimate_mau || 0,
        estimate_ready: estimate.estimate_ready || false,
      };

      this.logger.log(`Successfully retrieved audience size estimate: ${result.estimate_mau} MAU`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting audience size estimate: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy audience size estimate',
        { adAccountId, targeting },
      );
    }
  }

  /**
   * Lấy available audience subtypes
   * @returns Danh sách subtypes có sẵn
   */
  getAvailableSubtypes(): string[] {
    return [
      'CUSTOM',
      'WEBSITE',
      'APP',
      'OFFLINE_CONVERSION',
      'CLAIM',
      'PARTNER',
      'MANAGED',
      'VIDEO',
      'LOOKALIKE',
      'ENGAGEMENT',
      'DATA_SET',
      'BAG_OF_ACCOUNTS',
      'STUDY_RULE_AUDIENCE',
      'FOX',
    ];
  }

  /**
   * Lấy available data schemas
   * @returns Danh sách schemas có sẵn
   */
  getAvailableSchemas(): string[] {
    return [
      'EMAIL',
      'PHONE',
      'MOBILE_ADVERTISER_ID',
      'EXTERN_ID',
      'FN',
      'LN',
      'FI',
      'DOBY',
      'DOBM',
      'DOBD',
      'ZIP',
      'ST',
      'COUNTRY',
      'CT',
      'GEN',
    ];
  }
}
