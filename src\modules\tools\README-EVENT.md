# User Registration Event System

Hệ thống event để tự động clone tools công khai khi có người dùng mới đăng ký.

## <PERSON><PERSON><PERSON> sử dụng

### 1. Trong module Auth (hoặc module xử lý đăng ký)

```typescript
import { UserRegistrationEventService } from '@modules/tools/services/user-registration-event.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly userRegistrationEventService: UserRegistrationEventService,
    // ... other dependencies
  ) {}

  async registerUser(registerDto: RegisterDto): Promise<any> {
    // Logic đăng ký user
    const newUser = await this.userRepository.save(userData);

    // Phát event để clone tools công khai
    await this.userRegistrationEventService.emitUserRegistered(
      newUser.id,
      'My_' // Tiền tố tùy chọn cho tên tools
    );

    return newUser;
  }
}
```

### 2. Import UserRegistrationEventService vào module

```typescript
import { UserRegistrationEventService } from '@modules/tools';

@Module({
  imports: [ToolsModule],
  providers: [
    AuthService,
    // ... other providers
  ],
})
export class AuthModule {}
```

## Event Flow

1. **User đăng ký** → AuthService.registerUser()
2. **Phát event** → userRegistrationEventService.emitUserRegistered()
3. **Event listener** → UserRegisteredListener.handleUserRegistered()
4. **Clone tools** → userToolService.cloneAllPublicToolsForNewUser()
5. **Log kết quả** → Ghi log số lượng tools đã clone

## Tính năng

- ✅ **Tự động clone**: Tự động clone tất cả tools công khai cho user mới
- ✅ **Error handling**: Không ảnh hưởng đến quá trình đăng ký nếu có lỗi
- ✅ **Logging**: Ghi log chi tiết quá trình clone
- ✅ **Customizable**: Có thể thêm tiền tố cho tên tools
- ✅ **Transactional**: Sử dụng transaction để đảm bảo tính nhất quán

## Event Details

### Event: `user.registered`
- **Payload**: `UserRegisteredEvent`
  - `userId: number` - ID của user mới
  - `namePrefix?: string` - Tiền tố cho tên tools (tùy chọn)

### Listener: `UserRegisteredListener`
- **Method**: `handleUserRegistered()`
- **Decorator**: `@OnEvent('user.registered')`
- **Transaction**: `@Transactional()`

## Lưu ý

1. **Performance**: Clone tools chạy bất đồng bộ, không làm chậm quá trình đăng ký
2. **Error Safety**: Lỗi trong quá trình clone không ảnh hưởng đến đăng ký user
3. **Idempotent**: Có thể chạy nhiều lần mà không tạo duplicate tools
4. **Logging**: Tất cả hoạt động đều được log để theo dõi
