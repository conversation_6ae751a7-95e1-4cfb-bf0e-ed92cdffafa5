import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNumber, ArrayMinSize, ArrayMaxSize } from 'class-validator';

/**
 * DTO cho bulk delete flash sales (User)
 */
export class BulkDeleteFlashSaleDto {
  @ApiProperty({
    description: 'Danh sách ID flash sales cần xóa (tối đa 50 items)',
    example: [1, 2, 3],
    type: [Number],
    minItems: 1,
    maxItems: 50
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 flash sale để xóa' })
  @ArrayMaxSize(50, { message: 'Chỉ có thể xóa tối đa 50 flash sales cùng lúc' })
  @IsNumber({}, { each: true, message: 'Mỗi ID phải là số' })
  ids: number[];
}

/**
 * Response DTO cho bulk delete operation
 */
export class BulkDeleteResponseDto {
  @ApiProperty({
    description: 'Số lượng flash sales đã xóa thành công',
    example: 2
  })
  successCount: number;

  @ApiProperty({
    description: 'Danh sách ID flash sales đã xóa thành công',
    example: [1, 2]
  })
  successIds: number[];

  @ApiProperty({
    description: 'Số lượng flash sales xóa thất bại',
    example: 1
  })
  failureCount: number;

  @ApiProperty({
    description: 'Danh sách flash sales xóa thất bại với lý do',
    example: [
      {
        id: 3,
        reason: 'Flash sale không tồn tại'
      }
    ]
  })
  failures: Array<{
    id: number;
    reason: string;
  }>;

  @ApiProperty({
    description: 'Tổng số flash sales được xử lý',
    example: 3
  })
  totalProcessed: number;
}
