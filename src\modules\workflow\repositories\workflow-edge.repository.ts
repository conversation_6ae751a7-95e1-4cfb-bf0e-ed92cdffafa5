import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { WorkflowEdge } from '../entities';

/**
 * Repository cho WorkflowEdge entity
 * Xử lý các thao tác database liên quan đến workflow edges
 */
@Injectable()
export class WorkflowEdgeRepository extends Repository<WorkflowEdge> {
  constructor(private dataSource: DataSource) {
    super(WorkflowEdge, dataSource.createEntityManager());
  }

  /**
   * Tạo base query cho WorkflowEdge
   */
  private createBaseQuery() {
    return this.createQueryBuilder('workflow_edge');
  }

  /**
   * L<PERSON>y tất cả edges của một workflow
   * @param workflowId - ID của workflow
   * @returns Promise<WorkflowEdge[]>
   */
  async findByWorkflowId(workflowId: string): Promise<WorkflowEdge[]> {
    return this.createBaseQuery()
      .where('workflow_edge.workflowId = :workflowId', { workflowId })
      .orderBy('workflow_edge.createdAt', 'ASC')
      .getMany();
  }

  /**
   * Lấy edge theo workflow ID và edge ID
   * @param workflowId - ID của workflow
   * @param edgeId - ID của edge trong workflow
   * @returns Promise<WorkflowEdge | null>
   */
  async findByWorkflowAndEdgeId(workflowId: string, edgeId: string): Promise<WorkflowEdge | null> {
    return this.createBaseQuery()
      .where('workflow_edge.workflowId = :workflowId', { workflowId })
      .andWhere('workflow_edge.edgeId = :edgeId', { edgeId })
      .getOne();
  }

  /**
   * Lấy tất cả edges từ một source node
   * @param workflowId - ID của workflow
   * @param sourceNodeId - ID của source node
   * @returns Promise<WorkflowEdge[]>
   */
  async findBySourceNode(workflowId: string, sourceNodeId: string): Promise<WorkflowEdge[]> {
    return this.createBaseQuery()
      .where('workflow_edge.workflowId = :workflowId', { workflowId })
      .andWhere('workflow_edge.sourceNodeId = :sourceNodeId', { sourceNodeId })
      .orderBy('workflow_edge.createdAt', 'ASC')
      .getMany();
  }

  /**
   * Lấy tất cả edges đến một target node
   * @param workflowId - ID của workflow
   * @param targetNodeId - ID của target node
   * @returns Promise<WorkflowEdge[]>
   */
  async findByTargetNode(workflowId: string, targetNodeId: string): Promise<WorkflowEdge[]> {
    return this.createBaseQuery()
      .where('workflow_edge.workflowId = :workflowId', { workflowId })
      .andWhere('workflow_edge.targetNodeId = :targetNodeId', { targetNodeId })
      .orderBy('workflow_edge.createdAt', 'ASC')
      .getMany();
  }

  /**
   * Lấy tất cả edges liên quan đến một node (cả source và target)
   * @param workflowId - ID của workflow
   * @param nodeId - ID của node
   * @returns Promise<WorkflowEdge[]>
   */
  async findByNode(workflowId: string, nodeId: string): Promise<WorkflowEdge[]> {
    return this.createBaseQuery()
      .where('workflow_edge.workflowId = :workflowId', { workflowId })
      .andWhere(
        '(workflow_edge.sourceNodeId = :nodeId OR workflow_edge.targetNodeId = :nodeId)',
        { nodeId }
      )
      .orderBy('workflow_edge.createdAt', 'ASC')
      .getMany();
  }

  /**
   * Lấy edges theo edge type
   * @param workflowId - ID của workflow
   * @param edgeType - Type của edge
   * @returns Promise<WorkflowEdge[]>
   */
  async findByEdgeType(workflowId: string, edgeType: string): Promise<WorkflowEdge[]> {
    return this.createBaseQuery()
      .where('workflow_edge.workflowId = :workflowId', { workflowId })
      .andWhere('workflow_edge.edgeType = :edgeType', { edgeType })
      .orderBy('workflow_edge.createdAt', 'ASC')
      .getMany();
  }

  /**
   * Đếm số lượng edges trong một workflow
   * @param workflowId - ID của workflow
   * @returns Promise<number>
   */
  async countByWorkflowId(workflowId: string): Promise<number> {
    return this.createBaseQuery()
      .where('workflow_edge.workflowId = :workflowId', { workflowId })
      .getCount();
  }

  /**
   * Xóa tất cả edges của một workflow
   * @param workflowId - ID của workflow
   * @returns Promise<void>
   */
  async deleteByWorkflowId(workflowId: string): Promise<void> {
    await this.createBaseQuery()
      .delete()
      .where('workflowId = :workflowId', { workflowId })
      .execute();
  }

  /**
   * Xóa tất cả edges liên quan đến một node
   * @param workflowId - ID của workflow
   * @param nodeId - ID của node
   * @returns Promise<void>
   */
  async deleteByNode(workflowId: string, nodeId: string): Promise<void> {
    await this.createBaseQuery()
      .delete()
      .where('workflowId = :workflowId', { workflowId })
      .andWhere(
        '(sourceNodeId = :nodeId OR targetNodeId = :nodeId)',
        { nodeId }
      )
      .execute();
  }

  /**
   * Kiểm tra edge có tồn tại trong workflow
   * @param workflowId - ID của workflow
   * @param edgeId - ID của edge
   * @returns Promise<boolean>
   */
  async existsInWorkflow(workflowId: string, edgeId: string): Promise<boolean> {
    const count = await this.createBaseQuery()
      .where('workflow_edge.workflowId = :workflowId', { workflowId })
      .andWhere('workflow_edge.edgeId = :edgeId', { edgeId })
      .getCount();

    return count > 0;
  }

  /**
   * Kiểm tra có circular dependency không
   * @param workflowId - ID của workflow
   * @param sourceNodeId - ID của source node
   * @param targetNodeId - ID của target node
   * @returns Promise<boolean>
   */
  async hasCircularDependency(
    workflowId: string, 
    sourceNodeId: string, 
    targetNodeId: string
  ): Promise<boolean> {
    // Simple check: xem target node có path về source node không
    const pathExists = await this.createBaseQuery()
      .where('workflow_edge.workflowId = :workflowId', { workflowId })
      .andWhere('workflow_edge.sourceNodeId = :targetNodeId', { targetNodeId })
      .andWhere('workflow_edge.targetNodeId = :sourceNodeId', { sourceNodeId })
      .getCount();

    return pathExists > 0;
  }

  /**
   * Lấy statistics về edge types trong workflow
   * @param workflowId - ID của workflow
   * @returns Promise<{edgeType: string, count: number}[]>
   */
  async getEdgeTypeStats(workflowId: string): Promise<{edgeType: string, count: number}[]> {
    const result = await this.createBaseQuery()
      .select('workflow_edge.edgeType', 'edgeType')
      .addSelect('COUNT(*)', 'count')
      .where('workflow_edge.workflowId = :workflowId', { workflowId })
      .groupBy('workflow_edge.edgeType')
      .getRawMany();

    return result.map(row => ({
      edgeType: row.edgeType,
      count: parseInt(row.count)
    }));
  }



  /**
   * Bulk insert edges
   * @param edges - Array of edges to insert
   * @returns Promise with insert result
   */
  async bulkInsert(edges: Partial<WorkflowEdge>[]): Promise<WorkflowEdge[]> {
    const entities = edges.map(edge => this.create(edge));
    return this.save(entities);
  }

  /**
   * Get statistics
   * @returns Promise with statistics
   */
  async getStatistics(): Promise<any> {
    const total = await this.count();
    return {
      total,
      byType: {}
    };
  }
}
