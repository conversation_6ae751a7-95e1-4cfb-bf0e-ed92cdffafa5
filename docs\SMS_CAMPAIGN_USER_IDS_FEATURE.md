# SMS Campaign với UserIds Feature

## Tổng quan
Cập nhật API `createSmsCampaignWithTemplate` để hỗ trợ gửi SMS đến danh sách users cụ thể thông qua `userIds` ngoài việc gửi đến `audience` và `segment`.

## Thay đổi thực hiện

### 1. DTO Updates

#### CreateSmsCampaignAdminDto
- **File**: `src/modules/integration/admin/dto/create-sms-campaign-admin.dto.ts`
- **Thay đổi**: Thêm trường `userIds?: number[]`

```typescript
@ApiPropertyOptional({
  description: 'Danh sách user IDs (nếu gửi cho users cụ thể)',
  example: [1, 2, 3, 4, 5],
  type: [Number],
})
@IsOptional()
@IsArray()
@Type(() => Number)
@IsNumber({}, { each: true })
@Min(1, { each: true })
userIds?: number[];
```

#### CreateSmsCampaignAdminWithTemplateDto
- **File**: `src/modules/integration/admin/dto/create-sms-campaign-admin.dto.ts`
- **Thay đổi**: Thêm trường `userIds?: number[]` tương tự

### 2. Service Updates

#### SmsCampaignAdminService
- **File**: `src/modules/integration/admin/services/sms-campaign-admin.service.ts`
- **Thay đổi**:
  - Import `UserRepository`, `IntegrationRepository`, `KeyPairEncryptionService`
  - Inject repositories và encryption service vào constructor
  - Cập nhật `createSmsCampaignWithTemplate()` để truyền `userIds`
  - Cập nhật `getRecipientsFromSegmentOrAudience()` để xử lý `userIds`
  - Cập nhật validation logic để chấp nhận `userIds`
  - Thay đổi từ `smsServerId` sang `smsIntegrationId`
  - Thêm validation cho `smsIntegrationId`
  - Thêm method `getSmsConfigFromIntegration()` để giải mã cấu hình

```typescript
// Validation logic mới
if (!createDto.segmentId &&
    (!createDto.audienceIds || createDto.audienceIds.length === 0) &&
    (!createDto.userIds || createDto.userIds.length === 0)) {
  throw new AppException(
    new ErrorCode(400, 'Phải chọn segment, audience hoặc users để gửi SMS', 400),
    'Phải chọn segment, audience hoặc users để gửi SMS'
  );
}

// Validation SMS Integration
const smsIntegration = await this.integrationRepository.findOne({
  where: { id: createDto.smsIntegrationId }
});

if (!smsIntegration) {
  throw new AppException(
    new ErrorCode(404, 'Không tìm thấy cấu hình SMS integration', 404),
    `SMS integration với ID ${createDto.smsIntegrationId} không tồn tại`
  );
}

// Validate SMS integration có encryptedConfig và secretKey
if (!smsIntegration.encryptedConfig || !smsIntegration.secretKey) {
  throw new AppException(
    new ErrorCode(400, 'SMS integration không có cấu hình hợp lệ', 400),
    'SMS integration thiếu encryptedConfig hoặc secretKey'
  );
}
```

#### Logic xử lý userIds
```typescript
else if (userIds && userIds.length > 0) {
  // Lấy users từ database theo userIds
  const users = await this.userRepository.findByIds(userIds);
  
  if (users.length === 0) {
    throw new AppException(
      ErrorCode.VALIDATION_ERROR,
      'Không tìm thấy user nào với các ID được cung cấp'
    );
  }

  // Validate users có số điện thoại hợp lệ
  const validUsers = users.filter(user => user.phoneNumber && user.countryCode);
  
  if (validUsers.length === 0) {
    throw new AppException(
      ErrorCode.VALIDATION_ERROR,
      'Không có user nào có số điện thoại hợp lệ trong danh sách'
    );
  }

  // Chuyển đổi users thành recipients
  for (const user of validUsers) {
    recipients.push({
      audienceId: user.id, // Sử dụng user.id làm audienceId
      phone: `+${user.countryCode}${user.phoneNumber}`,
      customFields: { 
        name: user.fullName || `User ${user.id}`,
        email: user.email,
        userId: user.id
      }
    });
  }
}
```

### 3. Module Updates

#### IntegrationAdminModule
- **File**: `src/modules/integration/admin/integration-admin.module.ts`
- **Thay đổi**:
  - Import `UserRepository` và `User` entity
  - Thêm `User` vào TypeOrmModule.forFeature
  - Thêm `UserRepository` vào providers

### 4. Controller Updates

#### SmsCampaignAdminController
- **File**: `src/modules/integration/admin/controllers/sms-campaign-admin.controller.ts`
- **Thay đổi**: Cập nhật Swagger documentation để bao gồm thông tin về `userIds`

#### AdminSmsCampaignController
- **File**: `src/modules/marketing/admin/controllers/admin-sms-campaign.controller.ts`
- **Thay đổi**: Cập nhật Swagger documentation tương tự

## Cách sử dụng

### API Request Example

```json
{
  "name": "SMS Campaign với UserIds",
  "description": "Gửi SMS đến danh sách users cụ thể",
  "smsServerId": 1,
  "templateId": 456,
  "userIds": [1, 2, 3, 4, 5],
  "templateVariables": {
    "customerName": "Nguyễn Văn A",
    "discountPercent": "50"
  },
  "campaignType": "ADS",
  "scheduledAt": **********
}
```

### Validation Rules

1. **Ít nhất một target**: Phải cung cấp ít nhất một trong các trường: `segmentId`, `audienceIds`, hoặc `userIds`
2. **UserIds validation**: 
   - Tất cả userIds phải tồn tại trong database
   - Users phải có số điện thoại hợp lệ (phoneNumber và countryCode)
3. **Phone format**: Số điện thoại sẽ được format thành `+{countryCode}{phoneNumber}`

### Response Structure

Response sẽ bao gồm thông tin về số lượng recipients được tạo từ userIds:

```json
{
  "success": true,
  "data": {
    "campaignId": 123,
    "jobCount": 1,
    "jobIds": ["job_admin_sms_123"],
    "status": "SCHEDULED",
    "scheduledAt": **********,
    "totalRecipients": 5,
    "campaignType": "ADS"
  },
  "message": "SMS campaign admin với template đã được tạo thành công. Campaign sẽ được xử lý bởi worker."
}
```

## Lưu ý kỹ thuật

1. **User Entity Structure**: API sử dụng các trường `phoneNumber`, `countryCode`, `fullName`, `email` từ User entity
2. **Phone Validation**: Chỉ users có cả `phoneNumber` và `countryCode` mới được coi là hợp lệ
3. **Recipient Mapping**: `user.id` được sử dụng làm `audienceId` trong recipient object
4. **Custom Fields**: Thông tin user được lưu trong `customFields` để sử dụng trong template variables

## SMS Integration Configuration

### Migration từ SmsServerConfiguration sang Integration
- **Bảng cũ**: `sms_server_configurations` ❌
- **Bảng mới**: `integration` ✅
- **Entity**: `Integration`
- **Repository**: `IntegrationRepository`

### Cấu trúc Integration
```sql
integration:
- id (UUID PK) - smsIntegrationId được lấy từ đây
- integration_name - Tên tích hợp
- type_id - ID tham chiếu đến integration_providers
- user_id - ID của user (null cho admin)
- owned_type - USER/ADMIN
- employee_id - ID nhân viên (nếu tạo bởi admin)
- encrypted_config - Cấu hình nhạy cảm được mã hóa
- secret_key - Public key để giải mã
- metadata - Thông tin không nhạy cảm (JSONB)
- created_at
```

### Cấu trúc dữ liệu SMS
**Metadata** (không mã hóa):
- `brandName` - Tên brandname (FPT SMS)
- `accountSid` - Account SID (Twilio)
- `phoneNumber` - Số điện thoại gửi
- `apiUrl` - URL API

**EncryptedConfig** (được mã hóa):
- `clientId`, `clientSecret` - FPT SMS credentials
- `authToken` - Twilio auth token
- `apiKey`, `apiSecret` - Vonage credentials

### Các nhà cung cấp SMS được hỗ trợ
1. **SMS_FPT** - FPT SMS Brandname cho Việt Nam
2. **SMS_TWILIO** - Twilio International SMS
3. **SMS_VONAGE** - Vonage (Nexmo) SMS
4. **SMS_SPEED** - SpeedSMS provider

### Validation smsIntegrationId
API sẽ kiểm tra:
- `smsIntegrationId` phải tồn tại trong bảng `integration`
- Integration phải có `encryptedConfig` và `secretKey`
- Trả về lỗi 404 nếu không tìm thấy integration

## Testing

Để test tính năng này:

1. Tạo một số users trong database với số điện thoại hợp lệ
2. Tạo hoặc sử dụng SMS server configuration có sẵn
3. Gọi API `POST /admin/integration/sms-campaigns/with-template` với `userIds` và `smsServerId`
4. Kiểm tra response và verify rằng campaign được tạo thành công
5. Kiểm tra job queue để đảm bảo SMS jobs được tạo đúng cách
