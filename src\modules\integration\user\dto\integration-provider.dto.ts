import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsNumber, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho query danh sách integration providers
 */
export class IntegrationProviderQueryDto {
  @ApiProperty({
    description: 'Số trang hiện tại',
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Số trang phải là số' })
  @Min(1, { message: 'Số trang phải lớn hơn 0' })
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng bản ghi trên mỗi trang',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Số lượng bản ghi phải là số' })
  @Min(1, { message: '<PERSON><PERSON> lượng bản ghi phải lớn hơn 0' })
  limit?: number = 10;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm theo tên hoặc type',
    example: 'zalo',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Từ khóa tìm kiếm phải là chuỗi' })
  search?: string;

  @ApiProperty({
    description: 'Lọc theo danh mục',
    example: 'chat',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Danh mục phải là chuỗi' })
  category?: string;

  @ApiProperty({
    description: 'Trường sắp xếp',
    example: 'createdAt',
    enum: ['createdAt', 'updatedAt', 'displayName', 'type'],
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Trường sắp xếp phải là chuỗi' })
  sortBy?: string = 'createdAt';

  @ApiProperty({
    description: 'Hướng sắp xếp',
    example: 'DESC',
    enum: ['ASC', 'DESC'],
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Hướng sắp xếp phải là chuỗi' })
  sortDirection?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * DTO cho tạo mới integration provider
 */
export class CreateIntegrationProviderDto {
  @ApiProperty({
    description: 'Mã định danh duy nhất cho loại tích hợp',
    example: 'zalo_oa',
  })
  @IsString({ message: 'Type phải là chuỗi' })
  type: string;

  @ApiProperty({
    description: 'Tên hiển thị của nhà cung cấp tích hợp',
    example: 'Zalo Official Account',
  })
  @IsString({ message: 'Tên hiển thị phải là chuỗi' })
  displayName: string;

  @ApiProperty({
    description: 'Schema cấu hình đầu vào dưới dạng JSON',
    example: {
      fields: [
        {
          name: 'accessToken',
          type: 'string',
          required: true,
          label: 'Access Token'
        }
      ]
    },
  })
  @IsObject({ message: 'Config schema phải là object' })
  configSchema: Record<string, any>;

  @ApiProperty({
    description: 'Schema dùng cho cấu hình MCP dưới dạng JSON',
    example: {
      mcp_type: 'oauth',
      endpoints: {
        auth: '/oauth/authorize',
        token: '/oauth/token'
      }
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'MCP schema phải là object' })
  mcpSchema?: Record<string, any>;

  @ApiProperty({
    description: 'Đường dẫn icon đại diện',
    example: '/icons/zalo.png',
  })
  @IsString({ message: 'Icon phải là chuỗi' })
  icon: string;

  @ApiProperty({
    description: 'Danh mục nhà cung cấp',
    example: 'chat',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Category phải là chuỗi' })
  category?: string;
}

/**
 * DTO cho cập nhật integration provider
 */
export class UpdateIntegrationProviderDto {
  @ApiProperty({
    description: 'Tên hiển thị của nhà cung cấp tích hợp',
    example: 'Zalo Official Account Updated',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên hiển thị phải là chuỗi' })
  displayName?: string;

  @ApiProperty({
    description: 'Schema cấu hình đầu vào dưới dạng JSON',
    example: {
      fields: [
        {
          name: 'accessToken',
          type: 'string',
          required: true,
          label: 'Access Token'
        }
      ]
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Config schema phải là object' })
  configSchema?: Record<string, any>;

  @ApiProperty({
    description: 'Schema dùng cho cấu hình MCP dưới dạng JSON',
    example: {
      mcp_type: 'oauth',
      endpoints: {
        auth: '/oauth/authorize',
        token: '/oauth/token'
      }
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'MCP schema phải là object' })
  mcpSchema?: Record<string, any>;

  @ApiProperty({
    description: 'Đường dẫn icon đại diện',
    example: '/icons/zalo-updated.png',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Icon phải là chuỗi' })
  icon?: string;

  @ApiProperty({
    description: 'Danh mục nhà cung cấp',
    example: 'chat',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Category phải là chuỗi' })
  category?: string;
}

/**
 * DTO response cho integration provider
 */
export class IntegrationProviderResponseDto {
  @ApiProperty({
    description: 'ID của integration provider',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Mã định danh duy nhất cho loại tích hợp',
    example: 'GHTK',
    enum: ['GMAIL', 'OPENAI', 'XAI', 'GEMINI', 'ANTHROPIC', 'DEEPSEEK', 'FACEBOOK_PAGE', 'FACEBOOK_PERSONAL', 'WEBSITE', 'ZALO_OA', 'GHTK', 'GHN', 'AHAMOVE', 'MB_BANK', 'OCB_BANK', 'KL_BANK', 'ACB_BANK', 'NONE', 'EMAIL_SMTP', 'EMAIL_TWILIO_SENDGRID', 'EMAIL_GMAIL', 'EMAIL_OUTLOOK', 'SMS_FPT', 'SMS_TWILIO', 'SMS_VONAGE', 'SMS_SPEED', 'GOOGLE_CALENDAR', 'GOOGLE_SHEETS', 'GOOGLE_DOCS'],
  })
  type: string;

  @ApiProperty({
    description: 'Schema dùng cho cấu hình MCP dưới dạng JSON',
    example: {
      mcp_type: 'oauth',
      endpoints: {
        auth: '/oauth/authorize',
        token: '/oauth/token'
      }
    },
    required: false,
  })
  mcpSchema?: Record<string, any> | null;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: *************,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (timestamp)',
    example: *************,
    required: false,
  })
  updatedAt?: number | null;

  @ApiProperty({
    description: 'ID người tạo bản ghi',
    example: 1,
    required: false,
  })
  createdBy?: number | null;

  @ApiProperty({
    description: 'ID người cập nhật bản ghi',
    example: 1,
    required: false,
  })
  updatedBy?: number | null;
}
