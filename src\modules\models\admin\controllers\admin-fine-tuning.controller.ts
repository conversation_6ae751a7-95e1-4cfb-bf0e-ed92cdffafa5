import { Controller, Post, Body, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto } from '@common/response';
import { AdminFineTuningService } from '../services/admin-fine-tuning.service';
import { AdminValidateDataFineTuneDto, AdminValidationResultDto } from '../dto/admin-fine-tuning-validation/admin-validate-data-fine-tune.dto';
import { AdminExecuteFineTuningDto, AdminExecuteFineTuningResponseDto } from '../dto/admin-fine-tuning-execution/admin-execute-fine-tuning.dto';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { MODELS_ERROR_CODES } from '@modules/models/exceptions/models.exception';
import { ErrorCode } from '@common/exceptions';

/**
 * Controller xử lý fine-tuning cho admin
 */
@ApiTags('Admin Fine-Tuning')
@Controller('admin/fine-tuning')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth()
export class AdminFineTuningController {
  constructor(
    private readonly adminFineTuningService: AdminFineTuningService,
  ) {}

  /**
   * Validate dataset cho fine-tuning (Admin)
   */
  @Post('validate')
  @ApiOperation({
    summary: 'Validate dataset cho fine-tuning (Admin)',
    description: `
    Validate dataset và tính toán token usage cho admin fine-tuning.
    
    **Đặc điểm Admin API:**
    - Không tính phí/trừ R-Points
    - Chỉ lưu lại thông tin token usage
    - Admin có thể fine-tune cho system models
    - Trả về cost estimate chỉ để tham khảo
    
    **Quy trình:**
    1. Validate dataset tồn tại và hợp lệ
    2. Validate model compatibility
    3. Tính toán token usage và cost estimate
    4. Trả về thông tin validation chi tiết
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Dataset validation thành công',
    type: ApiResponseDto<AdminValidationResultDto>,
  })
  @ApiErrorResponse(
    MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND,
    MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async validateDataset(
    @Req() req: any,
    @Body() validateDto: AdminValidateDataFineTuneDto,
  ): Promise<ApiResponseDto<AdminValidationResultDto>> {
    const employeeId = req.user.id;
    return this.adminFineTuningService.validateDataset(employeeId, validateDto);
  }

  /**
   * Execute fine-tuning job (Admin)
   */
  @Post('execute')
  @ApiOperation({
    summary: 'Execute fine-tuning job (Admin)',
    description: `
    Thực hiện fine-tuning job cho admin.
    
    **Đặc điểm Admin API:**
    - Không tính phí/trừ R-Points
    - Sử dụng system integration keys
    - Model được tạo thuộc về system (userId = null)
    - Tự động monitor job progress qua Redis queue
    
    **Quy trình:**
    1. Validate dataset và model
    2. Tính toán token usage (không trừ phí)
    3. Lấy system integration key
    4. Upload files và tạo job với provider
    5. Lưu model và metadata vào database
    6. Tạo Redis monitoring job
    7. Trả về thông tin job
    
    **Lưu ý:**
    - Job sẽ được monitor tự động
    - Model sẽ được activate khi hoàn thành
    - Admin có thể theo dõi progress qua monitoring APIs
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Fine-tuning job đã được tạo thành công',
    type: ApiResponseDto<AdminExecuteFineTuningResponseDto>,
  })
  @ApiErrorResponse(
    MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_NOT_FOUND,
    MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async executeFineTuning(
    @Req() req: any,
    @Body() executeDto: AdminExecuteFineTuningDto,
  ): Promise<ApiResponseDto<AdminExecuteFineTuningResponseDto>> {
    const employeeId = req.user.id;
    return this.adminFineTuningService.executeFineTuning(employeeId, executeDto);
  }
}
