import { Injectable, Logger } from '@nestjs/common';
import { get_encoding, encoding_for_model } from 'tiktoken';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { ProviderLlmEnum } from '../constants';

/**
 * Service đơn giản để đếm token từ text
 */
@Injectable()
export class TokenCounterService {
  private readonly logger = new Logger(TokenCounterService.name);

  /**
   * <PERSON>à<PERSON> chính để đếm token - API đơn giản
   * @param text Văn bản cần đếm token
   * @param provider Provider enum (OPENAI, GEMINI, etc.)
   * @param modelId ID của model (vd: 'gpt-4', 'gemini-pro')
   * @param apiKey API key (bắt buộc cho GEMINI, optional cho OPENAI)
   * @returns Promise<number> Số token
   */
  async countTokens(
    text: string,
    provider: ProviderLlmEnum,
    modelId: string,
    apiKey?: string
  ): Promise<number> {
    try {
      this.logger.debug(`Counting tokens for provider: ${provider}, model: ${modelId}, text length: ${text.length}`);

      switch (provider) {
        case ProviderLlmEnum.OPENAI:
          return this.countTokensForModel(text, modelId);

        case ProviderLlmEnum.GEMINI:
          if (!apiKey) {
            this.logger.warn(`API key required for GEMINI provider, using estimation`);
            return this.countWithEstimation(text);
          }
          return await this.countTokensForGoogle(text, modelId, apiKey);

        case ProviderLlmEnum.ANTHROPIC:
        case ProviderLlmEnum.DEEPSEEK:
        case ProviderLlmEnum.XAI:
          // Các provider khác sử dụng estimation
          this.logger.debug(`Using estimation for provider: ${provider}`);
          return this.countWithEstimation(text);

        default:
          this.logger.warn(`Unsupported provider: ${provider}, using estimation`);
          return this.countWithEstimation(text);
      }

    } catch (error) {
      this.logger.error(`Error counting tokens for ${provider}/${modelId}: ${error.message}`, error.stack);
      // Fallback to estimation
      return this.countWithEstimation(text);
    }
  }

  /**
   * Đếm token từ text input
   * @param text Văn bản cần đếm token
   * @returns Số token
   */
  countTokensOpenAI(text: string): number {
    try {
      // Thử sử dụng tiktoken trước (độ chính xác cao nhất)
      return this.countWithTiktoken(text);

    } catch (error) {
      this.logger.warn(`Error counting tokens with tiktoken, using estimation: ${error.message}`);
      return this.countWithEstimation(text);
    }
  }

  /**
   * Đếm token cho model cụ thể
   * @param text Văn bản cần đếm token
   * @param modelName Tên model (vd: 'gpt-4', 'gpt-3.5-turbo')
   * @returns Số token
   */
  countTokensForModel(text: string, modelName: string): number {
    try {
      // Sử dụng encoding cho model cụ thể
      const encoding = encoding_for_model(modelName as any);
      const tokens = encoding.encode(text);
      const tokenCount = tokens.length;

      // Giải phóng memory
      encoding.free();

      this.logger.debug(`Tiktoken count for ${modelName}: ${tokenCount} tokens for ${text.length} characters`);
      return tokenCount;

    } catch (error) {
      this.logger.warn(`Tiktoken failed for model ${modelName}: ${error.message}, using default encoding`);
      return this.countTokensOpenAI(text);
    }
  }

  /**
   * Đếm token cho Google models sử dụng Google Generative AI
   * @param text Văn bản cần đếm token
   * @param modelName Tên model Google (vd: 'gemini-pro', 'gemini-1.5-flash')
   * @param apiKey API key của Google
   * @returns Promise<number> Số token
   */
  async countTokensForGoogle(text: string, modelName: string, apiKey: string): Promise<number> {
    try {
      // Khởi tạo Google Generative AI client
      const genAI = new GoogleGenerativeAI(apiKey);
      const model = genAI.getGenerativeModel({ model: modelName });

      // Đếm token sử dụng Google API
      const result = await model.countTokens(text);
      const tokenCount = result.totalTokens;

      this.logger.debug(`Google AI count for ${modelName}: ${tokenCount} tokens for ${text.length} characters`);
      return tokenCount;

    } catch (error) {
      this.logger.warn(`Google AI token counting failed for model ${modelName}: ${error.message}, using estimation`);
      return this.countWithEstimation(text);
    }
  }

  /**
   * Đếm token cho Google models với contents array (hỗ trợ multipart)
   * @param contents Mảng contents (text, images, etc.)
   * @param modelName Tên model Google
   * @param apiKey API key của Google
   * @returns Promise<number> Số token
   */
  async countTokensForGoogleContents(contents: any[], modelName: string, apiKey: string): Promise<number> {
    try {
      const genAI = new GoogleGenerativeAI(apiKey);
      const model = genAI.getGenerativeModel({ model: modelName });

      // Đếm token cho contents array
      const result = await model.countTokens({ contents });
      const tokenCount = result.totalTokens;

      this.logger.debug(`Google AI count for ${modelName} (contents): ${tokenCount} tokens`);
      return tokenCount;

    } catch (error) {
      this.logger.warn(`Google AI token counting failed for contents: ${error.message}`);
      // Fallback: ước lượng dựa trên text content
      const textContent = contents
        .flatMap(content => content.parts || [])
        .filter(part => part.text)
        .map(part => part.text)
        .join(' ');

      return this.countWithEstimation(textContent);
    }
  }

  /**
   * Đếm token sử dụng tiktoken (OpenAI tokenizer)
   */
  private countWithTiktoken(text: string): number {
    try {
      // Sử dụng encoding phổ biến nhất cho GPT-4/3.5
      const encoding = get_encoding('cl100k_base');
      const tokens = encoding.encode(text);
      const tokenCount = tokens.length;

      // Giải phóng memory
      encoding.free();

      this.logger.debug(`Tiktoken count: ${tokenCount} tokens for ${text.length} characters`);
      return tokenCount;

    } catch (error) {
      this.logger.warn(`Tiktoken failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Ước lượng token dựa trên số ký tự
   */
  private countWithEstimation(text: string): number {
    // Phát hiện tiếng Việt
    const isVietnamese = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i.test(text);
    
    // Tỷ lệ ước lượng: tiếng Việt cần nhiều token hơn tiếng Anh
    const divisor = isVietnamese ? 2.5 : 4.0;
    const estimatedTokens = Math.ceil(text.length / divisor);
    
    this.logger.debug(`Estimation count: ${estimatedTokens} tokens for ${text.length} characters (${isVietnamese ? 'Vietnamese' : 'English'})`);
    return estimatedTokens;
  }

  /**
   * Đếm token cho nhiều đoạn text
   * @param texts Mảng các đoạn text
   * @returns Tổng số token
   */
  countTokensForTexts(texts: string[]): number {
    return texts.reduce((total, text) => total + this.countTokensOpenAI(text), 0);
  }

  /**
   * Đếm token tự động dựa trên provider và model
   * @param text Văn bản cần đếm token
   * @param modelName Tên model
   * @param apiKey API key (bắt buộc cho Google models)
   * @returns Promise<number> Số token
   */
  async countTokensAuto(text: string, modelName: string, apiKey?: string): Promise<number> {
    try {
      // Phát hiện provider dựa trên model name
      if (this.isGoogleModel(modelName)) {
        if (!apiKey) {
          this.logger.warn(`API key required for Google model ${modelName}, using estimation`);
          return this.countWithEstimation(text);
        }
        return await this.countTokensForGoogle(text, modelName, apiKey);
      } else {
        // Mặc định sử dụng tiktoken cho OpenAI models
        return this.countTokensForModel(text, modelName);
      }
    } catch (error) {
      this.logger.warn(`Auto token counting failed: ${error.message}, using estimation`);
      return this.countWithEstimation(text);
    }
  }

  /**
   * Kiểm tra xem model có phải là Google model không
   * @param modelName Tên model
   * @returns boolean
   */
  private isGoogleModel(modelName: string): boolean {
    const googleModelPrefixes = [
      'gemini',
      'text-bison',
      'chat-bison',
      'code-bison',
      'codechat-bison',
      'text-unicorn',
      'code-gecko'
    ];

    return googleModelPrefixes.some(prefix =>
      modelName.toLowerCase().includes(prefix.toLowerCase())
    );
  }

  /**
   * Ước lượng chi phí dựa trên số token
   * @param tokenCount Số token
   * @param pricePerToken Giá mỗi token (USD)
   * @returns Chi phí ước lượng
   */
  estimateCost(tokenCount: number, pricePerToken: number): number {
    return tokenCount * pricePerToken;
  }
}
