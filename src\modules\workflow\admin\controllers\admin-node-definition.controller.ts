import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import { NodeDefinitionService } from '../../services/node-definition.service';
import { NodeDefinition, NodeCategory } from '../../entities';
import {
  CreateNodeDefinitionDto,
  UpdateNodeDefinitionDto,
  NodeFilterDto,
  SearchOptionsDto,
  NodeDocumentationDto,
  NodeRegistryStatsDto,
} from '../../dto/node-definition';
import { SWAGGER_API_TAGS } from '../../constants';

/**
 * Admin controller cho Node Definition Registry
 * Quản lý 192 node types với enhanced functionality
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_NODE_DEFINITIONS)
@ApiBearerAuth()
@UseGuards(JwtEmployeeGuard)
@Controller('admin/workflow/node-definitions')
export class AdminNodeDefinitionController {
  constructor(
    private readonly nodeDefinitionService: NodeDefinitionService,
  ) {}

  /**
   * Đăng ký node type mới
   */
  @Post()
  @ApiOperation({ summary: 'Đăng ký node type mới' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Node type đã được đăng ký thành công',
    type: NodeDefinition,
  })
  async registerNodeType(
    @Body() createDto: CreateNodeDefinitionDto,
  ): Promise<ApiResponseDto<NodeDefinition>> {
    const nodeDefinition = await this.nodeDefinitionService.registerNodeType(createDto);
    
    return ApiResponseDto.created(nodeDefinition, 'Node type registered successfully');
  }

  /**
   * Lấy tất cả node definitions với filtering
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách node definitions với filtering' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách node definitions',
    type: [NodeDefinition],
  })
  async getNodeDefinitions(
    @Query() filters: NodeFilterDto,
  ): Promise<ApiResponseDto<{ nodes: NodeDefinition[]; total: number }>> {
    const [nodes, total] = await this.nodeDefinitionService.getNodesWithFilters(filters);
    
    return ApiResponseDto.success({ nodes, total }, 'Node definitions retrieved successfully');
  }

  /**
   * Lấy node definitions theo category
   */
  @Get('category/:category')
  @ApiOperation({ summary: 'Lấy node definitions theo category' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node definitions theo category',
    type: [NodeDefinition],
  })
  async getNodesByCategory(
    @Param('category') category: NodeCategory,
    @Query() filters: NodeFilterDto,
  ): Promise<ApiResponseDto<NodeDefinition[]>> {
    const nodes = await this.nodeDefinitionService.getNodesByCategory(category, filters);
    
    return ApiResponseDto.success(nodes, `Nodes for category '${category}' retrieved successfully`);
  }

  /**
   * Tìm kiếm node definitions
   */
  @Get('search')
  @ApiOperation({ summary: 'Tìm kiếm node definitions' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Kết quả tìm kiếm',
    type: [NodeDefinition],
  })
  async searchNodes(
    @Query('q') query: string,
    @Query() options: SearchOptionsDto,
  ): Promise<ApiResponseDto<NodeDefinition[]>> {
    const nodes = await this.nodeDefinitionService.searchNodes(query, options);
    
    return ApiResponseDto.success(nodes, `Found ${nodes.length} nodes matching '${query}'`);
  }

  /**
   * Bulk register multiple node definitions
   */
  @Post('bulk')
  @ApiOperation({ summary: 'Bulk register multiple node definitions' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Bulk registration completed',
  })
  async bulkRegisterNodes(
    @Body() nodeDefinitions: CreateNodeDefinitionDto[],
  ): Promise<ApiResponseDto<{ registered: number; skipped: number; errors: string[] }>> {
    const result = await this.nodeDefinitionService.bulkRegisterNodes(nodeDefinitions);

    return ApiResponseDto.created(
      result,
      `Bulk registration completed: ${result.registered} registered, ${result.skipped} skipped, ${result.errors.length} errors`
    );
  }

  /**
   * Initialize/refresh node registry with all 192 node types
   */
  @Post('initialize')
  @ApiOperation({ summary: 'Initialize node registry with all predefined nodes' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Registry initialized successfully',
  })
  async initializeRegistry(): Promise<ApiResponseDto<void>> {
    await (this.nodeDefinitionService as any).initializeNodeRegistry();

    return ApiResponseDto.success(undefined, 'Node registry initialized successfully');
  }

  /**
   * Lấy thống kê registry
   */
  @Get('statistics')
  @ApiOperation({ summary: 'Lấy thống kê node registry' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thống kê registry',
    type: NodeRegistryStatsDto,
  })
  async getRegistryStatistics(): Promise<ApiResponseDto<NodeRegistryStatsDto>> {
    const stats = await this.nodeDefinitionService.getRegistryStatistics();

    return ApiResponseDto.success(stats, 'Registry statistics retrieved successfully');
  }

  /**
   * Lấy node definition theo type
   */
  @Get(':type')
  @ApiOperation({ summary: 'Lấy node definition theo type' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node definition details',
    type: NodeDefinition,
  })
  async getNodeByType(
    @Param('type') type: string,
  ): Promise<ApiResponseDto<NodeDefinition>> {
    const node = await this.nodeDefinitionService.getNodeByType(type);
    
    return ApiResponseDto.success(node, 'Node definition retrieved successfully');
  }

  /**
   * Lấy documentation của node
   */
  @Get(':type/documentation')
  @ApiOperation({ summary: 'Lấy documentation chi tiết của node' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node documentation',
    type: NodeDocumentationDto,
  })
  async getNodeDocumentation(
    @Param('type') type: string,
  ): Promise<ApiResponseDto<NodeDocumentationDto>> {
    const documentation = await this.nodeDefinitionService.getNodeDocumentation(type);
    
    return ApiResponseDto.success(documentation, 'Node documentation retrieved successfully');
  }

  /**
   * Cập nhật node definition
   */
  @Put(':type')
  @ApiOperation({ summary: 'Cập nhật node definition' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node definition đã được cập nhật',
    type: NodeDefinition,
  })
  async updateNodeDefinition(
    @Param('type') type: string,
    @Body() updateDto: UpdateNodeDefinitionDto,
  ): Promise<ApiResponseDto<NodeDefinition>> {
    const nodeDefinition = await this.nodeDefinitionService.updateNodeDefinition(type, updateDto);
    
    return ApiResponseDto.updated(nodeDefinition, 'Node definition updated successfully');
  }

  // Deprecate functionality removed - simplified node management

  /**
   * Xóa node definition
   */
  @Delete(':type')
  @ApiOperation({ summary: 'Xóa node definition' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node definition đã được xóa',
  })
  async deleteNodeDefinition(
    @Param('type') type: string,
  ): Promise<ApiResponseDto<null>> {
    await this.nodeDefinitionService.deleteNodeDefinition(type);
    
    return ApiResponseDto.deleted(null, `Node definition '${type}' deleted successfully`);
  }
}
