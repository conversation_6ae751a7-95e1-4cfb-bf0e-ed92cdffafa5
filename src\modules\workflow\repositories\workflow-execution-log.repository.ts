import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { WorkflowExecutionLog } from '../entities';

/**
 * Repository cho WorkflowExecutionLog entity
 * Xử lý các thao tác database liên quan đến workflow execution logs
 */
@Injectable()
export class WorkflowExecutionLogRepository extends Repository<WorkflowExecutionLog> {
  constructor(private dataSource: DataSource) {
    super(WorkflowExecutionLog, dataSource.createEntityManager());
  }

  /**
   * Tạo base query cho WorkflowExecutionLog
   */
  private createBaseQuery() {
    return this.createQueryBuilder('log');
  }

  /**
   * L<PERSON><PERSON> tất cả logs theo execution ID
   * @param workflowExecutionId - ID của workflow execution
   * @returns Promise<WorkflowExecutionLog[]>
   */
  async findByExecutionId(workflowExecutionId: string): Promise<WorkflowExecutionLog[]> {
    return this.createBaseQuery()
      .where('log.workflowExecutionId = :workflowExecutionId', { workflowExecutionId })
      .orderBy('log.timestamp', 'ASC')
      .getMany();
  }

  /**
   * Lấy logs theo execution ID và node ID
   * @param workflowExecutionId - ID của workflow execution
   * @param nodeId - ID của node
   * @returns Promise<WorkflowExecutionLog[]>
   */
  async findByExecutionAndNode(
    workflowExecutionId: string,
    nodeId: string,
  ): Promise<WorkflowExecutionLog[]> {
    return this.createBaseQuery()
      .where('log.workflowExecutionId = :workflowExecutionId', { workflowExecutionId })
      .andWhere('log.nodeId = :nodeId', { nodeId })
      .orderBy('log.timestamp', 'ASC')
      .getMany();
  }

  /**
   * Lấy logs theo execution ID và event type
   * @param workflowExecutionId - ID của workflow execution
   * @param eventType - Loại event
   * @returns Promise<WorkflowExecutionLog[]>
   */
  async findByExecutionAndEventType(
    workflowExecutionId: string,
    eventType: string,
  ): Promise<WorkflowExecutionLog[]> {
    return this.createBaseQuery()
      .where('log.workflowExecutionId = :workflowExecutionId', { workflowExecutionId })
      .andWhere('log.eventType = :eventType', { eventType })
      .orderBy('log.timestamp', 'ASC')
      .getMany();
  }

  /**
   * Lấy logs với phân trang
   * @param workflowExecutionId - ID của workflow execution
   * @param page - Số trang
   * @param limit - Số lượng item per page
   * @returns Promise<[WorkflowExecutionLog[], number]>
   */
  async findWithPagination(
    workflowExecutionId: string,
    page: number = 1,
    limit: number = 50,
  ): Promise<[WorkflowExecutionLog[], number]> {
    const offset = (page - 1) * limit;

    return this.createBaseQuery()
      .where('log.workflowExecutionId = :workflowExecutionId', { workflowExecutionId })
      .orderBy('log.timestamp', 'DESC')
      .skip(offset)
      .take(limit)
      .getManyAndCount();
  }

  /**
   * Tạo log entry mới
   * @param logData - Dữ liệu log
   * @returns Promise<WorkflowExecutionLog>
   */
  async createLog(logData: {
    workflowExecutionId: string;
    nodeId: string;
    eventType: string;
    payload?: Record<string, any>;
  }): Promise<WorkflowExecutionLog> {
    const log = this.create({
      workflowExecutionId: logData.workflowExecutionId,
      nodeId: logData.nodeId,
      eventType: logData.eventType,
      payload: logData.payload || null,
    });

    return this.save(log);
  }

  /**
   * Xóa tất cả logs của một execution
   * @param workflowExecutionId - ID của workflow execution
   * @returns Promise<void>
   */
  async deleteByExecutionId(workflowExecutionId: string): Promise<void> {
    await this.createBaseQuery()
      .delete()
      .where('workflowExecutionId = :workflowExecutionId', { workflowExecutionId })
      .execute();
  }

  /**
   * Lấy log cuối cùng của một node trong execution
   * @param workflowExecutionId - ID của workflow execution
   * @param nodeId - ID của node
   * @returns Promise<WorkflowExecutionLog | null>
   */
  async findLatestByExecutionAndNode(
    workflowExecutionId: string,
    nodeId: string,
  ): Promise<WorkflowExecutionLog | null> {
    return this.createBaseQuery()
      .where('log.workflowExecutionId = :workflowExecutionId', { workflowExecutionId })
      .andWhere('log.nodeId = :nodeId', { nodeId })
      .orderBy('log.timestamp', 'DESC')
      .getOne();
  }

  /**
   * Đếm số lượng logs theo event type
   * @param workflowExecutionId - ID của workflow execution
   * @param eventType - Loại event
   * @returns Promise<number>
   */
  async countByEventType(workflowExecutionId: string, eventType: string): Promise<number> {
    return this.createBaseQuery()
      .where('log.workflowExecutionId = :workflowExecutionId', { workflowExecutionId })
      .andWhere('log.eventType = :eventType', { eventType })
      .getCount();
  }
}
