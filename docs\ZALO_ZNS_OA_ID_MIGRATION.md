# Zalo ZNS API Migration: officialAccountId to oaId

## Tổng quan

Tài liệu này mô tả việc cập nhật các API Zalo ZNS để sử dụng `oaId` (UUID từ Integration table) thay vì `officialAccountId` (numeric ID từ bảng cũ).

## Lý do thay đổi

- **Tích hợp với Integration Module**: Dữ liệu Zalo Official Account giờ được lưu trong bảng `integration` thay vì bảng riêng biệt
- **UUID thay vì Numeric ID**: `oaId` là UUID string từ Integration table, thay vì numeric ID
- **Tính nhất quán**: Tất cả các API Zalo khác đã sử dụng `oaId`
- **Bảo mật**: UUID khó đoán hơn so với numeric ID tuần tự

## Các thay đổi đã thực hiện

### 1. ZaloZnsController

**Các endpoint đã cập nhật:**

| Endpoint cũ | Endpoint mới | Mô tả |
|-------------|--------------|-------|
| `GET /marketing/zalo/zns/templates/:id` | `GET /marketing/zalo/zns/templates/:oaId` | Lấy danh sách template |
| `GET /marketing/zalo/zns/templates/zalo-api/:id` | `GET /marketing/zalo/zns/templates/zalo-api/:oaId` | Lấy template từ Zalo API |
| `GET /marketing/zalo/zns/templates/:officialAccountId/:templateId` | `GET /marketing/zalo/zns/templates/:oaId/:templateId` | Chi tiết template |
| `GET /marketing/zalo/zns/:id/quota` | `GET /marketing/zalo/zns/:oaId/quota` | Thông tin quota |
| `POST /marketing/zalo/zns/:id/templates` | `POST /marketing/zalo/zns/:oaId/templates` | Đăng ký template |

**Thay đổi parameter:**
- `@Param('id', ParseIntPipe) id: number` → `@Param('oaId') oaId: string`
- `@Param('officialAccountId', ParseIntPipe) officialAccountId: number` → `@Param('oaId') oaId: string`

### 2. ZaloZnsService

**Các method đã xóa (không còn cần thiết):**
- `getZnsTemplatesById()`
- `getZnsTemplatesFromZaloApiById()`
- `getZnsTemplateDetailById()`
- `getZnsQuotaById()`
- `registerZnsTemplateById()`

**Các method được sử dụng:**
- `getZnsTemplates(userId, oaId, queryDto)`
- `getZnsTemplatesFromZaloApi(userId, oaId, queryDto)`
- `getZnsTemplateDetail(userId, oaId, templateId)`
- `getZnsQuota(userId, oaId)`
- `registerZnsTemplate(userId, oaId, registerDto)`

### 3. Swagger Documentation

**Cập nhật ApiParam:**
```typescript
// Cũ
@ApiParam({
  name: 'id',
  description: 'ID của Zalo Official Account (khóa chính)',
  example: 1
})

// Mới
@ApiParam({
  name: 'oaId',
  description: 'ID của Zalo Official Account (UUID từ Integration)',
  example: 'oa123456789'
})
```

### 4. Controllers khác

**Đã kiểm tra và xác nhận sử dụng oaId đúng:**
- ✅ `ZaloZnsCampaignController` - Đã sử dụng oaId
- ✅ `ZaloZnsImageController` - Đã sử dụng oaId

## Ví dụ sử dụng

### Trước khi thay đổi
```bash
# Lấy danh sách template
GET /api/marketing/zalo/zns/templates/123

# Lấy quota
GET /api/marketing/zalo/zns/123/quota

# Đăng ký template
POST /api/marketing/zalo/zns/123/templates
```

### Sau khi thay đổi
```bash
# Lấy danh sách template
GET /api/marketing/zalo/zns/templates/oa123456789

# Lấy quota
GET /api/marketing/zalo/zns/oa123456789/quota

# Đăng ký template
POST /api/marketing/zalo/zns/oa123456789/templates
```

## Tác động

### Breaking Changes
- ⚠️ **API URL thay đổi**: Client cần cập nhật URL endpoints
- ⚠️ **Parameter type thay đổi**: Từ `number` sang `string`
- ⚠️ **oaId format**: Phải sử dụng UUID từ Integration table

### Backward Compatibility
- ❌ **Không tương thích ngược**: API cũ sẽ không hoạt động
- ✅ **Service methods**: Các method chính vẫn hoạt động với oaId

## Migration Guide

### Cho Frontend/Client
1. **Cập nhật URL endpoints**: Thay numeric ID bằng oaId string
2. **Lấy oaId từ Integration**: Sử dụng API Integration để lấy oaId
3. **Cập nhật type definitions**: Thay `number` thành `string` cho oaId

### Cho Backend
1. **Service calls**: Sử dụng trực tiếp các method với oaId
2. **Validation**: Đảm bảo oaId là UUID hợp lệ
3. **Error handling**: Cập nhật error messages phù hợp

## Testing

### Test Cases đã cập nhật
- ✅ `test-zalo-zns-image-api.http` - Sử dụng oaId
- ✅ Swagger documentation examples - Sử dụng oaId

### Cần test
- [ ] Integration tests với oaId mới
- [ ] Error handling với invalid oaId
- [ ] Performance với UUID lookup

## Tài liệu liên quan

- [Zalo ZNS Image Upload API](./zalo-zns-image-upload-api.md)
- [Integration Module Development Plan](./plan/20240620-integration-module-development-plan.md)
- [Swagger API Documentation](../src/modules/marketing/user/docs/swagger-api-documentation.md)

## Checklist hoàn thành

- [x] Cập nhật ZaloZnsController endpoints
- [x] Xóa các method *ById trong ZaloZnsService
- [x] Kiểm tra ZaloZnsCampaignController
- [x] Kiểm tra ZaloZnsImageController
- [x] Cập nhật Swagger documentation
- [x] Kiểm tra test files
- [x] Tạo migration documentation

## Lưu ý quan trọng

1. **oaId phải tồn tại**: Đảm bảo oaId có trong Integration table
2. **Permissions**: Kiểm tra user có quyền truy cập OA không
3. **Token validation**: Access token phải hợp lệ cho OA
4. **Error messages**: Cập nhật thông báo lỗi phù hợp với oaId
