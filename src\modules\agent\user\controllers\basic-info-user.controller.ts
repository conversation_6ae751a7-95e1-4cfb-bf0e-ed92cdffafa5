import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ErrorCode } from '@common/exceptions';
import { ApiResponseDto } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { BasicInfoUserService } from '../services/basic-info-user.service';
import {
  UpdateBasicInfoDto,
  BasicInfoResponseDto,
} from '../dto/basic-info';

/**
 * Controller xử lý các API liên quan đến basic info của agent cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents/:id/basic-info')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class BasicInfoUserController {
  constructor(private readonly basicInfoUserService: BasicInfoUserService) { }

  // ==================== BASIC INFO ENDPOINTS ====================

  /**
   * Lấy thông tin basic info của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @returns Thông tin basic info của agent
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy thông tin basic info của agent',
    description: 'Lấy thông tin cơ bản của agent bao gồm tên, avatar, model config, instruction'
  })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin basic info thành công',
    type: ApiResponseDto<BasicInfoResponseDto>,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getBasicInfo(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<BasicInfoResponseDto>> {
    const result = await this.basicInfoUserService.getBasicInfo(id, userId);
    return ApiResponseDto.success(result, 'Lấy thông tin basic info thành công');
  }

  /**
   * Cập nhật basic info của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param updateDto Thông tin basic info cần cập nhật
   * @returns Thông tin basic info đã cập nhật và avatar upload URL (nếu có)
   */
  @Put()
  @ApiOperation({
    summary: 'Cập nhật basic info của agent',
    description: 'Cập nhật thông tin cơ bản của agent như tên, avatar, model config, instruction'
  })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật basic info thành công',
    type: ApiResponseDto<{ id: string; urlUpload: string | null }>,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.INVALID_MODEL_CONFIG_FIELD,
    AGENT_ERROR_CODES.INVALID_S3_KEY,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateBasicInfo(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateBasicInfoDto,
  ): Promise<ApiResponseDto<{id: string, urlUpload: string | null }>> {
    const result = await this.basicInfoUserService.updateBasicInfo(id, userId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật basic info thành công');
  }
}
