import { ProviderLlmEnum } from '@/modules/models/constants';
import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';

/**
 * DTO cho response của user key LLM
 */
@Exclude()
export class KeyLlmAdminResponseDto {
  /**
   * UUID của user key LLM
   */
  @ApiProperty({
    description: 'UUID của user key LLM',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Expose()
  id: string;

  /**
   * Tên định danh cho key
   */
  @ApiProperty({
    description: 'Tên định danh cho key',
    example: 'My OpenAI Key',
  })
  @Expose()
  name: string;

  /**
   * Nhà cung cấp LLM
   */
  @ApiProperty({
    description: 'Nhà cung cấp LLM',
    enum: ProviderLlmEnum,
    example: ProviderLlmEnum.OPENAI,
  })
  @Expose()
  provider: ProviderLlmEnum;

  /**
   * Th<PERSON><PERSON> gian tạ<PERSON> (epoch millis)
   */
  @ApiProperty({
    description: '<PERSON>h<PERSON><PERSON> gian tạo (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  createdAt: number;
}
