import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của việc bật/tắt trạng thái Facebook Page
 */
export class ToggleFacebookPageStatusResponseDto {
  /**
   * ID của Facebook Page
   */
  @ApiProperty({
    description: 'ID của Facebook Page',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  facebookPageId: string;

  /**
   * Tên của Facebook Page
   */
  @ApiProperty({
    description: 'Tên của Facebook Page',
    example: 'Trang Facebook của tôi'
  })
  pageName: string;

  /**
   * Trạng thái hoạt động mới của Facebook Page
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động mới của Facebook Page',
    example: true
  })
  isActive: boolean;

  /**
   * Thông báo kết quả
   */
  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Facebook Page đã được bật thành công'
  })
  message: string;
}
