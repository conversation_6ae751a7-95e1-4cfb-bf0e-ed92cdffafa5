# SMS Integration Migration Summary

## Tổng quan
Migration từ `sms_server_configurations` sang `Integration` entity với encryption sử dụng KeyPairEncryptionService.

## Thay đổi chính

### 1. Database Schema Changes

#### Bảng mới/cập nhật:
- ✅ `integration` - B<PERSON><PERSON> ch<PERSON>h cho tất cả integrations
- ✅ `integration_providers` - Thêm SMS provider types
- ✅ `sms_campaign_admin` - Cậ<PERSON> nhật từ `sms_server_id` sang `sms_integration_id`

#### Provider Types mới:
```sql
SMS_FPT     - FPT SMS Brandname
SMS_TWILIO  - Twilio International SMS  
SMS_VONAGE  - Vonage (Nexmo) SMS
SMS_SPEED   - SpeedSMS Provider
```

### 2. Data Structure Changes

#### Trước (SmsServerConfiguration):
```sql
sms_server_configurations:
- id (integer PK)
- user_id (integer)
- provider_name (varchar)
- api_key (varchar) - Không mã hóa
- endpoint (varchar)
- additional_settings (jsonb) - <PERSON>hông mã hóa
```

#### Sau (Integration):
```sql
integration:
- id (UUID PK)
- integration_name (varchar)
- type_id (integer FK)
- user_id (integer, nullable)
- owned_type (enum: USER/ADMIN)
- employee_id (integer, nullable)
- encrypted_config (text) - Dữ liệu nhạy cảm được mã hóa
- secret_key (varchar) - Public key để giải mã
- metadata (jsonb) - Dữ liệu không nhạy cảm
```

### 3. Encryption Strategy

#### Dữ liệu được mã hóa (encryptedConfig):
- `clientId`, `clientSecret` (FPT SMS)
- `authToken` (Twilio)
- `apiKey`, `apiSecret` (Vonage)
- Các credentials nhạy cảm khác

#### Dữ liệu không mã hóa (metadata):
- `brandName` (FPT SMS)
- `accountSid` (Twilio)
- `phoneNumber`, `messagingServiceSid`
- `apiUrl`, `endpoint`

### 4. Code Changes

#### DTO Updates:
- `smsServerId: number` → `smsIntegrationId: string`
- Validation từ `@IsNumber()` → `@IsString()`

#### Entity Updates:
- `SmsCampaignAdmin.smsServerId` → `SmsCampaignAdmin.smsIntegrationId`
- Column type: `integer` → `uuid`

#### Service Updates:
- Import `IntegrationRepository`, `KeyPairEncryptionService`
- Method `getSmsConfigFromIntegration()` để giải mã
- Fallback strategy khi decryption thất bại

### 5. API Changes

#### Request Format:
```json
// Trước
{
  "smsServerId": 1,
  "templateId": 456,
  "userIds": [1, 2, 3]
}

// Sau  
{
  "smsIntegrationId": "uuid-string",
  "templateId": 456,
  "userIds": [1, 2, 3]
}
```

#### Validation Changes:
- Kiểm tra Integration tồn tại
- Validate `encryptedConfig` và `secretKey` có giá trị
- Error messages cập nhật

### 6. Migration Process

#### Bước 1: Chuẩn bị
1. Thêm SMS provider types vào `integration_providers`
2. Thêm column `sms_integration_id` vào `sms_campaign_admin`

#### Bước 2: Migrate dữ liệu
1. Lặp qua tất cả `sms_server_configurations`
2. Tạo Integration record mới với:
   - UUID mới
   - Metadata từ non-sensitive fields
   - EncryptedConfig từ sensitive fields (cần encryption thực tế)
   - SecretKey từ KeyPairEncryptionService
3. Cập nhật `sms_campaign_admin.sms_integration_id`

#### Bước 3: Cleanup (sau khi test)
1. Drop column `sms_server_id`
2. Set `sms_integration_id` NOT NULL
3. Thêm foreign key constraint

### 7. Testing Checklist

- [ ] Migration script chạy thành công
- [ ] Tất cả SMS campaigns có `sms_integration_id` hợp lệ
- [ ] Encryption/decryption hoạt động đúng
- [ ] Fallback strategy hoạt động khi decryption thất bại
- [ ] API endpoints hoạt động với `smsIntegrationId`
- [ ] Validation errors trả về đúng message
- [ ] Job queue nhận đúng SMS config data

### 8. Rollback Plan

Nếu cần rollback:
1. Giữ nguyên column `sms_server_id` trong `sms_campaign_admin`
2. Revert code changes về sử dụng `SmsServerConfigurationRepository`
3. Xóa Integration records đã tạo (nếu cần)

### 9. Production Deployment

#### Pre-deployment:
1. Backup database
2. Test migration script trên staging
3. Verify encryption keys trong environment

#### Deployment:
1. Deploy code changes
2. Run migration script
3. Verify API functionality
4. Monitor error logs

#### Post-deployment:
1. Test SMS sending functionality
2. Verify campaign creation works
3. Check job queue processing
4. Monitor for decryption errors

### 10. Benefits

✅ **Security**: Sensitive data được mã hóa
✅ **Consistency**: Tất cả integrations dùng chung structure
✅ **Scalability**: Dễ thêm SMS providers mới
✅ **Maintainability**: Code cleaner với unified approach
✅ **Flexibility**: Metadata structure linh hoạt cho từng provider

### 11. Risks & Mitigation

🚨 **Risk**: Encryption key loss → Data không thể giải mã
✅ **Mitigation**: Backup keys, fallback strategy

🚨 **Risk**: Migration script lỗi → Data corruption  
✅ **Mitigation**: Database backup, test thoroughly

🚨 **Risk**: Performance impact → Slower API response
✅ **Mitigation**: Cache decrypted configs, optimize queries
