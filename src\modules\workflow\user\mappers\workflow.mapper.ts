import { Workflow } from '@modules/workflow/entities';
import { WorkflowListItemDto, WorkflowDetailDto } from '../dto';

/**
 * Mapper cho Workflow entity
 * Following agent.mapper patterns
 */
export class WorkflowMapper {
  /**
   * Map Workflow entity to WorkflowListItemDto
   */
  static toListItemDto(workflow: Workflow): WorkflowListItemDto {
    const nodeCount = workflow.definition?.nodes?.length || 0;
    const edgeCount = workflow.definition?.edges?.length || 0;

    return {
      id: workflow.id,
      name: workflow.name,
      isActive: workflow.isActive,
      createdAt: workflow.createdAt,
      updatedAt: workflow.updatedAt,
      nodeCount,
      edgeCount,
    };
  }

  /**
   * Map Workflow entity to WorkflowDetailDto
   */
  static toDetailDto(workflow: Workflow): WorkflowDetailDto {
    return {
      id: workflow.id,
      userId: workflow.userId || 0,
      employeeId: workflow.employeeId || undefined,
      name: workflow.name,
      isActive: workflow.isActive,
      definition: {
        nodes: workflow.definition?.nodes || [],
        edges: workflow.definition?.edges || [],
        metadata: workflow.definition?.metadata || {}
      },
      createdAt: workflow.createdAt,
      updatedAt: workflow.updatedAt,
    };
  }

  /**
   * Map array of Workflow entities to WorkflowListItemDto array
   */
  static toListItemDtos(workflows: Workflow[]): WorkflowListItemDto[] {
    return workflows.map(workflow => this.toListItemDto(workflow));
  }
}
