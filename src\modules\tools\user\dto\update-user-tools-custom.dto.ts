import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsObject, IsEnum, IsNotEmpty, MaxLength } from 'class-validator';
import { ApiKeyLocationEnum } from '../../constants';

/**
 * DTO cho việc cập nhật công cụ tùy chỉnh của người dùng
 */
export class UpdateUserToolsCustomDto {
  /**
   * Tên công cụ
   */
  @ApiProperty({
    description: 'Tên công cụ',
    example: 'Tìm kiếm sản phẩm',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên công cụ phải là chuỗi' })
  @IsNotEmpty({ message: 'Tên công cụ không được để trống' })
  @MaxLength(100, { message: 'Tên công cụ không được vượt quá 100 ký tự' })
  toolName?: string;

  /**
   * <PERSON><PERSON> tả công cụ
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả công cụ',
    example: 'Công cụ tìm kiếm sản phẩm trong hệ thống',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả công cụ phải là chuỗi' })
  @MaxLength(500, { message: 'Mô tả công cụ không được vượt quá 500 ký tự' })
  toolDescription?: string;

  /**
   * URL cơ sở của API
   */
  @ApiProperty({
    description: 'URL cơ sở của API',
    example: 'https://api.example.com',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'URL cơ sở phải là chuỗi' })
  @MaxLength(255, { message: 'URL cơ sở không được vượt quá 255 ký tự' })
  baseUrl?: string;

  /**
   * Schema đầu vào theo chuẩn JSON Schema
   */
  @ApiProperty({
    description: 'Schema đầu vào theo chuẩn JSON Schema',
    example: {
      type: 'object',
      properties: {
        query_param: {
          type: 'object',
          description: 'Các tham số truy vấn',
          properties: {
            q: { type: 'string', description: 'Từ khóa tìm kiếm' }
          }
        }
      }
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Input schema phải là đối tượng' })
  inputSchema?: Record<string, unknown>;
}
