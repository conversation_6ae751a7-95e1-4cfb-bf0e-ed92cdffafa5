import { ConversionMapper } from '../conversion.mapper';
import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';

describe('ConversionMapper', () => {
  describe('createDefaultConvertConfig', () => {
    it('should create default config with email and phone', () => {
      const defaultConfig = ConversionMapper.createDefaultConvertConfig();
      
      expect(defaultConfig).toHaveLength(3);
      
      const emailField = defaultConfig.find(item => item.name === 'customer_email');
      const phoneField = defaultConfig.find(item => item.name === 'customer_phone');

      expect(emailField).toBeDefined();
      expect(emailField?.deletable).toBe(false);
      expect(emailField?.required).toBe(true);
      expect(emailField?.pattern).toContain('@');

      expect(phoneField).toBeDefined();
      expect(phoneField?.deletable).toBe(false);
      expect(phoneField?.required).toBe(true);
      expect(phoneField?.pattern).toBe('^\\+?[1-9][0-9]{0,15}$');
      expect(phoneField?.minLength).toBe(10);
      expect(phoneField?.maxLength).toBe(15);
    });
  });

  describe('ensureRequiredFields', () => {
    it('should add email and phone if missing', () => {
      const config: ConvertConfig[] = [
        {
          name: 'customer_name',
          type: 'string',
          description: 'Tên khách hàng',
          required: true,
          deletable: true
        }
      ];

      const result = ConversionMapper.ensureRequiredFields(config);
      
      expect(result).toHaveLength(3);
      
      const emailField = result.find(item => item.name === 'customer_email');
      const phoneField = result.find(item => item.name === 'customer_phone');
      
      expect(emailField).toBeDefined();
      expect(emailField?.deletable).toBe(false);
      
      expect(phoneField).toBeDefined();
      expect(phoneField?.deletable).toBe(false);
    });

    it('should mark existing email and phone as non-deletable', () => {
      const config: ConvertConfig[] = [
        {
          name: 'customer_email',
          type: 'string',
          description: 'Email',
          required: true,
          deletable: true // Sẽ được chuyển thành false
        },
        {
          name: 'customer_phone',
          type: 'string',
          description: 'Phone',
          required: true,
          deletable: true // Sẽ được chuyển thành false
        }
      ];

      const result = ConversionMapper.ensureRequiredFields(config);
      
      const emailField = result.find(item => item.name === 'customer_email');
      const phoneField = result.find(item => item.name === 'customer_phone');
      
      expect(emailField?.deletable).toBe(false);
      expect(phoneField?.deletable).toBe(false);
    });
  });

  describe('validateConvertConfig', () => {
    it('should return false if email is missing', () => {
      const config: ConvertConfig[] = [
        {
          name: 'customer_phone',
          type: 'string',
          description: 'Phone',
          required: true
        }
      ];

      const result = ConversionMapper.validateConvertConfig(config);
      expect(result).toBe(false);
    });

    it('should return false if phone is missing', () => {
      const config: ConvertConfig[] = [
        {
          name: 'customer_email',
          type: 'string',
          description: 'Email',
          required: true
        }
      ];

      const result = ConversionMapper.validateConvertConfig(config);
      expect(result).toBe(false);
    });

    it('should return true if both email and phone are present', () => {
      const config: ConvertConfig[] = [
        {
          name: 'customer_email',
          type: 'string',
          description: 'Email',
          required: true
        },
        {
          name: 'customer_phone',
          type: 'string',
          description: 'Phone',
          required: true
        }
      ];

      const result = ConversionMapper.validateConvertConfig(config);
      expect(result).toBe(true);
    });

    it('should return false for duplicate names', () => {
      const config: ConvertConfig[] = [
        {
          name: 'customer_email',
          type: 'string',
          description: 'Email',
          required: true
        },
        {
          name: 'customer_phone',
          type: 'string',
          description: 'Phone',
          required: true
        },
        {
          name: 'customer_email', // Duplicate
          type: 'string',
          description: 'Email 2',
          required: false
        }
      ];

      const result = ConversionMapper.validateConvertConfig(config);
      expect(result).toBe(false);
    });
  });

  describe('toJsonSchema', () => {
    it('should convert config to valid JSON Schema format', () => {
      const config: ConvertConfig[] = [
        {
          name: 'customer_email',
          type: 'string',
          description: 'Email khách hàng',
          required: true,
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'
        },
        {
          name: 'customer_phone',
          type: 'string',
          description: 'Số điện thoại',
          required: true,
          pattern: '^\\+?[1-9][0-9]{0,15}$',
          minLength: 10,
          maxLength: 15
        },
        {
          name: 'age',
          type: 'number',
          description: 'Tuổi',
          required: false,
          minimum: 0,
          maximum: 150
        }
      ];

      const schema = ConversionMapper.toJsonSchema(config);

      expect(schema.type).toBe('object');
      expect(schema.properties).toBeDefined();
      expect(schema.required).toEqual(['customer_email', 'customer_phone']);
      expect(schema.additionalProperties).toBe(false);

      // Kiểm tra email có format
      expect(schema.properties.customer_email.type).toBe('string');
      expect(schema.properties.customer_email.format).toBe('email');
      expect(schema.properties.customer_email.pattern).toBeUndefined(); // Không có pattern vì đã có format

      // Kiểm tra phone có pattern và length
      expect(schema.properties.customer_phone.type).toBe('string');
      expect(schema.properties.customer_phone.pattern).toBe('^\\+?[1-9][0-9]{0,15}$');
      expect(schema.properties.customer_phone.minLength).toBe(10);
      expect(schema.properties.customer_phone.maxLength).toBe(15);

      // Kiểm tra number properties
      expect(schema.properties.age.type).toBe('number');
      expect(schema.properties.age.minimum).toBe(0);
      expect(schema.properties.age.maximum).toBe(150);
    });

    it('should create minimal schema with only required fields', () => {
      const config: ConvertConfig[] = [
        {
          name: 'customer_email',
          type: 'string',
          description: 'Email',
          required: true,
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'
        },
        {
          name: 'customer_phone',
          type: 'string',
          description: 'Phone',
          required: true,
          pattern: '^\\+?[1-9][0-9]{0,15}$',
          minLength: 10,
          maxLength: 15
        }
      ];

      const schema = ConversionMapper.toJsonSchema(config);

      expect(schema).toEqual({
        type: 'object',
        properties: {
          customer_email: {
            type: 'string',
            format: 'email'
          },
          customer_phone: {
            type: 'string',
            pattern: '^\\+?[1-9][0-9]{0,15}$',
            minLength: 10,
            maxLength: 15
          }
        },
        required: ['customer_email', 'customer_phone'],
        additionalProperties: false
      });
    });
  });
});
