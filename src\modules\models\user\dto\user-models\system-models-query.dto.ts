import { ProviderLlmEnum } from '@/modules/models/constants/provider.enum';
import { QueryDto } from '@common/dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsEnum, IsOptional } from 'class-validator';

/**
 * DTO cho việc truy vấn danh sách system models
 */
export class SystemModelsQueryDto extends QueryDto {
  /**
   * Lọc theo nhà cung cấp
   */
  @ApiPropertyOptional({
    description: 'Lọc theo nhà cung cấp',
    enum: ProviderLlmEnum,
    example: ProviderLlmEnum.OPENAI,
  })
  @IsEnum(ProviderLlmEnum, { message: 'Provider phải là một trong các giá trị hợp lệ' })
  provider: ProviderLlmEnum;

  /**
   * Lọc theo hỗ trợ fine-tuning
   */
  @ApiPropertyOptional({
    description: 'Lọc theo hỗ trợ fine-tuning',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  fineTune?: boolean;
}
