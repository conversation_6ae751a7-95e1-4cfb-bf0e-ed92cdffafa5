import { ProviderLlmEnum } from '@/modules/models/constants/provider.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc tạo mới user key LLM
 */
export class CreateUserKeyLlmDto {
  /**
   * Tên định danh cho key 
   */
  @ApiProperty({
    description: 'Tên định danh cho key',
    example: 'My OpenAI Key',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * Nhà cung cấp LLM
   */
  @ApiProperty({
    description: 'Nhà cung cấp LLM',
    enum: ProviderLlmEnum,
    example: ProviderLlmEnum.OPENAI,
  })
  @IsEnum(ProviderLlmEnum)
  @IsNotEmpty()
  provider: ProviderLlmEnum;

  /**
   * API key cá nhân hóa
   */
  @ApiProperty({
    description: 'API key cá nhân hóa',
    example: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
  })
  @IsString()
  @IsNotEmpty()
  apiKey: string;
}
