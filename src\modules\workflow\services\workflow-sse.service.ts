import { Injectable, Logger } from '@nestjs/common';
import { Observable, Subject, fromEvent, map, takeUntil, timer, merge, of } from 'rxjs';
import { RedisService } from '@/shared/services/redis.service';
import {
  WORKFLOW_REDIS_CHANNELS,
  WORKFLOW_SSE_EVENTS,
  WORKFLOW_SSE_CONFIG,
  WorkflowExecutionEventData,
  WorkflowNodeEventData,
  WorkflowEventData,
  WorkflowTestEventData,
  WorkflowSseMessage,
  WorkflowFormattedSseEvent,
  WorkflowEventDataUnion,
} from '../constants/workflow-sse-events';

/**
 * Service quản lý SSE cho workflow events
 * Handles real-time workflow updates via Server-Sent Events
 */
@Injectable()
export class WorkflowSSEService {
  private readonly logger = new Logger(WorkflowSSEService.name);
  private readonly activeConnections = new Map<string, Set<string>>();

  constructor(private readonly redisService: RedisService) {}

  /**
   * Stream workflow execution events cho user
   * @param userId ID của user
   * @param executionId ID của execution (optional, filter theo execution)
   * @returns Observable stream của execution events
   */
  streamExecutionEvents(
    userId: number,
    executionId?: string,
  ): Observable<WorkflowSseMessage> {
    this.logger.log(
      `Starting execution events stream for user ${userId}${executionId ? `, execution ${executionId}` : ''}`,
    );

    return new Observable<WorkflowSseMessage>((observer) => {
      const cleanup$ = new Subject<void>();
      let isCompleted = false;
      const connectionId = this.generateConnectionId(userId, 'execution');

      // Track connection
      this.trackConnection(userId.toString(), connectionId);

      const subscriberClient = this.redisService.getDuplicateClient();

      const cleanup = () => {
        if (!isCompleted) {
          isCompleted = true;
          cleanup$.next();
          cleanup$.complete();
          subscriberClient.disconnect();
          this.untrackConnection(userId.toString(), connectionId);
          this.logger.log(`Execution events stream cleanup completed for user ${userId}`);
        }
      };

      observer.add(cleanup);

      // Send initial connection confirmation
      this.sendConnectionEstablished(observer, userId, 'execution');

      // Subscribe to Redis channel
      subscriberClient
        .subscribe(WORKFLOW_REDIS_CHANNELS.EXECUTION_EVENTS)
        .then(() => {
          this.logger.debug(
            `Subscribed to Redis channel ${WORKFLOW_REDIS_CHANNELS.EXECUTION_EVENTS} for user ${userId}`,
          );

          subscriberClient.on('message', (channel, message) => {
            if (isCompleted) return;

            try {
              const event: WorkflowExecutionEventData = JSON.parse(message);

              // Filter by user and optionally by execution
              if (
                event.userId === userId &&
                (!executionId || event.executionId === executionId)
              ) {
                const sseMessage = this.formatExecutionEvent(event);
                observer.next(sseMessage);
              }
            } catch (error) {
              this.logger.error(`Error processing execution event: ${error.message}`);
            }
          });

          // Setup heartbeat
          this.setupHeartbeat(observer, cleanup$);
        })
        .catch((error) => {
          this.logger.error(`Failed to subscribe to execution events: ${error.message}`);
          observer.error(error);
        });

      // Setup connection timeout
      timer(WORKFLOW_SSE_CONFIG.CONNECTION_TIMEOUT)
        .pipe(takeUntil(cleanup$))
        .subscribe(() => {
          this.logger.log(`Connection timeout for user ${userId} execution stream`);
          cleanup();
        });
    });
  }

  /**
   * Stream workflow node events cho user
   * @param userId ID của user
   * @param executionId ID của execution (optional)
   * @returns Observable stream của node events
   */
  streamNodeEvents(
    userId: number,
    executionId?: string,
  ): Observable<WorkflowSseMessage> {
    this.logger.log(
      `Starting node events stream for user ${userId}${executionId ? `, execution ${executionId}` : ''}`,
    );

    return new Observable<WorkflowSseMessage>((observer) => {
      const cleanup$ = new Subject<void>();
      let isCompleted = false;
      const connectionId = this.generateConnectionId(userId, 'node');

      this.trackConnection(userId.toString(), connectionId);

      const subscriberClient = this.redisService.getDuplicateClient();

      const cleanup = () => {
        if (!isCompleted) {
          isCompleted = true;
          cleanup$.next();
          cleanup$.complete();
          subscriberClient.disconnect();
          this.untrackConnection(userId.toString(), connectionId);
          this.logger.log(`Node events stream cleanup completed for user ${userId}`);
        }
      };

      observer.add(cleanup);

      this.sendConnectionEstablished(observer, userId, 'node');

      subscriberClient
        .subscribe(WORKFLOW_REDIS_CHANNELS.NODE_EVENTS)
        .then(() => {
          this.logger.debug(
            `Subscribed to Redis channel ${WORKFLOW_REDIS_CHANNELS.NODE_EVENTS} for user ${userId}`,
          );

          subscriberClient.on('message', (channel, message) => {
            if (isCompleted) return;

            try {
              const event: WorkflowNodeEventData = JSON.parse(message);

              if (
                event.userId === userId &&
                (!executionId || event.executionId === executionId)
              ) {
                const sseMessage = this.formatNodeEvent(event);
                observer.next(sseMessage);
              }
            } catch (error) {
              this.logger.error(`Error processing node event: ${error.message}`);
            }
          });

          this.setupHeartbeat(observer, cleanup$);
        })
        .catch((error) => {
          this.logger.error(`Failed to subscribe to node events: ${error.message}`);
          observer.error(error);
        });

      timer(WORKFLOW_SSE_CONFIG.CONNECTION_TIMEOUT)
        .pipe(takeUntil(cleanup$))
        .subscribe(() => {
          this.logger.log(`Connection timeout for user ${userId} node stream`);
          cleanup();
        });
    });
  }

  /**
   * Stream workflow test events cho user
   * @param userId ID của user
   * @param testId ID của test (optional)
   * @returns Observable stream của test events
   */
  streamTestEvents(
    userId: number,
    testId?: string,
  ): Observable<WorkflowSseMessage> {
    this.logger.log(
      `Starting test events stream for user ${userId}${testId ? `, test ${testId}` : ''}`,
    );

    return new Observable<WorkflowSseMessage>((observer) => {
      const cleanup$ = new Subject<void>();
      let isCompleted = false;
      const connectionId = this.generateConnectionId(userId, 'test');

      this.trackConnection(userId.toString(), connectionId);

      const subscriberClient = this.redisService.getDuplicateClient();

      const cleanup = () => {
        if (!isCompleted) {
          isCompleted = true;
          cleanup$.next();
          cleanup$.complete();
          subscriberClient.disconnect();
          this.untrackConnection(userId.toString(), connectionId);
          this.logger.log(`Test events stream cleanup completed for user ${userId}`);
        }
      };

      observer.add(cleanup);

      this.sendConnectionEstablished(observer, userId, 'test');

      subscriberClient
        .subscribe(WORKFLOW_REDIS_CHANNELS.TEST_EVENTS)
        .then(() => {
          this.logger.debug(
            `Subscribed to Redis channel ${WORKFLOW_REDIS_CHANNELS.TEST_EVENTS} for user ${userId}`,
          );

          subscriberClient.on('message', (channel, message) => {
            if (isCompleted) return;

            try {
              const event: WorkflowTestEventData = JSON.parse(message);

              if (
                event.userId === userId &&
                (!testId || event.testId === testId)
              ) {
                const sseMessage = this.formatTestEvent(event);
                observer.next(sseMessage);
              }
            } catch (error) {
              this.logger.error(`Error processing test event: ${error.message}`);
            }
          });

          this.setupHeartbeat(observer, cleanup$);
        })
        .catch((error) => {
          this.logger.error(`Failed to subscribe to test events: ${error.message}`);
          observer.error(error);
        });

      timer(WORKFLOW_SSE_CONFIG.CONNECTION_TIMEOUT)
        .pipe(takeUntil(cleanup$))
        .subscribe(() => {
          this.logger.log(`Connection timeout for user ${userId} test stream`);
          cleanup();
        });
    });
  }

  /**
   * Publish execution event to Redis
   * @param event Execution event data
   */
  async publishExecutionEvent(event: WorkflowExecutionEventData): Promise<void> {
    try {
      await this.redisService.getClient().publish(
        WORKFLOW_REDIS_CHANNELS.EXECUTION_EVENTS,
        JSON.stringify(event),
      );

      this.logger.debug(
        `Published execution event: ${event.status} for execution ${event.executionId}`,
      );
    } catch (error) {
      this.logger.error(`Failed to publish execution event: ${error.message}`);
      throw error;
    }
  }

  /**
   * Publish node event to Redis
   * @param event Node event data
   */
  async publishNodeEvent(event: WorkflowNodeEventData): Promise<void> {
    try {
      await this.redisService.getClient().publish(
        WORKFLOW_REDIS_CHANNELS.NODE_EVENTS,
        JSON.stringify(event),
      );

      this.logger.debug(
        `Published node event: ${event.status} for node ${event.nodeId}`,
      );
    } catch (error) {
      this.logger.error(`Failed to publish node event: ${error.message}`);
      throw error;
    }
  }

  /**
   * Publish test event to Redis
   * @param event Test event data
   */
  async publishTestEvent(event: WorkflowTestEventData): Promise<void> {
    try {
      await this.redisService.getClient().publish(
        WORKFLOW_REDIS_CHANNELS.TEST_EVENTS,
        JSON.stringify(event),
      );

      this.logger.debug(
        `Published test event: ${event.status} for test ${event.testId}`,
      );
    } catch (error) {
      this.logger.error(`Failed to publish test event: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get active connections count for user
   * @param userId ID của user
   * @returns Number of active connections
   */
  getActiveConnectionsCount(userId: number): number {
    const userConnections = this.activeConnections.get(userId.toString());
    return userConnections ? userConnections.size : 0;
  }

  /**
   * Get total active connections
   * @returns Total number of active connections
   */
  getTotalActiveConnections(): number {
    let total = 0;
    for (const connections of this.activeConnections.values()) {
      total += connections.size;
    }
    return total;
  }

  /**
   * Disconnect all connections for user
   * @param userId ID của user
   */
  disconnectUserConnections(userId: number): void {
    const userConnections = this.activeConnections.get(userId.toString());
    if (userConnections) {
      userConnections.clear();
      this.activeConnections.delete(userId.toString());
      this.logger.log(`Disconnected all connections for user ${userId}`);
    }
  }

  // Private helper methods

  /**
   * Generate unique connection ID
   */
  private generateConnectionId(userId: number, type: string): string {
    return `${type}_${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Track active connection
   */
  private trackConnection(userId: string, connectionId: string): void {
    if (!this.activeConnections.has(userId)) {
      this.activeConnections.set(userId, new Set());
    }

    const userConnections = this.activeConnections.get(userId)!;

    // Check connection limit
    if (userConnections.size >= WORKFLOW_SSE_CONFIG.MAX_CONNECTIONS_PER_USER) {
      this.logger.warn(`User ${userId} exceeded max connections limit`);
      // Remove oldest connection
      const oldestConnection = userConnections.values().next().value;
      userConnections.delete(oldestConnection);
    }

    userConnections.add(connectionId);
    this.logger.debug(`Tracked connection ${connectionId} for user ${userId}`);
  }

  /**
   * Untrack connection
   */
  private untrackConnection(userId: string, connectionId: string): void {
    const userConnections = this.activeConnections.get(userId);
    if (userConnections) {
      userConnections.delete(connectionId);
      if (userConnections.size === 0) {
        this.activeConnections.delete(userId);
      }
      this.logger.debug(`Untracked connection ${connectionId} for user ${userId}`);
    }
  }

  /**
   * Send connection established event
   */
  private sendConnectionEstablished(
    observer: any,
    userId: number,
    streamType: string,
  ): void {
    const connectionEvent: WorkflowFormattedSseEvent = {
      type: WORKFLOW_SSE_EVENTS.CONNECTION_ESTABLISHED,
      userId,
      timestamp: Date.now(),
      data: {
        streamType,
        message: `${streamType} events stream connected`,
      },
    };

    const sseMessage: WorkflowSseMessage = {
      id: `connection_${Date.now()}`,
      event: WORKFLOW_SSE_EVENTS.CONNECTION_ESTABLISHED,
      data: JSON.stringify(connectionEvent),
    };

    observer.next(sseMessage);
  }

  /**
   * Setup heartbeat for connection
   */
  private setupHeartbeat(observer: any, cleanup$: Subject<void>): void {
    timer(0, WORKFLOW_SSE_CONFIG.HEARTBEAT_INTERVAL)
      .pipe(takeUntil(cleanup$))
      .subscribe(() => {
        const heartbeatMessage: WorkflowSseMessage = {
          id: `heartbeat_${Date.now()}`,
          event: 'heartbeat',
          data: JSON.stringify({
            type: 'heartbeat',
            timestamp: Date.now(),
          }),
        };

        observer.next(heartbeatMessage);
      });
  }

  /**
   * Format execution event for SSE
   */
  private formatExecutionEvent(event: WorkflowExecutionEventData): WorkflowSseMessage {
    const formattedEvent: WorkflowFormattedSseEvent = {
      type: this.getExecutionEventType(event.status),
      executionId: event.executionId,
      workflowId: event.workflowId,
      userId: event.userId,
      timestamp: event.timestamp,
      data: {
        status: event.status,
        progress: event.progress,
        result: event.result,
        error: event.error,
        metadata: event.metadata,
      },
    };

    return {
      id: `execution_${event.executionId}_${event.timestamp}`,
      event: formattedEvent.type,
      data: JSON.stringify(formattedEvent),
    };
  }

  /**
   * Format node event for SSE
   */
  private formatNodeEvent(event: WorkflowNodeEventData): WorkflowSseMessage {
    const formattedEvent: WorkflowFormattedSseEvent = {
      type: this.getNodeEventType(event.status),
      executionId: event.executionId,
      workflowId: event.workflowId,
      nodeId: event.nodeId,
      userId: event.userId,
      timestamp: event.timestamp,
      data: {
        nodeType: event.nodeType,
        status: event.status,
        input: event.input,
        output: event.output,
        error: event.error,
        duration: event.duration,
        metadata: event.metadata,
      },
    };

    return {
      id: `node_${event.nodeId}_${event.timestamp}`,
      event: formattedEvent.type,
      data: JSON.stringify(formattedEvent),
    };
  }

  /**
   * Format test event for SSE
   */
  private formatTestEvent(event: WorkflowTestEventData): WorkflowSseMessage {
    const formattedEvent: WorkflowFormattedSseEvent = {
      type: this.getTestEventType(event.status),
      testId: event.testId,
      nodeId: event.nodeId,
      userId: event.userId,
      timestamp: event.timestamp,
      data: {
        nodeType: event.nodeType,
        status: event.status,
        input: event.input,
        output: event.output,
        error: event.error,
        duration: event.duration,
        metadata: event.metadata,
      },
    };

    return {
      id: `test_${event.testId}_${event.timestamp}`,
      event: formattedEvent.type,
      data: JSON.stringify(formattedEvent),
    };
  }

  /**
   * Get SSE event type for execution status
   */
  private getExecutionEventType(status: string): string {
    switch (status.toLowerCase()) {
      case 'running':
        return WORKFLOW_SSE_EVENTS.EXECUTION_STARTED;
      case 'completed':
        return WORKFLOW_SSE_EVENTS.EXECUTION_COMPLETED;
      case 'failed':
        return WORKFLOW_SSE_EVENTS.EXECUTION_FAILED;
      case 'paused':
        return WORKFLOW_SSE_EVENTS.EXECUTION_PAUSED;
      case 'cancelled':
        return WORKFLOW_SSE_EVENTS.EXECUTION_CANCELLED;
      default:
        return WORKFLOW_SSE_EVENTS.EXECUTION_PROGRESS;
    }
  }

  /**
   * Get SSE event type for node status
   */
  private getNodeEventType(status: string): string {
    switch (status.toLowerCase()) {
      case 'running':
        return WORKFLOW_SSE_EVENTS.NODE_STARTED;
      case 'completed':
        return WORKFLOW_SSE_EVENTS.NODE_COMPLETED;
      case 'failed':
        return WORKFLOW_SSE_EVENTS.NODE_FAILED;
      case 'skipped':
        return WORKFLOW_SSE_EVENTS.NODE_SKIPPED;
      default:
        return WORKFLOW_SSE_EVENTS.NODE_PROGRESS;
    }
  }

  /**
   * Get SSE event type for test status
   */
  private getTestEventType(status: string): string {
    switch (status.toLowerCase()) {
      case 'running':
        return WORKFLOW_SSE_EVENTS.NODE_TEST_STARTED;
      case 'completed':
        return WORKFLOW_SSE_EVENTS.NODE_TEST_COMPLETED;
      case 'failed':
        return WORKFLOW_SSE_EVENTS.NODE_TEST_FAILED;
      default:
        return WORKFLOW_SSE_EVENTS.NODE_TEST_STARTED;
    }
  }
}
