{"openapi": "3.0.0", "info": {"title": "Marketplace Module API", "description": "API documentation for Marketplace Module - <PERSON><PERSON><PERSON>n lý hệ thống chợ sản phẩm cho người dùng bao gồm tạo sản phẩm, quản lý giỏ hàng và thanh toán", "version": "1.0.0", "contact": {"name": "RedAI Development Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://api.redai.com", "description": "Production server"}], "tags": [{"name": "User - Marketplace Products", "description": "<PERSON><PERSON><PERSON><PERSON> lý sản phẩm cho người dùng"}, {"name": "User - Marketplace Cart", "description": "<PERSON><PERSON><PERSON><PERSON> lý giỏ hàng cho người dùng"}, {"name": "User - Marketplace Orders", "description": "<PERSON><PERSON><PERSON><PERSON> lý đơn hàng cho người dùng"}, {"name": "User - Marketplace Payment", "description": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>h toán cho người dùng"}], "paths": {"/user/marketplace/products": {"get": {"tags": ["User - Marketplace Products"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch sản phẩm của người dùng hiện tại", "description": "<PERSON><PERSON><PERSON> danh sách sản phẩm của người dùng hiện tại với các tùy chọn lọc và sắp xếp", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "Từ khóa tìm kiếm theo tên sản phẩm", "required": false, "schema": {"type": "string", "example": "AI Chatbot"}}, {"name": "category", "in": "query", "description": "<PERSON><PERSON><PERSON> theo lo<PERSON> sản phẩm", "required": false, "schema": {"type": "string", "enum": ["AGENT", "KNOWLEDGE_FILE", "FUNCTION", "FINETUNE", "STRATEGY"], "example": "AGENT"}}, {"name": "status", "in": "query", "description": "<PERSON><PERSON><PERSON> theo trạng thái sản phẩm", "required": false, "schema": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "DRAFT", "DELETED"], "example": "APPROVED"}}, {"name": "minPrice", "in": "query", "description": "<PERSON><PERSON><PERSON> tối thiểu", "required": false, "schema": {"type": "number", "minimum": 0, "example": 100}}, {"name": "maxPrice", "in": "query", "description": "<PERSON><PERSON><PERSON> tối đa", "required": false, "schema": {"type": "number", "minimum": 0, "example": 1000}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["name", "listedPrice", "discountedPrice", "createdAt", "updatedAt"], "default": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách sản phẩm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/PaginatedUserProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Marketplace Products"], "summary": "<PERSON><PERSON><PERSON> sản phẩm mới", "description": "<PERSON><PERSON><PERSON> sản phẩm mới và trả về URL để upload tài liệu", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> sản phẩm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 201}, "message": {"type": "string", "example": "Product created successfully"}, "result": {"$ref": "#/components/schemas/CreateProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/products/approved": {"get": {"tags": ["User - Marketplace Products"], "summary": "<PERSON><PERSON><PERSON> danh sách sản phẩm đ<PERSON><PERSON><PERSON> phê duy<PERSON>t", "description": "<PERSON><PERSON><PERSON> danh sách sản phẩm marketplace đượ<PERSON> phê duyệt (không bao gồm sản phẩm của người dùng hiện tại). Hỗ trợ filter theo category, gi<PERSON>, tìm kiếm và lọc sản phẩm đã mua.", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "search", "in": "query", "description": "Từ khóa tìm kiếm theo tên sản phẩm", "required": false, "schema": {"type": "string", "example": "AI Chatbot"}}, {"name": "category", "in": "query", "description": "<PERSON><PERSON><PERSON> theo lo<PERSON> sản phẩm", "required": false, "schema": {"type": "string", "enum": ["AGENT", "KNOWLEDGE_FILE", "FUNCTION", "FINETUNE", "STRATEGY"], "example": "AGENT"}}, {"name": "minPrice", "in": "query", "description": "<PERSON><PERSON><PERSON> tối thiểu", "required": false, "schema": {"type": "number", "minimum": 0, "example": 100}}, {"name": "maxPrice", "in": "query", "description": "<PERSON><PERSON><PERSON> tối đa", "required": false, "schema": {"type": "number", "minimum": 0, "example": 1000}}, {"name": "purchasedOnly", "in": "query", "description": "Chỉ l<PERSON>y sản phẩm đã mua (true) hoặc chưa mua (false)", "required": false, "schema": {"type": "boolean", "example": false}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["name", "discountedPrice", "createdAt", "category"], "default": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> danh sách sản phẩm đ<PERSON><PERSON><PERSON> phê duyệt thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> danh sách sản phẩm thành công"}, "result": {"$ref": "#/components/schemas/PaginatedUserProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/products/detail/{id}": {"get": {"tags": ["User - Marketplace Products"], "summary": "<PERSON><PERSON><PERSON> thông tin chi tiết sản phẩm theo ID", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một sản phẩm bất kỳ theo ID (không giới hạn sở hữu). <PERSON><PERSON> gồm thông tin canPurchase để kiểm tra khả năng mua.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/ProductDetailResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/products/{id}": {"get": {"tags": ["User - Marketplace Products"], "summary": "<PERSON><PERSON><PERSON> chi tiết sản phẩm của người dùng", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của một sản phẩm theo ID", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> chi tiết sản phẩm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/UserProductDetailResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["User - Marketplace Products"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin sản phẩm", "description": "Cập nhật thông tin sản phẩm (chỉ có thể cập nhật sản phẩm ở trạng thái DRAFT hoặc REJECTED)", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Product updated successfully"}, "result": {"$ref": "#/components/schemas/UpdateProductResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Marketplace Products"], "summary": "<PERSON><PERSON><PERSON> p<PERSON>m", "description": "<PERSON><PERSON><PERSON> sản phẩm (chỉ có thể xóa sản phẩm ở trạng thái DRAFT hoặc REJECTED)", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> sản phẩm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Product deleted successfully"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/products/{id}/pending": {"post": {"tags": ["User - Marketplace Products"], "summary": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "description": "<PERSON><PERSON><PERSON> sản phẩm từ trạng thái DRAFT sang PENDING để chờ kiểm duyệt", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> sản phẩm để kiểm duyệt thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Product submitted for review successfully"}, "result": {"$ref": "#/components/schemas/UserProductDetailResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/products/{id}/cancel-submission": {"post": {"tags": ["User - Marketplace Products"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> sản phẩm", "description": "<PERSON><PERSON><PERSON> gửi duyệt sản phẩm (chuyển trạng thái từ PENDING về DRAFT). Chỉ có thể thực hiện với sản phẩm ở trạng thái PENDING.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> du<PERSON> sản phẩm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> phẩm đã đư<PERSON><PERSON> hủy gửi duyệt thành công"}, "result": {"$ref": "#/components/schemas/UserProductDetailResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/products/{id}/withdraw": {"post": {"tags": ["User - Marketplace Products"], "summary": "Gỡ sản phẩm xuống", "description": "Gỡ sản phẩm xuống (chuyển trạng thái từ APPROVED về PENDING). Chỉ có thể thực hiện với sản phẩm ở trạng thái APPROVED.", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Gỡ sản phẩm xuống thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "<PERSON><PERSON>n phẩm đã được gỡ xuống thành công"}, "result": {"$ref": "#/components/schemas/UserProductDetailResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/products/batch": {"delete": {"tags": ["User - Marketplace Products"], "summary": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "description": "<PERSON><PERSON><PERSON> nhiều sản phẩm cùng lúc (chỉ có thể xóa sản phẩm ở trạng thái DRAFT hoặc REJECTED)", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteMultipleProductsDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhi<PERSON>u sản phẩm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Products deleted successfully"}, "result": {"$ref": "#/components/schemas/DeleteMultipleProductsResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/cart": {"get": {"tags": ["User - Marketplace Cart"], "summary": "<PERSON><PERSON><PERSON> thông tin giỏ hàng", "description": "<PERSON><PERSON><PERSON> thông tin giỏ hàng của người dùng hiện tại", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "sortBy", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>p xếp", "required": false, "schema": {"type": "string", "enum": ["productName", "discountedPrice", "quantity", "createdAt"], "default": "createdAt"}}, {"name": "sortDirection", "in": "query", "description": "<PERSON><PERSON><PERSON><PERSON> sắp xếp", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "DESC"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin giỏ hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/CartResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "post": {"tags": ["User - Marketplace Cart"], "summary": "<PERSON>h<PERSON><PERSON> sản phẩm vào giỏ hàng", "description": "<PERSON>hê<PERSON> sản phẩm vào giỏ hàng của người dùng", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddToCartDto"}}}}, "responses": {"200": {"description": "<PERSON>hêm sản phẩm vào giỏ hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Product added to cart successfully"}, "result": {"$ref": "#/components/schemas/CartResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/cart/{id}": {"put": {"tags": ["User - Marketplace Cart"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t số lượng sản phẩm trong giỏ hàng", "description": "Thêm số lượng sản phẩm vào giỏ hàng (cộng thêm vào số lượng hiện có)", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "ID của cart item", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCartItemDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t số lượng sản phẩm thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Cart item updated successfully"}, "result": {"$ref": "#/components/schemas/CartResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/cart/batch": {"delete": {"tags": ["User - Marketplace Cart"], "summary": "<PERSON><PERSON><PERSON> nhi<PERSON>u sản phẩm khỏi giỏ hàng", "description": "<PERSON><PERSON><PERSON> nhi<PERSON>u sản phẩm khỏi giỏ hàng cùng lúc", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveMultipleCartItemsDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nhiều sản phẩm khỏi giỏ hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Cart items removed successfully"}, "result": {"$ref": "#/components/schemas/CartResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/orders/rpoint-purchase-history": {"get": {"tags": ["User - Marketplace Orders"], "summary": "<PERSON><PERSON><PERSON> l<PERSON>ch sử mua hàng bằng R-Point của người dùng", "description": "<PERSON><PERSON><PERSON> lịch sử mua hàng bằng R-Point của người dùng với phân trang. API này trả về danh sách các sản phẩm đã mua bằng R-Point với thông tin chi tiết về giao dịch và người bán.", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> lịch sử mua hàng bằng R-Point thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/CombinedRPointPurchaseHistoryResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/orders/purchase-history": {"get": {"tags": ["User - Marketplace Orders"], "summary": "<PERSON><PERSON><PERSON> l<PERSON>ch sử mua hàng của người dùng", "description": "<PERSON><PERSON><PERSON> l<PERSON>ch sử mua hàng của người dùng với phân trang", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "<PERSON><PERSON> trang (bắt đầu từ 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "<PERSON><PERSON> lượng kết quả mỗi trang", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> lịch sử mua hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Success"}, "result": {"$ref": "#/components/schemas/CombinedPurchaseHistoryResponseDto"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/user/marketplace/payment": {"post": {"tags": ["User - Marketplace Payment"], "summary": "<PERSON><PERSON> <PERSON><PERSON> sản ph<PERSON>m", "description": "<PERSON>h to<PERSON> sản phẩm bằng R-Point", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> to<PERSON> thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Payment successful"}, "result": {"type": "null"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["User - Marketplace Payment"], "summary": "<PERSON><PERSON><PERSON> nhi<PERSON>u đơn hàng <PERSON>h toán", "description": "Xóa nhiều đơn hàng thanh toán của người dùng. API này cho phép xóa hàng loạt các đơn hàng đã thanh toán.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkDeletePaymentDto"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> đơn hàng thành công", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "Bulk delete completed successfully"}, "result": {"$ref": "#/components/schemas/BulkDeletePaymentResponseDto"}}}}}}, "207": {"description": "Một số đơn hàng không thể xóa", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 207}, "message": {"type": "string", "example": "Partial success - some orders could not be deleted"}, "result": {"$ref": "#/components/schemas/BulkDeletePaymentResponseDto"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"PaginatedUserProductResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/UserProductResponseDto"}}, "totalItems": {"type": "integer", "description": "Tổng số sản phẩm", "example": 100}, "itemCount": {"type": "integer", "description": "Số sản phẩm trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "Số sản phẩm mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 10}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "UserProductResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "example": 1}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "example": "AI Chatbot Template"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> sản phẩm", "example": "Mẫu chatbot AI hỗ trợ khách hàng tự động"}, "category": {"type": "string", "enum": ["AGENT", "KNOWLEDGE_FILE", "FUNCTION", "FINETUNE", "STRATEGY"], "description": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "example": "AGENT"}, "listedPrice": {"type": "integer", "description": "<PERSON><PERSON><PERSON>", "example": 1200}, "discountedPrice": {"type": "integer", "description": "Giá sau giảm", "example": 1000}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "DRAFT", "DELETED"], "description": "<PERSON><PERSON><PERSON><PERSON> thái sản phẩm", "example": "APPROVED"}, "images": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "products/images/123.jpg"}, "position": {"type": "integer", "example": 1}}}, "description": "<PERSON><PERSON> s<PERSON><PERSON> sản ph<PERSON>m"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": 1632474086123}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật (Unix timestamp)", "example": 1632474086123}, "soldCount": {"type": "integer", "description": "Số lượng đã bán", "example": 25, "nullable": true}}, "required": ["id", "name", "description", "category", "listedPrice", "discountedPrice", "status", "images", "createdAt", "updatedAt"]}, "CreateProductDto": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "example": "AI Chatbot Template", "maxLength": 500}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> sản phẩm", "example": "Mẫu chatbot AI hỗ trợ khách hàng tự động"}, "listedPrice": {"type": "number", "description": "<PERSON><PERSON><PERSON>", "example": 1200, "minimum": 0}, "discountedPrice": {"type": "number", "description": "Giá sau giảm", "example": 1000, "minimum": 0}, "category": {"type": "string", "enum": ["AGENT", "KNOWLEDGE_FILE", "FUNCTION", "FINETUNE", "STRATEGY"], "description": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "example": "KNOWLEDGE_FILE"}, "sourceId": {"type": "string", "description": "ID nguồn sản phẩm", "example": "34f5c7ef-649a-46e2-a399-34fc7c197032", "nullable": true}, "imagesMediaTypes": {"type": "array", "items": {"type": "string"}, "description": "Danh sách loại media cho hình <PERSON>nh sản phẩm", "example": ["image/jpeg"]}, "userManualMediaType": {"type": "string", "description": "Loại media cho hướng dẫn sử dụng", "example": "text/html", "nullable": true}, "detailMediaType": {"type": "string", "description": "Loại media cho thông tin chi tiết", "example": "text/html", "nullable": true}}, "required": ["name", "description", "listedPrice", "discountedPrice", "category"]}, "CreateProductResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của sản phẩm vừa tạo", "example": 1}, "imagesUploadUrls": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON> s<PERSON>ch URL để upload h<PERSON><PERSON><PERSON>", "example": ["https://s3.amazonaws.com/bucket/upload-image-1-url"]}, "userManualUploadUrl": {"type": "string", "description": "URL để upload hướng dẫn sử dụng", "example": "https://s3.amazonaws.com/bucket/upload-manual-url", "nullable": true}, "detailUploadUrl": {"type": "string", "description": "URL để upload thông tin chi tiết", "example": "https://s3.amazonaws.com/bucket/upload-detail-url", "nullable": true}}, "required": ["id", "imagesUploadUrls"]}, "UserProductDetailResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "example": 1}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "example": "AI Chatbot Template"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> sản phẩm", "example": "Mẫu chatbot AI hỗ trợ khách hàng tự động"}, "category": {"type": "string", "enum": ["AGENT", "KNOWLEDGE_FILE", "FUNCTION", "FINETUNE", "STRATEGY"], "description": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "example": "AGENT"}, "listedPrice": {"type": "integer", "description": "<PERSON><PERSON><PERSON>", "example": 1200}, "discountedPrice": {"type": "integer", "description": "Giá sau giảm", "example": 1000}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "DRAFT", "DELETED"], "description": "<PERSON><PERSON><PERSON><PERSON> thái sản phẩm", "example": "APPROVED"}, "images": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "products/images/123.jpg"}, "position": {"type": "integer", "example": 1}}}, "description": "<PERSON><PERSON> s<PERSON><PERSON> sản ph<PERSON>m"}, "userManual": {"type": "string", "description": "URL hướng dẫn sử dụng", "example": "https://cdn.example.com/manuals/123.pdf", "nullable": true}, "detail": {"type": "string", "description": "URL thông tin chi tiết", "example": "https://cdn.example.com/details/123.html", "nullable": true}, "sourceId": {"type": "string", "description": "ID nguồn sản phẩm", "example": "34f5c7ef-649a-46e2-a399-34fc7c197032", "nullable": true}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": 1632474086123}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật (Unix timestamp)", "example": 1632474086123}, "soldCount": {"type": "integer", "description": "Số lượng đã bán", "example": 25, "nullable": true}}, "required": ["id", "name", "description", "category", "listedPrice", "discountedPrice", "status", "images", "createdAt", "updatedAt"]}, "UpdateProductDto": {"type": "object", "properties": {"productInfo": {"type": "object", "properties": {"name": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "example": "AI Chatbot Template", "maxLength": 500}, "listedPrice": {"type": "number", "description": "<PERSON><PERSON><PERSON>", "example": 1200, "minimum": 0}, "discountedPrice": {"type": "number", "description": "Giá sau giảm", "example": 1000, "minimum": 0}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> sản phẩm", "example": "Mẫu chatbot AI hỗ trợ khách hàng tự động"}}, "required": ["name", "listedPrice", "discountedPrice"]}, "imageOperations": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["add", "delete"], "description": "<PERSON><PERSON><PERSON> thao tác"}, "position": {"type": "integer", "description": "<PERSON><PERSON> tr<PERSON>", "example": 1}, "mediaType": {"type": "string", "description": "Loại media (chỉ cho thao tác add)", "example": "image/jpeg"}}, "required": ["type", "position"]}, "description": "<PERSON><PERSON> s<PERSON>ch thao tác với <PERSON>nh"}}, "required": ["productInfo"]}, "UpdateProductResponseDto": {"type": "object", "properties": {"product": {"$ref": "#/components/schemas/UserProductDetailResponseDto"}, "uploadUrls": {"type": "array", "items": {"type": "object", "properties": {"position": {"type": "integer", "example": 1}, "uploadUrl": {"type": "string", "example": "https://s3.amazonaws.com/bucket/upload-url"}}}, "description": "<PERSON><PERSON> sách URL upload cho ảnh mới"}}, "required": ["product"]}, "DeleteMultipleProductsDto": {"type": "object", "properties": {"productIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON>ch <PERSON> sản phẩm cần xóa", "example": [1, 2, 3]}}, "required": ["productIds"]}, "DeleteMultipleProductsResponseDto": {"type": "object", "properties": {"deletedCount": {"type": "integer", "description": "Số lượng sản phẩm đã xóa", "example": 3}, "failedIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON> s<PERSON>ch <PERSON> sản phẩm không thể xóa", "example": []}}, "required": ["deletedCount", "failedIds"]}, "CartResponseDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CartItemResponseDto"}, "description": "<PERSON><PERSON> s<PERSON>ch sản phẩm trong giỏ hàng"}, "totalValue": {"type": "integer", "description": "Tổng giá trị giỏ hàng", "example": 2000}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo giỏ hàng", "example": 1625097600000}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật giỏ hàng", "example": 1625097600000}}, "required": ["items", "totalValue", "createdAt", "updatedAt"]}, "CartItemResponseDto": {"type": "object", "properties": {"cartItemId": {"type": "integer", "description": "ID của cart item", "example": 789}, "productId": {"type": "integer", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "example": 123}, "productName": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "example": "AI Chatbot Template"}, "discountedPrice": {"type": "integer", "description": "Giá sau giảm", "example": 1000}, "quantity": {"type": "integer", "description": "Số lượng", "example": 2}, "sellerName": {"type": "string", "description": "<PERSON><PERSON><PERSON> bán", "example": "<PERSON>"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian thêm vào giỏ hàng", "example": 1625097600000}}, "required": ["cartItemId", "productId", "productName", "discountedPrice", "quantity", "sellerName", "createdAt"]}, "AddToCartDto": {"type": "object", "properties": {"productId": {"type": "integer", "description": "<PERSON> sản phẩm", "example": 123}, "quantity": {"type": "integer", "description": "Số lượng", "example": 2, "minimum": 1}}, "required": ["productId", "quantity"]}, "UpdateCartItemDto": {"type": "object", "properties": {"quantity": {"type": "integer", "description": "<PERSON><PERSON> lượng cần thêm (sẽ được cộng vào số lượng hiện có)", "example": 1, "minimum": 1}}, "required": ["quantity"]}, "RemoveMultipleCartItemsDto": {"type": "object", "properties": {"cartItemIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON>h sách ID cart item cần xóa", "example": [1, 2, 3]}}, "required": ["cartItemIds"]}, "PaymentDto": {"type": "object", "properties": {"productIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON> s<PERSON>ch <PERSON> sản phẩm cần thanh toán", "example": [1, 2, 3]}}, "required": ["productIds"]}, "CombinedPurchaseHistoryResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/PurchaseHistoryItemDto"}}, "totalItems": {"type": "integer", "description": "Tổng số đơn hàng", "example": 50}, "itemCount": {"type": "integer", "description": "Số đơn hàng trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "<PERSON><PERSON> đơn hàng mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 5}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "PurchaseHistoryItemDto": {"type": "object", "properties": {"orderId": {"type": "integer", "description": "ID của đơn hàng", "example": 101}, "productId": {"type": "integer", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "example": 123}, "productName": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "example": "AI Chatbot Template"}, "discountedPrice": {"type": "integer", "description": "<PERSON><PERSON><PERSON> đã thanh toán", "example": 1000}, "quantity": {"type": "integer", "description": "Số lượng", "example": 2}, "platformFeePercent": {"type": "number", "description": "<PERSON><PERSON><PERSON> tr<PERSON>m phí sàn", "example": 5.0}, "sellerReceivePrice": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> bán nhận đ<PERSON><PERSON><PERSON> sau khi trừ phí sàn", "example": 950}, "seller": {"$ref": "#/components/schemas/PurchaseHistorySellerDto"}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian mua", "example": 1625097600000}}, "required": ["orderId", "productId", "productName", "discountedPrice", "quantity", "platformFeePercent", "sellerReceivePrice", "seller", "createdAt"]}, "PurchaseHistorySellerDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID của ngư<PERSON>i bán", "example": 456}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> bán", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "avatar": {"type": "string", "description": "Avatar c<PERSON>a ng<PERSON><PERSON> bán", "example": "https://example.com/avatar.jpg", "nullable": true}, "email": {"type": "string", "description": "<PERSON><PERSON> c<PERSON><PERSON> bán", "example": "<EMAIL>", "nullable": true}, "phoneNumber": {"type": "string", "description": "<PERSON><PERSON> điện tho<PERSON>i của người bán", "example": "0987654321", "nullable": true}}, "required": ["id", "name"]}, "ProductDetailResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "example": 1}, "name": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "example": "AI Chatbot Template"}, "description": {"type": "string", "description": "<PERSON><PERSON> t<PERSON> sản phẩm", "example": "Mẫu chatbot AI hỗ trợ khách hàng tự động"}, "category": {"type": "string", "enum": ["AGENT", "KNOWLEDGE_FILE", "FUNCTION", "FINETUNE", "STRATEGY"], "description": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "example": "AGENT"}, "listedPrice": {"type": "integer", "description": "<PERSON><PERSON><PERSON>", "example": 1200}, "discountedPrice": {"type": "integer", "description": "Giá sau giảm", "example": 1000}, "status": {"type": "string", "enum": ["PENDING", "APPROVED", "REJECTED", "DRAFT", "DELETED"], "description": "<PERSON><PERSON><PERSON><PERSON> thái sản phẩm", "example": "APPROVED"}, "images": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "products/images/123.jpg"}, "position": {"type": "integer", "example": 1}}}, "description": "<PERSON><PERSON> s<PERSON><PERSON> sản ph<PERSON>m"}, "userManual": {"type": "string", "description": "URL hướng dẫn sử dụng", "example": "https://cdn.example.com/manuals/123.pdf", "nullable": true}, "detail": {"type": "string", "description": "URL thông tin chi tiết", "example": "https://cdn.example.com/details/123.html", "nullable": true}, "sourceId": {"type": "string", "description": "ID nguồn sản phẩm", "example": "34f5c7ef-649a-46e2-a399-34fc7c197032", "nullable": true}, "seller": {"type": "object", "properties": {"id": {"type": "integer", "example": 456}, "name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "avatar": {"type": "string", "example": "https://example.com/avatar.jpg", "nullable": true}}, "description": "<PERSON>h<PERSON>ng tin người bán"}, "canPurchase": {"type": "boolean", "description": "<PERSON><PERSON> thể mua sản phẩm này hay không", "example": true}, "createdAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON> (Unix timestamp)", "example": 1632474086123}, "updatedAt": {"type": "integer", "description": "<PERSON><PERSON><PERSON><PERSON> gian cập nhật (Unix timestamp)", "example": 1632474086123}, "soldCount": {"type": "integer", "description": "Số lượng đã bán", "example": 25, "nullable": true}}, "required": ["id", "name", "description", "category", "listedPrice", "discountedPrice", "status", "images", "seller", "canPurchase", "createdAt", "updatedAt"]}, "CombinedRPointPurchaseHistoryResponseDto": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/RPointPurchaseHistoryItemDto"}}, "totalItems": {"type": "integer", "description": "Tổng số đơn hàng R-Point", "example": 30}, "itemCount": {"type": "integer", "description": "Số đơn hàng trong trang hiện tại", "example": 10}, "itemsPerPage": {"type": "integer", "description": "<PERSON><PERSON> đơn hàng mỗi trang", "example": 10}, "totalPages": {"type": "integer", "description": "Tổng số trang", "example": 3}, "currentPage": {"type": "integer", "description": "<PERSON><PERSON> hi<PERSON>n tại", "example": 1}}, "required": ["content", "totalItems", "itemCount", "itemsPerPage", "totalPages", "currentPage"]}, "RPointPurchaseHistoryItemDto": {"type": "object", "properties": {"orderId": {"type": "integer", "description": "ID của đơn hàng R-Point", "example": 201}, "productId": {"type": "integer", "description": "<PERSON> c<PERSON>a sản ph<PERSON>m", "example": 123}, "productName": {"type": "string", "description": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "example": "AI Chatbot Template"}, "discountedPrice": {"type": "integer", "description": "Giá đã thanh toán bằng R-Point", "example": 1000}, "quantity": {"type": "integer", "description": "Số lượng", "example": 1}, "seller": {"$ref": "#/components/schemas/PurchaseHistorySellerDto"}, "createdAt": {"type": "integer", "description": "Thời gian mua bằng R-Point", "example": 1625097600000}}, "required": ["orderId", "productId", "productName", "discountedPrice", "quantity", "seller", "createdAt"]}, "BulkDeletePaymentDto": {"type": "object", "properties": {"orderIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON> s<PERSON>ch <PERSON> đơn hàng cần xóa", "example": [101, 102, 103]}}, "required": ["orderIds"]}, "BulkDeletePaymentResponseDto": {"type": "object", "properties": {"deletedCount": {"type": "integer", "description": "Số lượng đơn hàng đã xóa thành công", "example": 2}, "failedCount": {"type": "integer", "description": "Số lượng đơn hàng xóa thất bại", "example": 1}, "successIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON> s<PERSON>ch ID đơn hàng đã xóa thành công", "example": [101, 102]}, "failedIds": {"type": "array", "items": {"type": "integer"}, "description": "<PERSON><PERSON> s<PERSON>ch <PERSON> đơn hàng xóa thất bại", "example": [103]}}, "required": ["deletedCount", "failedCount", "successIds", "failedIds"]}}, "responses": {"BadRequest": {"description": "<PERSON><PERSON><PERSON> c<PERSON>u kh<PERSON>ng h<PERSON>p lệ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "Validation failed"}, "errorCode": {"type": "integer", "example": 400}}}}}}, "Unauthorized": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 401}, "message": {"type": "string", "example": "Unauthorized"}, "errorCode": {"type": "integer", "example": 401}}}}}}, "Forbidden": {"description": "<PERSON><PERSON> cấm truy cập", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 403}, "message": {"type": "string", "example": "Forbidden"}, "errorCode": {"type": "integer", "example": 403}}}}}}, "NotFound": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài nguyên", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "Resource not found"}, "errorCode": {"type": "integer", "example": 404}}}}}}, "InternalServerError": {"description": "Lỗi máy chủ nội bộ", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 500}, "message": {"type": "string", "example": "Internal server error"}, "errorCode": {"type": "integer", "example": 500}}}}}}}}}