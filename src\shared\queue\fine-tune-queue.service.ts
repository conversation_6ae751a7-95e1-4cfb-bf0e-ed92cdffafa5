import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { AppException, ErrorCode } from '@/common';
import { MODELS_ERROR_CODES } from '@modules/models/exceptions/models.exception';
import { QueueName, FineTuneJobName, DEFAULT_JOB_OPTIONS } from './queue.constants';
import { FineTuneJobData } from '@modules/models/user/interfaces/fine-tune-job.interface';
import { JobOptions } from './queue.types';

/**
 * Service để quản lý việc thêm job fine-tuning vào queue
 */
@Injectable()
export class FineTuneQueueService {
  private readonly logger = new Logger(FineTuneQueueService.name);

  constructor(
    @InjectQueue(QueueName.FINE_TUNE) private fineTuneQueue: Queue,
  ) {}

  /**
   * Thêm job fine-tuning vào queue
   * @param jobData Dữ liệu job fine-tuning
   * @param options Tùy chọn job (tùy chọn)
   */
  async addFineTuningJob(
    jobData: FineTuneJobData,
    options?: JobOptions,
  ): Promise<void> {
    try {
      // Validate dữ liệu job
      this.validateJobData(jobData);

      // Xác định job name dựa trên dữ liệu
      const jobName = this.getJobName(jobData);

      // Tạo job options
      const jobOptions = {
        ...DEFAULT_JOB_OPTIONS,
        ...options,
        // Thêm metadata để tracking
        jobId: `${jobName}-${jobData.historyId}-${Date.now()}`,
      };

      // Thêm job vào queue
      await this.fineTuneQueue.add(jobName, jobData, jobOptions);

      this.logger.log(
        `Added fine-tuning job to queue: ${jobName} for history ${jobData.historyId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error adding fine-tuning job to queue for history ${jobData.historyId}:`,
        error,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi thêm job fine-tuning vào queue: ${error.message}`,
      );
    }
  }

  /**
   * Thêm job monitoring fine-tuning status
   * @param jobData Dữ liệu job monitoring
   * @param options Tùy chọn job (tùy chọn)
   */
  async addMonitoringJob(
    jobData: Partial<FineTuneJobData> & {
      historyId: string;
      providerJobId: string;
      provider: string;
      userId?: number;
      employeeId?: number;
    },
    options?: JobOptions,
  ): Promise<void> {
    try {
      // Tạo job options với delay để bắt đầu monitoring sau một khoảng thời gian
      const jobOptions = {
        ...DEFAULT_JOB_OPTIONS,
        ...options,
        delay: options?.delay || 30000, // Delay 30 giây mặc định
        repeat: {
          every: 60000, // Lặp lại mỗi 60 giây
        },
        jobId: `monitor-${jobData.historyId}-${Date.now()}`,
      };

      // Thêm job monitoring vào queue
      await this.fineTuneQueue.add(
        FineTuneJobName.FINE_TUNE_MONITOR,
        jobData,
        jobOptions,
      );

      this.logger.log(
        `Added monitoring job to queue for history ${jobData.historyId}, provider job ${jobData.providerJobId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error adding monitoring job to queue for history ${jobData.historyId}:`,
        error,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi thêm job monitoring vào queue: ${error.message}`,
      );
    }
  }

  /**
   * Validate dữ liệu job
   * @param jobData Dữ liệu job cần validate
   */
  private validateJobData(jobData: FineTuneJobData): void {
    // Validate required fields
    if (!jobData.historyId) {
      throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT, 'historyId is required');
    }

    if (!jobData.userModelFineTuneId) {
      throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT, 'userModelFineTuneId is required');
    }

    if (!jobData.context) {
      throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT, 'context is required');
    }

    if (!jobData.provider) {
      throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT, 'provider is required');
    }

    if (!jobData.modelType) {
      throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT, 'modelType is required');
    }

    if (!jobData.baseModel) {
      throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT, 'baseModel is required');
    }

    // Validate conditional fields
    if (jobData.context === 'USER' && !jobData.userId) {
      throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT, 'userId is required for USER context');
    }

    if (jobData.context === 'ADMIN' && !jobData.employeeId) {
      throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT, 'employeeId is required for ADMIN context');
    }

    if (jobData.modelType === 'USER' && !jobData.userKeyId) {
      throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT, 'userKeyId is required for USER model');
    }

    if (
      jobData.modelType === 'SYSTEM' &&
      jobData.context === 'USER' &&
      !jobData.pointsToRefund
    ) {
      throw new AppException(
        MODELS_ERROR_CODES.INVALID_INPUT,
        'pointsToRefund is required for USER + SYSTEM model',
      );
    }

    // Validate training data
    if (!jobData.trainingData && !jobData.trainingFileId) {
      throw new AppException(
        MODELS_ERROR_CODES.INVALID_INPUT,
        'Phải cung cấp trainingData hoặc trainingFileId',
      );
    }

    if (jobData.trainingData && jobData.trainingFileId) {
      throw new AppException(
        MODELS_ERROR_CODES.INVALID_INPUT,
        'Chỉ được cung cấp trainingData HOẶC trainingFileId',
      );
    }
  }

  /**
   * Xác định job name dựa trên dữ liệu
   * @param jobData Dữ liệu job
   * @returns Tên job
   */
  private getJobName(jobData: FineTuneJobData): string {
    if (jobData.trainingData) {
      // Có training data -> cần upload trong Worker
      return FineTuneJobName.FINE_TUNE_UPLOAD_DATA;
    } else if (jobData.trainingFileId) {
      // Đã có file ID -> xử lý trực tiếp
      return FineTuneJobName.FINE_TUNE_PROCESS;
    } else {
      throw new AppException(
        MODELS_ERROR_CODES.INVALID_INPUT,
        'Phải cung cấp trainingData hoặc trainingFileId',
      );
    }
  }
}
