# Zalo Group Management API Migration Guide

## Tổng quan

API tạo nhóm chat Zalo đã được cập nhật để tương thích với kiến trúc Integration mới và cải thiện bảo mật.

## Thay đổi chính

### 1. Cấu trúc URL

**Trước:**
```
POST /v1/zalo-group-management
```

**Sau:**
```
POST /v1/zalo-group-management/:oaId
```

### 2. Request Body

**Trước:**
```json
{
  "zaloOfficialAccountId": 123,  // number (int)
  "groupName": "Tên nhóm",
  "description": "<PERSON>ô tả",
  "memberUids": ["user1", "user2"],
  "avatarUrl": "https://...",
  "metadata": {}
}
```

**Sau:**
```json
{
  "zaloOfficialAccountId": "123e4567-e89b-12d3-a456-************",  // UUID string
  "groupName": "Tên nhóm", 
  "description": "<PERSON><PERSON> tả",
  "memberUids": ["user1", "user2"],
  "avatarUrl": "https://...",
  "metadata": {}
}
```

### 3. URL Parameters

**Mới:**
- `oaId`: ID của Official Account trên Zalo (từ metadata của Integration)

## Migration Steps

### Bước 1: Database Migration

Chạy script migration để chuyển đổi cột `zalo_official_account_id` từ `int` sang `uuid`:

```sql
-- Xem file: database/migrations/migrate_zalo_groups_to_uuid.sql
```

### Bước 2: Code Changes

#### Frontend/Client Changes

```javascript
// Trước
const response = await fetch('/v1/zalo-group-management', {
  method: 'POST',
  body: JSON.stringify({
    zaloOfficialAccountId: 123,  // number
    groupName: 'Test Group'
  })
});

// Sau  
const response = await fetch(`/v1/zalo-group-management/${oaId}`, {
  method: 'POST',
  body: JSON.stringify({
    zaloOfficialAccountId: 'uuid-string',  // UUID from Integration
    groupName: 'Test Group'
  })
});
```

#### Lấy Integration UUID

```javascript
// Lấy danh sách Integration để có UUID
const integrations = await fetch('/v1/integration/zalo-oa');
const integration = integrations.find(i => i.metadata.oaId === targetOaId);
const integrationUuid = integration.id;
```

### Bước 3: Validation Changes

API mới có validation chặt chẽ hơn:

1. **UUID Validation**: `zaloOfficialAccountId` phải là UUID hợp lệ
2. **Ownership Check**: User phải sở hữu Integration được chỉ định
3. **OA ID Matching**: `oaId` trong URL phải khớp với `oaId` trong Integration metadata

## Error Handling

### Lỗi mới có thể gặp:

```json
{
  "statusCode": 400,
  "message": "zaloOfficialAccountId must be a UUID"
}

{
  "statusCode": 404, 
  "message": "Zalo Official Account Integration không tồn tại"
}

{
  "statusCode": 403,
  "message": "Bạn không có quyền sử dụng Zalo Official Account này"
}

{
  "statusCode": 400,
  "message": "OA ID không khớp với Integration được chỉ định"
}
```

## Testing

Sử dụng file `test_zalo_group_api.http` để test API mới.

## Rollback Plan

Nếu cần rollback:

1. Revert code changes
2. Chạy rollback database migration:
   ```sql
   ALTER TABLE zalo_groups RENAME COLUMN zalo_official_account_id TO zalo_official_account_id_uuid;
   ALTER TABLE zalo_groups RENAME COLUMN zalo_official_account_id_old TO zalo_official_account_id;
   ALTER TABLE zalo_groups DROP COLUMN zalo_official_account_id_uuid;
   ```

## Benefits

1. **Bảo mật**: UUID thay vì số nguyên tăng dần
2. **Performance**: oaId trong URL giúp caching tốt hơn  
3. **Consistency**: Tương thích với kiến trúc Integration
4. **Validation**: Kiểm tra quyền sở hữu chặt chẽ hơn

## Timeline

- **Phase 1**: Database migration (cần downtime ngắn)
- **Phase 2**: Deploy backend changes
- **Phase 3**: Update frontend/client code
- **Phase 4**: Monitor và fix issues nếu có
