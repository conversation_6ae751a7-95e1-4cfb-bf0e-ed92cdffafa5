import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của việc bật/tắt trạng thái website
 */
export class ToggleWebsiteActiveResponseDto {
  /**
   * ID của website
   */
  @ApiProperty({
    description: 'ID của website',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  id: string;

  /**
   * Trạng thái hoạt động mới của website
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động mới của website',
    example: true
  })
  active: boolean;

  /**
   * Thông báo kết quả
   */
  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Website đã được bật thành công'
  })
  message: string;
}
