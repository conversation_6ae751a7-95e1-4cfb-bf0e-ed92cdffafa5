/**
 * Interface cho SSE events của fine-tuning job
 */

/**
 * Trạng thái của fine-tuning job
 */
export enum FineTuningJobStatus {
  QUEUED = 'queued',
  VALIDATING_FILES = 'validating_files',
  RUNNING = 'running',
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

/**
 * Loại event SSE
 */
export enum FineTuningEventType {
  STATUS = 'status',
  PROGRESS = 'progress',
  METRICS = 'metrics',
  ERROR = 'error',
  COMPLETED = 'completed',
  HEARTBEAT = 'heartbeat',
}

/**
 * Base interface cho tất cả SSE events
 */
export interface BaseFineTuningEvent {
  type: FineTuningEventType;
  timestamp: number;
  jobId: string;
}

/**
 * Event cập nhật trạng thái job
 */
export interface StatusUpdateEvent extends BaseFineTuningEvent {
  type: FineTuningEventType.STATUS;
  data: {
    status: FineTuningJobStatus;
    message?: string;
    estimatedTimeRemaining?: number; // seconds
  };
}

/**
 * Event cập nhật tiến độ training
 */
export interface ProgressUpdateEvent extends BaseFineTuningEvent {
  type: FineTuningEventType.PROGRESS;
  data: {
    progress: number; // 0-100
    currentEpoch?: number;
    totalEpochs?: number;
    currentStep?: number;
    totalSteps?: number;
    tokensProcessed?: number;
    totalTokens?: number;
  };
}

/**
 * Event cập nhật metrics training
 */
export interface MetricsUpdateEvent extends BaseFineTuningEvent {
  type: FineTuningEventType.METRICS;
  data: {
    epoch: number;
    step: number;
    trainingLoss?: number;
    validationLoss?: number;
    accuracy?: number;
    learningRate?: number;
    [key: string]: any; // Cho phép metrics khác
  };
}

/**
 * Event thông báo lỗi
 */
export interface ErrorEvent extends BaseFineTuningEvent {
  type: FineTuningEventType.ERROR;
  data: {
    error: string;
    errorCode?: string;
    details?: any;
    serverAction?: 'FORCE_DISCONNECT' | 'DISCONNECT_NOW' | string;
    message?: string;
  };
}

/**
 * Event job hoàn thành
 */
export interface CompletedEvent extends BaseFineTuningEvent {
  type: FineTuningEventType.COMPLETED;
  data: {
    status: FineTuningJobStatus.SUCCEEDED | FineTuningJobStatus.FAILED;
    fineTunedModel?: string;
    trainingStats?: {
      totalTokens: number;
      totalEpochs: number;
      finalLoss?: number;
      finalAccuracy?: number;
      trainingDuration: number; // seconds
    };
    downloadUrl?: string;
  };
}

/**
 * Event heartbeat để duy trì connection
 */
export interface HeartbeatEvent extends BaseFineTuningEvent {
  type: FineTuningEventType.HEARTBEAT;
  data: {
    message: 'alive';
    serverTime: number;
  };
}

/**
 * Union type cho tất cả events
 */
export type FineTuningEvent = 
  | StatusUpdateEvent 
  | ProgressUpdateEvent 
  | MetricsUpdateEvent 
  | ErrorEvent 
  | CompletedEvent 
  | HeartbeatEvent;

/**
 * Interface cho job monitoring state
 */
export interface JobMonitoringState {
  jobId: string;
  userId: number;
  provider: string;
  status: FineTuningJobStatus;
  startTime: number;
  lastUpdate: number;
  subscribers: number;
  isActive: boolean;
}

/**
 * Configuration cho SSE monitoring
 */
export interface SSEConfig {
  heartbeatInterval: number; // milliseconds
  statusCheckInterval: number; // milliseconds
  maxRetries: number;
  connectionTimeout: number; // milliseconds
  cleanupInterval: number; // milliseconds
}
