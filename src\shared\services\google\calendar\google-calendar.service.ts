import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { calendar_v3, google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import {
  GoogleCalendarConfig,
  GoogleCalendarCredentials,
  CalendarInfo,
  CalendarEvent,
  CreateEventRequest,
  UpdateEventRequest,
  ListEventsRequest,
  ListEventsResult,
  FreeBusyRequest,
  FreeBusyResult,
  FreeBusyCalendar,
  FreeBusyGroup,
  QuickAddEventRequest,
  MoveEventRequest,
} from '../interfaces/google-calendar.interface';

/**
 * Service để tương tác với Google Calendar API
 */
@Injectable()
export class GoogleCalendarService {
  private readonly logger = new Logger(GoogleCalendarService.name);
  private oauth2Client: OAuth2Client;
  private calendarConfig: GoogleCalendarConfig;

  constructor(private readonly configService: ConfigService) {
    this.initializeConfig();
    this.initializeOAuth2Client();
  }

  /**
   * Khởi tạo cấu hình từ environment variables
   */
  private initializeConfig(): void {
    this.calendarConfig = {
      clientId: this.configService.get<string>('GOOGLE_CLIENT_ID') || '',
      clientSecret: this.configService.get<string>('GOOGLE_CLIENT_SECRET') || '',
      redirectUri: this.configService.get<string>('GOOGLE_REDIRECT_URI') || '',
    };

    if (!this.calendarConfig.clientId || !this.calendarConfig.clientSecret) {
      this.logger.warn('Google Calendar configuration is incomplete');
    }
  }

  /**
   * Khởi tạo OAuth2 client
   */
  private initializeOAuth2Client(): void {
    this.oauth2Client = new google.auth.OAuth2(
      this.calendarConfig.clientId,
      this.calendarConfig.clientSecret,
      this.calendarConfig.redirectUri,
    );
  }

  /**
   * Thiết lập credentials cho OAuth2 client
   * @param credentials Thông tin xác thực
   */
  private setCredentials(credentials: GoogleCalendarCredentials): void {
    this.oauth2Client.setCredentials({
      access_token: credentials.accessToken,
      refresh_token: credentials.refreshToken,
      expiry_date: credentials.expiresAt,
    });
  }

  /**
   * Lấy instance của Calendar API
   * @param accessToken Access token
   * @returns Calendar API instance
   */
  private getCalendarInstance(accessToken: string): calendar_v3.Calendar {
    this.setCredentials({ accessToken });
    return google.calendar({ version: 'v3', auth: this.oauth2Client });
  }

  /**
   * Lấy danh sách calendars
   * @param accessToken Access token
   * @returns Danh sách calendars
   */
  async getCalendarList(accessToken: string): Promise<CalendarInfo[]> {
    try {
      const calendar = this.getCalendarInstance(accessToken);

      const response = await calendar.calendarList.list();

      const calendars = response.data.items?.map(item => this.mapToCalendarInfo(item)) || [];

      this.logger.log(`Retrieved ${calendars.length} calendars`);

      return calendars;
    } catch (error) {
      this.logger.error(`Error getting calendar list: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy danh sách calendar: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin calendar
   * @param accessToken Access token
   * @param calendarId ID của calendar
   * @returns Thông tin calendar
   */
  async getCalendar(accessToken: string, calendarId: string): Promise<CalendarInfo> {
    try {
      const calendar = this.getCalendarInstance(accessToken);

      const response = await calendar.calendars.get({
        calendarId,
      });

      return this.mapToCalendarInfo(response.data);
    } catch (error) {
      this.logger.error(`Error getting calendar: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy thông tin calendar: ${error.message}`);
    }
  }

  /**
   * Tạo event mới
   * @param accessToken Access token
   * @param request Thông tin tạo event
   * @returns Event đã tạo
   */
  async createEvent(
    accessToken: string,
    request: CreateEventRequest,
  ): Promise<CalendarEvent> {
    try {
      const calendar = this.getCalendarInstance(accessToken);

      const eventResource = this.mapToGoogleEvent(request.event);

      const response = await calendar.events.insert({
        calendarId: request.calendarId,
        requestBody: eventResource,
        conferenceDataVersion: request.conferenceDataVersion,
        maxAttendees: request.maxAttendees,
        sendNotifications: request.sendNotifications,
        sendUpdates: request.sendUpdates,
        supportsAttachments: request.supportsAttachments,
      });

      const createdEvent = this.mapToCalendarEvent(response.data);

      this.logger.log(`Event created: ${createdEvent.summary} (ID: ${createdEvent.id})`);

      return createdEvent;
    } catch (error) {
      this.logger.error(`Error creating event: ${error.message}`, error.stack);
      throw new Error(`Không thể tạo event: ${error.message}`);
    }
  }

  /**
   * Cập nhật event
   * @param accessToken Access token
   * @param request Thông tin cập nhật event
   * @returns Event đã cập nhật
   */
  async updateEvent(
    accessToken: string,
    request: UpdateEventRequest,
  ): Promise<CalendarEvent> {
    try {
      const calendar = this.getCalendarInstance(accessToken);

      const eventResource = this.mapToGoogleEvent(request.event);

      const response = await calendar.events.update({
        calendarId: request.calendarId,
        eventId: request.eventId,
        requestBody: eventResource,
        conferenceDataVersion: request.conferenceDataVersion,
        maxAttendees: request.maxAttendees,
        sendNotifications: request.sendNotifications,
        sendUpdates: request.sendUpdates,
        supportsAttachments: request.supportsAttachments,
      });

      const updatedEvent = this.mapToCalendarEvent(response.data);

      this.logger.log(`Event updated: ${updatedEvent.summary} (ID: ${updatedEvent.id})`);

      return updatedEvent;
    } catch (error) {
      this.logger.error(`Error updating event: ${error.message}`, error.stack);
      throw new Error(`Không thể cập nhật event: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin event
   * @param accessToken Access token
   * @param calendarId ID của calendar
   * @param eventId ID của event
   * @returns Thông tin event
   */
  async getEvent(
    accessToken: string,
    calendarId: string,
    eventId: string,
  ): Promise<CalendarEvent> {
    try {
      const calendar = this.getCalendarInstance(accessToken);

      const response = await calendar.events.get({
        calendarId,
        eventId,
      });

      return this.mapToCalendarEvent(response.data);
    } catch (error) {
      this.logger.error(`Error getting event: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy thông tin event: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách events
   * @param accessToken Access token
   * @param request Thông tin lấy danh sách events
   * @returns Danh sách events
   */
  async listEvents(
    accessToken: string,
    request: ListEventsRequest,
  ): Promise<ListEventsResult> {
    try {
      const calendar = this.getCalendarInstance(accessToken);

      const response = await calendar.events.list({
        calendarId: request.calendarId,
        timeMin: request.timeMin,
        timeMax: request.timeMax,
        q: request.q,
        maxResults: request.maxResults,
        pageToken: request.pageToken,
        orderBy: request.orderBy,
        showDeleted: request.showDeleted,
        showHiddenInvitations: request.showHiddenInvitations,
        singleEvents: request.singleEvents,
        syncToken: request.syncToken,
        updatedMin: request.updatedMin,
      });

      const events = response.data.items?.map(item => this.mapToCalendarEvent(item)) || [];

      this.logger.log(`Retrieved ${events.length} events from calendar ${request.calendarId}`);

      return {
        items: events,
        nextPageToken: response.data.nextPageToken || undefined,
        nextSyncToken: response.data.nextSyncToken || undefined,
        summary: response.data.summary || '',
        description: response.data.description || undefined,
        updated: response.data.updated || '',
        timeZone: response.data.timeZone || '',
        accessRole: response.data.accessRole || '',
        defaultReminders: response.data.defaultReminders?.map(reminder => ({
          method: reminder.method as 'email' | 'popup',
          minutes: reminder.minutes || 0,
        })) || [],
      };
    } catch (error) {
      this.logger.error(`Error listing events: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy danh sách events: ${error.message}`);
    }
  }

  /**
   * Xóa event
   * @param accessToken Access token
   * @param calendarId ID của calendar
   * @param eventId ID của event
   * @param sendNotifications Có gửi thông báo không
   * @param sendUpdates Loại update gửi
   * @returns True nếu thành công
   */
  async deleteEvent(
    accessToken: string,
    calendarId: string,
    eventId: string,
    sendNotifications?: boolean,
    sendUpdates?: 'all' | 'externalOnly' | 'none',
  ): Promise<boolean> {
    try {
      const calendar = this.getCalendarInstance(accessToken);

      await calendar.events.delete({
        calendarId,
        eventId,
        sendNotifications,
        sendUpdates,
      });

      this.logger.log(`Event deleted: ${eventId} from calendar ${calendarId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error deleting event: ${error.message}`, error.stack);
      throw new Error(`Không thể xóa event: ${error.message}`);
    }
  }

  /**
   * Tạo event nhanh từ text
   * @param accessToken Access token
   * @param request Thông tin tạo event nhanh
   * @returns Event đã tạo
   */
  async quickAddEvent(
    accessToken: string,
    request: QuickAddEventRequest,
  ): Promise<CalendarEvent> {
    try {
      const calendar = this.getCalendarInstance(accessToken);

      const response = await calendar.events.quickAdd({
        calendarId: request.calendarId,
        text: request.text,
        sendNotifications: request.sendNotifications,
      });

      const createdEvent = this.mapToCalendarEvent(response.data);

      this.logger.log(`Quick event created: ${createdEvent.summary} (ID: ${createdEvent.id})`);

      return createdEvent;
    } catch (error) {
      this.logger.error(`Error creating quick event: ${error.message}`, error.stack);
      throw new Error(`Không thể tạo quick event: ${error.message}`);
    }
  }

  /**
   * Di chuyển event sang calendar khác
   * @param accessToken Access token
   * @param request Thông tin di chuyển event
   * @returns Event đã di chuyển
   */
  async moveEvent(
    accessToken: string,
    request: MoveEventRequest,
  ): Promise<CalendarEvent> {
    try {
      const calendar = this.getCalendarInstance(accessToken);

      const response = await calendar.events.move({
        calendarId: request.calendarId,
        eventId: request.eventId,
        destination: request.destination,
        sendNotifications: request.sendNotifications,
        sendUpdates: request.sendUpdates,
      });

      const movedEvent = this.mapToCalendarEvent(response.data);

      this.logger.log(`Event moved: ${movedEvent.summary} to calendar ${request.destination}`);

      return movedEvent;
    } catch (error) {
      this.logger.error(`Error moving event: ${error.message}`, error.stack);
      throw new Error(`Không thể di chuyển event: ${error.message}`);
    }
  }

  /**
   * Kiểm tra free/busy time
   * @param accessToken Access token
   * @param request Thông tin kiểm tra free/busy
   * @returns Kết quả free/busy
   */
  async getFreeBusy(
    accessToken: string,
    request: FreeBusyRequest,
  ): Promise<FreeBusyResult> {
    try {
      const calendar = this.getCalendarInstance(accessToken);

      const response = await calendar.freebusy.query({
        requestBody: {
          timeMin: request.timeMin,
          timeMax: request.timeMax,
          timeZone: request.timeZone,
          groupExpansionMax: request.groupExpansionMax,
          calendarExpansionMax: request.calendarExpansionMax,
          items: request.items,
        },
      });

      this.logger.log(`Free/busy query completed for ${request.items.length} calendars`);

      // Map calendars data
      const calendars: Record<string, FreeBusyCalendar> = {};
      if (response.data.calendars) {
        Object.entries(response.data.calendars).forEach(([key, value]) => {
          calendars[key] = {
            busy: value.busy?.map(period => ({
              start: period.start || '',
              end: period.end || '',
            })) || [],
            errors: value.errors?.map(error => ({
              domain: error.domain || '',
              reason: error.reason || '',
            })),
          };
        });
      }

      // Map groups data
      const groups: Record<string, FreeBusyGroup> = {};
      if (response.data.groups) {
        Object.entries(response.data.groups).forEach(([key, value]) => {
          groups[key] = {
            calendars: value.calendars || [],
            errors: value.errors?.map(error => ({
              domain: error.domain || '',
              reason: error.reason || '',
            })),
          };
        });
      }

      return {
        timeMin: response.data.timeMin || '',
        timeMax: response.data.timeMax || '',
        calendars,
        groups,
      };
    } catch (error) {
      this.logger.error(`Error getting free/busy: ${error.message}`, error.stack);
      throw new Error(`Không thể kiểm tra free/busy: ${error.message}`);
    }
  }

  /**
   * Map từ CalendarEvent sang Google Calendar Event
   * @param event CalendarEvent
   * @returns Google Calendar Event
   */
  private mapToGoogleEvent(event: Partial<CalendarEvent>): calendar_v3.Schema$Event {
    return {
      id: event.id,
      summary: event.summary,
      description: event.description,
      location: event.location,
      start: event.start,
      end: event.end,
      attendees: event.attendees,
      organizer: event.organizer,
      creator: event.creator,
      recurrence: event.recurrence,
      recurringEventId: event.recurringEventId,
      originalStartTime: event.originalStartTime,
      status: event.status,
      visibility: event.visibility,
      transparency: event.transparency,
      colorId: event.colorId,
      reminders: event.reminders,
      attachments: event.attachments,
      conferenceData: event.conferenceData,
      guestsCanInviteOthers: event.guestsCanInviteOthers,
      guestsCanModify: event.guestsCanModify,
      guestsCanSeeOtherGuests: event.guestsCanSeeOtherGuests,
      privateCopy: event.privateCopy,
      locked: event.locked,
      source: event.source,
      extendedProperties: event.extendedProperties,
      gadget: event.gadget,
      anyoneCanAddSelf: event.anyoneCanAddSelf,
      hangoutLink: event.hangoutLink,
    };
  }

  /**
   * Map từ Google Calendar Event sang CalendarEvent
   * @param event Google Calendar Event
   * @returns CalendarEvent
   */
  private mapToCalendarEvent(event: calendar_v3.Schema$Event): CalendarEvent {
    return {
      id: event.id || undefined,
      summary: event.summary || '',
      description: event.description || undefined,
      location: event.location || undefined,
      start: event.start ? {
        dateTime: event.start.dateTime || undefined,
        date: event.start.date || undefined,
        timeZone: event.start.timeZone || undefined,
      } : { dateTime: '' },
      end: event.end ? {
        dateTime: event.end.dateTime || undefined,
        date: event.end.date || undefined,
        timeZone: event.end.timeZone || undefined,
      } : { dateTime: '' },
      attendees: event.attendees?.map(attendee => ({
        email: attendee.email || '',
        displayName: attendee.displayName || undefined,
        responseStatus: attendee.responseStatus as 'needsAction' | 'declined' | 'tentative' | 'accepted' | undefined,
        comment: attendee.comment || undefined,
        additionalGuests: attendee.additionalGuests || undefined,
        optional: attendee.optional || undefined,
        resource: attendee.resource || undefined,
        organizer: attendee.organizer || undefined,
        self: attendee.self || undefined,
        id: attendee.id || undefined,
      })),
      organizer: event.organizer ? {
        email: event.organizer.email || '',
        displayName: event.organizer.displayName || undefined,
        self: event.organizer.self || undefined,
        id: event.organizer.id || undefined,
      } : undefined,
      creator: event.creator ? {
        email: event.creator.email || '',
        displayName: event.creator.displayName || undefined,
        self: event.creator.self || undefined,
        id: event.creator.id || undefined,
      } : undefined,
      recurrence: event.recurrence || undefined,
      recurringEventId: event.recurringEventId || undefined,
      originalStartTime: event.originalStartTime ? {
        dateTime: event.originalStartTime.dateTime || undefined,
        date: event.originalStartTime.date || undefined,
        timeZone: event.originalStartTime.timeZone || undefined,
      } : undefined,
      status: event.status as 'confirmed' | 'tentative' | 'cancelled',
      visibility: event.visibility as 'default' | 'public' | 'private' | 'confidential',
      transparency: event.transparency as 'opaque' | 'transparent',
      colorId: event.colorId || undefined,
      reminders: event.reminders ? {
        useDefault: event.reminders.useDefault || undefined,
        overrides: event.reminders.overrides?.map(reminder => ({
          method: (reminder.method as 'email' | 'popup') || 'email',
          minutes: reminder.minutes || 0,
        })),
      } : undefined,
      attachments: event.attachments?.map(attachment => ({
        fileUrl: attachment.fileUrl || '',
        title: attachment.title || undefined,
        mimeType: attachment.mimeType || undefined,
        iconLink: attachment.iconLink || undefined,
        fileId: attachment.fileId || undefined,
      })),
      conferenceData: event.conferenceData ? {
        entryPoints: event.conferenceData.entryPoints?.map(entryPoint => ({
          entryPointType: (entryPoint.entryPointType as 'video' | 'phone' | 'sip' | 'more') || 'video',
          uri: entryPoint.uri || undefined,
          label: entryPoint.label || undefined,
          pin: entryPoint.pin || undefined,
          accessCode: entryPoint.accessCode || undefined,
          meetingCode: entryPoint.meetingCode || undefined,
          passcode: entryPoint.passcode || undefined,
          password: entryPoint.password || undefined,
        })),
        conferenceSolution: event.conferenceData.conferenceSolution ? {
          key: event.conferenceData.conferenceSolution.key ? {
            type: event.conferenceData.conferenceSolution.key.type || '',
          } : undefined,
          name: event.conferenceData.conferenceSolution.name || undefined,
          iconUri: event.conferenceData.conferenceSolution.iconUri || undefined,
        } : undefined,
        conferenceId: event.conferenceData.conferenceId || undefined,
        signature: event.conferenceData.signature || undefined,
        notes: event.conferenceData.notes || undefined,
      } : undefined,
      created: event.created || undefined,
      updated: event.updated || undefined,
      htmlLink: event.htmlLink || undefined,
      iCalUID: event.iCalUID || undefined,
      sequence: event.sequence || undefined,
      guestsCanInviteOthers: event.guestsCanInviteOthers || undefined,
      guestsCanModify: event.guestsCanModify || undefined,
      guestsCanSeeOtherGuests: event.guestsCanSeeOtherGuests || undefined,
      privateCopy: event.privateCopy || undefined,
      locked: event.locked || undefined,
      source: event.source ? {
        url: event.source.url || '',
        title: event.source.title || '',
      } : undefined,
      extendedProperties: event.extendedProperties || undefined,
      gadget: event.gadget ? {
        type: event.gadget.type || '',
        title: event.gadget.title || undefined,
        link: event.gadget.link || undefined,
        iconLink: event.gadget.iconLink || undefined,
        width: event.gadget.width || undefined,
        height: event.gadget.height || undefined,
        display: event.gadget.display || undefined,
        preferences: event.gadget.preferences || undefined,
      } : undefined,
      anyoneCanAddSelf: event.anyoneCanAddSelf || undefined,
      hangoutLink: event.hangoutLink || undefined,
    };
  }

  /**
   * Map từ Google Calendar sang CalendarInfo
   * @param calendar Google Calendar
   * @returns CalendarInfo
   */
  private mapToCalendarInfo(calendar: calendar_v3.Schema$Calendar | calendar_v3.Schema$CalendarListEntry): CalendarInfo {
    return {
      id: calendar.id || '',
      summary: calendar.summary || '',
      description: calendar.description || undefined,
      timeZone: calendar.timeZone || '',
      backgroundColor: (calendar as calendar_v3.Schema$CalendarListEntry).backgroundColor || undefined,
      foregroundColor: (calendar as calendar_v3.Schema$CalendarListEntry).foregroundColor || undefined,
      accessRole: (calendar as calendar_v3.Schema$CalendarListEntry).accessRole || undefined,
      primary: (calendar as calendar_v3.Schema$CalendarListEntry).primary || undefined,
      selected: (calendar as calendar_v3.Schema$CalendarListEntry).selected || undefined,
    };
  }

  /**
   * Kiểm tra kết nối với Google Calendar API
   * @param accessToken Access token
   * @returns True nếu kết nối thành công
   */
  async testConnection(accessToken: string): Promise<boolean> {
    try {
      const calendar = this.getCalendarInstance(accessToken);
      
      // Thử lấy danh sách calendars
      const response = await calendar.calendarList.list({
        maxResults: 1,
      });

      if (response.data.items) {
        this.logger.log('Google Calendar connection test successful');
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Google Calendar connection test failed: ${error.message}`);
      return false;
    }
  }
}
