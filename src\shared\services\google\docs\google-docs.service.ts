import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { docs_v1, google, Auth } from 'googleapis';
import {
  GoogleDocsConfig,
  GoogleDocsCredentials,
  DocumentInfo,
  DocumentBody,
  CreateDocumentRequest,
  BatchUpdateDocumentRequest,
  InsertTextRequest,
  DeleteContentRangeRequest,
  ReplaceAllTextRequest,
  UpdateTextStyleRequest,
  UpdateParagraphStyleRequest,
  InsertTableRequest,
  InsertPageBreakRequest,
  Location,
  Range,
  TextStyle,
  ParagraphStyle,
} from '../interfaces/google-docs.interface';

/**
 * Service để tương tác với Google Docs API
 */
@Injectable()
export class GoogleDocsService {
  private readonly logger = new Logger(GoogleDocsService.name);
  private oauth2Client: Auth.OAuth2Client;
  private docsConfig: GoogleDocsConfig;

  constructor(private readonly configService: ConfigService) {
    this.initializeConfig();
    this.initializeOAuth2Client();
  }

  /**
   * Khởi tạo cấu hình từ environment variables
   */
  private initializeConfig(): void {
    this.docsConfig = {
      clientId: this.configService.get<string>('GOOGLE_CLIENT_ID') || '',
      clientSecret: this.configService.get<string>('GOOGLE_CLIENT_SECRET') || '',
      redirectUri: this.configService.get<string>('GOOGLE_REDIRECT_URI') || '',
    };

    if (!this.docsConfig.clientId || !this.docsConfig.clientSecret) {
      this.logger.warn('Google Docs configuration is incomplete');
    }
  }

  /**
   * Khởi tạo OAuth2 client
   */
  private initializeOAuth2Client(): void {
    this.oauth2Client = new google.auth.OAuth2(
      this.docsConfig.clientId,
      this.docsConfig.clientSecret,
      this.docsConfig.redirectUri,
    );
  }

  /**
   * Thiết lập credentials cho OAuth2 client
   * @param credentials Thông tin xác thực
   */
  private setCredentials(credentials: GoogleDocsCredentials): void {
    this.oauth2Client.setCredentials({
      access_token: credentials.accessToken,
      refresh_token: credentials.refreshToken,
      expiry_date: credentials.expiresAt,
    });
  }

  /**
   * Lấy instance của Docs API
   * @param accessToken Access token
   * @returns Docs API instance
   */
  private getDocsInstance(accessToken: string): docs_v1.Docs {
    this.setCredentials({ accessToken });
    return google.docs({ version: 'v1', auth: this.oauth2Client });
  }

  /**
   * Tạo document mới
   * @param accessToken Access token
   * @param request Thông tin tạo document
   * @returns Thông tin document đã tạo
   */
  async createDocument(
    accessToken: string,
    request: CreateDocumentRequest,
  ): Promise<DocumentInfo> {
    try {
      const docs = this.getDocsInstance(accessToken);

      const response = await docs.documents.create({
        requestBody: {
          title: request.title,
        },
      });

      const document = response.data;
      
      this.logger.log(`Document created: ${document.title} (ID: ${document.documentId})`);

      return this.mapToDocumentInfo(document);
    } catch (error) {
      this.logger.error(`Error creating document: ${error.message}`, error.stack);
      throw new Error(`Không thể tạo document: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin document
   * @param accessToken Access token
   * @param documentId ID của document
   * @returns Thông tin document
   */
  async getDocument(
    accessToken: string,
    documentId: string,
  ): Promise<DocumentInfo> {
    try {
      const docs = this.getDocsInstance(accessToken);

      const response = await docs.documents.get({
        documentId,
      });

      return this.mapToDocumentInfo(response.data);
    } catch (error) {
      this.logger.error(`Error getting document: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy thông tin document: ${error.message}`);
    }
  }

  /**
   * Lấy nội dung text của document
   * @param accessToken Access token
   * @param documentId ID của document
   * @returns Nội dung text
   */
  async getDocumentText(accessToken: string, documentId: string): Promise<string> {
    try {
      const document = await this.getDocument(accessToken, documentId);
      
      if (!document.body?.content) {
        return '';
      }

      let text = '';
      for (const element of document.body.content) {
        if (element.paragraph?.elements) {
          for (const paragraphElement of element.paragraph.elements) {
            if (paragraphElement.textRun?.content) {
              text += paragraphElement.textRun.content;
            }
          }
        }
      }

      return text;
    } catch (error) {
      this.logger.error(`Error getting document text: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy nội dung document: ${error.message}`);
    }
  }

  /**
   * Thêm text vào document
   * @param accessToken Access token
   * @param documentId ID của document
   * @param text Text để thêm
   * @param index Vị trí để thêm (mặc định là cuối document)
   * @returns True nếu thành công
   */
  async insertText(
    accessToken: string,
    documentId: string,
    text: string,
    index?: number,
  ): Promise<boolean> {
    try {
      const docs = this.getDocsInstance(accessToken);

      // Nếu không có index, thêm vào cuối document
      if (index === undefined) {
        const document = await this.getDocument(accessToken, documentId);
        index = this.getDocumentEndIndex(document);
      }

      const insertTextRequest: InsertTextRequest = {
        location: { index },
        text,
      };

      const batchUpdateRequest: BatchUpdateDocumentRequest = {
        requests: [{ insertText: insertTextRequest }],
      };

      await docs.documents.batchUpdate({
        documentId,
        requestBody: batchUpdateRequest,
      });

      this.logger.log(`Text inserted into document: ${documentId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error inserting text: ${error.message}`, error.stack);
      throw new Error(`Không thể thêm text: ${error.message}`);
    }
  }

  /**
   * Thay thế text trong document
   * @param accessToken Access token
   * @param documentId ID của document
   * @param searchText Text cần tìm
   * @param replaceText Text thay thế
   * @param matchCase Có phân biệt hoa thường không
   * @returns Số lượng text đã thay thế
   */
  async replaceText(
    accessToken: string,
    documentId: string,
    searchText: string,
    replaceText: string,
    matchCase: boolean = false,
  ): Promise<number> {
    try {
      const docs = this.getDocsInstance(accessToken);

      const replaceAllTextRequest: ReplaceAllTextRequest = {
        containsText: {
          text: searchText,
          matchCase,
        },
        replaceText,
      };

      const batchUpdateRequest: BatchUpdateDocumentRequest = {
        requests: [{ replaceAllText: replaceAllTextRequest }],
      };

      const response = await docs.documents.batchUpdate({
        documentId,
        requestBody: batchUpdateRequest,
      });

      const replaceCount = response.data.replies?.[0]?.replaceAllText?.occurrencesChanged || 0;
      
      this.logger.log(`Replaced ${replaceCount} occurrences in document: ${documentId}`);

      return replaceCount;
    } catch (error) {
      this.logger.error(`Error replacing text: ${error.message}`, error.stack);
      throw new Error(`Không thể thay thế text: ${error.message}`);
    }
  }

  /**
   * Xóa nội dung trong range
   * @param accessToken Access token
   * @param documentId ID của document
   * @param startIndex Vị trí bắt đầu
   * @param endIndex Vị trí kết thúc
   * @returns True nếu thành công
   */
  async deleteContent(
    accessToken: string,
    documentId: string,
    startIndex: number,
    endIndex: number,
  ): Promise<boolean> {
    try {
      const docs = this.getDocsInstance(accessToken);

      const deleteContentRangeRequest: DeleteContentRangeRequest = {
        range: {
          startIndex,
          endIndex,
        },
      };

      const batchUpdateRequest: BatchUpdateDocumentRequest = {
        requests: [{ deleteContentRange: deleteContentRangeRequest }],
      };

      await docs.documents.batchUpdate({
        documentId,
        requestBody: batchUpdateRequest,
      });

      this.logger.log(`Content deleted from document: ${documentId} (${startIndex}-${endIndex})`);

      return true;
    } catch (error) {
      this.logger.error(`Error deleting content: ${error.message}`, error.stack);
      throw new Error(`Không thể xóa nội dung: ${error.message}`);
    }
  }

  /**
   * Cập nhật style của text
   * @param accessToken Access token
   * @param documentId ID của document
   * @param startIndex Vị trí bắt đầu
   * @param endIndex Vị trí kết thúc
   * @param textStyle Style để áp dụng
   * @returns True nếu thành công
   */
  async updateTextStyle(
    accessToken: string,
    documentId: string,
    startIndex: number,
    endIndex: number,
    textStyle: Partial<TextStyle>,
  ): Promise<boolean> {
    try {
      const docs = this.getDocsInstance(accessToken);

      const updateTextStyleRequest: UpdateTextStyleRequest = {
        range: {
          startIndex,
          endIndex,
        },
        textStyle: textStyle as TextStyle,
        fields: Object.keys(textStyle).join(','),
      };

      const batchUpdateRequest: BatchUpdateDocumentRequest = {
        requests: [{ updateTextStyle: updateTextStyleRequest }],
      };

      await docs.documents.batchUpdate({
        documentId,
        requestBody: batchUpdateRequest,
      });

      this.logger.log(`Text style updated in document: ${documentId} (${startIndex}-${endIndex})`);

      return true;
    } catch (error) {
      this.logger.error(`Error updating text style: ${error.message}`, error.stack);
      throw new Error(`Không thể cập nhật style text: ${error.message}`);
    }
  }

  /**
   * Thêm table vào document
   * @param accessToken Access token
   * @param documentId ID của document
   * @param rows Số hàng
   * @param columns Số cột
   * @param index Vị trí để thêm (mặc định là cuối document)
   * @returns True nếu thành công
   */
  async insertTable(
    accessToken: string,
    documentId: string,
    rows: number,
    columns: number,
    index?: number,
  ): Promise<boolean> {
    try {
      const docs = this.getDocsInstance(accessToken);

      // Nếu không có index, thêm vào cuối document
      if (index === undefined) {
        const document = await this.getDocument(accessToken, documentId);
        index = this.getDocumentEndIndex(document);
      }

      const insertTableRequest: InsertTableRequest = {
        location: { index },
        rows,
        columns,
      };

      const batchUpdateRequest: BatchUpdateDocumentRequest = {
        requests: [{ insertTable: insertTableRequest }],
      };

      await docs.documents.batchUpdate({
        documentId,
        requestBody: batchUpdateRequest,
      });

      this.logger.log(`Table inserted into document: ${documentId} (${rows}x${columns})`);

      return true;
    } catch (error) {
      this.logger.error(`Error inserting table: ${error.message}`, error.stack);
      throw new Error(`Không thể thêm table: ${error.message}`);
    }
  }

  /**
   * Thêm page break
   * @param accessToken Access token
   * @param documentId ID của document
   * @param index Vị trí để thêm (mặc định là cuối document)
   * @returns True nếu thành công
   */
  async insertPageBreak(
    accessToken: string,
    documentId: string,
    index?: number,
  ): Promise<boolean> {
    try {
      const docs = this.getDocsInstance(accessToken);

      // Nếu không có index, thêm vào cuối document
      if (index === undefined) {
        const document = await this.getDocument(accessToken, documentId);
        index = this.getDocumentEndIndex(document);
      }

      const insertPageBreakRequest: InsertPageBreakRequest = {
        location: { index },
      };

      const batchUpdateRequest: BatchUpdateDocumentRequest = {
        requests: [{ insertPageBreak: insertPageBreakRequest }],
      };

      await docs.documents.batchUpdate({
        documentId,
        requestBody: batchUpdateRequest,
      });

      this.logger.log(`Page break inserted into document: ${documentId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error inserting page break: ${error.message}`, error.stack);
      throw new Error(`Không thể thêm page break: ${error.message}`);
    }
  }

  /**
   * Thực hiện batch update với nhiều operations
   * @param accessToken Access token
   * @param documentId ID của document
   * @param request Batch update request
   * @returns True nếu thành công
   */
  async batchUpdate(
    accessToken: string,
    documentId: string,
    request: BatchUpdateDocumentRequest,
  ): Promise<boolean> {
    try {
      const docs = this.getDocsInstance(accessToken);

      await docs.documents.batchUpdate({
        documentId,
        requestBody: request,
      });

      this.logger.log(`Batch update completed for document: ${documentId} (${request.requests.length} operations)`);

      return true;
    } catch (error) {
      this.logger.error(`Error in batch update: ${error.message}`, error.stack);
      throw new Error(`Không thể thực hiện batch update: ${error.message}`);
    }
  }

  /**
   * Map từ Google Docs API response sang DocumentInfo
   * @param document Document từ API
   * @returns DocumentInfo
   */
  private mapToDocumentInfo(document: docs_v1.Schema$Document): DocumentInfo {
    return {
      documentId: document.documentId || '',
      title: document.title || '',
      body: document.body as DocumentBody | undefined,
      revisionId: document.revisionId || '',
    };
  }

  /**
   * Lấy index cuối của document
   * @param document Document info
   * @returns End index
   */
  private getDocumentEndIndex(document: DocumentInfo): number {
    if (!document.body?.content || document.body.content.length === 0) {
      return 1;
    }

    const lastElement = document.body.content[document.body.content.length - 1];
    return lastElement.endIndex - 1;
  }

  /**
   * Kiểm tra kết nối với Google Docs API
   * @param accessToken Access token
   * @returns True nếu kết nối thành công
   */
  async testConnection(accessToken: string): Promise<boolean> {
    try {
      const docs = this.getDocsInstance(accessToken);
      
      // Thử tạo một document test và xóa ngay
      const testDocument = await docs.documents.create({
        requestBody: {
          title: 'Test Connection - ' + new Date().toISOString(),
        },
      });

      if (testDocument.data.documentId) {
        // Xóa document test
        const drive = google.drive({ version: 'v3', auth: this.oauth2Client });
        await drive.files.delete({
          fileId: testDocument.data.documentId,
        });
        
        this.logger.log('Google Docs connection test successful');
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Google Docs connection test failed: ${error.message}`);
      return false;
    }
  }
}
