### Test SMS Campaign API với DTO mới

### Test 1: OTP Campaign với serverId, segmentIds, audienceIds
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "OTP Campaign Test Updated",
  "campaignType": "OTP",
  "description": "Test OTP campaign với DTO mới",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "segmentIds": [1, 2],
  "audienceIds": [1, 2, 3],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}

### Test 2: OTP Campaign chỉ với audienceIds
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "OTP Campaign Audience Only",
  "campaignType": "OTP",
  "description": "Test OTP campaign chỉ với audienceIds",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "audienceIds": [1, 2, 3],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}

### Test 3: OTP Campaign chỉ với segmentIds
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "OTP Campaign Segment Only",
  "campaignType": "OTP",
  "description": "Test OTP campaign chỉ với segmentIds",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "segmentIds": [1],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}

### Test 4: OTP Campaign với phoneNumbers
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "OTP Campaign Phone Numbers",
  "campaignType": "OTP",
  "description": "Test OTP campaign với phoneNumbers",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "phoneNumbers": ["0901234567", "0987654321"],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}

### Test 5: ADS Campaign với scheduledAt
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "ADS Campaign Scheduled",
  "campaignType": "ADS",
  "description": "Test ADS campaign với scheduledAt",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "audienceIds": [1, 2, 3],
  "templateId": 15,
  "templateVariables": {
    "PROMOTION_CODE": "SALE50"
  },
  "scheduledAt": 1735689600
}

### Test 6: Validation Error - Missing serverId
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Invalid Campaign",
  "campaignType": "OTP",
  "audienceIds": [1, 2, 3],
  "templateId": 15
}

### Test 7: Validation Error - Invalid serverId format
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Invalid Server ID",
  "campaignType": "OTP",
  "serverId": "invalid-uuid",
  "audienceIds": [1, 2, 3],
  "templateId": 15
}

### Test 8: Validation Error - No audiences/segments/phoneNumbers
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "No Recipients",
  "campaignType": "OTP",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "templateId": 15
}

### Test 9: Validation Error - ADS without scheduledAt
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "ADS No Schedule",
  "campaignType": "ADS",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "audienceIds": [1, 2, 3],
  "templateId": 15
}
