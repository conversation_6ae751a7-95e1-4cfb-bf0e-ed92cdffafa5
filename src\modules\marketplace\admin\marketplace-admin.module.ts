import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Product, Cart, CartItem, MarketOrder, MarketOrderLine, UserBuyProductMarketplace, FlashSale } from '../entities';
import { ProductRepository, CartRepository, CartItemRepository, MarketOrderRepository, MarketOrderLineRepository, UserBuyProductMarketplaceRepository, FlashSaleRepository } from '../repositories';
import { S3Service } from '@shared/services/s3.service';
import { RedisService } from '@shared/services/redis.service';
import { CdnService } from '@shared/services/cdn.service';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { User } from '@modules/user/entities';
import { ProductHelper, ProductAutoHelper, Validation<PERSON>elper, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FlashSaleValidationHelper } from '../helpers';
import { EmployeeRepository } from '@modules/employee/repositories/employee.repository';
import { Employee } from '@modules/employee/entities';
import { SqlHelper } from '@common/helpers/sql.helper';
// Import entities và repositories cho validation
import { KnowledgeFile } from '@modules/data/knowledge-files/entities';
import { Agent, AgentMemories, TypeAgent } from '@modules/agent/entities';
import { UserDataFineTune, AdminDataFineTune } from '@modules/models/entities';
import { AdminTool } from '@modules/tools/entities';
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { AgentRepository, AgentMemoriesRepository, TypeAgentRepository } from '@modules/agent/repositories';
import { UserDataFineTuneRepository, AdminDataFineTuneRepository } from '@modules/models/repositories';
import { AdminToolRepository } from '@modules/tools/repositories';
import {
  ProductAdminController,
  OrderAdminController,
  CartAdminController,
  FlashSaleAdminController
} from './controllers';
import {
  ProductAdminService,
  OrderAdminService,
  CartAdminService,
  FlashSaleAdminService
} from './services';
// ✅ REMOVED: FlashSaleStatusUpdateService chỉ register ở User Module để tránh duplicate cron jobs
// Import Validators
import { AgentValidator, KnowledgeFileValidator, FineTuneValidator } from '../validation';

/**
 * Module quản lý chức năng marketplace cho admin
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product,
      Cart,
      CartItem,
      MarketOrder,
      MarketOrderLine,
      UserBuyProductMarketplace,
      FlashSale,
      User,
      Employee,
      KnowledgeFile,
      Agent,
      TypeAgent,
      AgentMemories,
      UserDataFineTune,
      AdminDataFineTune,
      AdminTool,
    ]),
  ],
  controllers: [
    ProductAdminController,
    OrderAdminController,
    CartAdminController,
    FlashSaleAdminController
  ],
  providers: [
    // Services
    ProductAdminService,
    OrderAdminService,
    CartAdminService,
    FlashSaleAdminService,

    // Repositories
    ProductRepository,
    CartRepository,
    CartItemRepository,
    MarketOrderRepository,
    MarketOrderLineRepository,
    UserBuyProductMarketplaceRepository,
    FlashSaleRepository,
    UserRepository,
    EmployeeRepository,
    KnowledgeFileRepository,
    AgentRepository,
    TypeAgentRepository,
    AgentMemoriesRepository,
    UserDataFineTuneRepository,
    AdminDataFineTuneRepository,
    AdminToolRepository,

    // Shared services
    S3Service,
    RedisService,
    CdnService,
    SqlHelper,

    // Helpers
    ProductHelper,
    ProductAutoHelper,
    CartHelper,
    OrderHelper,
    ValidationHelper,
    MediaHelper,
    FlashSaleValidationHelper,

    // ✅ REMOVED: FlashSaleStatusUpdateService để tránh duplicate cron jobs

    // Validators
    AgentValidator,
    KnowledgeFileValidator,
    FineTuneValidator
  ],
  exports: [
    ProductAdminService,
    OrderAdminService,
    CartAdminService,
    FlashSaleAdminService
  ],
})
export class MarketplaceAdminModule {}
