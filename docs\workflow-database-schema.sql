-- =====================================================
-- WORKFLOW MODULE - COMPLETE DATABASE SCHEMA
-- =====================================================
-- Version: 1.0
-- Date: 2025-01-13
-- Description: Complete SQL schema for workflow automation platform
-- Tables: 6 tables (4 existing enhanced + 2 new)

-- Enable UUID extension if not exists
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. WORKFLOWS - Main workflow metadata table
-- =====================================================
CREATE TABLE IF NOT EXISTS workflows (
    id UUID DEFAULT uuid_generate_v4() NOT NULL PRIMARY KEY,
    user_id INTEGER,
    employee_id INTEGER,
    name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT false NOT NULL,
    created_at BIGINT DEFAULT ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint,
    updated_at BIGINT DEFAULT ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint,
    definition JSONB DEFAULT '{}'::jsonb NOT NULL,
    
    -- Constraints
    CONSTRAINT uq_workflows_user_name UNIQUE (user_id, name)
);

-- Comments for workflows table
COMMENT ON TABLE workflows IS 'Bảng chính chứa định nghĩa của các workflow';
COMMENT ON COLUMN workflows.id IS 'ID định danh duy nhất cho một workflow';
COMMENT ON COLUMN workflows.user_id IS 'Khóa ngoại, liên kết đến người dùng sở hữu workflow';
COMMENT ON COLUMN workflows.employee_id IS 'Khóa ngoại, liên kết đến nhân viên chịu trách nhiệm cho workflow';
COMMENT ON COLUMN workflows.name IS 'Tên của workflow, phải là duy nhất cho mỗi người dùng';
COMMENT ON COLUMN workflows.is_active IS 'Trạng thái kích hoạt của workflow (true = đang chạy, false = đã tắt)';
COMMENT ON COLUMN workflows.created_at IS 'Thời gian tạo workflow, lưu dưới dạng Unix timestamp (milliseconds)';
COMMENT ON COLUMN workflows.updated_at IS 'Thời gian cập nhật workflow lần cuối, lưu dưới dạng Unix timestamp (milliseconds)';
COMMENT ON COLUMN workflows.definition IS 'Lược đồ Workflow Tổng thể - lưu trữ định nghĩa cấu trúc workflow (backup/legacy)';

-- =====================================================
-- 2. NODE_DEFINITIONS - Registry for all available node types
-- =====================================================
CREATE TABLE IF NOT EXISTS node_definitions (
    type VARCHAR(100) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL DEFAULT 'system',
    input_schema JSONB NOT NULL,
    output_schema JSONB NOT NULL,
    version VARCHAR(50) NOT NULL,
    
    -- Constraints
    CONSTRAINT chk_node_definitions_category CHECK (
        category IN (
            'system', 'google_sheet', 'google_docs', 'google_gmail', 
            'google_ads', 'google_drive', 'google_calendar', 'facebook_page', 
            'facebook_ads', 'facebook_messenger', 'instagram', 'zalo_oa', 
            'zalo_zns', 'zalo'
        )
    )
);

-- Comments for node_definitions table
COMMENT ON TABLE node_definitions IS 'Registry cho tất cả các loại node có sẵn trong hệ thống (192 types)';
COMMENT ON COLUMN node_definitions.type IS 'Loại node, đóng vai trò là khóa chính (e.g., google.sheet.getRows)';
COMMENT ON COLUMN node_definitions.name IS 'Tên hiển thị của node';
COMMENT ON COLUMN node_definitions.description IS 'Mô tả chức năng của node';
COMMENT ON COLUMN node_definitions.category IS 'Danh mục của node (system, google_sheet, facebook_page, etc.)';
COMMENT ON COLUMN node_definitions.input_schema IS 'JSON Schema định nghĩa cấu trúc form cấu hình cho node';
COMMENT ON COLUMN node_definitions.output_schema IS 'JSON Schema định nghĩa cấu trúc các đầu ra của node';
COMMENT ON COLUMN node_definitions.version IS 'Phiên bản của node definition';

-- =====================================================
-- 3. WORKFLOW_NODES - Node instances within workflows
-- =====================================================
CREATE TABLE IF NOT EXISTS workflow_nodes (
    id UUID DEFAULT uuid_generate_v4() NOT NULL PRIMARY KEY,
    workflow_id UUID NOT NULL,
    node_id VARCHAR(255) NOT NULL,
    node_type VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    config JSONB DEFAULT '{}'::jsonb NOT NULL,
    position JSONB DEFAULT '{"x": 0, "y": 0}'::jsonb NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb NOT NULL,
    created_at BIGINT DEFAULT ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint,
    updated_at BIGINT DEFAULT ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint,
    
    -- Constraints
    CONSTRAINT uq_workflow_nodes_workflow_node UNIQUE (workflow_id, node_id),
    CONSTRAINT fk_workflow_nodes_workflow FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE,
    CONSTRAINT fk_workflow_nodes_node_type FOREIGN KEY (node_type) REFERENCES node_definitions(type)
);

-- Comments for workflow_nodes table
COMMENT ON TABLE workflow_nodes IS 'Lưu trữ các node instances trong một workflow cụ thể';
COMMENT ON COLUMN workflow_nodes.id IS 'ID định danh duy nhất cho một workflow node';
COMMENT ON COLUMN workflow_nodes.workflow_id IS 'Khóa ngoại, liên kết đến workflow chứa node này';
COMMENT ON COLUMN workflow_nodes.node_id IS 'ID của node trong workflow (unique trong một workflow)';
COMMENT ON COLUMN workflow_nodes.node_type IS 'Loại node, tham chiếu đến node_definitions.type';
COMMENT ON COLUMN workflow_nodes.name IS 'Tên hiển thị của node instance';
COMMENT ON COLUMN workflow_nodes.description IS 'Mô tả của node instance';
COMMENT ON COLUMN workflow_nodes.config IS 'Cấu hình của node instance (input parameters, settings)';
COMMENT ON COLUMN workflow_nodes.position IS 'Vị trí của node trên canvas (x, y coordinates)';
COMMENT ON COLUMN workflow_nodes.metadata IS 'Metadata bổ sung cho node (UI state, annotations, etc.)';

-- =====================================================
-- 4. WORKFLOW_EDGES - Connections between nodes
-- =====================================================
CREATE TABLE IF NOT EXISTS workflow_edges (
    id UUID DEFAULT uuid_generate_v4() NOT NULL PRIMARY KEY,
    workflow_id UUID NOT NULL,
    edge_id VARCHAR(255) NOT NULL,
    source_node_id VARCHAR(255) NOT NULL,
    source_port VARCHAR(100),
    target_node_id VARCHAR(255) NOT NULL,
    target_port VARCHAR(100),
    edge_type VARCHAR(50) DEFAULT 'normal' NOT NULL,
    condition JSONB,
    metadata JSONB DEFAULT '{}'::jsonb NOT NULL,
    created_at BIGINT DEFAULT ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint,
    updated_at BIGINT DEFAULT ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint,
    
    -- Constraints
    CONSTRAINT uq_workflow_edges_workflow_edge UNIQUE (workflow_id, edge_id),
    CONSTRAINT fk_workflow_edges_workflow FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE,
    CONSTRAINT chk_workflow_edges_type CHECK (edge_type IN ('normal', 'conditional', 'error', 'success'))
);

-- Comments for workflow_edges table
COMMENT ON TABLE workflow_edges IS 'Lưu trữ các kết nối giữa các nodes trong workflow';
COMMENT ON COLUMN workflow_edges.id IS 'ID định danh duy nhất cho một workflow edge';
COMMENT ON COLUMN workflow_edges.workflow_id IS 'Khóa ngoại, liên kết đến workflow chứa edge này';
COMMENT ON COLUMN workflow_edges.edge_id IS 'ID của edge trong workflow (unique trong một workflow)';
COMMENT ON COLUMN workflow_edges.source_node_id IS 'ID của source node';
COMMENT ON COLUMN workflow_edges.source_port IS 'Output port của source node (nếu có nhiều outputs)';
COMMENT ON COLUMN workflow_edges.target_node_id IS 'ID của target node';
COMMENT ON COLUMN workflow_edges.target_port IS 'Input port của target node (nếu có nhiều inputs)';
COMMENT ON COLUMN workflow_edges.edge_type IS 'Loại edge (normal, conditional, error, success)';
COMMENT ON COLUMN workflow_edges.condition IS 'Điều kiện để edge được kích hoạt (cho conditional edges)';
COMMENT ON COLUMN workflow_edges.metadata IS 'Metadata bổ sung cho edge (UI styling, labels, etc.)';
