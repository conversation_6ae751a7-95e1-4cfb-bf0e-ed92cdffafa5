# Workflow API Documentation

## 📋 Overview

Welcome to the comprehensive Workflow API documentation. This module provides a complete workflow automation platform with real-time execution monitoring, node testing capabilities, and webhook integrations.

## 🚀 Quick Start

### 1. Authentication

All API endpoints require JWT authentication:

```http
Authorization: Bearer <your-jwt-token>
```

### 2. Base URL

```
http://localhost:3000/api/v1
```

### 3. Content Type

```http
Content-Type: application/json
```

## 📚 Documentation Sections

### 🔧 Core Documentation

- **[API Usage Examples](./api-usage-examples.md)** - Comprehensive examples for all endpoints
- **[Error Handling Guide](./api-error-handling.md)** - Complete error codes and handling strategies
- **[Swagger Documentation](http://localhost:3000/api/docs)** - Interactive API documentation

### 🔄 Workflow Management

#### Endpoints Overview

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/user/workflows` | List user workflows with pagination |
| `POST` | `/user/workflows` | Create new workflow |
| `GET` | `/user/workflows/{id}` | Get workflow details |
| `PUT` | `/user/workflows/{id}` | Update workflow |
| `DELETE` | `/user/workflows/{id}` | Delete workflow |
| `POST` | `/user/workflows/{id}/duplicate` | Duplicate workflow |

#### Key Features

- ✅ **CRUD Operations** - Complete workflow management
- ✅ **Validation** - Comprehensive input validation
- ✅ **Pagination** - Efficient data retrieval
- ✅ **Search & Filter** - Advanced query capabilities
- ✅ **Access Control** - User-based isolation

### ⚡ Workflow Execution

#### Endpoints Overview

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/user/workflow-executions` | Trigger workflow execution |
| `GET` | `/user/workflow-executions` | List executions with filters |
| `GET` | `/user/workflow-executions/{id}` | Get execution details |
| `POST` | `/user/workflow-executions/{id}/start` | Start queued execution |
| `POST` | `/user/workflow-executions/{id}/cancel` | Cancel execution |
| `POST` | `/user/workflow-executions/{id}/pause` | Pause execution |
| `POST` | `/user/workflow-executions/{id}/resume` | Resume execution |
| `POST` | `/user/workflow-executions/{id}/retry` | Retry failed execution |
| `GET` | `/user/workflow-executions/statistics/overview` | Get execution statistics |

#### Key Features

- ✅ **Real-time Monitoring** - Live execution status updates
- ✅ **Execution Control** - Start, pause, resume, cancel operations
- ✅ **Retry Mechanism** - Automatic and manual retry capabilities
- ✅ **Progress Tracking** - Detailed execution progress
- ✅ **Statistics** - Performance metrics and analytics

### 🧪 Node Testing

#### Endpoints Overview

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/user/node-tests` | Create and execute node test |
| `GET` | `/user/node-tests` | List node tests |
| `GET` | `/user/node-tests/{testId}` | Get test details |
| `DELETE` | `/user/node-tests/{testId}` | Cancel test |
| `POST` | `/user/node-tests/mock-data` | Generate mock data |
| `POST` | `/user/node-tests/bulk` | Execute bulk tests |
| `GET` | `/user/node-tests/history/{nodeId}` | Get test history |
| `POST` | `/user/node-tests/{testId}/retry` | Retry failed test |
| `GET` | `/user/node-tests/statistics/overview` | Get test statistics |

#### Key Features

- ✅ **Individual Testing** - Test nodes in isolation
- ✅ **Mock Data Generation** - Automatic test data creation
- ✅ **Bulk Testing** - Test multiple nodes simultaneously
- ✅ **Test History** - Track testing over time
- ✅ **Real-time Results** - Live test execution updates

### 📡 Real-time Updates (SSE)

#### Endpoints Overview

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/user/workflow-sse/executions/stream` | Stream execution events |
| `GET` | `/user/workflow-sse/nodes/stream` | Stream node events |
| `GET` | `/user/workflow-sse/tests/stream` | Stream test events |
| `GET` | `/user/workflow-sse/combined/stream` | Stream combined events |

#### Key Features

- ✅ **Server-Sent Events** - Real-time event streaming
- ✅ **Event Filtering** - Filter by execution, node, or test
- ✅ **Connection Management** - Automatic reconnection and heartbeat
- ✅ **Multiple Streams** - Separate streams for different event types

### 🔗 Webhook Integration

#### Endpoints Overview

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/webhooks/workflow/trigger` | Trigger workflow via webhook |
| `POST` | `/webhooks/workflow/validate` | Validate webhook payload |

#### Key Features

- ✅ **Webhook Triggers** - External system integration
- ✅ **Signature Validation** - Secure webhook processing
- ✅ **Payload Validation** - Input data verification
- ✅ **Automatic Execution** - Seamless workflow triggering

## 🔍 Response Format

All API responses follow the standardized format:

```json
{
  "code": 0,
  "message": "Success",
  "result": { ... },
  "timestamp": "2023-01-01T00:00:00.000Z",
  "path": "/api/v1/user/workflows"
}
```

### Success Response

- **`code`**: `0` for success
- **`message`**: Success message
- **`result`**: Response data
- **`timestamp`**: Response timestamp
- **`path`**: Request path

### Error Response

- **`code`**: Error code (> 0)
- **`message`**: Error message
- **`result`**: `null`
- **`detail`**: Error details
- **`timestamp`**: Error timestamp
- **`path`**: Request path

## 🛡️ Security

### Authentication

- **JWT Bearer Token** required for all endpoints
- **Token Validation** on every request
- **User Isolation** - Users can only access their own resources

### Authorization

- **Role-based Access** - User vs Admin permissions
- **Resource Ownership** - Users can only modify their own workflows
- **API Rate Limiting** - Protection against abuse

### Data Validation

- **Input Validation** - Comprehensive request validation
- **Schema Validation** - Workflow definition validation
- **Sanitization** - Input data sanitization

## 📊 Performance

### Pagination

- **Default Limit**: 20 items per page
- **Maximum Limit**: 100 items per page
- **Cursor-based** pagination for large datasets

### Caching

- **Response Caching** - Cached responses for read operations
- **Cache Invalidation** - Automatic cache updates
- **ETags** - Conditional requests support

### Rate Limiting

- **100 requests/minute** per user for standard endpoints
- **10 requests/minute** per user for execution endpoints
- **Rate limit headers** included in responses

## 🔧 Development Tools

### Swagger UI

Interactive API documentation available at:
```
http://localhost:3000/api/docs
```

### Postman Collection

Import the Postman collection for easy testing:
```
http://localhost:3000/api/docs-json
```

### SDK Support

- **TypeScript SDK** - Type-safe API client
- **JavaScript SDK** - Browser and Node.js support
- **Python SDK** - Python integration library

## 📞 Support

### Documentation

- **API Reference** - Complete endpoint documentation
- **Code Examples** - Working code samples
- **Error Codes** - Comprehensive error reference

### Community

- **GitHub Issues** - Bug reports and feature requests
- **Discord Channel** - Real-time community support
- **Stack Overflow** - Tagged questions and answers

### Enterprise Support

- **Priority Support** - Dedicated support channel
- **Custom Integration** - Tailored integration assistance
- **SLA Guarantees** - Service level agreements

---

## 🚀 Getting Started

1. **[Read the Usage Examples](./api-usage-examples.md)** - Start with practical examples
2. **[Understand Error Handling](./api-error-handling.md)** - Learn error management
3. **[Explore Swagger Docs](http://localhost:3000/api/docs)** - Interactive API testing
4. **[Join the Community](#community)** - Get help and share feedback

Happy workflow automation! 🎉
