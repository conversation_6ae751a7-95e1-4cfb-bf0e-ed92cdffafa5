# Google Docs Integration Controller

Controller x<PERSON> lý các API liên quan đến tích hợp Google Docs trong hệ thống RedAI.

## Tổng quan

Google Docs Integration cho phép người dùng:
- <PERSON><PERSON><PERSON> thực với Google OAuth 2.0
- <PERSON><PERSON><PERSON><PERSON> lý các tích hợp Google Docs
- Tạo, đ<PERSON><PERSON>, cập nhật documents
- <PERSON><PERSON><PERSON> s<PERSON>ch documents từ Google Drive

## API Endpoints

### Authentication & Integration Management

#### 1. Tạo URL xác thực O<PERSON>uth
```http
POST /api/v1/user/integration/google-docs/auth-url
```

**Request Body:**
```json
{
  "redirectUri": "https://app.redai.vn/integration/google-docs/callback",
  "state": "random-state-string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "URL xác thực được tạo thành công",
  "data": {
    "authUrl": "https://accounts.google.com/o/oauth2/v2/auth?client_id=...",
    "state": "random-state-string"
  }
}
```

#### 2. X<PERSON> lý OAuth callback
```http
POST /api/v1/user/integration/google-docs/oauth-callback
```

**Request Body:**
```json
{
  "code": "4/0AX4XfWjYZ...",
  "state": "random-state-string",
  "redirectUri": "https://app.redai.vn/integration/google-docs/callback"
}
```

#### 3. Tạo integration thủ công
```http
POST /api/v1/user/integration/google-docs
```

**Request Body:**
```json
{
  "integrationName": "My Google Docs Integration",
  "accessToken": "ya29.a0AfH6SMC...",
  "refreshToken": "1//04...",
  "expiresAt": *************,
  "scope": "https://www.googleapis.com/auth/documents"
}
```

#### 4. Lấy danh sách integrations
```http
GET /api/v1/user/integration/google-docs?page=1&limit=10&status=connected
```

#### 5. Cập nhật integration
```http
PUT /api/v1/user/integration/google-docs/{integrationId}
```

#### 6. Xóa integration
```http
DELETE /api/v1/user/integration/google-docs/{integrationId}
```

#### 7. Làm mới access token
```http
POST /api/v1/user/integration/google-docs/{integrationId}/refresh-token
```

#### 8. Thu hồi quyền truy cập
```http
DELETE /api/v1/user/integration/google-docs/{integrationId}/revoke-access
```

### Document Operations

#### 9. Lấy danh sách documents
```http
GET /api/v1/user/integration/google-docs/{integrationId}/documents?pageSize=10&orderBy=modifiedTime&sortOrder=desc
```

**Query Parameters:**
- `search`: Tìm kiếm theo tên document
- `pageSize`: Số lượng documents (1-100, mặc định 10)
- `pageToken`: Token để lấy trang tiếp theo
- `orderBy`: Sắp xếp theo (createdTime, modifiedTime, name)
- `sortOrder`: Thứ tự sắp xếp (asc, desc)

#### 10. Lấy thông tin document
```http
GET /api/v1/user/integration/google-docs/{integrationId}/documents/{documentId}?includeContent=false
```

#### 11. Đọc nội dung document
```http
GET /api/v1/user/integration/google-docs/{integrationId}/documents/{documentId}/content?format=text&includeFormatting=false
```

**Query Parameters:**
- `format`: Định dạng nội dung (text, html, markdown)
- `includeFormatting`: Có bao gồm thông tin formatting không

#### 12. Tạo document mới
```http
POST /api/v1/user/integration/google-docs/{integrationId}/documents
```

**Request Body:**
```json
{
  "title": "New Document",
  "content": "This is the initial content of the document.",
  "folderId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
}
```

#### 13. Cập nhật nội dung document
```http
PUT /api/v1/user/integration/google-docs/{integrationId}/documents/{documentId}/content
```

**Request Body:**
```json
{
  "content": "This is the updated content.",
  "insertIndex": 1,
  "replaceAll": false
}
```

## Scopes Required

Google Docs integration yêu cầu các scopes sau:
- `https://www.googleapis.com/auth/documents` - Truy cập Google Docs
- `https://www.googleapis.com/auth/drive.file` - Truy cập Google Drive files
- `https://www.googleapis.com/auth/userinfo.email` - Thông tin email người dùng
- `https://www.googleapis.com/auth/userinfo.profile` - Thông tin profile người dùng

## Error Codes

| Code | Message | HTTP Status |
|------|---------|-------------|
| 11700 | Xác thực Google Docs thất bại | 401 |
| 11701 | Làm mới token Google Docs thất bại | 401 |
| 11702 | Thu hồi quyền truy cập Google Docs thất bại | 400 |
| 11703 | Lỗi API Google Docs | 400 |
| 11704 | Không tìm thấy document | 404 |
| 11705 | Không có quyền truy cập document | 403 |

## Usage Statistics

Hệ thống tự động theo dõi thống kê sử dụng:
- `documentsCreated`: Số documents đã tạo
- `documentsRead`: Số lần đọc documents
- `documentsUpdated`: Số lần cập nhật documents
- `lastUsedAt`: Thời gian sử dụng cuối cùng

## Security

- Tất cả sensitive data (access token, refresh token) được mã hóa trước khi lưu database
- Sử dụng JWT authentication cho tất cả endpoints
- Tự động refresh token khi hết hạn
- Hỗ trợ revoke token để thu hồi quyền truy cập

## Examples

### Tạo integration và document mới

```javascript
// 1. Tạo auth URL
const authResponse = await fetch('/api/v1/user/integration/google-docs/auth-url', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    redirectUri: 'https://app.redai.vn/integration/google-docs/callback'
  })
});

// 2. Redirect user to authUrl
window.location.href = authResponse.data.authUrl;

// 3. Handle callback (sau khi user authorize)
const callbackResponse = await fetch('/api/v1/user/integration/google-docs/oauth-callback', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    code: 'AUTHORIZATION_CODE_FROM_GOOGLE',
    redirectUri: 'https://app.redai.vn/integration/google-docs/callback'
  })
});

const integration = callbackResponse.data;

// 4. Tạo document mới
const documentResponse = await fetch(`/api/v1/user/integration/google-docs/${integration.id}/documents`, {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    title: 'My New Document',
    content: 'Hello, this is my first document created via API!'
  })
});

console.log('Document created:', documentResponse.data);
```

## Notes

- Token sẽ tự động được refresh khi hết hạn
- Tất cả operations đều được log để audit
- Hỗ trợ pagination cho danh sách documents
- Document content được extract thành plain text, HTML hoặc Markdown
- Hỗ trợ tạo document trong folder cụ thể
