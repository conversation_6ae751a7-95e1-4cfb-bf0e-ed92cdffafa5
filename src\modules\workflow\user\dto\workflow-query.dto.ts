import { SortDirection } from '@common/dto/query.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';
import { IsEnum, IsOptional, IsBoolean, IsString, IsInt, Min, Max } from 'class-validator';

/**
 * Enum cho các trường sắp xếp của workflow
 */
export enum WorkflowSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  IS_ACTIVE = 'isActive',
}

/**
 * DTO cho việc truy vấn danh sách workflow
 * Following agent-query.dto patterns
 */
export class WorkflowQueryDto {
  /**
   * Số trang hiện tại (bắt đầu từ 1)
   */
  @ApiPropertyOptional({
    description: 'Số trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  page: number = 1;

  /**
   * S<PERSON> lượng bản ghi trên mỗi trang
   */
  @ApiPropertyOptional({
    description: 'Số lượng bản ghi trên mỗi trang',
    example: 10,
    default: 10,
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Max(100)
  @Min(1)
  limit: number = 10;

  /**
   * Từ khóa tìm kiếm theo tên workflow
   */
  @ApiPropertyOptional({
    description: 'Từ khóa tìm kiếm theo tên workflow',
    example: 'My Workflow',
  })
  @IsOptional()
  @IsString()
  search?: string;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: WorkflowSortBy,
    default: WorkflowSortBy.UPDATED_AT,
  })
  @IsEnum(WorkflowSortBy)
  @IsOptional()
  sortBy: WorkflowSortBy = WorkflowSortBy.UPDATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
  })
  @IsEnum(SortDirection)
  @IsOptional()
  sortDirection: SortDirection = SortDirection.DESC;

  /**
   * Lọc theo trạng thái kích hoạt
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái kích hoạt (true: đang hoạt động, false: đã tắt)',
    example: true,
  })
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
