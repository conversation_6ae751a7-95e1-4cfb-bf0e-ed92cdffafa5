import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID, IsEnum, IsOptional, IsString, ValidateIf } from 'class-validator';
import { ProviderFineTuneEnum } from '@modules/models/constants/provider.enum';

/**
 * DTO cho admin execute fine-tuning job
 */
export class AdminExecuteFineTuningDto {
  /**
   * ID của dataset đã được validate
   */
  @ApiProperty({
    description: 'UUID của dataset đã được validate từ bảng admin_data_fine_tune',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsNotEmpty()
  datasetId: string;

  /**
   * ID của model cơ sở từ bảng models
   */
  @ApiProperty({
    description: 'UUID của model cơ sở để fine-tune từ bảng models',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  modelId: string;

  /**
   * Nhà cung cấp AI
   */
  @ApiProperty({
    description: 'Nhà cung cấp AI',
    enum: ProviderFineTuneEnum,
    example: ProviderFineTuneEnum.OPENAI,
  })
  @IsEnum(ProviderFineTuneEnum)
  provider: ProviderFineTuneEnum;

  /**
   * Suffix cho tên model (chỉ cho OpenAI)
   */
  @ApiPropertyOptional({
    description: 'Suffix cho tên model (chỉ cho OpenAI)',
    example: 'admin-model-v1',
    maxLength: 40,
  })
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.provider === ProviderFineTuneEnum.OPENAI)
  suffix?: string;

  /**
   * Siêu tham số cho quá trình fine-tuning
   */
  @ApiPropertyOptional({
    description: 'Siêu tham số cho quá trình fine-tuning',
    type: 'object',
    additionalProperties: true,
    example: {
      epochs: 3,
      batchSize: 'auto',
      learningRate: 0.0001,
    },
  })
  @IsOptional()
  hyperparameters?: {
    /**
     * Số epoch để huấn luyện (OpenAI: 1-50, Google: 1-100)
     */
    epochs?: number;

    /**
     * Kích thước batch (OpenAI: auto hoặc số, Google: số)
     */
    batchSize?: number | 'auto';

    /**
     * Tốc độ học (OpenAI: auto hoặc số, Google: số)
     */
    learningRate?: number | 'auto';
  };
}

/**
 * DTO cho response của admin execute fine-tuning
 */
export class AdminExecuteFineTuningResponseDto {
  /**
   * ID của job fine-tuning từ provider
   */
  @ApiProperty({
    description: 'ID của job fine-tuning từ provider (ví dụ: ft-abc123def456)',
    example: 'ft-abc123def456',
  })
  jobId: string;

  /**
   * ID của model mới được tạo
   */
  @ApiProperty({
    description: 'UUID của model mới được tạo trong hệ thống',
    example: '789e0123-e89b-12d3-a456-************',
  })
  modelId: string;

  /**
   * Trạng thái ban đầu của job
   */
  @ApiProperty({
    description: 'Trạng thái ban đầu của job fine-tuning',
    example: 'validating_files',
  })
  status: string;

  /**
   * Số lượng token được sử dụng
   */
  @ApiProperty({
    description: 'Số lượng token được sử dụng cho fine-tuning',
    example: 15000,
  })
  tokensUsed: number;

  /**
   * Thời gian ước tính hoàn thành (phút)
   */
  @ApiProperty({
    description: 'Thời gian ước tính hoàn thành (phút)',
    example: 60,
  })
  estimatedDurationMinutes: number;

  /**
   * Thông báo
   */
  @ApiProperty({
    description: 'Thông báo cho admin',
    example: 'Fine-tuning job đã được tạo thành công. Job sẽ được monitor tự động.',
  })
  message: string;
}
