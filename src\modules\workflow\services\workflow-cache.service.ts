import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@/shared/services/redis.service';
import { Workflow, WorkflowExecution, NodeDefinition } from '../entities';
// import { WorkflowDto, WorkflowExecutionDto, NodeDefinitionDto } from '../dto'; // Not needed for caching

/**
 * Service quản lý cache cho workflow module
 * Implements Redis caching patterns for frequently accessed data
 */
@Injectable()
export class WorkflowCacheService {
  private readonly logger = new Logger(WorkflowCacheService.name);
  
  // Cache prefixes
  private readonly CACHE_PREFIXES = {
    WORKFLOW: 'workflow',
    WORKFLOW_LIST: 'workflow_list',
    EXECUTION: 'execution',
    EXECUTION_LIST: 'execution_list',
    NODE_DEFINITION: 'node_def',
    NODE_DEFINITION_LIST: 'node_def_list',
    STATISTICS: 'stats',
    USER_WORKFLOWS: 'user_workflows',
  } as const;

  // Cache TTL (Time To Live) in seconds
  private readonly CACHE_TTL = {
    WORKFLOW: 3600, // 1 hour
    WORKFLOW_LIST: 1800, // 30 minutes
    EXECUTION: 1800, // 30 minutes
    EXECUTION_LIST: 900, // 15 minutes
    NODE_DEFINITION: 7200, // 2 hours (rarely changes)
    NODE_DEFINITION_LIST: 3600, // 1 hour
    STATISTICS: 600, // 10 minutes
    USER_WORKFLOWS: 1800, // 30 minutes
  } as const;

  constructor(private readonly redisService: RedisService) {}

  // Workflow caching methods

  /**
   * Cache workflow data
   * @param workflow Workflow entity
   * @param userId User ID for user-specific caching
   */
  async cacheWorkflow(workflow: Workflow, userId?: number): Promise<void> {
    try {
      const cacheKey = this.buildWorkflowCacheKey(workflow.id);
      const cacheData = {
        ...workflow,
        cachedAt: Date.now(),
        userId,
      };

      await this.redisService.setWithExpiry(
        cacheKey,
        JSON.stringify(cacheData),
        this.CACHE_TTL.WORKFLOW,
      );

      this.logger.debug(`Cached workflow: ${workflow.id}`);
    } catch (error) {
      this.logger.error(`Error caching workflow ${workflow.id}: ${error.message}`);
    }
  }

  /**
   * Get cached workflow
   * @param workflowId Workflow ID
   * @returns Cached workflow or null
   */
  async getCachedWorkflow(workflowId: string): Promise<Workflow | null> {
    try {
      const cacheKey = this.buildWorkflowCacheKey(workflowId);
      const cachedData = await this.redisService.get(cacheKey);

      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        this.logger.debug(`Cache hit for workflow: ${workflowId}`);
        return parsed;
      }

      this.logger.debug(`Cache miss for workflow: ${workflowId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting cached workflow ${workflowId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Cache workflow list for user
   * @param userId User ID
   * @param workflows List of workflows
   * @param queryParams Query parameters for cache key
   */
  async cacheWorkflowList(
    userId: number,
    workflows: Workflow[],
    queryParams: Record<string, any> = {},
  ): Promise<void> {
    try {
      const cacheKey = this.buildWorkflowListCacheKey(userId, queryParams);
      const cacheData = {
        workflows,
        cachedAt: Date.now(),
        queryParams,
      };

      await this.redisService.setWithExpiry(
        cacheKey,
        JSON.stringify(cacheData),
        this.CACHE_TTL.WORKFLOW_LIST,
      );

      this.logger.debug(`Cached workflow list for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error caching workflow list for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Get cached workflow list
   * @param userId User ID
   * @param queryParams Query parameters
   * @returns Cached workflow list or null
   */
  async getCachedWorkflowList(
    userId: number,
    queryParams: Record<string, any> = {},
  ): Promise<Workflow[] | null> {
    try {
      const cacheKey = this.buildWorkflowListCacheKey(userId, queryParams);
      const cachedData = await this.redisService.get(cacheKey);

      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        this.logger.debug(`Cache hit for workflow list, user ${userId}`);
        return parsed.workflows;
      }

      this.logger.debug(`Cache miss for workflow list, user ${userId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting cached workflow list for user ${userId}: ${error.message}`);
      return null;
    }
  }

  // Execution caching methods

  /**
   * Cache execution data
   * @param execution Execution entity
   */
  async cacheExecution(execution: WorkflowExecution): Promise<void> {
    try {
      const cacheKey = this.buildExecutionCacheKey(execution.id);
      const cacheData = {
        ...execution,
        cachedAt: Date.now(),
      };

      await this.redisService.setWithExpiry(
        cacheKey,
        JSON.stringify(cacheData),
        this.CACHE_TTL.EXECUTION,
      );

      this.logger.debug(`Cached execution: ${execution.id}`);
    } catch (error) {
      this.logger.error(`Error caching execution ${execution.id}: ${error.message}`);
    }
  }

  /**
   * Get cached execution
   * @param executionId Execution ID
   * @returns Cached execution or null
   */
  async getCachedExecution(executionId: string): Promise<WorkflowExecution | null> {
    try {
      const cacheKey = this.buildExecutionCacheKey(executionId);
      const cachedData = await this.redisService.get(cacheKey);

      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        this.logger.debug(`Cache hit for execution: ${executionId}`);
        return parsed;
      }

      this.logger.debug(`Cache miss for execution: ${executionId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting cached execution ${executionId}: ${error.message}`);
      return null;
    }
  }

  // Node definition caching methods

  /**
   * Cache node definition
   * @param nodeDefinition Node definition entity
   */
  async cacheNodeDefinition(nodeDefinition: NodeDefinition): Promise<void> {
    try {
      const cacheKey = this.buildNodeDefinitionCacheKey(nodeDefinition.type);
      const cacheData = {
        ...nodeDefinition,
        cachedAt: Date.now(),
      };

      await this.redisService.setWithExpiry(
        cacheKey,
        JSON.stringify(cacheData),
        this.CACHE_TTL.NODE_DEFINITION,
      );

      this.logger.debug(`Cached node definition: ${nodeDefinition.type}`);
    } catch (error) {
      this.logger.error(`Error caching node definition ${nodeDefinition.type}: ${error.message}`);
    }
  }

  /**
   * Get cached node definition
   * @param nodeType Node type
   * @returns Cached node definition or null
   */
  async getCachedNodeDefinition(nodeType: string): Promise<NodeDefinition | null> {
    try {
      const cacheKey = this.buildNodeDefinitionCacheKey(nodeType);
      const cachedData = await this.redisService.get(cacheKey);

      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        this.logger.debug(`Cache hit for node definition: ${nodeType}`);
        return parsed;
      }

      this.logger.debug(`Cache miss for node definition: ${nodeType}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting cached node definition ${nodeType}: ${error.message}`);
      return null;
    }
  }

  // Statistics caching methods

  /**
   * Cache statistics data
   * @param key Statistics key
   * @param data Statistics data
   * @param customTtl Custom TTL (optional)
   */
  async cacheStatistics(
    key: string,
    data: any,
    customTtl?: number,
  ): Promise<void> {
    try {
      const cacheKey = this.buildStatisticsCacheKey(key);
      const cacheData = {
        data,
        cachedAt: Date.now(),
      };

      await this.redisService.setWithExpiry(
        cacheKey,
        JSON.stringify(cacheData),
        customTtl || this.CACHE_TTL.STATISTICS,
      );

      this.logger.debug(`Cached statistics: ${key}`);
    } catch (error) {
      this.logger.error(`Error caching statistics ${key}: ${error.message}`);
    }
  }

  /**
   * Get cached statistics
   * @param key Statistics key
   * @returns Cached statistics or null
   */
  async getCachedStatistics(key: string): Promise<any | null> {
    try {
      const cacheKey = this.buildStatisticsCacheKey(key);
      const cachedData = await this.redisService.get(cacheKey);

      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        this.logger.debug(`Cache hit for statistics: ${key}`);
        return parsed.data;
      }

      this.logger.debug(`Cache miss for statistics: ${key}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting cached statistics ${key}: ${error.message}`);
      return null;
    }
  }

  // Cache invalidation methods

  /**
   * Invalidate workflow cache
   * @param workflowId Workflow ID
   */
  async invalidateWorkflowCache(workflowId: string): Promise<void> {
    try {
      const cacheKey = this.buildWorkflowCacheKey(workflowId);
      await this.redisService.del(cacheKey);
      this.logger.debug(`Invalidated workflow cache: ${workflowId}`);
    } catch (error) {
      this.logger.error(`Error invalidating workflow cache ${workflowId}: ${error.message}`);
    }
  }

  /**
   * Invalidate user workflow list cache
   * @param userId User ID
   */
  async invalidateUserWorkflowListCache(userId: number): Promise<void> {
    try {
      const pattern = `${this.CACHE_PREFIXES.WORKFLOW_LIST}:${userId}:*`;
      const keys = await this.redisService.getClient().keys(pattern);
      
      if (keys.length > 0) {
        await this.redisService.getClient().del(...keys);
        this.logger.debug(`Invalidated ${keys.length} workflow list cache entries for user ${userId}`);
      }
    } catch (error) {
      this.logger.error(`Error invalidating user workflow list cache ${userId}: ${error.message}`);
    }
  }

  /**
   * Invalidate execution cache
   * @param executionId Execution ID
   */
  async invalidateExecutionCache(executionId: string): Promise<void> {
    try {
      const cacheKey = this.buildExecutionCacheKey(executionId);
      await this.redisService.del(cacheKey);
      this.logger.debug(`Invalidated execution cache: ${executionId}`);
    } catch (error) {
      this.logger.error(`Error invalidating execution cache ${executionId}: ${error.message}`);
    }
  }

  /**
   * Clear all workflow-related cache
   */
  async clearAllCache(): Promise<void> {
    try {
      const patterns = Object.values(this.CACHE_PREFIXES).map(prefix => `${prefix}:*`);
      
      for (const pattern of patterns) {
        const keys = await this.redisService.getClient().keys(pattern);
        if (keys.length > 0) {
          await this.redisService.getClient().del(...keys);
          this.logger.debug(`Cleared ${keys.length} cache entries for pattern: ${pattern}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error clearing all cache: ${error.message}`);
    }
  }

  // Private helper methods

  private buildWorkflowCacheKey(workflowId: string): string {
    return `${this.CACHE_PREFIXES.WORKFLOW}:${workflowId}`;
  }

  private buildWorkflowListCacheKey(userId: number, queryParams: Record<string, any>): string {
    const queryString = this.serializeQueryParams(queryParams);
    return `${this.CACHE_PREFIXES.WORKFLOW_LIST}:${userId}:${queryString}`;
  }

  private buildExecutionCacheKey(executionId: string): string {
    return `${this.CACHE_PREFIXES.EXECUTION}:${executionId}`;
  }

  private buildNodeDefinitionCacheKey(nodeType: string): string {
    return `${this.CACHE_PREFIXES.NODE_DEFINITION}:${nodeType}`;
  }

  private buildStatisticsCacheKey(key: string): string {
    return `${this.CACHE_PREFIXES.STATISTICS}:${key}`;
  }

  private serializeQueryParams(params: Record<string, any>): string {
    const sortedKeys = Object.keys(params).sort();
    const serialized = sortedKeys.map(key => `${key}=${params[key]}`).join('&');
    return Buffer.from(serialized).toString('base64');
  }
}
