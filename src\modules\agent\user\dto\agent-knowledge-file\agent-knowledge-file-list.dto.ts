import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc hiển thị knowledge file trong danh sách agent
 */
export class AgentKnowledgeFileListDto {
  /**
   * ID của knowledge file
   */
  @ApiProperty({
    description: 'ID của knowledge file',
    example: 'uuid-knowledge-file-1',
  })
  id: string;

  /**
   * Tên hiển thị của knowledge file
   */
  @ApiProperty({
    description: 'Tên hiển thị của knowledge file',
    example: 'Tài liệu hướng dẫn sử dụng',
  })
  name: string;

  /**
   * Khóa lưu trữ của file
   */
  @ApiProperty({
    description: 'Khóa lưu trữ của file',
    example: 'knowledge/uuid-knowledge-file-1.pdf',
  })
  storageKey: string;

  /**
   * Loại người sở hữu file
   */
  @ApiProperty({
    description: 'Loại người sở hữu file',
    example: 'user',
    enum: ['user', 'employee'],
  })
  ownerType: string;

  /**
   * ID của người sở hữu file
   */
  @ApiProperty({
    description: 'ID của người sở hữu file',
    example: 1,
  })
  ownedBy: number;

  /**
   * Thời gian tạo file (timestamp)
   */
  @ApiProperty({
    description: 'Thời gian tạo file (timestamp)',
    example: 1640995200000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật file (timestamp)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật file (timestamp)',
    example: 1640995200000,
  })
  updatedAt: number;
}
