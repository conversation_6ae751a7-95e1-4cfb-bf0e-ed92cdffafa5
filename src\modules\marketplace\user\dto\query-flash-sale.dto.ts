import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsEnum, IsNumber, Min } from 'class-validator';
import { FlashSaleStatus } from '../../enums/flash-sale-status.enum';

/**
 * DTO cho query flash sale (User)
 */
export class QueryFlashSaleUserDto {
  @ApiPropertyOptional({
    description: 'Trang hiện tại',
    example: 1,
    default: 1,
    minimum: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Số item mỗi trang',
    example: 10,
    default: 10,
    minimum: 1
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái flash sale',
    enum: FlashSaleStatus,
    example: FlashSaleStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(FlashSaleStatus)
  status?: FlashSaleStatus;

  @ApiPropertyOptional({
    description: 'Lọc theo ID sản phẩm',
    example: 123
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  productId?: number;

  @ApiPropertyOptional({
    description: 'Lọc flash sale đang hoạt động',
    example: true
  })
  @IsOptional()
  @Type(() => Boolean)
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Sắp xếp theo (createdAt, startTime, endTime)',
    example: 'createdAt',
    default: 'createdAt'
  })
  @IsOptional()
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({
    description: 'Thứ tự sắp xếp (ASC, DESC)',
    example: 'DESC',
    default: 'DESC'
  })
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
