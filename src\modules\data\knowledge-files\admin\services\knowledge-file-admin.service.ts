import { Injectable, Logger } from '@nestjs/common';
import { DataSource, In } from 'typeorm';
import { KnowledgeFileRepository, VectorStoreFileRepository, VectorStoreRepository } from '../../repositories';
import { BatchCreateFilesDto, FileResponseDto, QueryFileDto, QueryUnassignedFileDto, AdminBatchCreateFilesResponseDto } from '../dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { AppException } from '@common/exceptions/app.exception';
import { KNOWLEDGE_FILE_ERROR_CODES } from '../../exceptions';
import { Transactional } from 'typeorm-transactional';

import { CategoryFolderEnum, FileSizeEnum, TimeIntervalEnum } from '@shared/utils';
import { FileType } from '@shared/utils/file/file-media-type.util';
import { OwnerType } from '@shared/enums';
import { KnowledgeFile, VectorStore, VectorStoreFile } from '../../entities';
import { KnowledgeFileStatus } from '../../enums/knowledge-file-status.enum';
import { v4 as uuidv4 } from 'uuid';
import { plainToInstance } from 'class-transformer';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { ValidationHelper } from '../../helpers/validation.helper';
import { FileHelper } from '../../helpers/file.helper';
@Injectable()
export class KnowledgeFileAdminService {
    private readonly logger = new Logger(KnowledgeFileAdminService.name);

    constructor(
        private readonly knowledgeFileRepository: KnowledgeFileRepository,
        private readonly vectorStoreFileRepository: VectorStoreFileRepository,
        private readonly vectorStoreRepository: VectorStoreRepository,
        private readonly s3Service: S3Service,
        private readonly cdnService: CdnService,
        private readonly openAiService: OpenAiService,
        private readonly dataSource: DataSource,
        private readonly validationHelper: ValidationHelper
    ) {}

    /**
     * Tạo nhiều file tri thức mới
     * @param dto Thông tin các file cần tạo
     * @param employeeId ID của nhân viên
     * @returns Thông tin về việc tạo file thành công và danh sách file với presigned URLs
     */
    @Transactional()
    async batchCreateFiles(dto: BatchCreateFilesDto, employeeId: number): Promise<AdminBatchCreateFilesResponseDto> {
        try {
            // Kiểm tra dữ liệu đầu vào bằng ValidationHelper
            this.validationHelper.validateBatchCreateFilesDto(dto);

            this.logger.log(`Bắt đầu tạo ${dto.files.length} file tri thức cho admin ID ${employeeId}`);

            // Tạo một mảng để lưu trữ thông tin về các file đã tạo
            const fileCreationResults = await Promise.all(
                dto.files.map(async (file) => {
                    this.logger.log(`Đang xử lý file: ${file.name}, kích thước: ${file.storage} bytes`);

                    // Xác định loại file dựa trên MIME type
                    const fileType = FileType.getMimeType(file.mime);

                    // Tạo S3 key cho file
                    const s3Key = `${CategoryFolderEnum.KNOWLEDGE_FILES}/${employeeId}/${uuidv4()}-${file.name}`;
                    this.logger.log(`Đã tạo S3 key: ${s3Key}`);

                    // Tạo bản ghi file trong database
                    const knowledgeFile = this.knowledgeFileRepository.create({
                        name: file.name,
                        storageKey: s3Key,
                        ownerType: OwnerType.ADMIN,
                        ownedBy: employeeId,
                        isOwner: true,
                        isForSale: false,
                        storage: file.storage,
                        status: KnowledgeFileStatus.APPROVED,
                        fileId: '',
                    });

                    // Lưu bản ghi file vào database
                    const savedFile = await this.knowledgeFileRepository.save(knowledgeFile);
                    this.logger.log(`Đã lưu thông tin file vào database với ID: ${savedFile.id}`);

                    // Tạo presigned URL để admin có thể upload file
                    const uploadUrl = await this.s3Service.createPresignedWithID(
                        s3Key,
                        TimeIntervalEnum.TEN_MINUTES,
                        fileType,
                        FileSizeEnum.TWENTY_MB
                    );

                    // Trả về thông tin đầy đủ về file đã tạo và URL tải lên
                    return {
                        id: savedFile.id,
                        name: savedFile.name,
                        uploadUrl,
                        storageKey: s3Key,
                    };
                }),
            );

            // Chuyển đổi kết quả thành DTO
            const result = plainToInstance(
                AdminBatchCreateFilesResponseDto,
                { files: fileCreationResults },
                { excludeExtraneousValues: true },
            );

            return result;
        } catch (error) {
            this.logger.error(`Lỗi khi tạo batch files: ${error.message}`, error.stack);

            if (error instanceof AppException) {
                throw error;
            }

            throw new AppException(
                KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
                `Lỗi khi tạo batch files: ${error.message}`
            );
        }
    }

    /**
     * Lấy danh sách file tri thức từ hệ thống
     * @param queryDto Tham số truy vấn
     * @param employeeId ID của nhân viên
     * @returns Danh sách file tri thức với phân trang
     */
    async getFiles(queryDto: QueryFileDto, employeeId: number): Promise<PaginatedResult<FileResponseDto>> {
        try {
            // Kiểm tra dữ liệu đầu vào bằng ValidationHelper
            this.validationHelper.validateQueryFileDto(queryDto);

            this.logger.log(`Admin ${employeeId} đang lấy danh sách file với query: ${JSON.stringify(queryDto)}`);

            // Sử dụng repository method để xử lý đúng tất cả filters bao gồm marketplaceReady
            // Admin chỉ xem tài nguyên của admin, không xem tài nguyên của user
            const result = await this.knowledgeFileRepository.findAllWithPaginationAndFilters(
                { ...queryDto, ownerType: OwnerType.ADMIN }, // Chỉ lấy tài nguyên của admin
                null, // userId = null
                KnowledgeFileStatus.DELETED, // Loại trừ file đã xóa mềm
                undefined, // fileIds không cần thiết
                true // isAdmin = true để bỏ qua filter theo userId
            );

            const files = result.items;
            const meta = result.meta;

            // Lấy thông tin vector store nếu có
            let vectorStoreName: string | undefined;
            if (queryDto.vectorStoreId) {
                try {
                    const vectorStore = await this.checkVectorStoreExists(queryDto.vectorStoreId);
                    if (vectorStore) {
                        vectorStoreName = vectorStore.name || 'Vector Store';
                    }
                } catch (error) {
                    this.logger.warn(`Lỗi khi lấy tên vector store: ${error.message}`);
                }
            }

            // Tạo presigned URL để download file
            const fileResponses = await Promise.all(files.map(async (file) => {
                return await this.mapFileToResponseDto(file, queryDto.vectorStoreId, vectorStoreName);
            }));

            this.logger.log(`Admin ${employeeId} lấy được ${fileResponses.length} file từ tổng ${meta.totalItems} file`);

            // Trả về kết quả phân trang
            return {
                items: fileResponses,
                meta,
            };
        } catch (error) {
            this.logger.error(`Lỗi khi lấy danh sách file cho admin ${employeeId}: ${error.message}`, error.stack);
            if (error instanceof AppException) {
                throw error;
            }

            throw new AppException(
                KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR,
                `Lỗi khi lấy danh sách file: ${error.message}`
            );
        }
    }

    /**
     * Xóa mềm file tri thức - chỉ cập nhật trạng thái sang DELETED mà không xóa thực sự
     * @param fileIdOrIds ID của file hoặc mảng ID các file cần xóa
     * @param employeeId ID của nhân viên
     * @returns Thông tin về việc xóa file thành công
     */
    @Transactional()
    async deleteFile(fileIdOrIds: string | string[], employeeId: number): Promise<{
        success: boolean;
        deletedCount: number;
        failedItems?: { id: string; reason: string }[];
    }> {
        try {
            // Chuyển đổi tham số thành mảng để xử lý thống nhất
            const fileIds = Array.isArray(fileIdOrIds) ? fileIdOrIds : [fileIdOrIds];

            // Kiểm tra dữ liệu đầu vào bằng ValidationHelper
            this.validationHelper.validateFileIds(fileIds);

            this.logger.log(`Bắt đầu xóa mềm ${fileIds.length} file cho admin ${employeeId}`);

            // Kết quả xử lý
            const results = {
                success: true,
                deletedCount: 0,
                failedItems: [] as { id: string; reason: string }[]
            };

            // Xử lý từng file
            for (const fileId of fileIds) {
                try {
                    // Kiểm tra file ID hợp lệ
                    this.validationHelper.validateFileId(fileId);

                    // Tìm file trong database
                    // Admin có thể xóa file của user nhưng không thể xóa file của admin khác
                    const file = await this.knowledgeFileRepository.findOne({
                        where: [
                            // File của user (admin có thể xóa)
                            { id: fileId }, // Bỏ ownerType vì không có trong DB
                            // File của chính admin này
                            { id: fileId, ownedBy: employeeId } // Bỏ ownerType vì không có trong DB
                        ],
                    });

                    if (!file) {
                        this.logger.warn(`File ${fileId} không tồn tại hoặc admin ${employeeId} không có quyền xóa`);
                        results.failedItems.push({
                            id: fileId,
                            reason: 'File không tồn tại hoặc bạn không có quyền xóa'
                        });
                        continue;
                    }

                    // Xóa liên kết với vector store nếu có
                    try {
                        // Sử dụng repository để xóa liên kết và lấy danh sách vector store ID bị ảnh hưởng
                        const deleteResult = await this.vectorStoreFileRepository.deleteByFileId(fileId);

                        if (deleteResult.affected > 0 && deleteResult.vectorStoreIds.length > 0) {
                            this.logger.debug('Giảm dung lượng các vector store đang bị gán');

                            // Cập nhật dung lượng các vector store bị ảnh hưởng
                            await this.vectorStoreRepository.updateStorageByIds(
                                deleteResult.vectorStoreIds,
                                -file.storage // Giảm dung lượng (số âm)
                            );

                            this.logger.debug('Đã giảm dung lượng các vector store đang bị gán');
                        }
                    } catch (error) {
                        this.logger.warn(`Lỗi khi xóa liên kết vector store: ${error.message}`, error.stack);
                        // Tiếp tục xóa mềm file ngay cả khi xóa liên kết thất bại
                    }

                    // Xóa file từ OpenAI nếu có fileId
                    // if (file.fileId) {
                    //     try {
                    //         await this.openAiService.deleteOpenAIFile(file.fileId);
                    //         this.logger.log(`Đã xóa file từ OpenAI: ${file.fileId}`);
                    //     } catch (error) {
                    //         this.logger.warn(`Lỗi khi xóa file từ OpenAI: ${error.message}`, error.stack);
                    //         // Tiếp tục xóa mềm file ngay cả khi xóa từ OpenAI thất bại
                    //     }
                    // }

                    // Bỏ set status vì không có cột status trong database
                    // file.status = KnowledgeFileStatus.DELETED;
                    await this.knowledgeFileRepository.save(file);
                    this.logger.log(`Đã xóa mềm file: ${fileId} (đã chuyển trạng thái sang DELETED)`);

                    // Tăng số lượng file đã xóa thành công
                    results.deletedCount++;
                } catch (error) {
                    this.logger.error(`Lỗi khi xóa file ${fileId}: ${error.message}`, error.stack);
                    results.failedItems.push({
                        id: fileId,
                        reason: `Lỗi khi xóa: ${error.message}`
                    });
                }
            }

            // Nếu không xóa được file nào, đánh dấu là không thành công
            if (results.deletedCount === 0 && fileIds.length > 0) {
                results.success = false;
            }

            this.logger.log(`Hoàn thành xóa mềm hàng loạt. Đã xóa: ${results.deletedCount}, Thất bại: ${results.failedItems.length}`);

            // Nếu không có lỗi nào, không trả về mảng failedItems
            if (results.failedItems.length === 0) {
                const { failedItems, ...rest } = results;
                return rest;
            }

            return results;
        } catch (error) {
            this.logger.error(`Lỗi khi xóa các file: ${error.message}`, error.stack);
            if (error instanceof AppException) {
                throw error;
            }

            throw new AppException(
                KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR,
                `Lỗi khi xóa file: ${error.message}`
            );
        }
    }

    /**
     * Lấy danh sách file tri thức chưa gắn với vector store nào
     * @param queryDto Tham số truy vấn
     * @param employeeId ID của nhân viên
     * @returns Danh sách file chưa gắn với vector store với phân trang
     */
    async getUnassignedFiles(queryDto: QueryUnassignedFileDto, employeeId: number): Promise<PaginatedResult<FileResponseDto>> {
        try {
            // Kiểm tra dữ liệu đầu vào bằng ValidationHelper
            this.validationHelper.validateAdminQueryUnassignedFileDto(queryDto);

            // Kiểm tra vector store có tồn tại không (admin có thể xem tất cả vector stores)
            const vectorStore = await this.vectorStoreRepository.findOne({
                where: { id: queryDto.vectorStoreId }
            });
            if (!vectorStore) {
                throw new AppException(
                    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
                    `Vector store với ID ${queryDto.vectorStoreId} không tồn tại`
                );
            }

            this.logger.log(`Lấy danh sách file chưa gắn với vector store ${queryDto.vectorStoreId} cho admin ${employeeId}`);

            // Admin chỉ lấy file của chính admin
            const ownerType = OwnerType.ADMIN;
            const userId = employeeId;

            // Sử dụng repository để lấy danh sách file chưa gắn với vector store
            const result = await this.knowledgeFileRepository.findUnassignedToVectorStore(
                queryDto,
                userId,
                ownerType,
                KnowledgeFileStatus.DELETED,
            );

            const files = result.items;
            const meta = result.meta;

            // Tạo URL xem file cho từng file
            const fileResponses = await Promise.all(
                files.map(async (file) => {
                    return await this.mapFileToResponseDto(file);
                }),
            );

            // Trả về kết quả phân trang
            return {
                items: fileResponses,
                meta,
            };
        } catch (error) {
            this.logger.error(`Error getting unassigned files: ${error.message}`, error.stack);

            if (error instanceof AppException) {
                throw error;
            }

            throw new AppException(
                KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR,
                `Lỗi khi lấy danh sách file chưa gắn với vector store: ${error.message}`,
            );
        }
    }

    /**
     * Kiểm tra xem vector store có tồn tại không
     * @param vectorStoreId ID của vector store
     * @param userId ID của người dùng
     * @returns Thông tin về vector store nếu tồn tại, null nếu không tồn tại
     */
    private async checkVectorStoreExists(vectorStoreId: string, userId?: number): Promise<VectorStore | null> {
        try {
            // Kiểm tra tham số đầu vào
            try {
                this.validationHelper.validateVectorStoreId(vectorStoreId);
            } catch (error) {
                this.logger.warn(`Vector store ID không hợp lệ: ${vectorStoreId}`);
                return null;
            }

            // Nếu có userId, kiểm tra xem vector store có thuộc về người dùng này không
            if (userId) {
                return await this.vectorStoreRepository.findOneByIdAndUserId(vectorStoreId, userId);
            }

            // Nếu không có userId, chỉ kiểm tra vector store có tồn tại không
            return await this.vectorStoreRepository.findOne({
                where: { id: vectorStoreId }
            });
        } catch (error) {
            this.logger.error(`Lỗi khi kiểm tra vector store: ${error.message}`, error.stack);
            return null;
        }
    }

    /**
     * Chuyển đổi thông tin file sang DTO để trả về cho client
     * @param file Thông tin file từ database
     * @param vectorStoreId ID của vector store (nếu có)
     * @param vectorStoreName Tên của vector store (nếu có)
     * @returns FileResponseDto đã được chuyển đổi
     */
    private async mapFileToResponseDto(file: KnowledgeFile, vectorStoreId?: string, vectorStoreName?: string): Promise<FileResponseDto> {
        // Tạo CDN URL để xem file
        let downloadURL = '';
        try {
            // Sử dụng CDN service để tạo URL xem file với thời gian hết hạn 1 giờ
            const cdnUrl = this.cdnService.generateUrlView(file.storageKey, TimeIntervalEnum.ONE_HOUR);
            if (cdnUrl) {
                downloadURL = cdnUrl;
                this.logger.log(`Đã tạo CDN URL cho file ${file.id}: ${downloadURL}`);
            } else {
                throw new Error('CDN service trả về null');
            }
        } catch (error) {
            this.logger.warn(`Lỗi khi tạo CDN URL cho file ${file.id}: ${error.message}`);
            // Fallback sử dụng S3 download URL nếu CDN thất bại
            try {
                downloadURL = await this.s3Service.getDownloadUrl(file.storageKey, TimeIntervalEnum.ONE_HOUR);
                this.logger.log(`Sử dụng S3 download URL cho file ${file.id}: ${downloadURL}`);
            } catch (s3Error) {
                this.logger.error(`Không thể tạo S3 URL cho file ${file.id}: ${s3Error.message}`);
                downloadURL = '#'; // URL mặc định nếu cả hai phương pháp đều thất bại
            }
        }

        // Kiểm tra xem file có thuộc về vector store nào không
        let fileVectorStoreId: string | undefined = undefined;
        let fileVectorStoreName: string | undefined = undefined;

        try {
            if (vectorStoreId) {
                // Nếu đang lọc theo vectorStoreId, sử dụng vectorStoreId đó
                fileVectorStoreId = vectorStoreId;
                fileVectorStoreName = vectorStoreName || 'Vector Store';
            } else {
                // Nếu không lọc theo vectorStoreId, kiểm tra xem file có thuộc về vector store nào không
                const vectorStoreFile = await this.vectorStoreFileRepository.findByFileId(file.id);

                if (vectorStoreFile) {
                    fileVectorStoreId = vectorStoreFile.vectorStoreId;

                    // Lấy tên vector store
                    const vectorStore = await this.checkVectorStoreExists(vectorStoreFile.vectorStoreId);
                    if (vectorStore) {
                        fileVectorStoreName = vectorStore.name || 'Vector Store';
                    } else {
                        fileVectorStoreName = 'Vector Store';
                    }
                }
            }
        } catch (error) {
            this.logger.warn(`Lỗi khi lấy thông tin vector store cho file ${file.id}: ${error.message}`);
        }

        // Sử dụng FileHelper để chuyển đổi sang DTO
        return FileHelper.mapToResponseDto(file, downloadURL, fileVectorStoreId, fileVectorStoreName);
    }
}