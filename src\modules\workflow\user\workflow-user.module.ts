import { UserModule } from '@modules/user/user.module';
import { Module } from '@nestjs/common';
import {
  WorkflowUserService,
  UserWorkflowDefinitionService,
  UserWorkflowExecutionService,
} from './services';
import {
  WorkflowUserController,
  UserWorkflowDefinitionController,
  UserWorkflowExecutionController,
  UserNodeDefinitionController,
  UserWorkflowSSEController,
  // UserNodeTestController removed - focusing on real execution only
} from './controllers';
import { UserWorkflowCrudController } from './controllers/user-workflow-crud.controller';
import { WorkflowSharedModule } from '../shared/workflow-shared.module';
import { WorkflowService } from '../services/workflow.service';

/**
 * Module quản lý workflow cho người dùng
 * Bao gồm CRUD cho workflow, nodes và connections
 * Following agent-user.module patterns
 */
@Module({
  imports: [
    WorkflowSharedModule,
    UserModule,
  ],
  providers: [
    WorkflowUserService,
    UserWorkflowDefinitionService,
    UserWorkflowExecutionService,
    WorkflowService,
  ],
  controllers: [
    WorkflowUserController,
    UserWorkflowDefinitionController,
    UserWorkflowExecutionController,
    UserNodeDefinitionController,
    UserWorkflowSSEController,
    UserWorkflowCrudController,
  ],
  exports: [
    WorkflowUserService,
    UserWorkflowDefinitionService,
    UserWorkflowExecutionService,
  ],
})
export class WorkflowUserModule { }
