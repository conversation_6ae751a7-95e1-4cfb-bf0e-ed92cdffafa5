import { IsObject, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Validate Workflow Definition DTO
 */
export class ValidateWorkflowDefinitionDto {
  @ApiProperty({ description: 'Workflow nodes' })
  @IsObject()
  nodes: any[];

  @ApiPropertyOptional({ description: 'Workflow edges' })
  @IsOptional()
  @IsObject()
  edges?: any[];

  @ApiPropertyOptional({ description: 'Workflow metadata' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
