import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { Workflow } from '@modules/workflow/entities';
import { WorkflowRepository } from '@modules/workflow/repositories';
import { WORKFLOW_ERROR_CODES } from '@modules/workflow/exceptions/workflow.exception';
import { Transactional } from 'typeorm-transactional';
import { ILike, FindOptionsWhere } from 'typeorm';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  WorkflowQueryDto,
  WorkflowListItemDto,
  WorkflowDetailDto,
  WorkflowStatisticsDto
} from '../dto';
import { WorkflowMapper } from '../mappers/workflow.mapper';

/**
 * Service xử lý các thao tác liên quan đến workflow cho người dùng
 * Following agent-user.service patterns
 */
@Injectable()
export class WorkflowUserService {
  private readonly logger = new Logger(WorkflowUserService.name);

  constructor(
    private readonly workflowRepository: WorkflowRepository,
  ) {}

  /**
   * L<PERSON>y danh sách workflows của user với pagination và filtering
   */
  async getWorkflows(
    userId: number,
    queryDto: WorkflowQueryDto,
  ): Promise<PaginatedResult<WorkflowListItemDto>> {
    try {
      const { page, limit, search, isActive, sortBy, sortDirection } = queryDto;

      // Build where conditions
      const where: FindOptionsWhere<Workflow> = {
        userId,
      };

      // Add search condition
      if (search) {
        where.name = ILike(`%${search}%`);
      }

      // Add active filter
      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Execute query with pagination
      const [workflows, total] = await this.workflowRepository.findAndCount({
        where,
        order: {
          [sortBy]: sortDirection,
        },
        skip,
        take: limit,
      });

      // Map to DTOs
      const items = workflows.map(workflow => 
        WorkflowMapper.toListItemDto(workflow)
      );

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
          hasItems: total > 0
        }
      };
    } catch (error) {
      this.logger.error(`Error getting workflows for user ${userId}:`, error);
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_LIST_FAILED);
    }
  }

  /**
   * Lấy chi tiết workflow theo ID
   */
  async getWorkflowById(
    userId: number,
    workflowId: string,
  ): Promise<WorkflowDetailDto> {
    try {
      const workflow = await this.workflowRepository.findOne({
        where: {
          id: workflowId,
          userId,
        },
      });

      if (!workflow) {
        throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND);
      }

      return WorkflowMapper.toDetailDto(workflow);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error getting workflow ${workflowId} for user ${userId}:`, error);
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_GET_FAILED);
    }
  }

  /**
   * Tạo workflow mới
   */
  @Transactional()
  async createWorkflow(
    userId: number,
    createDto: CreateWorkflowDto,
  ): Promise<WorkflowDetailDto> {
    try {
      // Check if workflow name already exists for this user
      const existingWorkflow = await this.workflowRepository.findOne({
        where: {
          userId,
          name: createDto.name,
        },
      });

      if (existingWorkflow) {
        throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NAME_EXISTS);
      }

      // Create new workflow
      const workflow = this.workflowRepository.create({
        userId,
        name: createDto.name,
        isActive: createDto.isActive ?? false,
        definition: createDto.definition ?? { nodes: [], edges: [] },
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      const savedWorkflow = await this.workflowRepository.save(workflow);

      this.logger.log(`Created workflow ${savedWorkflow.id} for user ${userId}`);
      return WorkflowMapper.toDetailDto(savedWorkflow);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error creating workflow for user ${userId}:`, error);
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_CREATE_FAILED);
    }
  }

  /**
   * Cập nhật workflow
   */
  @Transactional()
  async updateWorkflow(
    userId: number,
    workflowId: string,
    updateDto: UpdateWorkflowDto,
  ): Promise<WorkflowDetailDto> {
    try {
      const workflow = await this.workflowRepository.findOne({
        where: {
          id: workflowId,
          userId,
        },
      });

      if (!workflow) {
        throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND);
      }

      // Check name uniqueness if name is being updated
      if (updateDto.name && updateDto.name !== workflow.name) {
        const existingWorkflow = await this.workflowRepository.findOne({
          where: {
            userId,
            name: updateDto.name,
          },
        });

        if (existingWorkflow) {
          throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NAME_EXISTS);
        }
      }

      // Update workflow
      Object.assign(workflow, {
        ...updateDto,
        updatedAt: Date.now(),
      });

      const savedWorkflow = await this.workflowRepository.save(workflow);

      this.logger.log(`Updated workflow ${workflowId} for user ${userId}`);
      return WorkflowMapper.toDetailDto(savedWorkflow);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating workflow ${workflowId} for user ${userId}:`, error);
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_UPDATE_FAILED);
    }
  }

  /**
   * Xóa workflow
   */
  @Transactional()
  async deleteWorkflow(userId: number, workflowId: string): Promise<void> {
    try {
      const workflow = await this.workflowRepository.findOne({
        where: {
          id: workflowId,
          userId,
        },
      });

      if (!workflow) {
        throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND);
      }

      await this.workflowRepository.remove(workflow);

      this.logger.log(`Deleted workflow ${workflowId} for user ${userId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error deleting workflow ${workflowId} for user ${userId}:`, error);
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_DELETE_FAILED);
    }
  }

  /**
   * Toggle workflow active status
   */
  @Transactional()
  async toggleWorkflowStatus(
    userId: number,
    workflowId: string,
    isActive: boolean,
  ): Promise<WorkflowDetailDto> {
    try {
      const workflow = await this.workflowRepository.findOne({
        where: {
          id: workflowId,
          userId,
        },
      });

      if (!workflow) {
        throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND);
      }

      workflow.isActive = isActive;
      workflow.updatedAt = Date.now();

      const savedWorkflow = await this.workflowRepository.save(workflow);

      this.logger.log(`Toggled workflow ${workflowId} status to ${isActive} for user ${userId}`);
      return WorkflowMapper.toDetailDto(savedWorkflow);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error toggling workflow ${workflowId} status for user ${userId}:`, error);
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_UPDATE_FAILED);
    }
  }

  /**
   * Search workflows
   */
  async searchWorkflows(
    userId: number,
    searchQuery: string,
    limit: number = 10,
  ): Promise<WorkflowListItemDto[]> {
    try {
      const workflows = await this.workflowRepository.find({
        where: {
          userId,
          name: ILike(`%${searchQuery}%`),
        },
        order: {
          updatedAt: 'DESC',
        },
        take: limit,
      });

      return workflows.map(workflow => WorkflowMapper.toListItemDto(workflow));
    } catch (error) {
      this.logger.error(`Error searching workflows for user ${userId}:`, error);
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_SEARCH_FAILED);
    }
  }

  /**
   * Get workflow statistics for user
   */
  async getWorkflowStatistics(userId: number): Promise<WorkflowStatisticsDto> {
    try {
      const [totalWorkflows, activeWorkflows] = await Promise.all([
        this.workflowRepository.count({ where: { userId } }),
        this.workflowRepository.count({ where: { userId, isActive: true } }),
      ]);

      return {
        totalWorkflows,
        activeWorkflows,
        inactiveWorkflows: totalWorkflows - activeWorkflows,
      };
    } catch (error) {
      this.logger.error(`Error getting workflow statistics for user ${userId}:`, error);
      throw new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_STATISTICS_FAILED);
    }
  }
}
