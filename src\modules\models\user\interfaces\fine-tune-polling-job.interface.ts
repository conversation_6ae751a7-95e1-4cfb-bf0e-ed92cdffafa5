import { ProviderFineTuneEnum } from '@modules/models/constants/provider.enum';

/**
 * Interface cho polling job data - payload đơn giản cho monitoring
 */
export interface FineTunePollingJobData {

  /**
   * ID của user
   */
  userId: number;

  /**
   * ID của user model fine tune record
   */
  modelFineTuneId: string;

  /**
   * Provider AI (OPENAI hoặc GOOGLE)
   */
  provider: ProviderFineTuneEnum;

  /**
   * Thời gian tạo job (timestamp)
   */
  timestamp: number;
}
