import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { ProductAdminService } from '../services/product-admin.service';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { SwaggerApiTag } from '@common/swagger/swagger.tags';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  QueryProductDto,
  ProductDetailResponseDto,
  UpdateProductStatusDto,
  UpdateProductDto,
  CreateProductAdminDto,
  CreateProductResponseDto,
  UpdateProductResponseDto,
  PaginatedProductResponseDto,
  UpdateMultipleProductsStatusDto,
  UpdateMultipleProductsStatusResponseDto,
  AddImageOperationDto,
  DeleteImageOperationDto,
  DeleteMultipleProductsDto,
  DeleteMultipleProductsResponseDto
} from '../dto';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { PresignedUrlImageDto } from '../dto/presigned-url.dto';

/**
 * Controller xử lý các API liên quan đến sản phẩm cho admin
 */
@ApiTags(SwaggerApiTag.ADMIN_MARKETPLACE_PRODUCTS)
@ApiExtraModels(
  ApiResponseDto,
  ProductDetailResponseDto,
  PaginatedResult,
  ApiErrorResponseDto,
  CreateProductResponseDto,
  UpdateProductResponseDto,
  PaginatedProductResponseDto,
  PresignedUrlImageDto,
  UpdateMultipleProductsStatusResponseDto,
  AddImageOperationDto,
  DeleteImageOperationDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/marketplace/products')
export class ProductAdminController {
  constructor(private readonly productAdminService: ProductAdminService) {}

  /**
   * Lấy danh sách tất cả sản phẩm với phân trang, tìm kiếm, lọc và sắp xếp
   * Mặc định loại trừ sản phẩm có status PENDING và DELETED (trừ khi includeDeleted = true)
   * Hỗ trợ các tham số query:
   * - page: Số trang (bắt đầu từ 1)
   * - limit: Số lượng sản phẩm trên một trang
   * - search: Từ khóa tìm kiếm (tìm theo tên sản phẩm)
   * - status: Lọc theo trạng thái sản phẩm cụ thể
   * - category: Lọc theo loại sản phẩm
   * - userId: Lọc theo ID người dùng
   * - employeeId: Lọc theo ID nhân viên
   * - includeDeleted: Bao gồm cả sản phẩm đã bị xóa (mặc định: false)
   * - sortBy: Sắp xếp theo trường (name, createdAt, price, ...)
   * - sortOrder: Thứ tự sắp xếp (ASC, DESC)
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách sản phẩm phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách tất cả sản phẩm',
    description: 'Lấy danh sách tất cả sản phẩm (bao gồm cả của admin và user). Mặc định loại trừ những sản phẩm có status = PENDING và DELETED, trừ khi includeDeleted=true cho sản phẩm đã xóa'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách sản phẩm phân trang',
    schema: ApiResponseDto.getSchema(PaginatedProductResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
    MARKETPLACE_ERROR_CODES.GENERAL_ERROR
  )
  async getAllProducts(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: QueryProductDto
  ): Promise<ApiResponseDto<PaginatedProductResponseDto>> {
    // Admin không cần currentUserId để tính canPurchase, truyền undefined
    const result = await this.productAdminService.getProducts(employeeId, queryDto, undefined);

    // Chuyển đổi kết quả thành DTO phân trang
    const paginatedResponse: PaginatedProductResponseDto = {
      items: result.items as unknown as ProductDetailResponseDto[],
      meta: result.meta
    };
    return ApiResponseDto.success(paginatedResponse);
  }

  /**
   * Lấy thông tin chi tiết sản phẩm theo ID
   * Trả về thông tin chi tiết của sản phẩm bao gồm tên, mô tả, giá, hình ảnh, trạng thái, v.v.
   * Admin có thể xem thông tin chi tiết của tất cả sản phẩm, bao gồm cả sản phẩm của người dùng
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm cần xem thông tin
   * @returns Thông tin chi tiết sản phẩm
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết sản phẩm theo ID' })
  @ApiParam({ name: 'id', description: 'ID của sản phẩm', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết sản phẩm',
    schema: ApiResponseDto.getSchema(ProductDetailResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR
  )
  async getProductById(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) productId: number
  ): Promise<ApiResponseDto<ProductDetailResponseDto>> {
    // Admin không cần currentUserId để tính canPurchase, truyền undefined
    const result = await this.productAdminService.getProductById(employeeId, productId, undefined);
    return ApiResponseDto.success(result);
  }

  /**
   * Tạo sản phẩm mới
   * Tạo sản phẩm mới với trạng thái mặc định là DRAFT
   * Trả về thông tin sản phẩm đã tạo và các URL ký sẵn để upload tài liệu (hình ảnh, hướng dẫn sử dụng, chi tiết)
   * Sau khi tạo sản phẩm, cần upload các tài liệu lên các URL ký sẵn để hoàn tất quá trình tạo sản phẩm
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param createProductDto DTO chứa thông tin sản phẩm mới
   * @returns Thông tin sản phẩm đã tạo và các URL ký sẵn để upload tài liệu
   */
  @Post()
  @ApiOperation({ summary: 'Tạo sản phẩm mới' })
  @ApiBody({ type: CreateProductAdminDto })
  @ApiResponse({
    status: 201,
    description: 'Sản phẩm đã được tạo thành công',
    schema: ApiResponseDto.getSchema(CreateProductResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_CREATION_FAILED,
    MARKETPLACE_ERROR_CODES.MISSING_REQUIRED_FIELDS,
    MARKETPLACE_ERROR_CODES.FILE_UPLOAD_FAILED,
    MARKETPLACE_ERROR_CODES.INVALID_PRICE
  )
  async createProduct(
    @CurrentEmployee('id') employeeId: number,
    @Body() createProductDto: CreateProductAdminDto
  ): Promise<ApiResponseDto<CreateProductResponseDto>> {
    const result = await this.productAdminService.createProduct(employeeId, createProductDto);
    return ApiResponseDto.created(result);
  }

  /**
   * Cập nhật sản phẩm
   * Cho phép cập nhật thông tin sản phẩm như tên, mô tả, giá, hình ảnh, v.v.
   * Nếu cập nhật hình ảnh, sẽ trả về các URL ký sẵn để upload hình ảnh mới
   * Có thể yêu cầu đăng bán sản phẩm ngay sau khi cập nhật bằng cách đặt publishAfterUpdate = true
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm cần cập nhật
   * @param updateProductDto DTO chứa thông tin cập nhật sản phẩm
   * @returns Sản phẩm đã cập nhật và các URL ký sẵn để upload tài liệu
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật sản phẩm' })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm',
    type: Number,
    example: 1,
  })
  @ApiBody({ type: UpdateProductDto })
  @ApiResponse({
    status: 200,
    description: 'Sản phẩm đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(UpdateProductResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.PRODUCT_UPDATE_FAILED,
    MARKETPLACE_ERROR_CODES.MISSING_REQUIRED_FIELDS,
    MARKETPLACE_ERROR_CODES.FILE_UPLOAD_FAILED
  )
  async updateProduct(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) productId: number,
    @Body() updateProductDto: UpdateProductDto
  ): Promise<ApiResponseDto<UpdateProductResponseDto>> {
    const serviceResult = await this.productAdminService.updateProduct(employeeId, productId, updateProductDto);

    // Chuyển đổi kết quả từ service thành DTO
    const responseDto: UpdateProductResponseDto = {
      product: serviceResult.product,
      presignedUrlImage: serviceResult.presignedUrlImage,
      presignedUrlDetail: serviceResult.presignedUrlDetail,
      presignedUrlUserManual: serviceResult.presignedUrlUserManual,
      publishError: serviceResult.publishError
    };

    // Kiểm tra nếu yêu cầu đăng bán và không có lỗi đăng bán
    if (updateProductDto.publishAfterUpdate && responseDto.product.status === ProductStatus.APPROVED && !responseDto.publishError) {
      return ApiResponseDto.success(responseDto, 'Sản phẩm đã được cập nhật và đăng bán thành công');
    }

    // Nếu có lỗi đăng bán
    if (updateProductDto.publishAfterUpdate && responseDto.publishError) {
      return ApiResponseDto.success(responseDto, 'Sản phẩm đã được cập nhật nhưng không thể đăng bán');
    }

    // Trường hợp chỉ cập nhật thông thường
    return ApiResponseDto.success(responseDto, 'Sản phẩm đã được cập nhật thành công');
  }

  /**
   * Đăng bán sản phẩm
   * Chuyển trạng thái sản phẩm từ APPROVED sang PUBLISHED để đăng bán
   * Sản phẩm chỉ có thể được đăng bán khi đã được phê duyệt (APPROVED)
   * Sản phẩm đã đăng bán sẽ hiển thị cho người dùng và có thể được mua
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm cần đăng bán
   * @returns Thông tin chi tiết sản phẩm sau khi đăng bán
   */
  @Post(':id/publish')
  @ApiOperation({ summary: 'Đăng bán sản phẩm' })
  @ApiParam({ name: 'id', description: 'ID của sản phẩm', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Sản phẩm đã được đăng bán thành công',
    schema: ApiResponseDto.getSchema(ProductDetailResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.INVALID_STATUS,
    MARKETPLACE_ERROR_CODES.PRODUCT_UPDATE_FAILED
  )
  async publishProduct(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) productId: number
  ): Promise<ApiResponseDto<ProductDetailResponseDto>> {
    const result = await this.productAdminService.publishProduct(employeeId, productId);
    return ApiResponseDto.success(result);
  }

  /**
   * Gỡ sản phẩm khỏi marketplace
   * Chuyển trạng thái từ APPROVED về DRAFT và reset isForSale = false cho tài nguyên gốc
   * Chỉ admin tạo sản phẩm mới có quyền gỡ sản phẩm
   * Áp dụng cho tất cả loại sản phẩm: AGENT, KNOWLEDGE_FILE, FINETUNE
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm cần gỡ khỏi marketplace
   * @returns Thông tin chi tiết sản phẩm sau khi gỡ khỏi marketplace
   */
  @Post(':id/unpublish')
  @ApiOperation({ summary: 'Gỡ sản phẩm khỏi marketplace' })
  @ApiParam({ name: 'id', description: 'ID của sản phẩm', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Sản phẩm đã được gỡ khỏi marketplace thành công',
    type: ProductDetailResponseDto,
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.UNAUTHORIZED,
    MARKETPLACE_ERROR_CODES.INVALID_STATUS,
    MARKETPLACE_ERROR_CODES.PRODUCT_STATUS_CHANGE_FAILED
  )
  async unpublishProduct(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) productId: number
  ): Promise<ApiResponseDto<ProductDetailResponseDto>> {
    const result = await this.productAdminService.unpublishProduct(employeeId, productId);
    return ApiResponseDto.success(result, 'Sản phẩm đã được gỡ khỏi marketplace thành công');
  }

  /**
   * Xóa nhiều sản phẩm
   * Thực hiện xóa mềm nhiều sản phẩm (soft delete) bằng cách cập nhật trạng thái thành DELETED
   * Sản phẩm đã xóa sẽ không xuất hiện trong danh sách sản phẩm
   * Chỉ admin mới có quyền xóa sản phẩm
   * Cập nhật is_for_sale = false cho tài nguyên gốc
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param deleteMultipleProductsDto DTO chứa danh sách ID sản phẩm cần xóa
   * @returns Danh sách ID sản phẩm đã xóa thành công và danh sách ID sản phẩm thất bại
   */
  @Delete('batch')
  @ApiOperation({ summary: 'Xóa nhiều sản phẩm' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa nhiều sản phẩm',
    type: DeleteMultipleProductsResponseDto,
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.PRODUCT_DELETED,
    MARKETPLACE_ERROR_CODES.UNAUTHORIZED,
    MARKETPLACE_ERROR_CODES.PRODUCT_DELETE_FAILED
  )
  async deleteMultipleProducts(
    @CurrentEmployee('id') employeeId: number,
    @Body() deleteMultipleProductsDto: DeleteMultipleProductsDto
  ): Promise<ApiResponseDto<DeleteMultipleProductsResponseDto>> {
    const result = await this.productAdminService.deleteMultipleProducts(
      employeeId,
      deleteMultipleProductsDto
    );

    const message = result.successIds.length > 0
      ? `Đã xóa thành công ${result.successIds.length} sản phẩm${result.failedIds.length > 0 ? `, ${result.failedIds.length} sản phẩm thất bại` : ''}`
      : 'Không có sản phẩm nào được xóa thành công';

    return ApiResponseDto.success(result, message);
  }


  /**
   * Cập nhật trạng thái nhiều sản phẩm cùng lúc
   * Cho phép admin thay đổi trạng thái nhiều sản phẩm theo quy tắc linh hoạt
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param updateMultipleStatusDto DTO chứa thông tin trạng thái mới và danh sách ID sản phẩm
   * @returns Danh sách ID sản phẩm đã cập nhật thành công và danh sách ID sản phẩm thất bại
   */
  @Post('batch-status-update')
  @ApiOperation({
    summary: 'Cập nhật trạng thái nhiều sản phẩm',
    description: `
    Cập nhật trạng thái nhiều sản phẩm cùng lúc với quy tắc linh hoạt cho admin:

    **Quy tắc chuyển trạng thái cho sản phẩm user:**
    - Sản phẩm DRAFT: có thể chuyển sang PENDING, APPROVED, REJECTED (không thể xóa)
    - Sản phẩm PENDING: có thể chuyển sang APPROVED, REJECTED, DRAFT
    - Sản phẩm APPROVED: có thể chuyển sang DRAFT, REJECTED
    - Sản phẩm REJECTED: có thể chuyển sang DRAFT, APPROVED

    **Quy tắc chuyển trạng thái cho sản phẩm admin:**
    - DRAFT → APPROVED, DELETED
    - APPROVED → DRAFT, DELETED

    **Các trạng thái có thể sử dụng:**
    - DRAFT: Bản nháp
    - PENDING: Chờ duyệt
    - APPROVED: Đã duyệt
    - REJECTED: Bị từ chối
    - DELETED: Đã xóa (chỉ cho sản phẩm admin)
    `
  })
  @ApiBody({ type: UpdateMultipleProductsStatusDto })
  @ApiResponse({
    status: 200,
    description: 'Kết quả cập nhật trạng thái sản phẩm',
    schema: ApiResponseDto.getSchema(UpdateMultipleProductsStatusResponseDto),
    examples: {
      success: {
        summary: 'Cập nhật thành công',
        value: {
          code: 200,
          message: 'Cập nhật trạng thái thành công',
          result: {
            successIds: [234, 235],
            failedIds: []
          }
        }
      },
      partial_success: {
        summary: 'Cập nhật một phần',
        value: {
          code: 200,
          message: 'Cập nhật trạng thái một phần thành công',
          result: {
            successIds: [234],
            failedIds: [
              {
                id: 235,
                reason: 'Sản phẩm APPROVED của người dùng chỉ có thể chuyển sang DRAFT hoặc REJECTED'
              }
            ]
          }
        }
      },
      all_failed: {
        summary: 'Tất cả thất bại',
        value: {
          code: 400,
          message: 'Không thể cập nhật trạng thái sản phẩm',
          result: {
            successIds: [],
            failedIds: [
              {
                id: 249,
                reason: 'Repository không được cung cấp để kiểm tra User Data Fine Tune'
              }
            ]
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Tất cả sản phẩm đều thất bại khi cập nhật trạng thái',
    schema: ApiResponseDto.getSchema(UpdateMultipleProductsStatusResponseDto),
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.INVALID_STATUS,
    MARKETPLACE_ERROR_CODES.PRODUCT_STATUS_CHANGE_FAILED,
    MARKETPLACE_ERROR_CODES.INVALID_STATUS_TRANSITION
  )
  async updateMultipleProductsStatus(
    @CurrentEmployee('id') employeeId: number,
    @Body() updateMultipleStatusDto: UpdateMultipleProductsStatusDto
  ): Promise<ApiResponseDto<UpdateMultipleProductsStatusResponseDto>> {
    const result = await this.productAdminService.updateMultipleProductsStatus(employeeId, updateMultipleStatusDto);

    // Kiểm tra kết quả và trả về response phù hợp
    const { successIds, failedIds } = result;
    const totalProducts = updateMultipleStatusDto.productIds.length;
    const successCount = successIds.length;
    const failedCount = failedIds.length;

    // Nếu tất cả thất bại
    if (successCount === 0 && failedCount > 0) {
      return new ApiResponseDto(
        result,
        'Không thể cập nhật trạng thái sản phẩm',
        HttpStatus.BAD_REQUEST
      );
    }

    // Nếu tất cả thành công
    if (successCount === totalProducts && failedCount === 0) {
      return ApiResponseDto.success(result, 'Cập nhật trạng thái thành công');
    }

    // Nếu một phần thành công
    return ApiResponseDto.success(result, 'Cập nhật trạng thái một phần thành công');
  }

}