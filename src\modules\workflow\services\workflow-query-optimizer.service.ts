import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, FindManyOptions } from 'typeorm';
import { Workflow, WorkflowExecution, NodeDefinition } from '../entities';
import { WorkflowCacheService } from './workflow-cache.service';

/**
 * Service tối ưu hóa database queries cho workflow module
 * Implements query optimization patterns and caching strategies
 */
@Injectable()
export class WorkflowQueryOptimizerService {
  private readonly logger = new Logger(WorkflowQueryOptimizerService.name);

  constructor(
    @InjectRepository(Workflow)
    private readonly workflowRepository: Repository<Workflow>,
    @InjectRepository(WorkflowExecution)
    private readonly executionRepository: Repository<WorkflowExecution>,
    @InjectRepository(NodeDefinition)
    private readonly nodeDefinitionRepository: Repository<NodeDefinition>,
    private readonly cacheService: WorkflowCacheService,
  ) {}

  /**
   * Optimized workflow query with caching
   * @param userId User ID
   * @param options Query options
   * @returns Workflows with caching
   */
  async findWorkflowsOptimized(
    userId: number,
    options: FindManyOptions<Workflow> = {},
  ): Promise<{
    workflows: Workflow[];
    total: number;
    fromCache: boolean;
  }> {
    const startTime = Date.now();
    
    try {
      // Try cache first
      const cachedWorkflows = await this.cacheService.getCachedWorkflowList(userId, options);
      
      if (cachedWorkflows) {
        this.logger.debug(`Cache hit for user workflows: ${userId} (${Date.now() - startTime}ms)`);
        return {
          workflows: cachedWorkflows,
          total: cachedWorkflows.length,
          fromCache: true,
        };
      }

      // Build optimized query
      const queryBuilder = this.workflowRepository
        .createQueryBuilder('workflow')
        .where('workflow.userId = :userId', { userId })
        .orderBy('workflow.updatedAt', 'DESC');

      // Apply filters
      this.applyWorkflowFilters(queryBuilder, options.where as any);

      // Apply pagination
      if (options.take) {
        queryBuilder.limit(options.take);
      }
      if (options.skip) {
        queryBuilder.offset(options.skip);
      }

      // Execute optimized query
      const [workflows, total] = await queryBuilder.getManyAndCount();

      // Cache results
      await this.cacheService.cacheWorkflowList(userId, workflows, options);

      this.logger.debug(`Database query for user workflows: ${userId} (${Date.now() - startTime}ms)`);

      return {
        workflows,
        total,
        fromCache: false,
      };
    } catch (error) {
      this.logger.error(`Error in optimized workflow query: ${error.message}`);
      throw error;
    }
  }

  /**
   * Optimized single workflow query with caching
   * @param workflowId Workflow ID
   * @param userId User ID for access control
   * @returns Workflow with caching
   */
  async findWorkflowByIdOptimized(
    workflowId: string,
    userId: number,
  ): Promise<{
    workflow: Workflow | null;
    fromCache: boolean;
  }> {
    const startTime = Date.now();

    try {
      // Try cache first
      const cachedWorkflow = await this.cacheService.getCachedWorkflow(workflowId);
      
      if (cachedWorkflow && cachedWorkflow.userId === userId) {
        this.logger.debug(`Cache hit for workflow: ${workflowId} (${Date.now() - startTime}ms)`);
        return {
          workflow: cachedWorkflow,
          fromCache: true,
        };
      }

      // Optimized database query
      const workflow = await this.workflowRepository
        .createQueryBuilder('workflow')
        .where('workflow.id = :workflowId', { workflowId })
        .andWhere('workflow.userId = :userId', { userId })
        .getOne();

      if (workflow) {
        // Cache the result
        await this.cacheService.cacheWorkflow(workflow, userId);
      }

      this.logger.debug(`Database query for workflow: ${workflowId} (${Date.now() - startTime}ms)`);

      return {
        workflow,
        fromCache: false,
      };
    } catch (error) {
      this.logger.error(`Error in optimized workflow query: ${error.message}`);
      throw error;
    }
  }

  /**
   * Optimized execution queries with pagination and caching
   * @param userId User ID
   * @param options Query options
   * @returns Executions with optimization
   */
  async findExecutionsOptimized(
    userId: number,
    options: {
      workflowId?: string;
      status?: string;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'ASC' | 'DESC';
    } = {},
  ): Promise<{
    executions: WorkflowExecution[];
    total: number;
    fromCache: boolean;
  }> {
    const startTime = Date.now();
    const { page = 1, limit = 20, sortBy = 'startedAt', sortOrder = 'DESC' } = options;

    try {
      // Build optimized query with joins
      const queryBuilder = this.executionRepository
        .createQueryBuilder('execution')
        .leftJoinAndSelect('execution.workflow', 'workflow')
        .where('execution.metadata ->> \'userId\' = :userId', { userId: userId.toString() });

      // Apply filters
      if (options.workflowId) {
        queryBuilder.andWhere('execution.workflowId = :workflowId', { 
          workflowId: options.workflowId 
        });
      }

      if (options.status) {
        queryBuilder.andWhere('execution.status = :status', { 
          status: options.status 
        });
      }

      // Apply sorting
      queryBuilder.orderBy(`execution.${sortBy}`, sortOrder);

      // Apply pagination
      const skip = (page - 1) * limit;
      queryBuilder.skip(skip).take(limit);

      // Execute query
      const [executions, total] = await queryBuilder.getManyAndCount();

      this.logger.debug(`Optimized execution query: ${userId} (${Date.now() - startTime}ms)`);

      return {
        executions,
        total,
        fromCache: false,
      };
    } catch (error) {
      this.logger.error(`Error in optimized execution query: ${error.message}`);
      throw error;
    }
  }

  /**
   * Optimized node definition queries with caching
   * @param category Node category filter
   * @returns Node definitions with caching
   */
  async findNodeDefinitionsOptimized(
    category?: string,
  ): Promise<{
    nodeDefinitions: NodeDefinition[];
    fromCache: boolean;
  }> {
    const startTime = Date.now();

    try {
      // Build cache key
      const cacheKey = `node_definitions_${category || 'all'}`;
      
      // Try cache first
      const cachedData = await this.cacheService.getCachedStatistics(cacheKey);
      
      if (cachedData) {
        this.logger.debug(`Cache hit for node definitions (${Date.now() - startTime}ms)`);
        return {
          nodeDefinitions: cachedData,
          fromCache: true,
        };
      }

      // Optimized database query
      const queryBuilder = this.nodeDefinitionRepository
        .createQueryBuilder('nodeDefinition')
        .orderBy('nodeDefinition.category', 'ASC')
        .addOrderBy('nodeDefinition.name', 'ASC');

      if (category) {
        queryBuilder.where('nodeDefinition.category = :category', { category });
      }

      const nodeDefinitions = await queryBuilder.getMany();

      // Cache results
      await this.cacheService.cacheStatistics(cacheKey, nodeDefinitions, 3600); // 1 hour

      this.logger.debug(`Database query for node definitions (${Date.now() - startTime}ms)`);

      return {
        nodeDefinitions,
        fromCache: false,
      };
    } catch (error) {
      this.logger.error(`Error in optimized node definition query: ${error.message}`);
      throw error;
    }
  }

  /**
   * Optimized statistics queries with aggressive caching
   * @param userId User ID
   * @param workflowId Optional workflow ID filter
   * @returns Statistics with caching
   */
  async getExecutionStatisticsOptimized(
    userId: number,
    workflowId?: string,
  ): Promise<{
    statistics: any;
    fromCache: boolean;
  }> {
    const startTime = Date.now();

    try {
      // Build cache key
      const cacheKey = `execution_stats_${userId}_${workflowId || 'all'}`;
      
      // Try cache first
      const cachedStats = await this.cacheService.getCachedStatistics(cacheKey);
      
      if (cachedStats) {
        this.logger.debug(`Cache hit for execution statistics (${Date.now() - startTime}ms)`);
        return {
          statistics: cachedStats,
          fromCache: true,
        };
      }

      // Optimized aggregation query
      const queryBuilder = this.executionRepository
        .createQueryBuilder('execution')
        .where('execution.metadata ->> \'userId\' = :userId', { userId: userId.toString() });

      if (workflowId) {
        queryBuilder.andWhere('execution.workflowId = :workflowId', { workflowId });
      }

      // Get total count
      const total = await queryBuilder.getCount();

      // Get status distribution
      const statusStats = await queryBuilder
        .select('execution.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .groupBy('execution.status')
        .getRawMany();

      // Get average duration for completed executions
      const avgDurationResult = await queryBuilder
        .select('AVG(execution.duration)', 'avgDuration')
        .where('execution.duration IS NOT NULL')
        .andWhere('execution.metadata ->> \'userId\' = :userId', { userId: userId.toString() })
        .getRawOne();

      // Build statistics object
      const byStatus = statusStats.reduce((acc, stat) => {
        acc[stat.status] = parseInt(stat.count);
        return acc;
      }, {});

      const completedCount = byStatus['completed'] || 0;
      const successRate = total > 0 ? (completedCount / total) * 100 : 0;

      const statistics = {
        total,
        byStatus,
        avgDuration: avgDurationResult?.avgDuration ? parseFloat(avgDurationResult.avgDuration) : null,
        successRate,
      };

      // Cache results for 10 minutes
      await this.cacheService.cacheStatistics(cacheKey, statistics, 600);

      this.logger.debug(`Database query for execution statistics (${Date.now() - startTime}ms)`);

      return {
        statistics,
        fromCache: false,
      };
    } catch (error) {
      this.logger.error(`Error in optimized statistics query: ${error.message}`);
      throw error;
    }
  }

  /**
   * Batch invalidate cache when workflow is updated
   * @param workflowId Workflow ID
   * @param userId User ID
   */
  async invalidateWorkflowRelatedCache(workflowId: string, userId: number): Promise<void> {
    try {
      await Promise.all([
        this.cacheService.invalidateWorkflowCache(workflowId),
        this.cacheService.invalidateUserWorkflowListCache(userId),
        // Invalidate related statistics
        this.cacheService.cacheStatistics(`execution_stats_${userId}_${workflowId}`, null, 0),
        this.cacheService.cacheStatistics(`execution_stats_${userId}_all`, null, 0),
      ]);

      this.logger.debug(`Invalidated cache for workflow: ${workflowId}`);
    } catch (error) {
      this.logger.error(`Error invalidating workflow cache: ${error.message}`);
    }
  }

  /**
   * Batch invalidate cache when execution is updated
   * @param executionId Execution ID
   * @param userId User ID
   * @param workflowId Workflow ID
   */
  async invalidateExecutionRelatedCache(
    executionId: string,
    userId: number,
    workflowId: string,
  ): Promise<void> {
    try {
      await Promise.all([
        this.cacheService.invalidateExecutionCache(executionId),
        // Invalidate statistics that might be affected
        this.cacheService.cacheStatistics(`execution_stats_${userId}_${workflowId}`, null, 0),
        this.cacheService.cacheStatistics(`execution_stats_${userId}_all`, null, 0),
      ]);

      this.logger.debug(`Invalidated cache for execution: ${executionId}`);
    } catch (error) {
      this.logger.error(`Error invalidating execution cache: ${error.message}`);
    }
  }

  // Private helper methods

  /**
   * Apply filters to workflow query builder
   * @param queryBuilder Query builder
   * @param filters Filter conditions
   */
  private applyWorkflowFilters(
    queryBuilder: SelectQueryBuilder<Workflow>,
    filters: any = {},
  ): void {
    if (filters.isActive !== undefined) {
      queryBuilder.andWhere('workflow.isActive = :isActive', { 
        isActive: filters.isActive 
      });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(workflow.name ILIKE :search OR workflow.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    if (filters.tags && filters.tags.length > 0) {
      queryBuilder.andWhere('workflow.tags && :tags', { 
        tags: filters.tags 
      });
    }

    if (filters.createdAfter) {
      queryBuilder.andWhere('workflow.createdAt >= :createdAfter', { 
        createdAfter: filters.createdAfter 
      });
    }

    if (filters.createdBefore) {
      queryBuilder.andWhere('workflow.createdAt <= :createdBefore', { 
        createdBefore: filters.createdBefore 
      });
    }
  }
}
