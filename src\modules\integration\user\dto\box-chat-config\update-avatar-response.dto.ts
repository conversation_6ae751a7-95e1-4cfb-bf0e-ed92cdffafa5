import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của việc cập nhật avatar box chat
 */
export class UpdateAvatarResponseDto {
  /**
   * ID của box chat config
   */
  @ApiProperty({
    description: 'ID của box chat config',
    example: 1
  })
  id: number;

  /**
   * S3 key của avatar
   */
  @ApiProperty({
    description: 'S3 key của avatar',
    example: '1/IMAGE/2025/06/avatar-1750999921123.png',
    nullable: true
  })
  avatar: string | null;

  /**
   * Presigned URL để upload avatar (nếu có avatarMime)
   */
  @ApiProperty({
    description: 'Presigned URL để upload avatar (chỉ có khi avatarMime được cung cấp)',
    example: 'https://s3.amazonaws.com/bucket/path/to/upload?signature=...',
    nullable: true
  })
  avatarUploadUrl: string | null;

  /**
   * Thông báo kết quả
   */
  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Cập nhật avatar thành công'
  })
  message: string;
}
