-- Script SQL để thêm trường ảnh bìa vào bảng users và employees
-- Chạy script này để cập nhật cấu trúc database

-- Thêm cột cover_image vào bảng users
ALTER TABLE users 
ADD COLUMN cover_image VARCHAR(500) NULL COMMENT 'ảnh bìa của người dùng';

-- Thêm cột cover_image vào bảng employees  
ALTER TABLE employees 
ADD COLUMN cover_image VARCHAR(500) NULL COMMENT 'ảnh bìa của nhân viên';

-- Tạo index cho cột cover_image nếu cần thiết (tù<PERSON> chọn)
-- CREATE INDEX idx_users_cover_image ON users(cover_image);
-- CREATE INDEX idx_employees_cover_image ON employees(cover_image);

-- <PERSON><PERSON><PERSON> tra kết quả
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'cover_image';

SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'employees' AND COLUMN_NAME = 'cover_image';
