# SMS Config Decryption

## Tổng quan

Khi tạo SMS campaign, hệ thống sẽ tự động lấy và giải mã cấu hình SMS từ Integration entity dựa trên `serverId`. Config đã giải mã sẽ được truyền vào worker để thực hiện gửi SMS.

## Quy trình giải mã

### 1. Lấy Integration từ Database
```typescript
const integration = await this.integrationRepository.findOne({
  where: { id: integrationId }
});
```

### 2. <PERSON>ể<PERSON> tra dữ liệu mã hóa
- Kiểm tra `encryptedConfig` và `secretKey` có tồn tại không
- <PERSON><PERSON><PERSON> không có, sử dụng fallback từ `metadata`

### 3. Giải mã config
```typescript
const decryptionResult = this.keyPairEncryptionService.decrypt(
  integration.encryptedConfig,
  integration.secretKey
);
```

### 4. Parse JSON và xử lý
```typescript
const decryptedConfig = JSON.parse(decryptionResult.decryptedData);
```

### 5. Trả về config đầy đủ
```typescript
return {
  id: integration.id,
  integrationName: integration.integrationName,
  typeId: integration.typeId,
  provider: metadata.provider,
  // Config đã giải mã
  apiKey: decryptedConfig.apiKey,
  endpoint: decryptedConfig.endpoint,
  additionalSettings: decryptedConfig.additionalSettings,
  fallback: false
};
```

## Fallback Mechanism

Khi giải mã thất bại, hệ thống sẽ fallback về `metadata`:

```typescript
return {
  id: integration.id,
  integrationName: integration.integrationName,
  typeId: integration.typeId,
  provider: metadata.provider,
  // Fallback từ metadata
  apiKey: metadata.apiKey || null,
  endpoint: metadata.apiUrl || metadata.endpoint || null,
  additionalSettings: metadata.additionalSettings || {},
  fallback: true
};
```

## Cấu trúc Config cho các Provider

### FPT SMS
```json
{
  "apiKey": "your-fpt-api-key",
  "endpoint": "https://api01.sms.fpt.net",
  "brandName": "YOUR_BRAND",
  "additionalSettings": {
    "timeout": 30000,
    "retries": 3
  }
}
```

### Twilio
```json
{
  "apiKey": "your-twilio-auth-token",
  "clientId": "your-twilio-account-sid",
  "endpoint": "https://api.twilio.com",
  "additionalSettings": {
    "messagingServiceSid": "MGxxxxx",
    "statusCallback": "https://your-webhook.com/status"
  }
}
```

### Generic Provider
```json
{
  "apiKey": "your-api-key",
  "endpoint": "https://api.provider.com",
  "additionalSettings": {
    "headers": {
      "Content-Type": "application/json"
    },
    "timeout": 30000
  }
}
```

## Validation

### Provider Validation
Hệ thống chỉ chấp nhận các provider sau:
- `FPT_SMS`
- `TWILIO`
- `VONAGE`
- `SPEED_SMS`

### Config Validation
- `apiKey` hoặc `clientId` phải tồn tại
- `endpoint` phải là URL hợp lệ
- `provider` phải được định nghĩa trong metadata

## Error Handling

### 1. Integration không tồn tại
```json
{
  "code": 4001,
  "message": "Không tìm thấy SMS integration với ID: {id}"
}
```

### 2. Không phải SMS integration
```json
{
  "code": 4001,
  "message": "Integration không phải là SMS server"
}
```

### 3. Lỗi giải mã
- Hệ thống sẽ log warning và sử dụng fallback
- Không throw exception để đảm bảo service hoạt động

### 4. Lỗi parse JSON
```json
{
  "code": 5000,
  "message": "Lỗi parse cấu hình SMS đã giải mã"
}
```

## Logging

### Debug Logs
```
[DEBUG] Giải mã config cho integration: {integrationId}
[DEBUG] SMS config đã được giải mã thành công cho provider: {provider}
```

### Warning Logs
```
[WARN] Integration {integrationId} không có encrypted config, sử dụng metadata
[WARN] Không thể giải mã SMS config cho integration {integrationId}
```

### Error Logs
```
[ERROR] Lỗi khi lấy SMS config từ integration {integrationId}: {error}
[ERROR] Lỗi parse JSON config: {error}
```

## Worker Integration

Config đã giải mã sẽ được truyền vào worker thông qua `SmsMarketingJobDto`:

```typescript
const jobData: SmsMarketingJobDto = {
  campaignId: campaign.id,
  userId: campaign.userId,
  smsIntegrationConfig: decryptedConfig, // Config đã giải mã
  recipients: recipients,
  content: processedContent,
  // ... other fields
};
```

## Security Considerations

1. **Encryption**: Config nhạy cảm được mã hóa trong database
2. **Decryption**: Chỉ giải mã khi cần thiết
3. **Memory**: Config đã giải mã không được cache lâu dài
4. **Logging**: Không log config đã giải mã để tránh leak thông tin
5. **Access Control**: Verify ownership trước khi giải mã

## Testing

Sử dụng file `test-sms-config-decryption.http` để test:

1. Test với các provider khác nhau
2. Test fallback mechanism
3. Test error handling
4. Test ownership validation
5. Verify config được truyền đúng vào worker

## Performance

- Giải mã chỉ thực hiện khi tạo campaign
- Config đã giải mã được lưu trong campaign entity
- Worker sử dụng config đã sẵn sàng, không cần giải mã lại
