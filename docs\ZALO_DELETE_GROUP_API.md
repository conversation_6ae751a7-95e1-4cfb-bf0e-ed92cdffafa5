# Zalo Group Delete API - <PERSON><PERSON><PERSON><PERSON> tán nhóm

## Tổng quan

API gi<PERSON>i tán nhóm chat Zalo cho phép OA (Official Account) xóa hoàn toàn một nhóm chat mà OA đã tạo ra.

## Endpoint

### Gi<PERSON>i tán nhóm chat

```
DELETE /v1/zalo-group-management/{integrationId}/{groupId}
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| integrationId | string | Yes | ID của Integration Zalo OA (UUID format) |
| groupId | string | Yes | ID của nhóm chat Zalo |

#### Headers

| Header | Type | Required | Description |
|--------|------|----------|-------------|
| Authorization | string | Yes | Bearer JWT token |
| Content-Type | string | Yes | application/json |

## Zalo API Implementation

### Zalo API Endpoint
```
POST https://openapi.zalo.me/v3.0/oa/group/delete
```

### Request Headers
```
access_token: <your_access_token>
Content-Type: application/json
```

### Request Body
```json
{
    "group_id": "f414c8f76fa586fbdfb4"
}
```

### Response
```json
{
    "error": 0,
    "message": "Success"
}
```

## Điều kiện sử dụng

1. **Quyền hạn OA:**
   - OA phải là admin của nhóm
   - Chỉ có thể xóa nhóm do OA tạo ra
   - Ứng dụng cần được cấp quyền quản lý thông tin nhóm

2. **Trạng thái nhóm:**
   - Nhóm phải đang hoạt động (chưa bị xóa)
   - Nhóm phải tồn tại trong hệ thống

3. **Hậu quả:**
   - Tất cả dữ liệu nhóm sẽ bị xóa vĩnh viễn
   - Không thể khôi phục sau khi xóa

## Response

### Success Response (200 OK)

```json
{
  "success": true,
  "data": {
    "success": true
  },
  "message": "Giải tán nhóm chat thành công"
}
```

### Error Responses

**403 Forbidden - Không có quyền xóa nhóm:**
```json
{
  "success": false,
  "message": "Chỉ có thể xóa nhóm do OA tạo ra",
  "error": "FORBIDDEN"
}
```

**404 Not Found - Nhóm không tồn tại:**
```json
{
  "success": false,
  "message": "Nhóm không tồn tại hoặc không có quyền truy cập",
  "error": "NOT_FOUND"
}
```

**500 Internal Server Error - Lỗi Zalo API:**
```json
{
  "success": false,
  "message": "Lỗi từ Zalo API: <error_message>",
  "error": "EXTERNAL_SERVICE_ERROR"
}
```

## Sử dụng

### cURL Example

```bash
curl -X DELETE \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json'
```

### JavaScript Example

```javascript
const response = await fetch('/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4', {
  method: 'DELETE',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
if (result.success) {
  console.log('Nhóm đã được giải tán thành công');
} else {
  console.error('Lỗi:', result.message);
}
```

## Lưu ý quan trọng

1. **Không thể hoàn tác:** Việc giải tán nhóm là vĩnh viễn và không thể khôi phục
2. **Quyền admin:** Chỉ OA admin mới có thể giải tán nhóm
3. **Nhóm do OA tạo:** Chỉ có thể giải tán nhóm mà OA đã tạo ra
4. **Soft delete:** Hệ thống sẽ thực hiện soft delete trong database sau khi giải tán thành công trên Zalo

## Workflow

1. Kiểm tra quyền truy cập nhóm của user
2. Xác minh nhóm được tạo bởi OA
3. Lấy access token từ Integration
4. Gọi Zalo API để giải tán nhóm
5. Kiểm tra response từ Zalo
6. Thực hiện soft delete trong database
7. Trả về kết quả cho client

## Error Codes

| Error Code | Description |
|------------|-------------|
| 0 | Thành công |
| -124 | Không có quyền truy cập |
| -201 | Nhóm không tồn tại |
| -216 | OA không phải admin của nhóm |
| -217 | Không thể xóa nhóm không do OA tạo |
