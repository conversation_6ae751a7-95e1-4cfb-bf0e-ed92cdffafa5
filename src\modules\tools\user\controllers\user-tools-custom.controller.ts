import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { UserToolsCustomService } from '../services';
import {
  CreateUserToolsCustomDto,
  QueryUserToolsCustomDto,
  UserToolsCustomResponseDto,
  UpdateUserToolsCustomDto,
  DeleteMultipleToolsCustomDto,
  ManageToolApiKeyDto,
  ToolApiKeyResponseDto,
} from '../dto';
import { JwtUserGuard } from '@/modules/auth/guards';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';

@ApiTags('User Tools Custom')
@Controller('user/tools-custom')
@UseGuards(JwtUserGuard)
export class UserToolsCustomController {
  constructor(private readonly userToolsCustomService: UserToolsCustomService) {}

  /**
   * Tạo mới công cụ tùy chỉnh từ OpenAPI spec
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo mới công cụ tùy chỉnh từ OpenAPI spec',
    description: 'Tạo một hoặc nhiều công cụ tùy chỉnh từ đặc tả OpenAPI',
  })
  @ApiBody({ type: CreateUserToolsCustomDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo công cụ tùy chỉnh thành công',
    type: ApiResponseDto,
  })
  async createUserToolCustom(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateUserToolsCustomDto,
  ): Promise<ApiResponseDto<{ message: string; toolsCreated: number; tools: UserToolsCustomResponseDto[] }>> {
    const result = await this.userToolsCustomService.createUserToolCustom(user.id, createDto);
    return ApiResponseDto.success(result, 'Tạo công cụ tùy chỉnh thành công');
  }

  /**
   * Lấy danh sách công cụ tùy chỉnh với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách công cụ tùy chỉnh',
    description: 'Lấy danh sách công cụ tùy chỉnh của người dùng với phân trang và tìm kiếm',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách thành công',
    type: ApiResponseDto<PaginatedResult<UserToolsCustomResponseDto>>,
  })
  async findUserToolsCustom(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: QueryUserToolsCustomDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserToolsCustomResponseDto>>> {
    const result = await this.userToolsCustomService.findByUser(user.id, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách công cụ tùy chỉnh thành công');
  }

  /**
   * Lấy chi tiết công cụ tùy chỉnh
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết công cụ tùy chỉnh',
    description: 'Lấy thông tin chi tiết của một công cụ tùy chỉnh',
  })
  @ApiParam({ name: 'id', description: 'ID của công cụ tùy chỉnh' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy chi tiết thành công',
    type: ApiResponseDto<UserToolsCustomResponseDto>,
  })
  async findUserToolCustomById(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<UserToolsCustomResponseDto>> {
    const result = await this.userToolsCustomService.findById(id, user.id);
    return ApiResponseDto.success(result, 'Lấy chi tiết công cụ tùy chỉnh thành công');
  }

  /**
   * Cập nhật công cụ tùy chỉnh
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật công cụ tùy chỉnh',
    description: 'Cập nhật thông tin của một công cụ tùy chỉnh',
  })
  @ApiParam({ name: 'id', description: 'ID của công cụ tùy chỉnh' })
  @ApiBody({ type: UpdateUserToolsCustomDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật thành công',
    type: ApiResponseDto<UserToolsCustomResponseDto>,
  })
  async updateUserToolCustom(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
    @Body() updateDto: UpdateUserToolsCustomDto,
  ): Promise<ApiResponseDto<UserToolsCustomResponseDto>> {
    const result = await this.userToolsCustomService.updateToolCustom(id, user.id, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật công cụ tùy chỉnh thành công');
  }

  /**
   * Xóa công cụ tùy chỉnh
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa công cụ tùy chỉnh',
    description: 'Xóa một công cụ tùy chỉnh',
  })
  @ApiParam({ name: 'id', description: 'ID của công cụ tùy chỉnh' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa thành công',
    type: ApiResponseDto,
  })
  async deleteUserToolCustom(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<void>> {
    await this.userToolsCustomService.deleteToolCustom(id, user.id);
    return ApiResponseDto.success(undefined, 'Xóa công cụ tùy chỉnh thành công');
  }

  /**
   * Xóa nhiều công cụ tùy chỉnh cùng lúc
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa nhiều công cụ tùy chỉnh',
    description: 'Xóa nhiều công cụ tùy chỉnh cùng lúc',
  })
  @ApiBody({ type: DeleteMultipleToolsCustomDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa thành công',
    type: ApiResponseDto,
  })
  async deleteMultipleUserToolsCustom(
    @CurrentUser() user: JwtPayload,
    @Body() deleteDto: DeleteMultipleToolsCustomDto,
  ): Promise<ApiResponseDto<{ message: string; deletedCount: number; failedIds: string[] }>> {
    const result = await this.userToolsCustomService.deleteMultipleToolsCustom(user.id, deleteDto);
    return ApiResponseDto.success(result, 'Xóa công cụ tùy chỉnh thành công');
  }

  /**
   * Cập nhật trạng thái active
   */
  @Put(':id/toggle-active')
  @ApiOperation({
    summary: 'Cập nhật trạng thái active',
    description: 'Bật/tắt trạng thái hoạt động của công cụ tùy chỉnh',
  })
  @ApiParam({ name: 'id', description: 'ID của công cụ tùy chỉnh' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật trạng thái thành công',
    type: ApiResponseDto<UserToolsCustomResponseDto>,
  })
  async toggleToolCustomActive(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<UserToolsCustomResponseDto>> {
    const result = await this.userToolsCustomService.toggleToolCustomActive(id, user.id);
    return ApiResponseDto.success(result, 'Cập nhật trạng thái thành công');
  }

  /**
   * Thêm hoặc cập nhật API Key cho tool custom
   */
  @Put(':id/api-key')
  @ApiOperation({
    summary: 'Quản lý API Key',
    description: 'Thêm hoặc cập nhật API Key cho công cụ tùy chỉnh',
  })
  @ApiParam({ name: 'id', description: 'ID của công cụ tùy chỉnh' })
  @ApiBody({ type: ManageToolApiKeyDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Quản lý API Key thành công',
    type: ApiResponseDto<ToolApiKeyResponseDto>,
  })
  async manageToolApiKey(
    @CurrentUser() user: JwtPayload,
    @Param('id') toolId: string,
    @Body() apiKeyDto: ManageToolApiKeyDto,
  ): Promise<ApiResponseDto<ToolApiKeyResponseDto>> {
    const result = await this.userToolsCustomService.manageToolApiKey(toolId, user.id, apiKeyDto);
    return ApiResponseDto.success(result, 'Quản lý API Key thành công');
  }

  /**
   * Lấy thông tin API Key của tool custom
   */
  @Get(':id/api-key')
  @ApiOperation({
    summary: 'Lấy thông tin API Key',
    description: 'Lấy thông tin API Key của công cụ tùy chỉnh (các giá trị được ẩn)',
  })
  @ApiParam({ name: 'id', description: 'ID của công cụ tùy chỉnh' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin API Key thành công',
    type: ApiResponseDto<ToolApiKeyResponseDto>,
  })
  async getToolApiKey(
    @CurrentUser() user: JwtPayload,
    @Param('id') toolId: string,
  ): Promise<ApiResponseDto<ToolApiKeyResponseDto | null>> {
    const result = await this.userToolsCustomService.getToolApiKey(toolId, user.id);
    return ApiResponseDto.success(result, 'Lấy thông tin API Key thành công');
  }

  /**
   * Xóa API Key của tool custom
   */
  @Delete(':id/api-key')
  @ApiOperation({
    summary: 'Xóa API Key',
    description: 'Xóa API Key của công cụ tùy chỉnh',
  })
  @ApiParam({ name: 'id', description: 'ID của công cụ tùy chỉnh' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa API Key thành công',
    type: ApiResponseDto,
  })
  async deleteToolApiKey(
    @CurrentUser() user: JwtPayload,
    @Param('id') toolId: string,
  ): Promise<ApiResponseDto<void>> {
    // Cập nhật tool để xóa apiKeyId
    await this.userToolsCustomService.updateToolCustom(toolId, user.id, { apiKeyId: null } as any);
    return ApiResponseDto.success(undefined, 'Xóa API Key thành công');
  }
}
