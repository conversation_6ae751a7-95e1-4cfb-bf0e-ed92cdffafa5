import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentEmployee } from '@/modules/auth/decorators';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { ApiResponseDto } from '@common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import {
  Body,
  Controller,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { MODELS_ERROR_CODES } from '../../exceptions';
import {
  AdminValidateFineTuningJobDto,
  AdminFineTuningValidationResponseDto,
  AdminCreateFineTuningJobDto,
  AdminCreateFineTuningJobResponseDto,
} from '../dto/fine-tuning-validation';
import { AdminFineTuningValidationService } from '../services/admin-fine-tuning-validation.service';
import { AdminFineTuningJobService } from '../services/admin-fine-tuning-job.service';

/**
 * Controller xử lý API cho Admin Fine-Tuning Jobs
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_FINETUNING_JOB)
@Controller('admin/fine-tuning-job')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class AdminFineTuningJobController {
  constructor(
    private readonly adminFineTuningValidationService: AdminFineTuningValidationService,
    private readonly adminFineTuningJobService: AdminFineTuningJobService,
  ) {}

  // /**
  //  * Validate admin fine-tuning job và ước tính thời gian
  //  */
  // @Post('validate')
  // @ApiOperation({
  //   summary: 'Validate admin fine-tuning job và ước tính thời gian',
  //   description: `
  //   Validate dữ liệu đầu vào và ước tính thời gian cho admin fine-tuning job.

  //   **Đặc điểm admin fine-tuning:**
  //   - Chỉ sử dụng system keys, không cần user keys
  //   - Không có logic R-Points
  //   - Sử dụng admin datasets đã được approve
  //   - Có thể tạo models cho hệ thống

  //   **Quy trình validate:**
  //   1. Kiểm tra admin dataset đã approve và valid
  //   2. Kiểm tra system model hỗ trợ fine-tuning
  //   3. Kiểm tra system key active cho provider
  //   4. Ước tính thời gian hoàn thành
  //   5. Tạo validation token cho execute

  //   **Lưu ý:**
  //   - Dataset phải có status = APPROVED
  //   - Dataset phải có isValid = true
  //   - System model phải supportFineTuning = true
  //   - System key phải active cho provider tương ứng
  //   `,
  // })
  // @ApiBody({
  //   type: AdminValidateFineTuningJobDto,
  //   description: 'Thông tin để validate admin fine-tuning job',
  //   examples: {
  //     'OpenAI Admin Fine-Tuning': {
  //       summary: 'Validate OpenAI admin fine-tuning',
  //       description: 'Validate fine-tuning job với OpenAI provider sử dụng system key',
  //       value: {
  //         datasetId: '112b8f44-82a1-4426-8b19-2413dab6c262',
  //         systemModelId: '96ba4303-a318-4d16-b064-e4489eec78a8',
  //         provider: 'OPENAI',
  //       }
  //     },
  //     'Google Admin Fine-Tuning': {
  //       summary: 'Validate Google admin fine-tuning',
  //       description: 'Validate fine-tuning job với Google provider sử dụng system key',
  //       value: {
  //         datasetId: '223c9f55-93b2-5537-9c2a-3524ebc7d373',
  //         systemModelId: 'a7cb5414-b429-5e27-c175-f5590ffd89b9',
  //         provider: 'GOOGLE',
  //       }
  //     }
  //   }
  // })
  // @ApiResponse({
  //   status: HttpStatus.OK,
  //   description: 'Validation completed successfully',
  //   type: ApiResponseDto<AdminFineTuningValidationResponseDto>,
  // })
  // @ApiErrorResponse(
  //   MODELS_ERROR_CODES.INVALID_INPUT,
  //   MODELS_ERROR_CODES.ADMIN_DATA_FINE_TUNE_NOT_FOUND,
  //   MODELS_ERROR_CODES.MODEL_NOT_FOUND,
  //   MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND,
  // )
  // async validateFineTuningJob(
  //   @CurrentEmployee('id') employeeId: number,
  //   @Body() validateDto: AdminValidateFineTuningJobDto,
  // ): Promise<ApiResponseDto<AdminFineTuningValidationResponseDto>> {
  //   return this.adminFineTuningValidationService.validateFineTuningJob(employeeId, validateDto);
  // }

  // /**
  //  * Execute admin fine-tuning job
  //  */
  // @Post('execute')
  // @ApiOperation({
  //   summary: 'Execute admin fine-tuning job',
  //   description: `
  //   Thực hiện admin fine-tuning job sau khi đã validate thành công.

  //   **Quy trình execute:**
  //   1. Validate dataset và permissions
  //   2. Lấy system key cho provider
  //   3. Upload dataset lên provider (nếu cần)
  //   4. Tạo fine-tuning job với provider
  //   5. Lưu thông tin vào database
  //   6. Tạo Redis job để monitor status

  //   **Đặc điểm admin execute:**
  //   - Không trừ R-Points
  //   - Sử dụng system keys
  //   - Tạo job với quyền admin
  //   - Auto-monitor qua Redis queue
  //   - Lưu vào fine_tune_histories với employeeId

  //   **Lưu ý:**
  //   - Không cần validation token, thực hiện validation trực tiếp
  //   - Job sẽ được monitor tự động qua Redis queue
  //   - Không có auto-refund logic vì không có points
  //   `,
  // })
  // @ApiBody({
  //   type: AdminCreateFineTuningJobDto,
  //   description: 'Thông tin để execute admin fine-tuning job',
  //   examples: {
  //     'Execute Admin Fine-Tuning': {
  //       summary: 'Execute admin fine-tuning job',
  //       description: 'Thực hiện fine-tuning job với quyền admin',
  //       value: {
  //         name: 'Admin Custom GPT Model',
  //         description: 'Model được fine-tune bởi admin cho hệ thống',
  //         datasetId: '112b8f44-82a1-4426-8b19-2413dab6c262',
  //         systemModelId: '96ba4303-a318-4d16-b064-e4489eec78a8',
  //         provider: 'OPENAI',
  //       }
  //     }
  //   }
  // })
  // @ApiResponse({
  //   status: HttpStatus.CREATED,
  //   description: 'Admin fine-tuning job executed successfully',
  //   type: ApiResponseDto<AdminCreateFineTuningJobResponseDto>,
  // })
  // @ApiErrorResponse(
  //   MODELS_ERROR_CODES.INVALID_INPUT,
  //   MODELS_ERROR_CODES.ADMIN_DATA_FINE_TUNE_NOT_FOUND,
  //   MODELS_ERROR_CODES.MODEL_NOT_FOUND,
  //   MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND,
  //   MODELS_ERROR_CODES.FINE_TUNING_JOB_CREATION_FAILED,
  // )
  // async executeFineTuningJob(
  //   @CurrentEmployee('id') employeeId: number,
  //   @Body() executeDto: AdminCreateFineTuningJobDto,
  // ): Promise<ApiResponseDto<AdminCreateFineTuningJobResponseDto>> {
  //   return this.adminFineTuningJobService.executeFineTuningJob(employeeId, executeDto);
  // }
}
