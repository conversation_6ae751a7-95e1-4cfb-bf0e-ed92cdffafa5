import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException } from '@common/exceptions';
import { GOOGLE_ERROR_CODES, handleGoogleApiError } from '../exceptions/google.exception';
import { GoogleGmailApiService } from '../gmail/google-gmail-api.service';
import {
  GmailTokens,
  GmailUserInfo,
  GmailOAuthState,
  GmailOAuthResult,
  GMAIL_SCOPES,
} from '../interfaces/google-gmail.interface';

/**
 * Service xử lý Gmail OAuth flow
 */
@Injectable()
export class GoogleGmailOAuthService {
  private readonly logger = new Logger(GoogleGmailOAuthService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly gmailApiService: GoogleGmailApiService,
  ) {}

  /**
   * Tạo URL xác thực OAuth2 cho Gmail
   * @param userId ID của user
   * @param scopes Danh sách quyền cần xin
   * @param action Action để phân biệt các flow khác nhau
   * @param metadata Metadata bổ sung (có thể chứa redirectUri)
   * @returns Auth URL và state
   */
  generateAuthUrl(
    userId: number,
    scopes: string[] = GoogleGmailApiService.getDefaultScopes('send'),
    action: string = 'connect',
    metadata?: Record<string, unknown>,
  ): { authUrl: string; state: string } {
    try {
      const state = this.generateState(userId, action, metadata);
      const redirectUri = metadata?.redirectUri as string;
      const authUrl = this.gmailApiService.generateAuthUrl(scopes, state, redirectUri);

      this.logger.log(`Generated Gmail auth URL for user ${userId}${redirectUri ? ` with custom redirect URI: ${redirectUri}` : ''}`);

      return { authUrl, state };
    } catch (error) {
      this.logger.error(`Error generating Gmail auth URL: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        GOOGLE_ERROR_CODES.GOOGLE_API_CONFIGURATION_ERROR,
        'Không thể tạo URL xác thực Gmail',
      );
    }
  }

  /**
   * Xử lý OAuth callback
   * @param code Authorization code
   * @param state State token
   * @returns OAuth result
   */
  async handleOAuthCallback(code: string, state: string): Promise<GmailOAuthResult> {
    try {
      // Parse state
      const parsedState = this.parseState(state);
      
      // Validate state (check timestamp, etc.)
      this.validateState(parsedState);

      // Get tokens from code
      const tokens = await this.gmailApiService.getTokensFromCode(code);

      // Set credentials and get user info
      this.gmailApiService.setCredentials(tokens);
      const userInfo = await this.gmailApiService.getUserInfo();

      this.logger.log(`Gmail OAuth callback processed for user ${parsedState.userId}`);

      return {
        tokens,
        userInfo,
        state: parsedState,
      };
    } catch (error) {
      this.logger.error(`Error handling Gmail OAuth callback: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
      throw new AppException(
        errorCode,
        'Không thể xử lý OAuth callback',
      );
    }
  }

  /**
   * Refresh access token
   * @param refreshToken Refresh token
   * @returns New tokens
   */
  async refreshToken(refreshToken: string): Promise<GmailTokens> {
    try {
      const tokens = await this.gmailApiService.refreshAccessToken(refreshToken);
      this.logger.log('Gmail access token refreshed successfully');
      return tokens;
    } catch (error) {
      this.logger.error(`Error refreshing Gmail token: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
      throw new AppException(
        errorCode,
        'Không thể refresh Gmail token',
      );
    }
  }

  /**
   * Kiểm tra token có hết hạn không
   * @param expiryDate Thời gian hết hạn (timestamp)
   * @param bufferMinutes Buffer time trước khi hết hạn (phút)
   * @returns True nếu token sắp hết hạn hoặc đã hết hạn
   */
  isTokenExpired(expiryDate: number, bufferMinutes: number = 5): boolean {
    const now = Date.now();
    const bufferMs = bufferMinutes * 60 * 1000;
    return now >= (expiryDate - bufferMs);
  }

  /**
   * Kiểm tra token có cần refresh không
   * @param tokens Gmail tokens
   * @param bufferMinutes Buffer time trước khi hết hạn (phút)
   * @returns True nếu cần refresh
   */
  needsRefresh(tokens: GmailTokens, bufferMinutes: number = 5): boolean {
    if (!tokens.expiry_date) {
      return false; // Không có expiry date, giả sử token vẫn hợp lệ
    }

    return this.isTokenExpired(tokens.expiry_date, bufferMinutes);
  }

  /**
   * Auto refresh token nếu cần
   * @param tokens Gmail tokens hiện tại
   * @returns Tokens mới hoặc tokens cũ nếu không cần refresh
   */
  async autoRefreshIfNeeded(tokens: GmailTokens): Promise<GmailTokens> {
    try {
      if (!this.needsRefresh(tokens)) {
        return tokens; // Token vẫn hợp lệ
      }

      if (!tokens.refresh_token) {
        throw new AppException(
          GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN,
          'Không có refresh token để làm mới access token',
        );
      }

      this.logger.log('Auto refreshing Gmail token');
      return await this.refreshToken(tokens.refresh_token);
    } catch (error) {
      this.logger.error(`Error in auto refresh: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN,
        'Không thể tự động refresh token',
      );
    }
  }

  /**
   * Validate Gmail connection với tokens
   * @param tokens Gmail tokens
   * @returns True nếu kết nối thành công
   */
  async validateConnection(tokens: GmailTokens): Promise<boolean> {
    try {
      // Auto refresh if needed
      const validTokens = await this.autoRefreshIfNeeded(tokens);
      
      this.gmailApiService.setCredentials(validTokens);
      return await this.gmailApiService.testConnection();
    } catch (error) {
      this.logger.error(`Error validating Gmail connection: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Lấy thông tin user từ tokens
   * @param tokens Gmail tokens
   * @returns User info
   */
  async getUserInfoFromTokens(tokens: GmailTokens): Promise<GmailUserInfo> {
    try {
      // Auto refresh if needed
      const validTokens = await this.autoRefreshIfNeeded(tokens);
      
      this.gmailApiService.setCredentials(validTokens);
      return await this.gmailApiService.getUserInfo();
    } catch (error) {
      this.logger.error(`Error getting user info from tokens: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
      throw new AppException(
        errorCode,
        'Không thể lấy thông tin user từ tokens',
      );
    }
  }

  /**
   * Revoke tokens (logout)
   * @param tokens Gmail tokens
   * @returns True nếu thành công
   */
  async revokeTokens(tokens: GmailTokens): Promise<boolean> {
    try {
      // Google OAuth2 revoke endpoint
      const revokeUrl = `https://oauth2.googleapis.com/revoke?token=${tokens.access_token}`;
      
      const response = await fetch(revokeUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      if (response.ok) {
        this.logger.log('Gmail tokens revoked successfully');
        return true;
      } else {
        this.logger.warn(`Failed to revoke tokens: ${response.status} ${response.statusText}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`Error revoking Gmail tokens: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Tạo state token với tiền tố gmail
   * @param userId ID của user
   * @param action Action
   * @param metadata Metadata bổ sung
   * @returns State string với tiền tố gmail
   */
  private generateState(userId: number, action: string, metadata?: Record<string, unknown>): string {
    const stateObj: GmailOAuthState = {
      userId,
      timestamp: Date.now(),
      action,
      metadata,
    };

    const base64State = Buffer.from(JSON.stringify(stateObj)).toString('base64');
    return `gmail_${base64State}`;
  }

  /**
   * Parse state token với tiền tố gmail
   * @param state State string với tiền tố gmail
   * @returns Parsed state object
   */
  private parseState(state: string): GmailOAuthState {
    try {
      // Kiểm tra và loại bỏ tiền tố gmail
      if (!state.startsWith('gmail_')) {
        throw new Error('State token không có tiền tố gmail hợp lệ');
      }

      const base64State = state.substring(6); // Loại bỏ "gmail_"
      const decoded = Buffer.from(base64State, 'base64').toString('utf-8');
      return JSON.parse(decoded);
    } catch (error) {
      this.logger.error(`Error parsing Gmail state: ${error.message}`, error.stack);

      throw new AppException(
        GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN,
        'Gmail state token không hợp lệ',
      );
    }
  }

  /**
   * Validate state token
   * @param state Parsed state object
   */
  private validateState(state: GmailOAuthState): void {
    const now = Date.now();
    const maxAge = 10 * 60 * 1000; // 10 minutes

    if (now - state.timestamp > maxAge) {
      throw new AppException(
        GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN,
        'State token đã hết hạn',
      );
    }

    if (!state.userId || !state.action) {
      throw new AppException(
        GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN,
        'State token thiếu thông tin bắt buộc',
      );
    }
  }

  /**
   * Lấy scopes mặc định cho các use case khác nhau
   */
  static getDefaultScopes(useCase: 'send' | 'read' | 'full' = 'send'): string[] {
    return GoogleGmailApiService.getDefaultScopes(useCase);
  }

  /**
   * Lấy tất cả scopes có sẵn
   */
  static getAllScopes(): typeof GMAIL_SCOPES {
    return GMAIL_SCOPES;
  }
}
