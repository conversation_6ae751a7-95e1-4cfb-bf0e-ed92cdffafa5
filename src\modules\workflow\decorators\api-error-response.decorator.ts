import { applyDecorators } from '@nestjs/common';
import { ApiResponse, getSchemaPath } from '@nestjs/swagger';
import { ErrorCode } from '@/common/exceptions';
import { ApiResponseDto } from '@/common/response';

/**
 * Interface cho error response schema
 */
interface ErrorResponseSchema {
  code: number;
  message: string;
  detail?: any;
  timestamp: string;
  path: string;
}

/**
 * Custom decorator để document workflow error responses trong Swagger
 * Follows existing error handling patterns from the codebase
 */
export function ApiErrorResponse(...errorCodes: (ErrorCode | string)[]) {
  const responses = errorCodes.map((errorCode) => {
    let code: number;
    let message: string;
    let httpStatus: number;

    if (typeof errorCode === 'string') {
      // Legacy string error codes
      code = 15000; // Default workflow error code
      message = errorCode;
      httpStatus = 400;
    } else {
      // ErrorCode objects
      code = errorCode.code;
      message = errorCode.message;
      httpStatus = errorCode.status;
    }

    return ApiResponse({
      status: httpStatus,
      description: `Error: ${message}`,
      schema: {
        allOf: [
          { $ref: getSchemaPath(ApiResponseDto) },
          {
            properties: {
              code: {
                type: 'number',
                example: code,
                description: 'Error code',
              },
              message: {
                type: 'string',
                example: message,
                description: 'Error message',
              },
              result: {
                type: 'object',
                nullable: true,
                example: null,
                description: 'Result data (null for errors)',
              },
              detail: {
                type: 'object',
                nullable: true,
                description: 'Additional error details',
                example: {
                  field: 'workflowId',
                  value: 'invalid-uuid',
                  constraint: 'must be a valid UUID',
                },
              },
              timestamp: {
                type: 'string',
                format: 'date-time',
                example: '2023-01-01T00:00:00.000Z',
                description: 'Error timestamp',
              },
              path: {
                type: 'string',
                example: '/api/v1/user/workflows',
                description: 'Request path where error occurred',
              },
            },
          },
        ],
      },
    });
  });

  return applyDecorators(...responses);
}

/**
 * Common workflow error responses for reuse
 */
export const CommonWorkflowErrorResponses = {
  /**
   * Standard validation errors
   */
  VALIDATION_ERRORS: ApiErrorResponse(
    // Using string codes for common HTTP errors
    'VALIDATION_FAILED',
    'MISSING_REQUIRED_FIELDS',
    'INVALID_DATA_FORMAT',
  ),

  /**
   * Authentication and authorization errors
   */
  AUTH_ERRORS: ApiErrorResponse(
    'UNAUTHORIZED',
    'ACCESS_DENIED',
    'TOKEN_EXPIRED',
  ),

  /**
   * Resource not found errors
   */
  NOT_FOUND_ERRORS: ApiErrorResponse(
    'WORKFLOW_NOT_FOUND',
    'NODE_NOT_FOUND',
    'EXECUTION_NOT_FOUND',
  ),

  /**
   * Workflow execution errors
   */
  EXECUTION_ERRORS: ApiErrorResponse(
    'EXECUTION_FAILED',
    'EXECUTION_TIMEOUT',
    'EXECUTION_CANCELLED',
    'INVALID_STATUS',
  ),

  /**
   * Node testing errors
   */
  NODE_TEST_ERRORS: ApiErrorResponse(
    'NODE_TEST_FAILED',
    'NODE_EXECUTION_FAILED',
    'NODE_INVALID_CONFIG',
  ),

  /**
   * Webhook errors
   */
  WEBHOOK_ERRORS: ApiErrorResponse(
    'WEBHOOK_VALIDATION_FAILED',
    'WEBHOOK_PROCESSING_FAILED',
    'INVALID_WEBHOOK_PAYLOAD',
  ),

  /**
   * SSE connection errors
   */
  SSE_ERRORS: ApiErrorResponse(
    'CONNECTION_FAILED',
    'CONNECTION_TIMEOUT',
    'INVALID_AUTH_TOKEN',
  ),
};

/**
 * Workflow-specific error response decorator
 * Combines common errors with workflow-specific ones
 */
export function WorkflowApiErrorResponse(...additionalErrors: (ErrorCode | string)[]) {
  return applyDecorators(
    CommonWorkflowErrorResponses.VALIDATION_ERRORS,
    CommonWorkflowErrorResponses.AUTH_ERRORS,
    ApiErrorResponse(...additionalErrors),
  );
}

/**
 * Node testing specific error response decorator
 */
export function NodeTestApiErrorResponse(...additionalErrors: (ErrorCode | string)[]) {
  return applyDecorators(
    CommonWorkflowErrorResponses.VALIDATION_ERRORS,
    CommonWorkflowErrorResponses.AUTH_ERRORS,
    CommonWorkflowErrorResponses.NOT_FOUND_ERRORS,
    CommonWorkflowErrorResponses.NODE_TEST_ERRORS,
    ApiErrorResponse(...additionalErrors),
  );
}

/**
 * Workflow execution specific error response decorator
 */
export function ExecutionApiErrorResponse(...additionalErrors: (ErrorCode | string)[]) {
  return applyDecorators(
    CommonWorkflowErrorResponses.VALIDATION_ERRORS,
    CommonWorkflowErrorResponses.AUTH_ERRORS,
    CommonWorkflowErrorResponses.NOT_FOUND_ERRORS,
    CommonWorkflowErrorResponses.EXECUTION_ERRORS,
    ApiErrorResponse(...additionalErrors),
  );
}

/**
 * SSE specific error response decorator
 */
export function SSEApiErrorResponse(...additionalErrors: (ErrorCode | string)[]) {
  return applyDecorators(
    CommonWorkflowErrorResponses.AUTH_ERRORS,
    CommonWorkflowErrorResponses.SSE_ERRORS,
    ApiErrorResponse(...additionalErrors),
  );
}

/**
 * Webhook specific error response decorator
 */
export function WebhookApiErrorResponse(...additionalErrors: (ErrorCode | string)[]) {
  return applyDecorators(
    CommonWorkflowErrorResponses.VALIDATION_ERRORS,
    CommonWorkflowErrorResponses.WEBHOOK_ERRORS,
    ApiErrorResponse(...additionalErrors),
  );
}
