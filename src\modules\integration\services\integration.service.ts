import { Injectable, Logger } from '@nestjs/common';
import { IntegrationRepository } from '../repositories/integration.repository';
import { IntegrationProviderRepository } from '../repositories/integration-provider.repository';
import { Integration } from '../entities/integration.entity';
import { 
  CreateIntegrationDto, 
  UpdateIntegrationDto, 
  IntegrationResponseDto,
  IntegrationQueryDto 
} from '../dto';
import { PaginatedResult } from '@common/response';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';

/**
 * Service xử lý logic nghiệp vụ cho Integration
 */
@Injectable()
export class IntegrationService {
  private readonly logger = new Logger(IntegrationService.name);

  constructor(
    private readonly integrationRepository: IntegrationRepository,
    private readonly integrationProviderRepository: IntegrationProviderRepository,
  ) {}

  /**
   * Lấy danh sách integrations của user với phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách integrations với phân trang
   */
  async getUserIntegrations(
    userId: number, 
    queryDto: IntegrationQueryDto
  ): Promise<PaginatedResult<IntegrationResponseDto>> {
    try {
      this.logger.log(`Getting integrations for user ${userId}`);

      const result = await this.integrationRepository.findAll({
        userId,
        page: queryDto.page,
        limit: queryDto.limit,
        typeId: queryDto.typeId,
        ownedType: queryDto.ownedType,
      });

      // Transform entities to DTOs
      const items = result.items.map(integration => this.toResponseDto(integration));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Error getting user integrations: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy integration theo ID
   * @param id UUID của integration
   * @param userId ID của người dùng
   * @returns IntegrationResponseDto hoặc null
   */
  async getIntegrationById(id: string, userId: number): Promise<IntegrationResponseDto | null> {
    try {
      this.logger.log(`Getting integration ${id} for user ${userId}`);

      const integration = await this.integrationRepository.findById(id, userId);
      if (!integration) {
        return null;
      }

      return this.toResponseDto(integration);
    } catch (error) {
      this.logger.error(`Error getting integration by ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo integration mới
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo integration
   * @returns IntegrationResponseDto
   */
  @Transactional()
  async createIntegration(userId: number, createDto: CreateIntegrationDto): Promise<IntegrationResponseDto> {
    try {
      this.logger.log(`Creating integration for user ${userId} with typeId ${createDto.typeId}`);

      // Validate integration provider exists
      const provider = await this.integrationProviderRepository.findOne({
        where: { id: createDto.typeId }
      });

      if (!provider) {
        // throw new AppException('INTEGRATION_PROVIDER_NOT_FOUND', 'Integration provider không tồn tại');
      }

      const integration = await this.integrationRepository.upsertIntegration({
        integrationName: createDto.integrationName,
        typeId: createDto.typeId,
        userId,
        ownedType: createDto.ownedType,
        employeeId: createDto.employeeId,
        encryptedConfig: createDto.encryptedConfig,
        mcpEncryptedConfig: createDto.mcpEncryptedConfig,
        metadata: createDto.metadata,
      });

      this.logger.log(`Successfully created integration ${integration.id} for user ${userId}`);
      return this.toResponseDto(integration);
    } catch (error) {
      this.logger.error(`Error creating integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật integration
   * @param id UUID của integration
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns IntegrationResponseDto hoặc null
   */
  @Transactional()
  async updateIntegration(
    id: string, 
    userId: number, 
    updateDto: UpdateIntegrationDto
  ): Promise<IntegrationResponseDto | null> {
    try {
      this.logger.log(`Updating integration ${id} for user ${userId}`);

      // Verify integration exists and belongs to user
      const existingIntegration = await this.integrationRepository.findById(id, userId);
      if (!existingIntegration) {
        this.logger.warn(`Integration ${id} not found for user ${userId}`);
        return null;
      }

      // Update integration
      if (updateDto.integrationName !== undefined) {
        existingIntegration.integrationName = updateDto.integrationName;
      }
      if (updateDto.ownedType !== undefined) {
        existingIntegration.ownedType = updateDto.ownedType;
      }
      if (updateDto.employeeId !== undefined) {
        existingIntegration.employeeId = updateDto.employeeId;
      }
      if (updateDto.encryptedConfig !== undefined) {
        existingIntegration.encryptedConfig = updateDto.encryptedConfig;
      }
      if (updateDto.mcpEncryptedConfig !== undefined) {
        existingIntegration.mcpEncryptedConfig = updateDto.mcpEncryptedConfig;
      }
      if (updateDto.metadata !== undefined) {
        existingIntegration.metadata = updateDto.metadata;
      }

      const updatedIntegration = await this.integrationRepository.save(existingIntegration);

      this.logger.log(`Successfully updated integration ${id}`);
      return this.toResponseDto(updatedIntegration);
    } catch (error) {
      this.logger.error(`Error updating integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa integration
   * @param id UUID của integration
   * @param userId ID của người dùng
   * @returns Số lượng bản ghi đã xóa
   */
  @Transactional()
  async deleteIntegration(id: string, userId: number): Promise<number> {
    try {
      this.logger.log(`Deleting integration ${id} for user ${userId}`);

      const deletedCount = await this.integrationRepository.deleteIntegration(id, userId);

      this.logger.log(`Successfully deleted ${deletedCount} integrations`);
      return deletedCount;
    } catch (error) {
      this.logger.error(`Error deleting integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy integrations theo typeIds
   * @param userId ID của người dùng
   * @param typeIds Danh sách type IDs
   * @returns Danh sách IntegrationResponseDto
   */
  async getIntegrationsByTypeIds(userId: number, typeIds: number[]): Promise<IntegrationResponseDto[]> {
    try {
      this.logger.log(`Getting integrations for user ${userId} by typeIds: ${typeIds.join(', ')}`);

      const integrations = await this.integrationRepository.findByUserIdAndTypeIds(userId, typeIds);
      
      return integrations.map(integration => this.toResponseDto(integration));
    } catch (error) {
      this.logger.error(`Error getting integrations by typeIds: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật encrypted config
   * @param id UUID của integration
   * @param userId ID của người dùng
   * @param encryptedConfig Config được mã hóa
   * @param mcpEncryptedConfig MCP config được mã hóa
   * @returns IntegrationResponseDto hoặc null
   */
  @Transactional()
  async updateEncryptedConfig(
    id: string,
    userId: number,
    encryptedConfig?: string,
    mcpEncryptedConfig?: string,
  ): Promise<IntegrationResponseDto | null> {
    try {
      this.logger.log(`Updating encrypted config for integration ${id}`);

      // Verify integration belongs to user
      const existingIntegration = await this.integrationRepository.findById(id, userId);
      if (!existingIntegration) {
        this.logger.warn(`Integration ${id} not found for user ${userId}`);
        return null;
      }

      const updatedIntegration = await this.integrationRepository.updateEncryptedConfig(
        id,
        encryptedConfig,
        mcpEncryptedConfig,
      );

      if (!updatedIntegration) {
        return null;
      }

      this.logger.log(`Successfully updated encrypted config for integration ${id}`);
      return this.toResponseDto(updatedIntegration);
    } catch (error) {
      this.logger.error(`Error updating encrypted config: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Chuyển đổi entity sang response DTO
   * @param integration Integration entity
   * @returns IntegrationResponseDto
   */
  private toResponseDto(integration: Integration): IntegrationResponseDto {
    return {
      id: integration.id,
      integrationName: integration.integrationName,
      typeId: integration.typeId,
      userId: integration.userId,
      ownedType: integration.ownedType,
      createdAt: integration.createdAt,
      employeeId: integration.employeeId,
      encryptedConfig: integration.encryptedConfig,
      mcpEncryptedConfig: integration.mcpEncryptedConfig,
      metadata: integration.metadata,
    };
  }
}
