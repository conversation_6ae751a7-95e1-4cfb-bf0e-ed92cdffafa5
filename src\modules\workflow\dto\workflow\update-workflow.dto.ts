import { ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsBoolean,
  IsOptional,
  IsObject,
  MaxLength,
  <PERSON>Length,
  ValidateNested
} from 'class-validator';
import { CreateWorkflowDto } from './create-workflow.dto';

/**
 * DTO for updating an existing workflow
 * Uses PartialType to make all fields optional
 */
export class UpdateWorkflowDto extends PartialType(CreateWorkflowDto) {
  /**
   * Workflow name - must be unique per user (optional for update)
   */
  @ApiPropertyOptional({
    description: 'Workflow name (unique per user)',
    example: 'Updated Customer Onboarding Flow',
    minLength: 1,
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  @MinLength(1, { message: 'Workflow name cannot be empty' })
  @MaxLength(255, { message: 'Workflow name cannot exceed 255 characters' })
  name?: string;

  /**
   * Whether the workflow is active
   */
  @ApiPropertyOptional({
    description: 'Whether the workflow is active',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  /**
   * Workflow definition structure (JSONB)
   */
  @ApiPropertyOptional({
    description: 'Workflow definition structure',
    example: {
      nodes: [
        { id: 'start', type: 'start', data: {} },
        { id: 'end', type: 'end', data: {} }
      ],
      edges: [
        { id: 'e1', source: 'start', target: 'end' }
      ],
      version: '1.0.1'
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  definition?: Record<string, any>;
}
