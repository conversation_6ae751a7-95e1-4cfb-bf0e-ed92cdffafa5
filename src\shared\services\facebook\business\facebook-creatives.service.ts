import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@config/config.service';
import { ConfigType } from '@/config';
import { FacebookConfig } from '@config/interfaces';
import { firstValueFrom } from 'rxjs';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  AdCreative,
  CreateAdCreativeRequest,
  UpdateAdCreativeRequest,
  GetAdCreativesResponse,
  CreativePreview,
  GetCreativePreviewRequest,
  GetCreativePreviewResponse,
} from '../interfaces/facebook-creatives.interface';

/**
 * Service để quản lý Ad Creatives trong Facebook Business API
 */
@Injectable()
export class FacebookCreativesService {
  private readonly logger = new Logger(FacebookCreativesService.name);
  private readonly facebookConfig: FacebookConfig;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.facebookConfig = this.configService.getConfig<FacebookConfig>(
      ConfigType.Facebook,
    );
  }

  /**
   * Lấy danh sách Ad Creatives của Ad Account
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param fields Các trường cần lấy
   * @param limit Số lượng kết quả
   * @returns Danh sách Ad Creatives
   */
  async getAdCreatives(
    adAccountId: string,
    accessToken: string,
    fields?: string,
    limit?: number,
  ): Promise<GetAdCreativesResponse> {
    try {
      this.logger.log(`Lấy danh sách Ad Creatives cho Ad Account ${adAccountId}`);

      const params: any = {
        access_token: accessToken,
      };

      if (fields) {
        params.fields = fields;
      } else {
        params.fields = 'id,name,object_story_spec,image_hash,image_url,video_id,body,title,link_url,call_to_action,status,created_time,updated_time';
      }

      if (limit) {
        params.limit = limit;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetAdCreativesResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/act_${adAccountId}/adcreatives`,
          { params },
        ),
      );

      this.logger.log(
        `Đã lấy ${response.data.data.length} Ad Creatives cho Ad Account ${adAccountId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách Ad Creatives: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách Ad Creatives',
        { adAccountId },
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của một Ad Creative
   * @param creativeId ID của Ad Creative
   * @param accessToken Access token
   * @param fields Các trường cần lấy
   * @returns Thông tin Ad Creative
   */
  async getAdCreative(
    creativeId: string,
    accessToken: string,
    fields?: string,
  ): Promise<AdCreative> {
    try {
      this.logger.log(`Lấy thông tin Ad Creative ${creativeId}`);

      const params: any = {
        access_token: accessToken,
      };

      if (fields) {
        params.fields = fields;
      } else {
        params.fields = 'id,name,object_story_spec,image_hash,image_url,video_id,body,title,link_url,call_to_action,status,created_time,updated_time,account_id,actor_id,adlabels,asset_feed_spec,authorization_category,auto_update,branded_content_sponsor_page_id,bundle_folder_id,categorization_criteria,category_media_source,destination_set_id,dynamic_ad_voice,effective_authorization_category,effective_instagram_media_id,effective_instagram_story_id,effective_object_story_id,enable_direct_install,enable_launch_instant_app,image_crops,image_file,instagram_actor_id,instagram_permalink_url,instagram_story_id,interactive_components_spec,link_deep_link_url,link_destination_display_url,link_og_id,messenger_sponsored_message,object_id,object_story_id,object_url,place_page_set_id,platform_customizations,playable_asset_id,portrait_customizations,product_set_id,recommender_settings,source_instagram_media_id,template_url,template_url_spec,thumbnail_url,url_tags,use_page_actor_override,video_data';
      }

      const response = await firstValueFrom(
        this.httpService.get<AdCreative>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${creativeId}`,
          { params },
        ),
      );

      this.logger.log(`Đã lấy thông tin Ad Creative ${creativeId}`);

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thông tin Ad Creative ${creativeId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin Ad Creative',
        { creativeId },
      );
    }
  }

  /**
   * Tạo Ad Creative mới
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param creativeData Dữ liệu Ad Creative
   * @returns Ad Creative đã tạo
   */
  async createAdCreative(
    adAccountId: string,
    accessToken: string,
    creativeData: CreateAdCreativeRequest,
  ): Promise<AdCreative> {
    try {
      this.logger.log(`Tạo Ad Creative mới cho Ad Account ${adAccountId}`);

      const requestData = {
        ...creativeData,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post<AdCreative>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/act_${adAccountId}/adcreatives`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(
        `Đã tạo Ad Creative ${response.data.id} cho Ad Account ${adAccountId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo Ad Creative cho Ad Account ${adAccountId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo Ad Creative',
        { adAccountId, creativeData },
      );
    }
  }

  /**
   * Cập nhật Ad Creative
   * @param creativeId ID của Ad Creative
   * @param accessToken Access token
   * @param updateData Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  async updateAdCreative(
    creativeId: string,
    accessToken: string,
    updateData: UpdateAdCreativeRequest,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Cập nhật Ad Creative ${creativeId}`);

      const requestData = {
        ...updateData,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${creativeId}`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(`Đã cập nhật Ad Creative ${creativeId}`);

      return { success: response.data.success || true };
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật Ad Creative ${creativeId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật Ad Creative',
        { creativeId, updateData },
      );
    }
  }

  /**
   * Xóa Ad Creative
   * @param creativeId ID của Ad Creative
   * @param accessToken Access token
   * @returns Kết quả xóa
   */
  async deleteAdCreative(
    creativeId: string,
    accessToken: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Xóa Ad Creative ${creativeId}`);

      const response = await firstValueFrom(
        this.httpService.delete(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${creativeId}`,
          {
            params: {
              access_token: accessToken,
            },
          },
        ),
      );

      this.logger.log(`Đã xóa Ad Creative ${creativeId}`);

      return { success: response.data.success || true };
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa Ad Creative ${creativeId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa Ad Creative',
        { creativeId },
      );
    }
  }

  /**
   * Lấy preview của Ad Creative
   * @param creativeId ID của Ad Creative
   * @param accessToken Access token
   * @param previewRequest Tham số preview
   * @returns Preview của Ad Creative
   */
  async getCreativePreviews(
    creativeId: string,
    accessToken: string,
    previewRequest: GetCreativePreviewRequest,
  ): Promise<GetCreativePreviewResponse> {
    try {
      this.logger.log(`Lấy preview cho Ad Creative ${creativeId}`);

      const params = {
        access_token: accessToken,
        ...previewRequest,
      };

      const response = await firstValueFrom(
        this.httpService.get<GetCreativePreviewResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${creativeId}/previews`,
          { params },
        ),
      );

      this.logger.log(`Đã lấy preview cho Ad Creative ${creativeId}`);

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy preview Ad Creative ${creativeId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy preview Ad Creative',
        { creativeId, previewRequest },
      );
    }
  }

  /**
   * Tạo Video Creative
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param videoId ID của video đã upload
   * @param name Tên creative
   * @param pageId ID của page
   * @param message Nội dung message
   * @param callToAction Call to action
   * @returns Video Creative đã tạo
   */
  async createVideoCreative(
    adAccountId: string,
    accessToken: string,
    videoId: string,
    name: string,
    pageId: string,
    message?: string,
    callToAction?: any,
  ): Promise<AdCreative> {
    try {
      this.logger.log(`Tạo Video Creative cho Ad Account ${adAccountId}`);

      const creativeData: CreateAdCreativeRequest = {
        name,
        object_story_spec: {
          page_id: pageId,
          video_data: {
            video_id: videoId,
            message,
            call_to_action: callToAction,
          },
        },
      };

      return await this.createAdCreative(adAccountId, accessToken, creativeData);
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo Video Creative: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo Video Creative',
        { adAccountId, videoId, name },
      );
    }
  }

  /**
   * Tạo Image Creative
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param imageHash Hash của image đã upload
   * @param name Tên creative
   * @param pageId ID của page
   * @param message Nội dung message
   * @param link URL link
   * @param callToAction Call to action
   * @returns Image Creative đã tạo
   */
  async createImageCreative(
    adAccountId: string,
    accessToken: string,
    imageHash: string,
    name: string,
    pageId: string,
    message?: string,
    link?: string,
    callToAction?: any,
  ): Promise<AdCreative> {
    try {
      this.logger.log(`Tạo Image Creative cho Ad Account ${adAccountId}`);

      const creativeData: CreateAdCreativeRequest = {
        name,
        object_story_spec: {
          page_id: pageId,
          link_data: {
            link: link || '',
            message,
            call_to_action: callToAction,
            picture: `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${imageHash}/picture`,
          },
        },
        image_hash: imageHash,
      };

      return await this.createAdCreative(adAccountId, accessToken, creativeData);
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo Image Creative: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo Image Creative',
        { adAccountId, imageHash, name },
      );
    }
  }

  /**
   * Tạo Carousel Creative
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param name Tên creative
   * @param pageId ID của page
   * @param childAttachments Danh sách child attachments
   * @param message Nội dung message
   * @returns Carousel Creative đã tạo
   */
  async createCarouselCreative(
    adAccountId: string,
    accessToken: string,
    name: string,
    pageId: string,
    childAttachments: any[],
    message?: string,
  ): Promise<AdCreative> {
    try {
      this.logger.log(`Tạo Carousel Creative cho Ad Account ${adAccountId}`);

      const creativeData: CreateAdCreativeRequest = {
        name,
        object_story_spec: {
          page_id: pageId,
          link_data: {
            link: '',
            message,
            child_attachments: childAttachments,
            multi_share_optimized: true,
          },
        },
      };

      return await this.createAdCreative(adAccountId, accessToken, creativeData);
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo Carousel Creative: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo Carousel Creative',
        { adAccountId, name, childAttachments },
      );
    }
  }
}
