# Zalo ZNS Image Upload API

## Tổng quan

API này cho phép upload ảnh/logo cho ZNS template theo quy định của Zalo và lấy thông tin loại nội dung ZNS được phép gửi.

## Endpoints

### 1. Upload ảnh cho ZNS template

**POST** `/api/marketing/zalo/zns/{integrationId}/images/upload`

Upload ảnh/logo cho ZNS template theo quy định của Zalo.

#### Quy định về ảnh

**Logo:**
- Định dạng: PNG
- Kích thước: 400x96 px
- Dung lượng tối đa: 500 KB

**Hình ảnh:**
- Định dạng: JPG, PNG
- Tỉ lệ: 16:9
- Dung lượng tối đa: 500 KB

**Hạn mức:**
- Tối đa 5000 ảnh được upload cho mỗi ứng dụng mỗi tháng

#### Request

**Headers:**
```
Authorization: Bearer {jwt_token}
Content-Type: multipart/form-data
```

**Path Parameters:**
- `integrationId` (string): ID của Integration (UUID)

**Form Data:**
- `file` (file): File ảnh (JPG/PNG, tối đa 500KB) - **Bắt buộc**
- `description` (string): Mô tả ảnh (tùy chọn, tối đa 255 ký tự)

#### Response

**Success (201):**
```json
{
  "code": 0,
  "message": "Upload ảnh ZNS thành công",
  "data": {
    "id": 1,
    "mediaId": "wESbL53O_shdvIPILC7iR_JpC552r_fjukKg",
    "originalFilename": "logo.png",
    "fileSize": 45678,
    "mimeType": "image/png",
    "width": 400,
    "height": 96,
    "description": "Logo công ty cho template ZNS",
    "createdAt": *************
  }
}
```

**Error (400):**
```json
{
  "code": 9999,
  "message": "File phải có định dạng JPG hoặc PNG"
}
```

**Error (413):**
```json
{
  "code": 9999,
  "message": "File không được vượt quá 500KB"
}
```

### 2. Lấy thông tin loại nội dung ZNS được phép gửi

**GET** `/api/marketing/zalo/zns/{integrationId}/template-tags`

Lấy danh sách các loại nội dung ZNS mà Official Account được phép gửi.

#### Loại nội dung ZNS

- **TRANSACTION**: Giao dịch (Cấp độ 1)
- **CUSTOMER_CARE**: Chăm sóc khách hàng (Cấp độ 2)
- **PROMOTION**: Hậu mãi (Cấp độ 3)

#### Request

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Path Parameters:**
- `integrationId` (string): ID của Integration (UUID)

#### Response

**Success (200):**
```json
{
  "code": 0,
  "message": "Lấy thông tin loại nội dung ZNS thành công",
  "data": {
    "data": ["TRANSACTION", "CUSTOMER_CARE", "PROMOTION"]
  }
}
```

## Cách sử dụng

### 1. Upload ảnh

```bash
curl -X POST \
  'https://api.example.com/api/marketing/zalo/zns/*********/images/upload' \
  -H 'Authorization: Bearer your_jwt_token' \
  -F 'file=@/path/to/your/image.png' \
  -F 'description=Logo công ty cho template ZNS'
```

### 2. Lấy loại nội dung ZNS

```bash
curl -X GET \
  'https://api.example.com/api/marketing/zalo/zns/*********/template-tags' \
  -H 'Authorization: Bearer your_jwt_token'
```

### 3. Sử dụng media_id trong template ZNS

Sau khi upload thành công, bạn sẽ nhận được `media_id`. Sử dụng `media_id` này khi tạo template ZNS:

```json
{
  "template_name": "Template với logo",
  "template_type": 1,
  "tag": "1",
  "layout": {
    "header": {
      "components": [
        {
          "LOGO": {
            "value": "wESbL53O_shdvIPILC7iR_JpC552r_fjukKg"
          }
        }
      ]
    },
    "body": {
      "components": [
        {
          "PARAGRAPH": {
            "value": "Nội dung tin nhắn..."
          }
        }
      ]
    }
  }
}
```

## Lưu ý

1. **Quyền truy cập**: Cần có access token hợp lệ cho Official Account
2. **Hạn mức**: Tối đa 5000 ảnh/tháng cho mỗi ứng dụng
3. **Kích thước**: File không được vượt quá 500KB
4. **Định dạng**: Chỉ hỗ trợ JPG và PNG
5. **Lưu trữ**: Ảnh được lưu trữ trên server Zalo, `media_id` được lưu trong database để tái sử dụng

## Database Schema

Bảng `zalo_zns_images` lưu trữ thông tin ảnh đã upload:

```sql
CREATE TABLE zalo_zns_images (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  oa_id VARCHAR(50) NOT NULL,
  media_id VARCHAR(255) NOT NULL UNIQUE,
  original_filename VARCHAR(255) NOT NULL,
  file_size INT NOT NULL,
  mime_type VARCHAR(50) NOT NULL,
  width INT,
  height INT,
  description TEXT,
  created_at BIGINT NOT NULL,
  updated_at BIGINT NOT NULL
);
```
