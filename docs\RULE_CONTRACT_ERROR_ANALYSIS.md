# Phân Tích Lỗi Rule Contract Registration

## 🔍 Tóm Tắt Lỗi

**Lỗi**: `column "user_signature_data" of relation "rule_contract" does not exist`

**API Endpoint**: `POST /v1/user/rule-contracts/register`

**Request ID**: `12309edc-056f-416f-92c3-129e25541c80`

## 📋 Chi Tiết Lỗi

### Stack Trace
```
QueryFailedError: column "user_signature_data" of relation "rule_contract" does not exist
    at PostgresQueryRunner.query
    at InsertQueryBuilder.execute
    at SubjectExecutor.executeInsertOperations
    at RuleContractActionsService.saveContract
    at RuleContractStateService.createContract
    at RuleContractUserController.registerTypeRuleContract
```

### Nguyên Nhân Gốc
1. **Migration chưa được chạy**: Migration `1734534000000-AddSignatureFieldsToRuleContract.ts` đã được tạo nhưng chưa được thực thi trên database
2. **Vấn đề quyền truy cập**: Database user hiện tại không có quyền `CREATE TABLE` trong schema `public`
3. **Mismatch giữa Entity và Database Schema**: Entity `RuleContract` đã được cập nhật với các trường mới nhưng database chưa có các cột tương ứng

## 🛠️ Giải Pháp

### Giải Pháp 1: Chạy Migration Thủ Công (Khuyến Nghị)

#### Windows (PowerShell):
```powershell
# Thiết lập biến môi trường
$env:DB_HOST = "your_host"
$env:DB_DATABASE = "your_database"
$env:DB_USERNAME = "your_username"
$env:DB_PASSWORD = "your_password"

# Chạy migration
.\scripts\run-signature-fields-migration.ps1
```

#### Linux/Mac (Bash):
```bash
# Thiết lập biến môi trường
export DB_HOST="your_host"
export DB_DATABASE="your_database"
export DB_USERNAME="your_username"
export DB_PASSWORD="your_password"

# Cấp quyền thực thi
chmod +x scripts/run-signature-fields-migration.sh

# Chạy migration
./scripts/run-signature-fields-migration.sh
```

#### SQL Trực Tiếp:
```bash
psql -h $DB_HOST -d $DB_DATABASE -U $DB_USERNAME -f scripts/run-signature-fields-migration.sql
```

### Giải Pháp 2: Cấp Quyền Database User

```sql
-- Cấp quyền CREATE cho user hiện tại
GRANT CREATE ON SCHEMA public TO your_username;
GRANT ALL PRIVILEGES ON SCHEMA public TO your_username;

-- Sau đó chạy TypeORM migration
npm run migration:run
```

### Giải Pháp 3: Sử dụng Database Admin User

```bash
# Chạy migration với user có quyền cao hơn
DB_USERNAME=postgres npm run migration:run
```

## 📊 Các Cột Sẽ Được Thêm

| Tên Cột | Kiểu Dữ Liệu | Nullable | Mô Tả |
|----------|--------------|----------|-------|
| `user_signature_data` | `text` | Yes | Dữ liệu chữ ký người dùng (base64 cho cá nhân, S3 key cho doanh nghiệp) |
| `signature_type` | `varchar(20)` | Yes | Loại chữ ký ('base64' hoặc 's3_key') |
| `signer_name` | `varchar(100)` | Yes | Tên người ký (cho doanh nghiệp) |
| `signer_position` | `varchar(255)` | Yes | Chức vụ người ký (cho doanh nghiệp) |

## 🔧 Kiểm Tra Sau Migration

### 1. Kiểm tra cột đã được tạo:
```sql
SELECT 
    column_name, 
    data_type, 
    character_maximum_length,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'rule_contract' 
AND column_name IN ('user_signature_data', 'signature_type', 'signer_name', 'signer_position')
ORDER BY column_name;
```

### 2. Test API endpoint:
```bash
curl -X POST http://localhost:3000/v1/user/rule-contracts/register \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{"type": "INDIVIDUAL"}'
```

### 3. Kiểm tra logs:
```bash
# Kiểm tra application logs
tail -f logs/application.log

# Kiểm tra database logs
tail -f /var/log/postgresql/postgresql-*.log
```

## 🚨 Lưu Ý Quan Trọng

1. **Backup Database**: Luôn tạo backup trước khi chạy migration
2. **Test Environment**: Chạy migration trên test environment trước
3. **Downtime**: Migration này không yêu cầu downtime vì chỉ thêm cột nullable
4. **Rollback Plan**: Có sẵn script rollback nếu cần thiết

## 📝 Rollback (Nếu Cần)

```sql
-- Rollback script
ALTER TABLE rule_contract DROP COLUMN IF EXISTS user_signature_data;
ALTER TABLE rule_contract DROP COLUMN IF EXISTS signature_type;
ALTER TABLE rule_contract DROP COLUMN IF EXISTS signer_name;
ALTER TABLE rule_contract DROP COLUMN IF EXISTS signer_position;
```

## 🎯 Kết Luận

Lỗi này xảy ra do migration chưa được chạy. Sau khi thực hiện migration thành công:

1. ✅ API `/v1/user/rule-contracts/register` sẽ hoạt động bình thường
2. ✅ Các tính năng chữ ký hợp đồng sẽ được kích hoạt
3. ✅ Hệ thống sẽ hỗ trợ cả chữ ký cá nhân (base64) và doanh nghiệp (S3)

**Thời gian ước tính**: 5-10 phút để hoàn thành migration và test.
