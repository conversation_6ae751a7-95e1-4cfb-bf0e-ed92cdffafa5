import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { AppException } from '@/common';
import { ApiResponseDto } from '@/common/response';
import { WORKFLOW_ERROR_CODES } from '../constants/workflow-error-codes';

/**
 * Global exception filter for workflow module
 * Handles workflow-specific errors and formats them consistently
 */
@Catch()
export class WorkflowExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(WorkflowExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status: number;
    let code: number;
    let message: string;
    let detail: any = null;

    // Handle different types of exceptions
    if (exception instanceof AppException) {
      // Custom application exceptions
      status = exception.getStatus();
      const errorCode = exception.getErrorCode();
      code = errorCode ? errorCode.code : 15000;
      message = exception.message;
      detail = exception.getAdditionalData();
    } else if (exception instanceof HttpException) {
      // Standard HTTP exceptions
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
        const responseObj = exceptionResponse as any;
        code = this.mapHttpStatusToErrorCode(status);
        message = responseObj.message || exception.message;
        detail = this.formatValidationErrors(responseObj);
      } else {
        code = this.mapHttpStatusToErrorCode(status);
        message = exception.message;
      }
    } else if (exception instanceof Error) {
      // Generic errors
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      code = 15000; // Default workflow internal error code
      message = 'Internal server error';
      detail = {
        originalError: exception.message,
        stack: process.env.NODE_ENV === 'development' ? exception.stack : undefined,
      };
    } else {
      // Unknown exceptions
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      code = 15000; // Default workflow internal error code
      message = 'Unknown error occurred';
      detail = {
        exception: String(exception),
      };
    }

    // Log error for monitoring
    this.logError(exception, request, status, code);

    // Format response manually since ApiResponseDto doesn't have error method
    const errorResponse = {
      code,
      message,
      result: null,
      detail,
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    response.status(status).json(errorResponse);
  }

  /**
   * Map HTTP status codes to workflow error codes
   */
  private mapHttpStatusToErrorCode(status: number): number {
    switch (status) {
      case HttpStatus.BAD_REQUEST:
        return 11001; // Validation failed
      case HttpStatus.UNAUTHORIZED:
        return 10001; // Unauthorized
      case HttpStatus.FORBIDDEN:
        return 10003; // Access denied
      case HttpStatus.NOT_FOUND:
        return 12001; // Resource not found
      case HttpStatus.CONFLICT:
        return 13001; // Conflict
      case HttpStatus.UNPROCESSABLE_ENTITY:
        return 11003; // Invalid data format
      case HttpStatus.TOO_MANY_REQUESTS:
        return 14001; // Rate limit exceeded
      case HttpStatus.INTERNAL_SERVER_ERROR:
        return 15000; // Internal server error
      case HttpStatus.SERVICE_UNAVAILABLE:
        return 15001; // Service unavailable
      case HttpStatus.GATEWAY_TIMEOUT:
        return 15002; // Gateway timeout
      default:
        return 15000; // Default to internal server error
    }
  }

  /**
   * Format validation errors from class-validator
   */
  private formatValidationErrors(responseObj: any): any {
    if (Array.isArray(responseObj.message)) {
      // class-validator errors
      return {
        errors: responseObj.message.map((error: any) => {
          if (typeof error === 'string') {
            return { message: error };
          }
          
          return {
            field: error.property,
            value: error.value,
            constraints: error.constraints,
            children: error.children?.length > 0 ? error.children : undefined,
          };
        }),
        errorType: 'validation',
      };
    }

    if (responseObj.message && typeof responseObj.message === 'string') {
      return {
        message: responseObj.message,
        errorType: 'http',
      };
    }

    return responseObj;
  }

  /**
   * Log error with appropriate level based on status
   */
  private logError(
    exception: unknown,
    request: Request,
    status: number,
    code: number,
  ): void {
    const errorContext = {
      method: request.method,
      url: request.url,
      userAgent: request.get('User-Agent'),
      ip: request.ip,
      userId: (request as any).user?.id,
      status,
      code,
    };

    if (status >= 500) {
      // Server errors - log as error
      this.logger.error(
        `Server Error: ${exception instanceof Error ? exception.message : String(exception)}`,
        exception instanceof Error ? exception.stack : undefined,
        errorContext,
      );
    } else if (status >= 400) {
      // Client errors - log as warning
      this.logger.warn(
        `Client Error: ${exception instanceof Error ? exception.message : String(exception)}`,
        errorContext,
      );
    } else {
      // Other errors - log as debug
      this.logger.debug(
        `Error: ${exception instanceof Error ? exception.message : String(exception)}`,
        errorContext,
      );
    }
  }
}

/**
 * Workflow-specific error codes mapping
 */
export const WORKFLOW_HTTP_ERROR_MAPPING = {
  // Workflow errors
  WORKFLOW_NOT_FOUND: HttpStatus.NOT_FOUND,
  WORKFLOW_ACCESS_DENIED: HttpStatus.FORBIDDEN,
  WORKFLOW_VALIDATION_FAILED: HttpStatus.BAD_REQUEST,
  WORKFLOW_EXECUTION_FAILED: HttpStatus.UNPROCESSABLE_ENTITY,
  
  // Node errors
  NODE_NOT_FOUND: HttpStatus.NOT_FOUND,
  NODE_EXECUTION_FAILED: HttpStatus.UNPROCESSABLE_ENTITY,
  NODE_VALIDATION_FAILED: HttpStatus.BAD_REQUEST,
  
  // Execution errors
  EXECUTION_NOT_FOUND: HttpStatus.NOT_FOUND,
  EXECUTION_ACCESS_DENIED: HttpStatus.FORBIDDEN,
  EXECUTION_INVALID_STATUS: HttpStatus.CONFLICT,
  EXECUTION_TIMEOUT: HttpStatus.REQUEST_TIMEOUT,
  
  // SSE errors
  SSE_CONNECTION_FAILED: HttpStatus.BAD_REQUEST,
  SSE_AUTH_FAILED: HttpStatus.UNAUTHORIZED,
  SSE_TIMEOUT: HttpStatus.REQUEST_TIMEOUT,
  
  // Webhook errors
  WEBHOOK_VALIDATION_FAILED: HttpStatus.BAD_REQUEST,
  WEBHOOK_PROCESSING_FAILED: HttpStatus.UNPROCESSABLE_ENTITY,
  WEBHOOK_AUTH_FAILED: HttpStatus.UNAUTHORIZED,
  
  // Generic errors
  VALIDATION_FAILED: HttpStatus.BAD_REQUEST,
  ACCESS_DENIED: HttpStatus.FORBIDDEN,
  RESOURCE_NOT_FOUND: HttpStatus.NOT_FOUND,
  INTERNAL_ERROR: HttpStatus.INTERNAL_SERVER_ERROR,
} as const;

/**
 * Helper function to create workflow-specific HTTP exceptions
 */
export function createWorkflowHttpException(
  errorCode: keyof typeof WORKFLOW_HTTP_ERROR_MAPPING,
  message: string,
  detail?: any,
): HttpException {
  const status = WORKFLOW_HTTP_ERROR_MAPPING[errorCode] || HttpStatus.INTERNAL_SERVER_ERROR;
  
  return new HttpException(
    {
      code: WORKFLOW_ERROR_CODES[errorCode]?.code || 15000,
      message,
      detail,
    },
    status,
  );
}
