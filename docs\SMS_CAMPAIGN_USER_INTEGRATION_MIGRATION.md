# SMS Campaign User Integration Migration

## Tổng quan

Tài liệu này mô tả việc migration SMS Campaign User từ sử dụng `SmsServerConfiguration` sang sử dụng `Integration` entity.

## Lý do Migration

1. **Tính nhất quán**: Tất cả các tích hợp khác đã sử dụng bảng `integration`
2. **B<PERSON><PERSON> mật**: Integration entity hỗ trợ mã hóa dữ liệu nhạy cảm
3. **Quản lý tập trung**: Tất cả tích hợp được quản lý ở một nơi
4. **Khả năng mở rộng**: Dễ dàng thêm các nhà cung cấp SMS mới

## Thay đổi chính

### 1. DTO Changes

#### CreateSmsCampaignDto
```typescript
// Trước
smsServerId: number;

// Sau  
smsIntegrationId: string;
```

#### CreateSmsCampaignWithTemplateDto
```typescript
// Trước
smsServerId: number;

// Sau
smsIntegrationId: string;
```

#### SmsMarketingJobDto
```typescript
// Trước
smsServerId: number;

// Sau
smsIntegrationId: string;
```

### 2. Entity Changes

#### SmsCampaignUser
```typescript
// Trước
@Column({ name: 'sms_server_id', type: 'integer' })
smsServerId: number;

// Sau
@Column({ name: 'sms_integration_id', type: 'uuid' })
smsIntegrationId: string;

// Thêm mới
@Column({ name: 'external_campaign_code', type: 'varchar', length: 255, nullable: true })
externalCampaignCode: string | null;
```

### 3. Service Changes

#### SmsCampaignService
- Import `IntegrationRepository` và `KeyPairEncryptionService`
- Thay thế `SmsServerConfigurationRepository` bằng `IntegrationRepository`
- Thêm method `getSmsConfigFromIntegration()` để giải mã cấu hình
- Cập nhật tất cả references từ `smsServerId` sang `smsIntegrationId`

### 4. Module Changes

#### MarketingUserModule
- Thêm import `ServicesModule` để có `KeyPairEncryptionService`
- `IntegrationRepository` có sẵn thông qua `IntegrationUserModule`

## Database Migration

### Các bước thực hiện:

1. **Thêm cột mới**:
   - `sms_integration_id` (UUID)
   - `external_campaign_code` (VARCHAR(255))

2. **Migration dữ liệu**:
   - Tìm Integration tương ứng cho mỗi `sms_server_id`
   - Cập nhật `sms_integration_id` với Integration ID

3. **Tối ưu hóa**:
   - Thêm indexes cho performance
   - Thêm comments cho documentation

4. **Cleanup** (sau khi verify):
   - Set `sms_integration_id` NOT NULL
   - Thêm foreign key constraint
   - Xóa cột `sms_server_id` cũ

### Chạy Migration

```bash
# Chạy migration
./scripts/run-sms-campaign-user-migration.sh

# Verify kết quả
psql -h $DB_HOST -d $DB_NAME -U $DB_USER -c "
SELECT 
    COUNT(*) as total_campaigns,
    COUNT(CASE WHEN sms_integration_id IS NOT NULL THEN 1 END) as campaigns_with_integration,
    COUNT(CASE WHEN sms_server_id IS NOT NULL THEN 1 END) as campaigns_with_old_server
FROM sms_campaign_user;
"
```

## API Changes

### Request Example

```json
// Trước
{
  "name": "Chiến dịch SMS khuyến mãi Black Friday",
  "campaignType": "ADS",
  "description": "Chiến dịch SMS marketing cho sự kiện Black Friday",
  "content": "Xin chào {{customerName}}! Khuyến mãi Black Friday - Giảm giá 50% tất cả sản phẩm. Mã: BF2024",
  "smsServerId": 1,
  "segmentId": 5
}

// Sau
{
  "name": "Chiến dịch SMS khuyến mãi Black Friday", 
  "campaignType": "ADS",
  "description": "Chiến dịch SMS marketing cho sự kiện Black Friday",
  "content": "Xin chào {{customerName}}! Khuyến mãi Black Friday - Giảm giá 50% tất cả sản phẩm. Mã: BF2024",
  "smsIntegrationId": "550e8400-e29b-41d4-a716-446655440000",
  "segmentId": 5
}
```

## Testing

### Các test case cần verify:

1. **Tạo SMS Campaign mới** với `smsIntegrationId`
2. **Lấy danh sách campaigns** hoạt động bình thường
3. **Gửi SMS** thông qua Integration hoạt động
4. **Giải mã cấu hình** từ Integration thành công
5. **Fallback mechanism** khi giải mã thất bại

### Rollback Plan

Nếu có vấn đề, có thể rollback bằng cách:

```sql
-- Xóa cột mới
ALTER TABLE sms_campaign_user DROP COLUMN IF EXISTS sms_integration_id;
ALTER TABLE sms_campaign_user DROP COLUMN IF EXISTS external_campaign_code;

-- Restore từ backup nếu cần
-- psql -h $DB_HOST -d $DB_NAME -U $DB_USER < backup_file.sql
```

## Lưu ý quan trọng

1. **Backup**: Luôn backup database trước khi chạy migration
2. **Testing**: Test kỹ trước khi deploy production
3. **Monitoring**: Monitor logs sau khi deploy để phát hiện issues
4. **Gradual rollout**: Có thể deploy từng bước để giảm risk

## Kết luận

Migration này giúp:
- Tăng tính nhất quán trong hệ thống
- Cải thiện bảo mật với encryption
- Dễ dàng quản lý và mở rộng
- Chuẩn bị cho các tích hợp SMS mới trong tương lai
