import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  IsString, 
  IsOptional, 
  IsBoolean, 
  IsObject, 
  IsEnum, 
  IsNumber, 
  IsUrl,
  Length,
  Min,
  Max,
  ValidateNested
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';
import { McpConfigDto } from './user-mcp-api.dto';

/**
 * DTO cho việc tạo User MCP mới
 */
export class CreateUserMcpDto {
  @ApiProperty({ description: 'Tên MCP server', example: 'my-mcp-server' })
  @IsString()
  @Length(1, 255)
  nameServer: string;

  @ApiPropertyOptional({ description: 'Mô tả MCP server' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Cấu hình MCP server' })
  @IsObject()
  @ValidateNested()
  @Type(() => McpConfigDto)
  config: McpConfigDto;
}

/**
 * DTO cho việc cập nhật User MCP
 */
export class UpdateUserMcpDto {
  @ApiPropertyOptional({ description: 'Tên MCP server' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  nameServer?: string;

  @ApiPropertyOptional({ description: 'Mô tả MCP server' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Cấu hình MCP server' })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => McpConfigDto)
  config?: McpConfigDto;
}

/**
 * DTO cho query User MCP
 */
export class QueryUserMcpDto extends QueryDto {
  @ApiPropertyOptional({ description: 'Lọc theo trạng thái enabled' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  enabled?: boolean;

  @ApiPropertyOptional({ description: 'Lọc theo connection type' })
  @IsOptional()
  @IsEnum(['http', 'websocket', 'stdio', 'sse'])
  connectionType?: 'http' | 'websocket' | 'stdio' | 'sse';
}

/**
 * Response DTO cho User MCP
 */
export class UserMcpResponseDto {
  @ApiProperty({ description: 'ID của User MCP' })
  id: string;

  @ApiProperty({ description: 'Tên MCP server' })
  nameServer: string;

  @ApiPropertyOptional({ description: 'Mô tả MCP server' })
  description?: string;

  @ApiProperty({ description: 'Cấu hình MCP server' })
  config: McpConfigDto;

  @ApiProperty({ description: 'ID của user' })
  userId: number;

  @ApiProperty({ description: 'Thời điểm tạo' })
  createdAt: number;

  @ApiProperty({ description: 'Thời điểm cập nhật' })
  updatedAt: number;

  @ApiPropertyOptional({ description: 'Thời điểm xóa' })
  deletedAt?: number;
}

/**
 * DTO cho việc test connection MCP
 */
export class TestMcpConnectionDto {
  @ApiProperty({ description: 'Cấu hình MCP server để test' })
  @IsObject()
  @ValidateNested()
  @Type(() => McpConfigDto)
  config: McpConfigDto;

  @ApiPropertyOptional({ description: 'Timeout cho test (ms)', default: 10000 })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(60000)
  timeout?: number;
}