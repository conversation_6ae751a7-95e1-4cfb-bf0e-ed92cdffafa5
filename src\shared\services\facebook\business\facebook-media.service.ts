import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@config/config.service';
import { ConfigType } from '@/config';
import { FacebookConfig } from '@config/interfaces';
import { firstValueFrom } from 'rxjs';
import { AppException, ErrorCode } from '@common/exceptions';
import * as FormData from 'form-data';
import {
  AdImage,
  AdVideo,
  UploadAdImageRequest,
  UploadAdImageResponse,
  UploadAdVideoRequest,
  UploadAdVideoResponse,
  GetAdImagesResponse,
  GetAdVideosResponse,
  VideoUploadStatus,
  CreateVideoThumbnailRequest,
  CreateVideoThumbnailResponse,
  VideoStats,
  VideoUploadPhase,
} from '../interfaces/facebook-media.interface';

/**
 * Service để quản lý Media (Images & Videos) trong Facebook Business API
 */
@Injectable()
export class FacebookMediaService {
  private readonly logger = new Logger(FacebookMediaService.name);
  private readonly facebookConfig: FacebookConfig;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.facebookConfig = this.configService.getConfig<FacebookConfig>(
      ConfigType.Facebook,
    );
  }

  /**
   * Upload ảnh quảng cáo lên Facebook
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param uploadRequest Dữ liệu upload
   * @returns Thông tin ảnh đã upload
   */
  async uploadAdImage(
    adAccountId: string,
    accessToken: string,
    uploadRequest: UploadAdImageRequest,
  ): Promise<UploadAdImageResponse> {
    try {
      this.logger.log(`Upload ảnh quảng cáo cho Ad Account ${adAccountId}`);

      const formData = new FormData();
      formData.append('access_token', accessToken);

      if (uploadRequest.bytes) {
        formData.append('bytes', uploadRequest.bytes, uploadRequest.filename);
      } else if (uploadRequest.copy_from) {
        formData.append('copy_from', JSON.stringify(uploadRequest.copy_from));
      }

      const response = await firstValueFrom(
        this.httpService.post<UploadAdImageResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/act_${adAccountId}/adimages`,
          formData,
          {
            headers: {
              ...formData.getHeaders(),
            },
          },
        ),
      );

      this.logger.log(
        `Đã upload ảnh quảng cáo cho Ad Account ${adAccountId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi upload ảnh quảng cáo: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi upload ảnh quảng cáo',
        { adAccountId, filename: uploadRequest.filename },
      );
    }
  }

  /**
   * Lấy danh sách ảnh quảng cáo
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param fields Các trường cần lấy
   * @param limit Số lượng kết quả
   * @returns Danh sách ảnh quảng cáo
   */
  async getAdImages(
    adAccountId: string,
    accessToken: string,
    fields?: string,
    limit?: number,
  ): Promise<GetAdImagesResponse> {
    try {
      this.logger.log(`Lấy danh sách ảnh quảng cáo cho Ad Account ${adAccountId}`);

      const params: any = {
        access_token: accessToken,
      };

      if (fields) {
        params.fields = fields;
      } else {
        params.fields = 'hash,url,url_128,width,height,original_width,original_height,permalink_url,name,status,account_id,created_time,updated_time,creatives,filename,is_associated_creatives_in_adgroups,zipbytes';
      }

      if (limit) {
        params.limit = limit;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetAdImagesResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/act_${adAccountId}/adimages`,
          { params },
        ),
      );

      this.logger.log(
        `Đã lấy ${response.data.data.length} ảnh quảng cáo cho Ad Account ${adAccountId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách ảnh quảng cáo: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách ảnh quảng cáo',
        { adAccountId },
      );
    }
  }

  /**
   * Xóa ảnh quảng cáo
   * @param adAccountId ID của Ad Account
   * @param imageHash Hash của ảnh
   * @param accessToken Access token
   * @returns Kết quả xóa
   */
  async deleteAdImage(
    adAccountId: string,
    imageHash: string,
    accessToken: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Xóa ảnh quảng cáo ${imageHash} từ Ad Account ${adAccountId}`);

      const response = await firstValueFrom(
        this.httpService.delete(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/act_${adAccountId}/adimages`,
          {
            params: {
              access_token: accessToken,
              hash: imageHash,
            },
          },
        ),
      );

      this.logger.log(`Đã xóa ảnh quảng cáo ${imageHash}`);

      return { success: response.data.success || true };
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa ảnh quảng cáo ${imageHash}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa ảnh quảng cáo',
        { adAccountId, imageHash },
      );
    }
  }

  /**
   * Upload video quảng cáo lên Facebook
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param uploadRequest Dữ liệu upload
   * @returns Thông tin video đã upload
   */
  async uploadAdVideo(
    adAccountId: string,
    accessToken: string,
    uploadRequest: UploadAdVideoRequest,
  ): Promise<UploadAdVideoResponse> {
    try {
      this.logger.log(`Upload video quảng cáo cho Ad Account ${adAccountId}`);

      const formData = new FormData();
      formData.append('access_token', accessToken);

      // Thêm các trường cơ bản
      if (uploadRequest.title) {
        formData.append('title', uploadRequest.title);
      }
      if (uploadRequest.description) {
        formData.append('description', uploadRequest.description);
      }
      if (uploadRequest.file_url) {
        formData.append('file_url', uploadRequest.file_url);
      }
      if (uploadRequest.file_size) {
        formData.append('file_size', uploadRequest.file_size.toString());
      }
      if (uploadRequest.upload_phase) {
        formData.append('upload_phase', uploadRequest.upload_phase);
      }
      if (uploadRequest.upload_session_id) {
        formData.append('upload_session_id', uploadRequest.upload_session_id);
      }
      if (uploadRequest.video_file_chunk) {
        formData.append('video_file_chunk', uploadRequest.video_file_chunk);
      }
      if (uploadRequest.start_offset !== undefined) {
        formData.append('start_offset', uploadRequest.start_offset.toString());
      }
      if (uploadRequest.end_offset !== undefined) {
        formData.append('end_offset', uploadRequest.end_offset.toString());
      }

      // Thêm các trường tùy chọn
      if (uploadRequest.content_category) {
        formData.append('content_category', uploadRequest.content_category);
      }
      if (uploadRequest.embeddable !== undefined) {
        formData.append('embeddable', uploadRequest.embeddable.toString());
      }
      if (uploadRequest.published !== undefined) {
        formData.append('published', uploadRequest.published.toString());
      }
      if (uploadRequest.scheduled_publish_time) {
        formData.append('scheduled_publish_time', uploadRequest.scheduled_publish_time);
      }
      if (uploadRequest.secret !== undefined) {
        formData.append('secret', uploadRequest.secret.toString());
      }
      if (uploadRequest.spherical !== undefined) {
        formData.append('spherical', uploadRequest.spherical.toString());
      }
      if (uploadRequest.slideshow_spec) {
        formData.append('slideshow_spec', JSON.stringify(uploadRequest.slideshow_spec));
      }
      if (uploadRequest.thumb) {
        formData.append('thumb', uploadRequest.thumb);
      }
      if (uploadRequest.transcode_setting) {
        formData.append('transcode_setting', JSON.stringify(uploadRequest.transcode_setting));
      }

      const response = await firstValueFrom(
        this.httpService.post<UploadAdVideoResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/act_${adAccountId}/advideos`,
          formData,
          {
            headers: {
              ...formData.getHeaders(),
            },
          },
        ),
      );

      this.logger.log(
        `Đã upload video quảng cáo cho Ad Account ${adAccountId}. Video ID: ${response.data.id}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi upload video quảng cáo: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi upload video quảng cáo',
        { adAccountId, title: uploadRequest.title },
      );
    }
  }

  /**
   * Lấy danh sách video quảng cáo
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param fields Các trường cần lấy
   * @param limit Số lượng kết quả
   * @returns Danh sách video quảng cáo
   */
  async getAdVideos(
    adAccountId: string,
    accessToken: string,
    fields?: string,
    limit?: number,
  ): Promise<GetAdVideosResponse> {
    try {
      this.logger.log(`Lấy danh sách video quảng cáo cho Ad Account ${adAccountId}`);

      const params: any = {
        access_token: accessToken,
      };

      if (fields) {
        params.fields = fields;
      } else {
        params.fields = 'id,title,description,length,created_time,updated_time,status,source,picture,permalink_url,thumbnails,format,embeddable,upload_phase,upload_session_id,file_url,file_size,content_category,content_tags,custom_labels,is_crosspost_video,is_crossposting_eligible,is_episode,is_instagram_eligible,is_reference_only,post_id,published,scheduled_publish_time,secret,spherical,universal_video_id';
      }

      if (limit) {
        params.limit = limit;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetAdVideosResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/act_${adAccountId}/advideos`,
          { params },
        ),
      );

      this.logger.log(
        `Đã lấy ${response.data.data.length} video quảng cáo cho Ad Account ${adAccountId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách video quảng cáo: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách video quảng cáo',
        { adAccountId },
      );
    }
  }

  /**
   * Kiểm tra trạng thái upload video
   * @param videoId ID của video
   * @param accessToken Access token
   * @returns Trạng thái upload video
   */
  async getVideoUploadStatus(
    videoId: string,
    accessToken: string,
  ): Promise<VideoUploadStatus> {
    try {
      this.logger.log(`Kiểm tra trạng thái upload video ${videoId}`);

      const params = {
        access_token: accessToken,
        fields: 'status,upload_phase,upload_session_id,upload_progress,processing_progress',
      };

      const response = await firstValueFrom(
        this.httpService.get<AdVideo>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${videoId}`,
          { params },
        ),
      );

      const uploadStatus: VideoUploadStatus = {
        video_id: response.data.id,
        phase: response.data.upload_phase || VideoUploadPhase.FINISH,
        upload_session_id: response.data.upload_session_id,
      };

      this.logger.log(`Trạng thái upload video ${videoId}: ${uploadStatus.phase}`);

      return uploadStatus;
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra trạng thái upload video ${videoId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi kiểm tra trạng thái upload video',
        { videoId },
      );
    }
  }

  /**
   * Tạo thumbnail cho video
   * @param videoId ID của video
   * @param accessToken Access token
   * @param thumbnailRequest Dữ liệu thumbnail
   * @returns Thông tin thumbnail đã tạo
   */
  async createVideoThumbnail(
    videoId: string,
    accessToken: string,
    thumbnailRequest: CreateVideoThumbnailRequest,
  ): Promise<CreateVideoThumbnailResponse> {
    try {
      this.logger.log(`Tạo thumbnail cho video ${videoId}`);

      const requestData = {
        ...thumbnailRequest,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post<CreateVideoThumbnailResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${videoId}/thumbnails`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(`Đã tạo thumbnail cho video ${videoId}`);

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo thumbnail cho video ${videoId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo thumbnail cho video',
        { videoId, thumbnailRequest },
      );
    }
  }

  /**
   * Lấy thống kê video
   * @param videoId ID của video
   * @param accessToken Access token
   * @returns Thống kê video
   */
  async getVideoStats(
    videoId: string,
    accessToken: string,
  ): Promise<VideoStats> {
    try {
      this.logger.log(`Lấy thống kê video ${videoId}`);

      const params = {
        access_token: accessToken,
        metric: 'total_video_views,total_video_views_unique,total_video_10s_views,total_video_15s_views,total_video_30s_views,total_video_60s_excludes_shorter_views,total_video_avg_time_watched,total_video_complete_views,total_video_complete_views_unique,total_video_impressions,total_video_impressions_unique,total_video_view_time,total_video_views_autoplayed,total_video_views_clicked_to_play,total_video_views_sound_on',
      };

      const response = await firstValueFrom(
        this.httpService.get<{ data: any[] }>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${videoId}/video_insights`,
          { params },
        ),
      );

      // Chuyển đổi dữ liệu insights thành VideoStats
      const stats: VideoStats = {
        video_id: videoId,
      };

      response.data.data.forEach((insight: any) => {
        const metricName = insight.name;
        const value = insight.values?.[0]?.value || 0;
        (stats as any)[metricName] = value;
      });

      this.logger.log(`Đã lấy thống kê video ${videoId}`);

      return stats;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thống kê video ${videoId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thống kê video',
        { videoId },
      );
    }
  }
}
