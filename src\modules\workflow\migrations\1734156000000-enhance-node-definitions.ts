import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration để enhance node_definitions table cho 192 node types
 * Thêm các fields mới cho documentation, examples, tags, deprecation, UI styling
 */
export class EnhanceNodeDefinitions1734156000000 implements MigrationInterface {
  name = 'EnhanceNodeDefinitions1734156000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new columns to node_definitions table
    await queryRunner.query(`
      ALTER TABLE "node_definitions" 
      ADD COLUMN "documentation" text,
      ADD COLUMN "examples" jsonb,
      ADD COLUMN "tags" text,
      ADD COLUMN "is_deprecated" boolean NOT NULL DEFAULT false,
      ADD COLUMN "deprecation_message" text,
      ADD COLUMN "icon_url" varchar(500),
      ADD COLUMN "color_scheme" varchar(50),
      ADD COLUMN "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      ADD COLUMN "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
    `);

    // Update CHECK constraint to include new categories
    await queryRunner.query(`
      ALTER TABLE "node_definitions" 
      DROP CONSTRAINT IF EXISTS "CHK_node_definitions_category"
    `);

    await queryRunner.query(`
      ALTER TABLE "node_definitions" 
      ADD CONSTRAINT "CHK_node_definitions_category" 
      CHECK (category IN (
        'system', 
        'google_sheets', 'google_docs', 'google_gmail', 'google_drive', 
        'google_calendar', 'google_ads', 'google_analytics',
        'facebook_pages', 'facebook_ads', 'facebook_business', 
        'facebook_messenger', 'facebook_insights', 'instagram',
        'zalo_oa', 'zalo_zns', 'zalo_social', 'zalo_agent', 'zalo_webhook',
        'ai_openai', 'ai_anthropic', 'ai_google', 'ai_deepseek', 'ai_meta'
      ))
    `);

    // Create indexes for performance optimization
    await queryRunner.query(`
      CREATE INDEX "IDX_node_definitions_category" ON "node_definitions" ("category")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_node_definitions_tags" ON "node_definitions" USING GIN ("tags")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_node_definitions_is_deprecated" ON "node_definitions" ("is_deprecated")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_node_definitions_created_at" ON "node_definitions" ("created_at")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_node_definitions_updated_at" ON "node_definitions" ("updated_at")
    `);

    // Create full-text search index for documentation and description
    await queryRunner.query(`
      CREATE INDEX "IDX_node_definitions_fulltext" ON "node_definitions" 
      USING GIN (to_tsvector('english', COALESCE(name, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(documentation, '')))
    `);

    // Add trigger to automatically update updated_at timestamp
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_node_definitions_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);

    await queryRunner.query(`
      CREATE TRIGGER trigger_update_node_definitions_updated_at
      BEFORE UPDATE ON "node_definitions"
      FOR EACH ROW
      EXECUTE FUNCTION update_node_definitions_updated_at()
    `);

    // Insert sample system nodes to demonstrate the enhanced structure
    await queryRunner.query(`
      INSERT INTO "node_definitions" (
        "type", "name", "description", "category", "input_schema", "output_schema", 
        "version", "documentation", "examples", "tags", "icon_url", "color_scheme"
      ) VALUES 
      (
        'system.webhook.trigger',
        'Webhook Trigger',
        'Triggers workflow execution when webhook is called',
        'system',
        '{"type": "object", "properties": {"url": {"type": "string"}, "method": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE"]}}}',
        '{"type": "object", "properties": {"data": {"type": "object"}, "headers": {"type": "object"}}}',
        '1.0.0',
        '# Webhook Trigger Node\n\nThis node triggers workflow execution when a webhook endpoint is called.\n\n## Usage\n\nConfigure the webhook URL and method to start your workflow.',
        '{"basic": {"url": "/webhook/my-workflow", "method": "POST"}}',
        '{"trigger", "webhook", "http"}',
        '/icons/webhook.svg',
        '#4CAF50'
      ),
      (
        'system.manual.trigger',
        'Manual Trigger',
        'Manually triggers workflow execution',
        'system',
        '{"type": "object", "properties": {"data": {"type": "object"}}}',
        '{"type": "object", "properties": {"data": {"type": "object"}}}',
        '1.0.0',
        '# Manual Trigger Node\n\nThis node allows manual triggering of workflow execution.\n\n## Usage\n\nClick the trigger button to start the workflow with optional input data.',
        '{"basic": {"data": {"message": "Hello World"}}}',
        '{"trigger", "manual"}',
        '/icons/manual.svg',
        '#2196F3'
      )
      ON CONFLICT (type) DO NOTHING
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop trigger and function
    await queryRunner.query(`DROP TRIGGER IF EXISTS trigger_update_node_definitions_updated_at ON "node_definitions"`);
    await queryRunner.query(`DROP FUNCTION IF EXISTS update_node_definitions_updated_at()`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definitions_fulltext"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definitions_updated_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definitions_created_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definitions_is_deprecated"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definitions_tags"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definitions_category"`);

    // Restore original CHECK constraint
    await queryRunner.query(`
      ALTER TABLE "node_definitions" 
      DROP CONSTRAINT IF EXISTS "CHK_node_definitions_category"
    `);

    await queryRunner.query(`
      ALTER TABLE "node_definitions" 
      ADD CONSTRAINT "CHK_node_definitions_category" 
      CHECK (category IN (
        'system', 'google_sheet', 'google_docs', 'google_gmail', 'google_ads', 
        'google_drive', 'google_calendar', 'facebook_page', 'facebook_ads', 
        'facebook_messenger', 'instagram', 'zalo_oa', 'zalo_zns', 'zalo'
      ))
    `);

    // Remove new columns
    await queryRunner.query(`
      ALTER TABLE "node_definitions" 
      DROP COLUMN "updated_at",
      DROP COLUMN "created_at",
      DROP COLUMN "color_scheme",
      DROP COLUMN "icon_url",
      DROP COLUMN "deprecation_message",
      DROP COLUMN "is_deprecated",
      DROP COLUMN "tags",
      DROP COLUMN "examples",
      DROP COLUMN "documentation"
    `);
  }
}
