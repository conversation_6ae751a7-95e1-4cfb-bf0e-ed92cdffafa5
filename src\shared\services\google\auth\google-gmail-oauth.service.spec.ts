import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { GoogleGmailOAuthService } from './google-gmail-oauth.service';
import { GoogleGmailApiService } from '../gmail/google-gmail-api.service';
import { AppException } from '@common/exceptions';
import { GOOGLE_ERROR_CODES } from '../exceptions/google.exception';

describe('GoogleGmailOAuthService', () => {
  let service: GoogleGmailOAuthService;
  let configService: jest.Mocked<ConfigService>;
  let gmailApiService: jest.Mocked<GoogleGmailApiService>;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn(),
    };

    const mockGmailApiService = {
      generateAuthUrl: jest.fn(),
      getTokensFromCode: jest.fn(),
      setCredentials: jest.fn(),
      getUserInfo: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GoogleGmailOAuthService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: GoogleGmailApiService,
          useValue: mockGmailApiService,
        },
      ],
    }).compile();

    service = module.get<GoogleGmailOAuthService>(GoogleGmailOAuthService);
    configService = module.get(ConfigService);
    gmailApiService = module.get(GoogleGmailApiService);
  });

  describe('generateAuthUrl', () => {
    it('should generate auth URL with gmail prefix in state', () => {
      const userId = 1;
      const scopes = ['https://www.googleapis.com/auth/gmail.send'];
      const action = 'connect';
      const metadata = { redirectUri: 'https://example.com/callback' };
      const mockAuthUrl = 'https://accounts.google.com/o/oauth2/v2/auth?...';

      gmailApiService.generateAuthUrl.mockReturnValue(mockAuthUrl);

      const result = service.generateAuthUrl(userId, scopes, action, metadata);

      expect(result.authUrl).toBe(mockAuthUrl);
      expect(result.state).toMatch(/^gmail_/);
      expect(gmailApiService.generateAuthUrl).toHaveBeenCalledWith(
        scopes,
        expect.stringMatching(/^gmail_/),
        metadata.redirectUri
      );
    });

    it('should generate state with correct structure', () => {
      const userId = 123;
      const action = 'connect';
      const metadata = { redirectUri: 'https://test.com' };

      gmailApiService.generateAuthUrl.mockReturnValue('mock-url');

      const result = service.generateAuthUrl(userId, [], action, metadata);

      // Decode state để kiểm tra cấu trúc
      const stateWithoutPrefix = result.state.substring(6); // Loại bỏ "gmail_"
      const decodedState = JSON.parse(Buffer.from(stateWithoutPrefix, 'base64').toString());

      expect(decodedState).toHaveProperty('userId', userId);
      expect(decodedState).toHaveProperty('action', action);
      expect(decodedState).toHaveProperty('metadata', metadata);
      expect(decodedState).toHaveProperty('timestamp');
      expect(typeof decodedState.timestamp).toBe('number');
    });
  });

  describe('handleOAuthCallback', () => {
    const mockTokens = {
      access_token: 'access-token',
      refresh_token: 'refresh-token',
      expiry_date: Date.now() + 3600000,
    };

    const mockUserInfo = {
      id: 'user-id',
      email: '<EMAIL>',
      name: 'Test User',
      picture: 'https://example.com/avatar.jpg',
    };

    beforeEach(() => {
      gmailApiService.getTokensFromCode.mockResolvedValue(mockTokens);
      gmailApiService.getUserInfo.mockResolvedValue(mockUserInfo);
    });

    it('should handle OAuth callback with valid gmail state', async () => {
      const userId = 123;
      const action = 'connect';
      const metadata = { redirectUri: 'https://test.com' };
      
      // Tạo state hợp lệ với tiền tố gmail
      const stateObj = {
        userId,
        timestamp: Date.now(),
        action,
        metadata,
      };
      const base64State = Buffer.from(JSON.stringify(stateObj)).toString('base64');
      const state = `gmail_${base64State}`;
      const code = 'auth-code';

      const result = await service.handleOAuthCallback(code, state);

      expect(result.tokens).toBe(mockTokens);
      expect(result.userInfo).toBe(mockUserInfo);
      expect(result.state).toEqual(stateObj);
      expect(gmailApiService.getTokensFromCode).toHaveBeenCalledWith(code);
      expect(gmailApiService.setCredentials).toHaveBeenCalledWith(mockTokens);
      expect(gmailApiService.getUserInfo).toHaveBeenCalled();
    });

    it('should throw error for state without gmail prefix', async () => {
      const stateObj = {
        userId: 123,
        timestamp: Date.now(),
        action: 'connect',
      };
      const invalidState = Buffer.from(JSON.stringify(stateObj)).toString('base64');
      const code = 'auth-code';

      await expect(service.handleOAuthCallback(code, invalidState))
        .rejects.toThrow(AppException);
    });

    it('should throw error for expired state', async () => {
      const stateObj = {
        userId: 123,
        timestamp: Date.now() - 15 * 60 * 1000, // 15 phút trước (quá hạn)
        action: 'connect',
      };
      const base64State = Buffer.from(JSON.stringify(stateObj)).toString('base64');
      const expiredState = `gmail_${base64State}`;
      const code = 'auth-code';

      await expect(service.handleOAuthCallback(code, expiredState))
        .rejects.toThrow(AppException);
    });

    it('should throw error for malformed state', async () => {
      const malformedState = 'gmail_invalid-base64';
      const code = 'auth-code';

      await expect(service.handleOAuthCallback(code, malformedState))
        .rejects.toThrow(AppException);
    });
  });
});
