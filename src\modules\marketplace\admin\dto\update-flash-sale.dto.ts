import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { 
  IsOptional, 
  IsNumber, 
  Min, 
  Max, 
  ValidateNested,
  IsObject,
  IsBoolean
} from 'class-validator';
import { MaxConfigurationDto } from './create-flash-sale.dto';

/**
 * DTO để cập nhật flash sale (Admin)
 */
export class UpdateFlashSaleDto {
  @ApiPropertyOptional({
    description: 'Phần trăm giảm giá (1-99%)',
    example: 25,
    minimum: 1,
    maximum: 99
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(99)
  discountPercentage?: number;

  @ApiPropertyOptional({
    description: 'Thời gian hiển thị sản phẩm flash sale cho user (giây, 1-60)',
    example: 30,
    minimum: 1,
    maximum: 60
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(60)
  displayTime?: number;

  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu flash sale (timestamp milliseconds)',
    example: 1641081600000
  })
  @IsOptional()
  @IsNumber()
  startTime?: number;

  @ApiPropertyOptional({
    description: 'Thời gian kết thúc flash sale (timestamp milliseconds)',
    example: 1641168000000
  })
  @IsOptional()
  @IsNumber()
  endTime?: number;

  @ApiPropertyOptional({
    description: 'Cấu hình giới hạn flash sale',
    type: MaxConfigurationDto
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => MaxConfigurationDto)
  maxConfiguration?: MaxConfigurationDto;

  @ApiPropertyOptional({
    description: 'Flash sale có đang hoạt động không',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;


}
