import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { WorkflowRepository } from '../../repositories/workflow.repository';
import { WorkflowNodeRepository } from '../../repositories/workflow-node.repository';
import { WorkflowEdgeRepository } from '../../repositories/workflow-edge.repository';
import { WorkflowValidationService, ValidationResult } from '../../services/workflow-validation.service';
import { UpdateWorkflowDefinitionDto, WorkflowNodeDto, WorkflowEdgeDto } from '../../dto/definition';
import {
  WorkflowDefinitionUpdateResponseDto,
  WorkflowNodeOperationResponseDto,
  WorkflowEdgeOperationResponseDto,
  WorkflowValidationResponseDto
} from '../../dto/definition/workflow-definition-response.dto';
import { WorkflowDefinitionMapper } from '../mappers/workflow-definition.mapper';
import { Workflow } from '../../entities/workflow.entity';

/**
 * Service để manage workflow definitions cho users
 * Following existing service patterns với repository pattern
 */
@Injectable()
export class UserWorkflowDefinitionService {
  private readonly logger = new Logger(UserWorkflowDefinitionService.name);

  constructor(
    private readonly workflowRepository: WorkflowRepository,
    private readonly workflowNodeRepository: WorkflowNodeRepository,
    private readonly workflowEdgeRepository: WorkflowEdgeRepository,
    private readonly validationService: WorkflowValidationService,
  ) {}

  /**
   * Update workflow definition với validation
   * @param workflowId - ID của workflow
   * @param updateDto - Definition update data
   * @param userId - ID của user thực hiện update
   * @returns Updated workflow response DTO với validation results
   */
  async updateWorkflowDefinition(
    workflowId: string,
    updateDto: UpdateWorkflowDefinitionDto,
    userId: number
  ): Promise<WorkflowDefinitionUpdateResponseDto> {
    this.logger.log(`Updating workflow definition: ${workflowId} by user: ${userId}`);

    // 1. Validate workflow exists và user có permission
    const workflow = await this.findWorkflowWithPermission(workflowId, userId);

    // 2. Validate definition structure
    const validationInput = {
      nodes: updateDto.nodes || [],
      edges: updateDto.edges || [],
      metadata: updateDto.metadata ? JSON.parse(JSON.stringify(updateDto.metadata)) : {}
    };
    const validation = await this.validationService.validateWorkflowDefinition(validationInput);
    
    if (!validation.isValid) {
      throw new BadRequestException({
        message: 'Workflow definition validation failed',
        errors: validation.errors,
        warnings: validation.warnings
      });
    }

    // 3. Update workflow definition
    const updatedDefinition = {
      nodes: updateDto.nodes || [],
      edges: updateDto.edges || [],
      metadata: {
        ...workflow.definition.metadata,
        ...updateDto.metadata,
        lastModified: {
          timestamp: Date.now(),
          userId,
          changes: 'Updated workflow definition'
        }
      }
    };

    // 4. Save to database using repository
    const updateResult = await this.workflowRepository.updateWorkflowDefinition(
      workflowId,
      updatedDefinition
    );

    if (!updateResult.affected) {
      throw new BadRequestException('Failed to update workflow definition');
    }

    // 5. Update normalized tables (nodes và edges)
    await this.updateNormalizedTables(workflowId, updateDto);

    // 6. Get updated workflow
    const savedWorkflow = await this.workflowRepository.findById(workflowId);
    if (!savedWorkflow) {
      throw new NotFoundException('Workflow not found after update');
    }

    this.logger.log(`Workflow definition updated successfully: ${workflowId}`);

    // Convert to DTO response
    return WorkflowDefinitionMapper.toDefinitionUpdateResponse(savedWorkflow, validation);
  }

  /**
   * Validate workflow definition without saving
   * @param workflowId - ID của workflow
   * @param definition - Definition to validate
   * @param userId - ID của user
   * @returns Validation result DTO
   */
  async validateWorkflowDefinition(
    workflowId: string,
    definition: Record<string, any>,
    userId: number
  ): Promise<WorkflowValidationResponseDto> {
    this.logger.log(`Validating workflow definition: ${workflowId} by user: ${userId}`);

    // Verify user has access to workflow
    await this.findWorkflowWithPermission(workflowId, userId);

    // Validate definition structure
    const validationInput = {
      nodes: definition.nodes || [],
      edges: definition.edges || [],
      metadata: definition.metadata || {}
    };
    const validation = await this.validationService.validateWorkflowDefinition(validationInput);

    this.logger.log(`Validation completed for workflow: ${workflowId}, valid: ${validation.isValid}`);

    // Get workflow for metadata
    const workflow = await this.workflowRepository.findById(workflowId);
    const metadata = workflow ? WorkflowDefinitionMapper.calculateWorkflowStats(workflow) : undefined;

    // Convert to DTO response
    return WorkflowDefinitionMapper.toValidationResponse(validation, metadata);
  }

  /**
   * Add node to workflow
   * @param workflowId - ID của workflow
   * @param nodeDto - Node data
   * @param userId - ID của user
   * @returns Updated workflow response DTO
   */
  async addNodeToWorkflow(
    workflowId: string,
    nodeDto: WorkflowNodeDto,
    userId: number
  ): Promise<WorkflowNodeOperationResponseDto> {
    this.logger.log(`Adding node to workflow: ${workflowId}, node: ${nodeDto.id} by user: ${userId}`);

    // 1. Validate workflow exists
    const workflow = await this.findWorkflowWithPermission(workflowId, userId);

    // 2. Validate node
    const nodeValidation = await this.validationService.validateNode(nodeDto);
    if (!nodeValidation.isValid) {
      throw new BadRequestException({
        message: 'Node validation failed',
        errors: nodeValidation.errors
      });
    }

    // 3. Check node ID uniqueness using repository
    const existingNode = await this.workflowNodeRepository.findByWorkflowAndNodeId(workflowId, nodeDto.id);
    if (existingNode) {
      throw new BadRequestException(`Node with ID '${nodeDto.id}' already exists`);
    }

    // 4. Add node to definition
    const existingNodes = workflow.definition.nodes || [];
    const updatedDefinition = {
      ...workflow.definition,
      nodes: [...existingNodes, nodeDto],
      metadata: {
        ...workflow.definition.metadata,
        lastModified: {
          timestamp: Date.now(),
          userId,
          changes: `Added node: ${nodeDto.id}`
        }
      }
    };

    // 5. Save workflow using repository
    await this.workflowRepository.updateWorkflowDefinition(workflowId, updatedDefinition);

    // 6. Create normalized node record
    await this.createNormalizedNode(workflowId, nodeDto);

    // 7. Get updated workflow
    const savedWorkflow = await this.workflowRepository.findById(workflowId);
    if (!savedWorkflow) {
      throw new NotFoundException('Workflow not found after update');
    }

    this.logger.log(`Node added successfully: ${workflowId}, node: ${nodeDto.id}`);

    // Convert to DTO response
    return WorkflowDefinitionMapper.toNodeOperationResponse(savedWorkflow);
  }

  /**
   * Update node trong workflow
   * @param workflowId - ID của workflow
   * @param nodeId - ID của node
   * @param nodeDto - Updated node data
   * @param userId - ID của user
   * @returns Updated workflow response DTO
   */
  async updateNodeInWorkflow(
    workflowId: string,
    nodeId: string,
    nodeDto: Partial<WorkflowNodeDto>,
    userId: number
  ): Promise<WorkflowNodeOperationResponseDto> {
    this.logger.log(`Updating node in workflow: ${workflowId}, node: ${nodeId} by user: ${userId}`);

    // 1. Validate workflow exists
    const workflow = await this.findWorkflowWithPermission(workflowId, userId);

    // 2. Find node trong definition
    const nodes = workflow.definition.nodes || [];
    const nodeIndex = nodes.findIndex((node: any) => node.id === nodeId);
    
    if (nodeIndex === -1) {
      throw new NotFoundException(`Node '${nodeId}' not found in workflow`);
    }

    // 3. Update node data
    const updatedNode = { ...nodes[nodeIndex], ...nodeDto };

    // 4. Validate updated node
    const nodeValidation = await this.validationService.validateNode(updatedNode);
    if (!nodeValidation.isValid) {
      throw new BadRequestException({
        message: 'Updated node validation failed',
        errors: nodeValidation.errors
      });
    }

    // 5. Update definition
    const updatedNodes = [...nodes];
    updatedNodes[nodeIndex] = updatedNode;

    const updatedDefinition = {
      ...workflow.definition,
      nodes: updatedNodes,
      metadata: {
        ...workflow.definition.metadata,
        lastModified: {
          timestamp: Date.now(),
          userId,
          changes: `Updated node: ${nodeId}`
        }
      }
    };

    // 6. Save workflow using repository
    await this.workflowRepository.updateWorkflowDefinition(workflowId, updatedDefinition);

    // 7. Update normalized node record
    await this.updateNormalizedNode(workflowId, nodeId, updatedNode);

    // 8. Get updated workflow
    const savedWorkflow = await this.workflowRepository.findById(workflowId);
    if (!savedWorkflow) {
      throw new NotFoundException('Workflow not found after update');
    }

    this.logger.log(`Node updated successfully: ${workflowId}, node: ${nodeId}`);

    // Convert to DTO response
    return WorkflowDefinitionMapper.toNodeOperationResponse(savedWorkflow);
  }

  /**
   * Remove node from workflow
   * @param workflowId - ID của workflow
   * @param nodeId - ID của node to remove
   * @param userId - ID của user
   * @returns Updated workflow response DTO
   */
  async removeNodeFromWorkflow(
    workflowId: string,
    nodeId: string,
    userId: number
  ): Promise<WorkflowNodeOperationResponseDto> {
    this.logger.log(`Removing node from workflow: ${workflowId}, node: ${nodeId} by user: ${userId}`);

    // 1. Validate workflow exists
    const workflow = await this.findWorkflowWithPermission(workflowId, userId);

    // 2. Remove node from definition
    const nodes = workflow.definition.nodes || [];
    const filteredNodes = nodes.filter((node: any) => node.id !== nodeId);

    if (filteredNodes.length === nodes.length) {
      throw new NotFoundException(`Node '${nodeId}' not found in workflow`);
    }

    // 3. Remove related edges
    const edges = workflow.definition.edges || [];
    const filteredEdges = edges.filter((edge: any) => 
      edge.sourceNodeId !== nodeId && edge.targetNodeId !== nodeId
    );

    // 4. Update definition
    const updatedDefinition = {
      ...workflow.definition,
      nodes: filteredNodes,
      edges: filteredEdges,
      metadata: {
        ...workflow.definition.metadata,
        lastModified: {
          timestamp: Date.now(),
          userId,
          changes: `Removed node: ${nodeId}`
        }
      }
    };

    // 5. Save workflow using repository
    await this.workflowRepository.updateWorkflowDefinition(workflowId, updatedDefinition);

    // 6. Remove normalized records
    await this.removeNormalizedNode(workflowId, nodeId);

    // 7. Get updated workflow
    const savedWorkflow = await this.workflowRepository.findById(workflowId);
    if (!savedWorkflow) {
      throw new NotFoundException('Workflow not found after update');
    }

    this.logger.log(`Node removed successfully: ${workflowId}, node: ${nodeId}`);

    // Convert to DTO response
    return WorkflowDefinitionMapper.toNodeOperationResponse(savedWorkflow);
  }

  /**
   * Add edge to workflow
   * @param workflowId - ID của workflow
   * @param edgeDto - Edge data
   * @param userId - ID của user
   * @returns Updated workflow response DTO
   */
  async addEdgeToWorkflow(
    workflowId: string,
    edgeDto: WorkflowEdgeDto,
    userId: number
  ): Promise<WorkflowEdgeOperationResponseDto> {
    this.logger.log(`Adding edge to workflow: ${workflowId}, edge: ${edgeDto.id} by user: ${userId}`);

    // 1. Validate workflow exists
    const workflow = await this.findWorkflowWithPermission(workflowId, userId);

    // 2. Validate edge
    const nodes = workflow.definition.nodes || [];
    const edgeValidation = this.validationService.validateEdge(edgeDto, nodes);
    if (!edgeValidation.isValid) {
      throw new BadRequestException({
        message: 'Edge validation failed',
        errors: edgeValidation.errors
      });
    }

    // 3. Check edge ID uniqueness
    const existingEdge = await this.workflowEdgeRepository.findByWorkflowAndEdgeId(workflowId, edgeDto.id);
    if (existingEdge) {
      throw new BadRequestException(`Edge with ID '${edgeDto.id}' already exists`);
    }

    // 4. Add edge to definition
    const existingEdges = workflow.definition.edges || [];
    const updatedDefinition = {
      ...workflow.definition,
      edges: [...existingEdges, edgeDto],
      metadata: {
        ...workflow.definition.metadata,
        lastModified: {
          timestamp: Date.now(),
          userId,
          changes: `Added edge: ${edgeDto.id}`
        }
      }
    };

    // 5. Save workflow using repository
    await this.workflowRepository.updateWorkflowDefinition(workflowId, updatedDefinition);

    // 6. Create normalized edge record
    await this.createNormalizedEdge(workflowId, edgeDto);

    // 7. Get updated workflow
    const savedWorkflow = await this.workflowRepository.findById(workflowId);
    if (!savedWorkflow) {
      throw new NotFoundException('Workflow not found after update');
    }

    this.logger.log(`Edge added successfully: ${workflowId}, edge: ${edgeDto.id}`);

    // Convert to DTO response
    return WorkflowDefinitionMapper.toEdgeOperationResponse(savedWorkflow);
  }

  /**
   * Remove edge from workflow
   * @param workflowId - ID của workflow
   * @param edgeId - ID của edge to remove
   * @param userId - ID của user
   * @returns Updated workflow response DTO
   */
  async removeEdgeFromWorkflow(
    workflowId: string,
    edgeId: string,
    userId: number
  ): Promise<WorkflowEdgeOperationResponseDto> {
    this.logger.log(`Removing edge from workflow: ${workflowId}, edge: ${edgeId} by user: ${userId}`);

    // 1. Validate workflow exists
    const workflow = await this.findWorkflowWithPermission(workflowId, userId);

    // 2. Remove edge from definition
    const edges = workflow.definition.edges || [];
    const filteredEdges = edges.filter((edge: any) => edge.id !== edgeId);

    if (filteredEdges.length === edges.length) {
      throw new NotFoundException(`Edge '${edgeId}' not found in workflow`);
    }

    // 3. Update definition
    const updatedDefinition = {
      ...workflow.definition,
      edges: filteredEdges,
      metadata: {
        ...workflow.definition.metadata,
        lastModified: {
          timestamp: Date.now(),
          userId,
          changes: `Removed edge: ${edgeId}`
        }
      }
    };

    // 4. Save workflow using repository
    await this.workflowRepository.updateWorkflowDefinition(workflowId, updatedDefinition);

    // 5. Remove normalized edge record
    await this.removeNormalizedEdge(workflowId, edgeId);

    // 6. Get updated workflow
    const savedWorkflow = await this.workflowRepository.findById(workflowId);
    if (!savedWorkflow) {
      throw new NotFoundException('Workflow not found after update');
    }

    this.logger.log(`Edge removed successfully: ${workflowId}, edge: ${edgeId}`);

    // Convert to DTO response
    return WorkflowDefinitionMapper.toEdgeOperationResponse(savedWorkflow);
  }

  /**
   * Find workflow với permission check
   * @param workflowId - ID của workflow
   * @param userId - ID của user
   * @returns Workflow entity
   */
  private async findWorkflowWithPermission(workflowId: string, userId: number): Promise<Workflow> {
    const workflow = await this.workflowRepository.findByIdAndUserId(workflowId, userId);

    if (!workflow) {
      throw new NotFoundException(`Workflow with ID '${workflowId}' not found or access denied`);
    }

    return workflow;
  }

  /**
   * Update normalized tables (workflow_nodes và workflow_edges)
   * @param workflowId - ID của workflow
   * @param definition - Workflow definition
   */
  private async updateNormalizedTables(
    workflowId: string,
    definition: UpdateWorkflowDefinitionDto
  ): Promise<void> {
    // Remove existing normalized records
    await this.workflowNodeRepository.deleteByWorkflowId(workflowId);
    await this.workflowEdgeRepository.deleteByWorkflowId(workflowId);

    // Create new normalized records
    if (definition.nodes) {
      for (const node of definition.nodes) {
        await this.createNormalizedNode(workflowId, node);
      }
    }

    if (definition.edges) {
      for (const edge of definition.edges) {
        await this.createNormalizedEdge(workflowId, edge);
      }
    }
  }

  /**
   * Create normalized node record
   * @param workflowId - ID của workflow
   * @param nodeDto - Node data
   */
  private async createNormalizedNode(workflowId: string, nodeDto: WorkflowNodeDto): Promise<void> {
    const node = this.workflowNodeRepository.create({
      workflowId,
      nodeId: nodeDto.id,
      nodeType: nodeDto.type,
      name: nodeDto.name,
      position: nodeDto.position,
      config: {
        inputs: nodeDto.inputs,
        outputs: nodeDto.outputs,
        config: nodeDto.config,
        metadata: nodeDto.metadata
      }
    });

    await this.workflowNodeRepository.save(node);
  }

  /**
   * Update normalized node record
   * @param workflowId - ID của workflow
   * @param nodeId - ID của node
   * @param nodeData - Updated node data
   */
  private async updateNormalizedNode(
    workflowId: string,
    nodeId: string,
    nodeData: any
  ): Promise<void> {
    await this.workflowNodeRepository.update(
      { workflowId, nodeId },
      {
        nodeType: nodeData.type,
        name: nodeData.name,
        position: nodeData.position,
        config: {
          inputs: nodeData.inputs,
          outputs: nodeData.outputs,
          config: nodeData.config,
          metadata: nodeData.metadata
        }
      }
    );
  }

  /**
   * Remove normalized node record
   * @param workflowId - ID của workflow
   * @param nodeId - ID của node
   */
  private async removeNormalizedNode(workflowId: string, nodeId: string): Promise<void> {
    await this.workflowNodeRepository.delete({ workflowId, nodeId });
  }

  /**
   * Create normalized edge record
   * @param workflowId - ID của workflow
   * @param edgeDto - Edge data
   */
  private async createNormalizedEdge(workflowId: string, edgeDto: WorkflowEdgeDto): Promise<void> {
    const edge = this.workflowEdgeRepository.create({
      workflowId,
      edgeId: edgeDto.id,
      sourceNodeId: edgeDto.sourceNodeId,
      targetNodeId: edgeDto.targetNodeId,
      edgeType: edgeDto.edgeType || 'normal',
      condition: edgeDto.condition,
      metadata: edgeDto.metadata
    });

    await this.workflowEdgeRepository.save(edge);
  }

  /**
   * Remove normalized edge record
   * @param workflowId - ID của workflow
   * @param edgeId - ID của edge
   */
  private async removeNormalizedEdge(workflowId: string, edgeId: string): Promise<void> {
    await this.workflowEdgeRepository.delete({ workflowId, edgeId });
  }
}
