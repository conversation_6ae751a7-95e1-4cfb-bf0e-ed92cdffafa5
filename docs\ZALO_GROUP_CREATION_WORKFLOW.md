# Zalo Group Creation Workflow

## Tổng quan

Quy trình tạo nhóm chat Zalo GMF gồm 2 bước chính:
1. **L<PERSON><PERSON> danh sách Asset ID** có sẵn
2. **Tạo nhóm chat** với Asset ID đã chọn

## Workflow Chi tiết

### Bước 1: <PERSON><PERSON><PERSON> danh sách Asset ID

**Endpoint:** `GET /v1/zalo-group-management/{integrationId}/assets/group`

```bash
curl -X GET \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-************/assets/group' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

**Response:**
```json
{
  "success": true,
  "message": "<PERSON><PERSON>y danh sách Asset ID thành công",
  "data": {
    "assets": [
      {
        "asset_id": "asset_123456789",
        "product_type": "gmf10",
        "quota_type": "sub_quota",
        "valid_through": "10/10/2024",
        "auto_renew": false,
        "status": "available",
        "used_id": null
      },
      {
        "asset_id": "asset_987654321",
        "product_type": "gmf50",
        "quota_type": "purchase_quota",
        "valid_through": "30/11/2024",
        "auto_renew": true,
        "status": "used",
        "used_id": "group_123456"
      }
    ],
    "total": 2
  }
}
```

### Bước 2: Chọn Asset ID phù hợp

Từ danh sách trên, chọn asset có:
- `status = "available"` (chưa được sử dụng)
- `valid_through` chưa hết hạn
- `product_type` phù hợp với nhu cầu:
  - `gmf10`: Nhóm tối đa 10 thành viên
  - `gmf50`: Nhóm tối đa 50 thành viên
  - `gmf100`: Nhóm tối đa 100 thành viên

**Ví dụ:** Chọn `asset_123456789` vì có `status = "available"`

### Bước 3: Tạo nhóm chat

**Endpoint:** `POST /v1/zalo-group-management/{integrationId}`

```bash
curl -X POST \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-************' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "groupName": "Nhóm khách hàng VIP",
    "description": "Nhóm dành cho các khách hàng VIP của công ty",
    "assetId": "asset_123456789",
    "memberUids": ["user123", "user456", "user789"],
    "avatarUrl": "https://example.com/avatar.jpg",
    "metadata": {
      "source": "manual",
      "campaign_id": "camp123"
    }
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Tạo nhóm chat thành công",
  "data": {
    "id": "uuid-group-id",
    "groupId": "zalo_group_id_123",
    "groupName": "Nhóm khách hàng VIP",
    "description": "Nhóm dành cho các khách hàng VIP của công ty",
    "memberCount": 4,
    "status": "ACTIVE",
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

## Error Handling

### Lỗi thường gặp

1. **Không có Asset ID khả dụng**
```json
{
  "success": true,
  "data": {
    "assets": [],
    "total": 0
  }
}
```
**Giải pháp:** Kiểm tra gói GMF của OA hoặc mua thêm quota

2. **Asset ID đã được sử dụng**
```json
{
  "success": false,
  "message": "Asset ID đã được sử dụng hoặc không hợp lệ",
  "error": "ASSET_NOT_AVAILABLE"
}
```
**Giải pháp:** Chọn asset khác có `status = "available"`

3. **Asset ID hết hạn**
```json
{
  "success": false,
  "message": "Asset ID đã hết hạn",
  "error": "ASSET_EXPIRED"
}
```
**Giải pháp:** Chọn asset khác chưa hết hạn

## Code Example (JavaScript)

```javascript
class ZaloGroupManager {
  constructor(apiBaseUrl, authToken) {
    this.apiBaseUrl = apiBaseUrl;
    this.authToken = authToken;
  }

  async getAvailableAssets(integrationId) {
    const response = await fetch(
      `${this.apiBaseUrl}/v1/zalo-group-management/${integrationId}/assets/group`,
      {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      }
    );
    
    const data = await response.json();
    return data.data.assets.filter(asset => asset.status === 'available');
  }

  async createGroup(integrationId, groupData) {
    const response = await fetch(
      `${this.apiBaseUrl}/v1/zalo-group-management/${integrationId}`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(groupData),
      }
    );
    
    return await response.json();
  }

  async createGroupWithAutoAsset(integrationId, groupData) {
    // Bước 1: Lấy danh sách asset
    const availableAssets = await this.getAvailableAssets(integrationId);
    
    if (availableAssets.length === 0) {
      throw new Error('Không có Asset ID khả dụng');
    }

    // Bước 2: Chọn asset phù hợp (ví dụ: gmf50 nếu có)
    const preferredAsset = availableAssets.find(asset => 
      asset.product_type === 'gmf50'
    ) || availableAssets[0];

    // Bước 3: Tạo nhóm
    return await this.createGroup(integrationId, {
      ...groupData,
      assetId: preferredAsset.asset_id,
    });
  }
}

// Sử dụng
const groupManager = new ZaloGroupManager('https://api.example.com', 'your-jwt-token');

groupManager.createGroupWithAutoAsset('integration-id', {
  groupName: 'Nhóm test',
  description: 'Mô tả nhóm',
  memberUids: ['user1', 'user2'],
}).then(result => {
  console.log('Nhóm đã được tạo:', result);
}).catch(error => {
  console.error('Lỗi tạo nhóm:', error);
});
```

## Best Practices

1. **Luôn kiểm tra Asset ID trước khi tạo nhóm**
2. **Cache danh sách Asset ID trong thời gian ngắn** để tránh gọi API liên tục
3. **Xử lý lỗi một cách graceful** khi không có asset khả dụng
4. **Chọn product_type phù hợp** với số lượng thành viên dự kiến
5. **Kiểm tra valid_through** để đảm bảo asset chưa hết hạn
