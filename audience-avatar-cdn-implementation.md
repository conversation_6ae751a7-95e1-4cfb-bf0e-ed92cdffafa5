# Cập Nhật API Audience Avatar Với CDN URL

## Tóm Tắt
Đã cập nhật API `GET /marketing/audiences/:id` để trả về avatar với URL CDN đầy đủ thay vì chỉ S3 key.

## Thay Đổi Thực <PERSON>n

### 1. Service Layer (`src/modules/marketing/user/services/user-audience.service.ts`)

#### Import CDN Service
```typescript
import { CdnService } from '@/shared/services/cdn.service';
```

#### Thêm CDN Service vào Constructor
```typescript
constructor(
  // ... other dependencies
  private readonly cdnService: CdnService,
) {}
```

#### Cập Nhật Method `mapToDto`
```typescript
// Chuyển đổi avatar S3 key thành CDN URL
let avatarUrl: string | null = audience.avatar;
if (audience.avatar) {
  try {
    const cdnUrl = this.cdnService.generateUrlView(audience.avatar, TimeIntervalEnum.ONE_DAY);
    avatarUrl = cdnUrl || audience.avatar; // Fallback to original key if CDN fails
  } catch (error) {
    console.warn(`Không thể tạo URL CDN cho avatar audience ${audience.id}: ${error.message}`);
    avatarUrl = audience.avatar; // Keep original key as fallback
  }
}
dto.avatar = avatarUrl;
```

### 2. DTO Layer (`src/modules/marketing/user/dto/audience/audience-response.dto.ts`)

#### Cập Nhật Documentation
```typescript
/**
 * URL avatar của khách hàng (CDN URL đầy đủ)
 * @example "https://cdn.redai.vn/customer_avatars/2024/01/1234567890-uuid.jpg?expires=1234567890&signature=abc123"
 */
@ApiProperty({
  description: 'URL avatar của khách hàng (CDN URL đầy đủ)',
  example: 'https://cdn.redai.vn/customer_avatars/2024/01/1234567890-uuid.jpg?expires=1234567890&signature=abc123',
  nullable: true,
})
avatar: string | null;
```

## Kết Quả

### Trước Khi Cập Nhật
```json
{
  "avatar": "marketing/customer_avatars/2025/07/user_1/1753112390692-cb390060-7917-4246-81dd-877719a2eef4.jpeg"
}
```

### Sau Khi Cập Nhật
```json
{
  "avatar": "https://cdn.redai.vn/marketing/customer_avatars/2025/07/user_1/1753112390692-cb390060-7917-4246-81dd-877719a2eef4.jpeg?expires=1234567890&signature=abc123"
}
```

## Tính Năng

1. **CDN URL Generation**: Sử dụng `CdnService.generateUrlView()` với thời hạn 1 ngày
2. **Error Handling**: Fallback về S3 key gốc nếu không thể tạo CDN URL
3. **Null Safety**: Xử lý trường hợp avatar null/undefined
4. **Consistent Implementation**: Giống với admin audience service

## Dependencies

- CDN Service đã có sẵn trong `ServicesModule` (Global module)
- Marketing User Module đã import `ServicesModule`
- Không cần thay đổi module configuration

## Testing

Sử dụng file `test-audience-avatar-cdn.http` để test API:
```
GET https://v2.redai.vn/api/v1/marketing/audiences/1126
```

## Lưu Ý

- CDN URL có thời hạn 1 ngày (TimeIntervalEnum.ONE_DAY)
- Nếu CDN service fail, sẽ trả về S3 key gốc
- Logging warning nếu không thể tạo CDN URL
- Tương thích với existing code và database schema
