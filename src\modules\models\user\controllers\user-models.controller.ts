import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';

// Common
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';

// DTOs
import {
  UserModelsResponseDto
} from '../dto/user-models';

// Services
import { UserModelFineTuneResponseDto } from '../dto/user-model-fine-tune';
import { UserModelQueryDto } from '../dto/user-models/model-query.dto';
import { SystemModelsResponseDto } from '../dto/user-models/system-models-response.dto';
import { UserModelsService } from '../services/user-models.service';

/**
 * Controller xử lý API cho User Models
 */
@ApiTags(SWAGGER_API_TAGS.USER_MODELS)
@Controller('user/models')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, PaginatedResult, UserModelsResponseDto, SystemModelsResponseDto, UserModelFineTuneResponseDto)
export class UserModelsController {
  constructor(private readonly userModelsService: UserModelsService) { }

  /**
   * Lấy danh sách models theo user keys
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách models theo user keys',
    description: 'Lấy danh sách models mà user có thể sử dụng thông qua LLM key cụ thể'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách models theo user keys',
    schema: ApiResponseDto.getPaginatedSchema(UserModelsResponseDto)
  })
  async getUserModelsByKeys(
    @CurrentUser('id') userId: number,
    @Query() queryDto: UserModelQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<UserModelsResponseDto>>> {
    return this.userModelsService.getUserModelsByKeys(userId, queryDto);
  }
}
