import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { ApiResponseDto } from '@common/response';
import { Transactional } from 'typeorm-transactional';
import { MODELS_ERROR_CODES } from '../../exceptions';
import { AdminDataFineTuneRepository } from '../../repositories/admin-data-fine-tune.repository';
import { AdminFineTuningValidationService } from './admin-fine-tuning-validation.service';
import { ProviderFineTuneEnum } from '../../constants/provider.enum';
import {
  AdminCreateFineTuningJobDto,
  AdminCreateFineTuningJobResponseDto,
  AdminValidateFineTuningJobDto,
} from '../dto/fine-tuning-validation';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';

/**
 * Service xử lý tạo admin fine-tuning jobs
 * Không có logic R-point, chỉ tạo job và monitor
 */
@Injectable()
export class AdminFineTuningJobService {
  private readonly logger = new Logger(AdminFineTuningJobService.name);

  constructor(
    private readonly adminDataFineTuneRepository: AdminDataFineTuneRepository,
    private readonly adminFineTuningValidationService: AdminFineTuningValidationService,
    @InjectQueue('fine-tuning-monitor') private readonly fineTuningMonitorQueue: Queue,
  ) {}

  // /**
  //  * Execute admin fine-tuning job
  //  */
  // @Transactional()
  // async executeFineTuningJob(
  //   employeeId: number,
  //   dto: AdminCreateFineTuningJobDto,
  // ): Promise<ApiResponseDto<AdminCreateFineTuningJobResponseDto>> {
  //   try {
  //     this.logger.log(`Executing admin fine-tuning job for employee ${employeeId}`);

  //     // 1. Validate trước khi execute
  //     const validationDto: AdminValidateFineTuningJobDto = {
  //       datasetId: dto.datasetId,
  //       systemModelId: dto.systemModelId,
  //       provider: dto.provider,
  //     };

  //     const validationResponse = await this.adminFineTuningValidationService.validateFineTuningJob(
  //       employeeId,
  //       validationDto,
  //     );

  //     const validationResult = validationResponse.result;

  //     if (!validationResult || !validationResult.isValid) {
  //       throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT);
  //     }

  //     // 2. Lấy thông tin cần thiết
  //     const dataset = await this.adminDataFineTuneRepository.findByIdWithFullData(dto.datasetId);
  //     const systemModel = await this.systemModelsRepository.findById(dto.systemModelId);
  //     const systemKey = await this.systemKeyLlmRepository.findDefaultByProvider(dto.provider as any);

  //     if (!dataset || !systemModel || !systemKey) {
  //       throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT);
  //     }

  //     // 3. Tạo fine-tune history record trước
  //     const fineTuneHistory = await this.fineTuneHistoriesRepository.save({
  //       modelName: dto.name,
  //       token: dataset.estimatedToken,
  //       method: { provider: dto.provider, type: 'admin' },
  //       metadata: {
  //         description: dto.description,
  //         datasetId: dto.datasetId,
  //         datasetName: dataset.name,
  //         systemModelId: dto.systemModelId,
  //         systemModelName: systemModel.modelId,
  //         employeeId: employeeId,
  //         createdBy: 'admin',
  //       },
  //       userId: null, // Admin job không có userId
  //       employeeId: employeeId,
  //       startDate: Date.now(),
  //       endDate: Date.now(),
  //       pointsDeducted: 0, // Admin không trừ points
  //       pointsRefunded: false,
  //       jobId: '', // Sẽ update sau khi tạo job với provider
  //     });

  //     // 4. Tạo job với provider (giả lập)
  //     const providerJobId = await this.createJobWithProvider(
  //       dataset,
  //       systemModel,
  //       systemKey,
  //       dto,
  //     );

  //     // 5. Update job ID vào history
  //     await this.fineTuneHistoriesRepository.update(fineTuneHistory.id, {
  //       jobId: providerJobId,
  //     });

  //     // 6. Tạo Redis job để monitor
  //     await this.createMonitoringJob(fineTuneHistory.id, providerJobId, dto.provider);

  //     // 7. Tạo response
  //     const response: AdminCreateFineTuningJobResponseDto = {
  //       jobId: providerJobId,
  //       historyId: fineTuneHistory.id,
  //       modelName: dto.name,
  //       status: 'running',
  //       estimatedDurationMinutes: validationResult.estimatedDurationMinutes,
  //       datasetInfo: {
  //         id: dataset.id,
  //         name: dataset.name,
  //         totalExamples: Math.floor(dataset.estimatedToken / 50),
  //       },
  //       baseModelInfo: {
  //         id: systemModel.id,
  //         modelId: systemModel.modelId,
  //         provider: systemModel.provider,
  //       },
  //       systemKeyInfo: {
  //         keyId: systemKey.id,
  //         keyName: systemKey.name,
  //         provider: systemKey.provider,
  //       },
  //       createdAt: Date.now(),
  //     };

  //     this.logger.log(`Admin fine-tuning job executed successfully for employee ${employeeId}`);
  //     return ApiResponseDto.success(response, 'Fine-tuning job executed successfully');
  //   } catch (error) {
  //     this.logger.error(`Error executing admin fine-tuning job for employee ${employeeId}:`, error);
  //     throw error;
  //   }
  // }

  // /**
  //  * Tạo job với provider (giả lập - cần implement thực tế)
  //  */
  // private async createJobWithProvider(
  //   dataset: any,
  //   systemModel: any,
  //   systemKey: any,
  //   dto: AdminCreateFineTuningJobDto,
  // ): Promise<string> {
  //   try {
  //     this.logger.log(`Creating job with provider ${dto.provider}`);

  //     // TODO: Implement thực tế với từng provider
  //     switch (dto.provider) {
  //       case ProviderFineTuneEnum.OPENAI:
  //         return this.createOpenAIJob(dataset, systemModel, systemKey, dto);
  //       case ProviderFineTuneEnum.GOOGLE:
  //         return this.createGoogleJob(dataset, systemModel, systemKey, dto);
  //       default:
  //         throw new AppException(MODELS_ERROR_CODES.INVALID_INPUT);
  //     }
  //   } catch (error) {
  //     this.logger.error('Error creating job with provider:', error);
  //     throw new AppException(MODELS_ERROR_CODES.FINE_TUNING_JOB_CREATION_FAILED);
  //   }
  // }

  // /**
  //  * Tạo OpenAI fine-tuning job
  //  */
  // private async createOpenAIJob(
  //   dataset: any,
  //   systemModel: any,
  //   systemKey: any,
  //   dto: AdminCreateFineTuningJobDto,
  // ): Promise<string> {
  //   // TODO: Implement OpenAI API call
  //   // Giả lập job ID
  //   const jobId = `ftjob-admin-${Date.now()}-${Math.random().toString(36).substring(7)}`;
    
  //   this.logger.log(`Created OpenAI job: ${jobId}`);
  //   return jobId;
  // }

  // /**
  //  * Tạo Google fine-tuning job
  //  */
  // private async createGoogleJob(
  //   dataset: any,
  //   systemModel: any,
  //   systemKey: any,
  //   dto: AdminCreateFineTuningJobDto,
  // ): Promise<string> {
  //   // TODO: Implement Google API call
  //   // Giả lập job ID
  //   const jobId = `google-admin-${Date.now()}-${Math.random().toString(36).substring(7)}`;
    
  //   this.logger.log(`Created Google job: ${jobId}`);
  //   return jobId;
  // }

  // /**
  //  * Tạo Redis job để monitor fine-tuning progress
  //  */
  // private async createMonitoringJob(
  //   historyId: string,
  //   providerJobId: string,
  //   provider: ProviderFineTuneEnum,
  // ): Promise<void> {
  //   try {
  //     const jobData = {
  //       historyId,
  //       providerJobId,
  //       provider,
  //       type: 'admin',
  //       createdAt: Date.now(),
  //     };

  //     await this.fineTuningMonitorQueue.add(
  //       'monitor-admin-fine-tuning',
  //       jobData,
  //       {
  //         delay: 30000, // Delay 30 giây trước khi bắt đầu monitor
  //         attempts: 3,
  //         backoff: {
  //           type: 'exponential',
  //           delay: 60000, // 1 phút
  //         },
  //       },
  //     );

  //     this.logger.log(`Created monitoring job for admin fine-tuning: ${historyId}`);
  //   } catch (error) {
  //     this.logger.error('Error creating monitoring job:', error);
  //     // Không throw error vì job đã được tạo thành công
  //   }
  // }
}
