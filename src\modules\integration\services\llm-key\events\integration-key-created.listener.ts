import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { LlmKeyModelCrawlerService } from '../llm-key-model-crawler.service';
import { IntegrationKeyCreatedEvent } from './integration-key-created.event';

/**
 * Listener lắng nghe event khi tích hợp key LLM thành công
 */
@Injectable()
export class IntegrationKeyCreatedListener {
  private readonly logger = new Logger(IntegrationKeyCreatedListener.name);

  constructor(
    private readonly llmKeyModelCrawlerService: LlmKeyModelCrawlerService,
  ) {}

  /**
   * Xử lý event khi tích hợp key LLM thành công
   * @param event IntegrationKeyCreatedEvent
   */
  @OnEvent('integration.key.created')
  async handleIntegrationKeyCreated(event: IntegrationKeyCreatedEvent): Promise<void> {
    try {
      this.logger.log(`Received integration key created event for integration ${event.integrationId}, provider ${event.provider}`);

      // Crawl models từ provider và sync vào database
      await this.llmKeyModelCrawlerService.crawlAndSyncModels(
        event.integrationId,
        event.provider,
        event.apiKey,
        event.userId // Optional - có thể undefined cho admin integrations
      );

      this.logger.log(`Successfully processed integration key created event for integration ${event.integrationId}`);
    } catch (error) {
      this.logger.error(
        `Error processing integration key created event for integration ${event.integrationId}: ${error.message}`,
        error.stack
      );
      
      // Có thể thêm logic retry hoặc notification ở đây
      // Ví dụ: emit event để retry sau, hoặc gửi notification cho admin
    }
  }

  /**
   * Xử lý event khi xóa key LLM
   * @param event Event với integrationId
   */
  @OnEvent('integration.key.deleted')
  async handleIntegrationKeyDeleted(event: { integrationId: string }): Promise<void> {
    try {
      this.logger.log(`Received integration key deleted event for integration ${event.integrationId}`);

      // Xóa models mapping
      await this.llmKeyModelCrawlerService.removeModelsMapping(event.integrationId);

      this.logger.log(`Successfully processed integration key deleted event for integration ${event.integrationId}`);
    } catch (error) {
      this.logger.error(
        `Error processing integration key deleted event for integration ${event.integrationId}: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Xử lý event khi cần re-crawl models
   * @param event Event với thông tin integration
   */
  @OnEvent('integration.key.recrawl')
  async handleIntegrationKeyRecrawl(event: IntegrationKeyCreatedEvent): Promise<void> {
    try {
      this.logger.log(`Received integration key recrawl event for integration ${event.integrationId}`);

      if (!event.apiKey) {
        this.logger.error(`API key is required for integration ${event.integrationId}`);
        return;
      }

      if (!event.userId) {
        this.logger.error(`User ID is required for integration ${event.integrationId}`);
        return;
      }

      // Re-crawl models
      await this.llmKeyModelCrawlerService.reCrawlModels(
        event.integrationId,
        event.provider,
        event.userId,
        event.apiKey
      );

      this.logger.log(`Successfully processed integration key recrawl event for integration ${event.integrationId}`);
    } catch (error) {
      this.logger.error(
        `Error processing integration key recrawl event for integration ${event.integrationId}: ${error.message}`,
        error.stack
      );
    }
  }
}
