import { ConfigType } from '@/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions/integration-error.code';
import { ConfigService } from '@config/config.service';
import { FacebookConfig } from '@config/interfaces';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  FacebookAuthResponse,
  FacebookLongLivedTokenResponse,
} from '../interfaces/facebook.interface';

/**
 * Service để xử lý xác thực Facebook
 */
@Injectable()
export class FacebookAuthService {
  private readonly logger = new Logger(FacebookAuthService.name);
  private readonly facebookConfig: FacebookConfig;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.facebookConfig = this.configService.getConfig<FacebookConfig>(
      ConfigType.Facebook,
    );
  }

  /**
   * Tạo URL xác thực Facebook
   * @param endpointCallback URI callback sau khi xác thực
   * @param state Trạng thái để xác thực callback
   * @param scopes Danh sách quyền cần yêu cầu
   * @returns URL xác thực Facebook
   */
  createAuthUrl(
    endpointCallback: string,
    state: string,
    scopes: string[] = [],
  ): string {
    if (!this.facebookConfig?.appId) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Thiếu cấu hình Facebook App ID',
      );
    }

    // Nếu không có scopes, sử dụng mặc định
    if (scopes.length === 0) {
      scopes = [
        'pages_show_list',
        'pages_messaging',
        'pages_manage_metadata',
        'pages_read_engagement',
      ];
    }

    // Xây dựng redirect_uri đúng cách
    const fullRedirectUri = this.facebookConfig.redirectUri.endsWith('/')
      ? this.facebookConfig.redirectUri.substring(0, this.facebookConfig.redirectUri.length - 1) + endpointCallback
      : this.facebookConfig.redirectUri + endpointCallback;

    return `https://www.facebook.com/${this.facebookConfig.graphApiVersion}/dialog/oauth?client_id=${this.facebookConfig.appId
      }&redirect_uri=${fullRedirectUri}&scope=${scopes.join(
        ',',
      )}&state=${state}`;
  }

  /**
   * Xử lý callback từ Facebook để lấy access token
   * @param code Code từ Facebook callback
   * @param endpointCallback URI callback đã sử dụng khi tạo auth URL
   * @returns Access token và thông tin hết hạn
   */
  async handleCallback(
    code: string,
    endpointCallback: string,
  ): Promise<FacebookAuthResponse> {
    try {

      if (!this.facebookConfig?.appId || !this.facebookConfig?.appSecret) {
        this.logger.error(`[DEBUG] Thiếu cấu hình Facebook App`);
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu cấu hình Facebook App',
        );
      }

      // Xây dựng redirect_uri đúng cách
    const fullRedirectUri = this.facebookConfig.redirectUri.endsWith('/')
      ? this.facebookConfig.redirectUri.substring(0, this.facebookConfig.redirectUri.length - 1) + endpointCallback
      : this.facebookConfig.redirectUri + endpointCallback;


      // Chuẩn bị request URL và params
      const requestUrl = `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/oauth/access_token`;
      const requestParams = {
        client_id: this.facebookConfig.appId,
        redirect_uri: fullRedirectUri,
        client_secret: this.facebookConfig.appSecret,
        code: code,
      };

      this.logger.log(`[DEBUG] Gọi Facebook API:`, {
        url: requestUrl,
        params: {
          ...requestParams,
          client_secret: '***HIDDEN***',
          code: code ? `${code.substring(0, 20)}...` : 'null',
        },
      });

      // Gọi API để lấy access token
      const response = await firstValueFrom(
        this.httpService.get<FacebookAuthResponse>(requestUrl, {
          params: requestParams,
        }),
      );

      if (!response.data.access_token) {
        this.logger.error(
          `[DEBUG] Facebook API không trả về access token:`,
          response.data,
        );
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể lấy access token từ Facebook',
        );
      }

      this.logger.log(
        `[DEBUG] FacebookAuthService.handleCallback completed successfully`,
      );
      return response.data;
    } catch (error) {

      // Kiểm tra loại lỗi cụ thể
      if (error.response?.status === 400) {
        const errorData = error.response.data;
        this.logger.error(`[DEBUG] Facebook API 400 Error Details:`, errorData);

        if (errorData?.error?.message) {
          // Xử lý các lỗi Facebook cụ thể
          const facebookError = errorData.error;

          // Xử lý lỗi rate limiting từ Facebook (code 368)
          if (facebookError.code === 368) {
            throw new AppException(
              ErrorCode.RATE_LIMIT_EXCEEDED,
              'Facebook đang giới hạn tần suất truy cập. Vui lòng thử lại sau ít phút.',
            );
          }

          if (
            facebookError.code === 100 &&
            facebookError.error_subcode === 36009
          ) {
            // Authorization code đã được sử dụng
            throw new AppException(
              INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_AUTH_CODE_USED,
              'Mã xác thực Facebook đã được sử dụng. Vui lòng thực hiện lại quá trình xác thực.',
            );
          }

          if (facebookError.code === 100) {
            // OAuth Exception khác
            throw new AppException(
              ErrorCode.EXTERNAL_SERVICE_ERROR,
              `Lỗi xác thực Facebook: ${facebookError.message}`,
            );
          }

          // Lỗi Facebook khác
          throw new AppException(
            ErrorCode.EXTERNAL_SERVICE_ERROR,
            `Facebook API Error (${facebookError.code}): ${facebookError.message}`,
          );
        }
      }

      this.logger.error(
        `Error handling Facebook callback: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xử lý callback từ Facebook',
      );
    }
  }

  /**
   * Chuyển đổi short-lived access token thành long-lived access token
   * @param accessToken Short-lived access token cần chuyển đổi
   * @returns Long-lived access token và thông tin hết hạn
   */
  async getLongLivedToken(
    accessToken: string,
  ): Promise<FacebookLongLivedTokenResponse> {
    try {
      if (!this.facebookConfig?.appId || !this.facebookConfig?.appSecret) {
        this.logger.error(
          `[DEBUG] Thiếu cấu hình Facebook App cho getLongLivedToken`,
        );
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu cấu hình Facebook App',
        );
      }

      const requestUrl = `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/oauth/access_token`;
      const requestParams = {
        grant_type: 'fb_exchange_token',
        client_id: this.facebookConfig.appId,
        client_secret: this.facebookConfig.appSecret,
        fb_exchange_token: accessToken,
      };

      this.logger.log(`[DEBUG] Gọi Facebook API để lấy long-lived token:`, {
        url: requestUrl,
        params: {
          ...requestParams,
          client_secret: '***HIDDEN***',
          fb_exchange_token: accessToken
            ? `${accessToken.substring(0, 20)}...`
            : 'null',
        },
      });

      // Gọi API để lấy long-lived access token
      const response = await firstValueFrom(
        this.httpService.get<FacebookLongLivedTokenResponse>(requestUrl, {
          params: requestParams,
        }),
      );

      this.logger.log(`[DEBUG] Facebook long-lived token API response:`, {
        status: response.status,
        hasAccessToken: !!response.data?.access_token,
        tokenType: response.data?.token_type,
        expiresIn: response.data?.expires_in,
        fullResponseData: response.data,
      });

      if (!response.data.access_token) {
        this.logger.error(
          `[DEBUG] Facebook long-lived token response không có access_token:`,
          response.data,
        );
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể lấy long-lived access token từ Facebook',
        );
      }

      this.logger.log(`[DEBUG] getLongLivedToken completed successfully`);
      return response.data;
    } catch (error) {
      this.logger.error(`[DEBUG] getLongLivedToken error:`, {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
      });

      this.logger.error(
        `Error getting long-lived token: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy long-lived access token từ Facebook',
      );
    }
  }
}
