# ZNS Campaign API Migration: oaId to integrationId

## Tổng quan

Tài liệu này mô tả việc cập nhật API `GET /v1/marketing/zalo/zns/zns-campaigns` để hỗ trợ `integrationId` thay vì `oaId` trong query parameters.

## Lý do thay đổi

- **Tích hợp với Integration Module**: Dữ liệu Zalo Official Account giờ được quản lý thông qua Integration entity
- **UUID thay vì String ID**: `integrationId` là UUID từ Integration table, dễ quản lý và bảo mật hơn
- **Tính nhất quán**: Các API Zalo khác đã sử dụng `integrationId`
- **Backward Compatibility**: API vẫn hỗ trợ cả `oaId` và `integrationId`

## Các thay đổi đã thực hiện

### 1. Query DTO Updates

**File**: `src/modules/marketing/user/dto/zalo/zns-campaign.dto.ts`

```typescript
// Trước
export class ZnsCampaignQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'ID Official Account (optional filter)',
    example: 'oa123456789',
  })
  @IsOptional()
  @IsString()
  oaId?: string;
}

// Sau
export class ZnsCampaignQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'ID Integration (optional filter)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsString()
  integrationId?: string;
}
```

### 2. Response DTO Updates

**File**: `src/modules/marketing/user/dto/zalo/zns-campaign.dto.ts`

```typescript
export class ZnsCampaignResponseDto {
  // ... existing fields
  
  @ApiProperty({
    description: 'ID Official Account',
    example: 'oa123456789',
  })
  oaId: string;

  @ApiPropertyOptional({
    description: 'ID Integration (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  integrationId?: string;
}
```

### 3. Service Layer Updates

**File**: `src/modules/marketing/user/services/zalo-zns-campaign.service.ts`

- Thêm helper methods để chuyển đổi giữa `integrationId` và `oaId`
- Cập nhật `getCampaignsByUserId()` để xử lý `integrationId` filter
- Thêm `integrationId` vào response data

### 4. API Endpoints

#### GET /v1/marketing/zalo/zns/zns-campaigns

**Query Parameters:**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `integrationId` | string (UUID) | Filter theo Integration ID | `123e4567-e89b-12d3-a456-************` |
| `page` | number | Số trang | `1` |
| `limit` | number | Số items per page | `10` |
| `search` | string | Tìm kiếm theo tên campaign | `thông báo` |
| `status` | enum | Filter theo trạng thái | `DRAFT`, `SCHEDULED`, `SENT`, `FAILED` |

**Response:**

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "userId": 123,
        "oaId": "oa123456789",
        "integrationId": "123e4567-e89b-12d3-a456-************",
        "name": "Chiến dịch thông báo đơn hàng",
        "description": "Gửi thông báo đơn hàng cho khách hàng",
        "templateId": "template123456789",
        "templateData": {
          "shopName": "RedAI Shop",
          "orderStatus": "Đã xác nhận"
        },
        "status": "DRAFT",
        "totalMessages": 100,
        "sentMessages": 0,
        "failedMessages": 0,
        "createdAt": 1640995200000,
        "updatedAt": 1640995200000
      }
    ],
    "meta": {
      "totalItems": 1,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  },
  "message": "Lấy danh sách chiến dịch ZNS thành công"
}
```

## Migration Guide

### Cho Frontend Developers

1. **Cập nhật API calls** để sử dụng `integrationId` thay vì `oaId`:

```javascript
// Trước
const response = await fetch('/api/v1/marketing/zalo/zns/zns-campaigns?oaId=oa123456789');

// Sau
const response = await fetch('/api/v1/marketing/zalo/zns/zns-campaigns?integrationId=123e4567-e89b-12d3-a456-************');
```

2. **Sử dụng integrationId từ response** cho các API calls khác:

```javascript
const campaigns = response.data.items;
campaigns.forEach(campaign => {
  console.log('Integration ID:', campaign.integrationId);
  console.log('OA ID:', campaign.oaId); // Vẫn có sẵn để backward compatibility
});
```

### Lấy Integration ID

Để lấy `integrationId` từ `oaId`, sử dụng API:

```
GET /api/v1/integration?type=ZALO_OA
```

Response sẽ chứa mapping giữa `integrationId` và `oaId` trong metadata.

## Testing

Sử dụng file `test-zns-campaign-api.http` để test các scenarios:

1. Lấy tất cả campaigns
2. Filter theo integrationId
3. Phân trang với filter
4. Tìm kiếm với filter

## Backward Compatibility

- API vẫn trả về `oaId` trong response để đảm bảo backward compatibility
- Database schema không thay đổi (vẫn lưu `oaId`)
- Chỉ query parameter thay đổi từ `oaId` sang `integrationId`
