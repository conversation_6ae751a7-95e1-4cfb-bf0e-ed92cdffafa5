import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { FacebookBusinessApiService } from './facebook-business-api.service';
import {
  FacebookCampaign,
  FacebookAdSet,
  FacebookAd,
  FACEBOOK_BUSINESS_CONSTANTS,
} from '../interfaces/facebook-business.interface';
import {
  FACEBOOK_BUSINESS_ERROR_CODES,
  createFacebookBusinessException,
  validateFacebookBusinessResponse,
  validateFacebookBusinessParams,
} from '../exceptions/facebook-business.exception';
import { Campaign } from 'facebook-nodejs-business-sdk';

/**
 * Service quản lý Facebook Campaigns
 */
@Injectable()
export class FacebookCampaignsService {
  private readonly logger = new Logger(FacebookCampaignsService.name);

  constructor(private readonly facebookApiService: FacebookBusinessApiService) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách campaigns
   * @param accessToken Access token
   * @param adAccountId ID của ad account
   * @param limit Số lượng campaigns tối đa
   * @returns <PERSON>h sách campaigns
   */
  async getCampaigns(accessToken: string, adAccountId?: string, limit: number = 25): Promise<FacebookCampaign[]> {
    try {
      this.logger.log(`Getting campaigns for ad account: ${adAccountId || 'default'}`);

      const adAccount = this.facebookApiService.getAdAccountInstance(accessToken, adAccountId);
      
      const campaigns = await adAccount.getCampaigns([
        'id',
        'name',
        'objective',
        'status',
        'configured_status',
        'effective_status',
        'created_time',
        'updated_time',
        'start_time',
        'stop_time',
        'daily_budget',
        'lifetime_budget',
        'budget_remaining',
        'buying_type',
        'bid_strategy',
        'special_ad_categories',
      ], {
        limit,
      });

      const result: FacebookCampaign[] = campaigns.map((campaign: any) => ({
        id: campaign.id,
        name: campaign.name,
        objective: campaign.objective,
        status: campaign.status,
        configured_status: campaign.configured_status,
        effective_status: campaign.effective_status,
        created_time: campaign.created_time,
        updated_time: campaign.updated_time,
        start_time: campaign.start_time,
        stop_time: campaign.stop_time,
        daily_budget: campaign.daily_budget,
        lifetime_budget: campaign.lifetime_budget,
        budget_remaining: campaign.budget_remaining,
        buying_type: campaign.buying_type,
        bid_strategy: campaign.bid_strategy,
        special_ad_categories: campaign.special_ad_categories,
      }));

      this.logger.log(`Successfully retrieved ${result.length} campaigns`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting campaigns: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy danh sách campaigns',
        { adAccountId },
      );
    }
  }

  /**
   * Lấy thông tin campaign cụ thể
   * @param campaignId ID của campaign
   * @returns Thông tin campaign
   */
  async getCampaign(campaignId: string): Promise<FacebookCampaign> {
    try {
      validateFacebookBusinessParams({ campaignId }, ['campaignId']);

      this.logger.log(`Getting campaign info for ID: ${campaignId}`);

      const campaign = new Campaign(campaignId);
      const campaignData = await campaign.get([
        'id',
        'name',
        'objective',
        'status',
        'configured_status',
        'effective_status',
        'created_time',
        'updated_time',
        'start_time',
        'stop_time',
        'daily_budget',
        'lifetime_budget',
        'budget_remaining',
        'buying_type',
        'bid_strategy',
        'special_ad_categories',
      ]);

      validateFacebookBusinessResponse(campaignData, ['id', 'name']);

      const result: FacebookCampaign = {
        id: campaignData.id,
        name: campaignData.name,
        objective: campaignData.objective,
        status: campaignData.status,
        configured_status: campaignData.configured_status,
        effective_status: campaignData.effective_status,
        created_time: campaignData.created_time,
        updated_time: campaignData.updated_time,
        start_time: campaignData.start_time,
        stop_time: campaignData.stop_time,
        daily_budget: campaignData.daily_budget,
        lifetime_budget: campaignData.lifetime_budget,
        budget_remaining: campaignData.budget_remaining,
        buying_type: campaignData.buying_type,
        bid_strategy: campaignData.bid_strategy,
        special_ad_categories: campaignData.special_ad_categories,
      };

      this.logger.log(`Successfully retrieved campaign: ${result.name}`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting campaign: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy thông tin campaign',
        { campaignId },
      );
    }
  }

  /**
   * Tạo campaign mới
   * @param adAccountId ID của ad account
   * @param campaignData Dữ liệu campaign
   * @returns Campaign đã tạo
   */
  async createCampaign(
    adAccountId: string,
    campaignData: {
      name: string;
      objective: string;
      status?: string;
      daily_budget?: string;
      lifetime_budget?: string;
      start_time?: string;
      stop_time?: string;
      special_ad_categories?: string[];
    },
  ): Promise<FacebookCampaign> {
    try {
      validateFacebookBusinessParams(campaignData, ['name', 'objective']);

      this.logger.log(`Creating campaign: ${campaignData.name}`);

      const adAccount = this.facebookApiService.getAdAccountInstance(adAccountId);

      const params = {
        name: campaignData.name,
        objective: campaignData.objective,
        status: campaignData.status || FACEBOOK_BUSINESS_CONSTANTS.AD_STATUSES.PAUSED,
        ...(campaignData.daily_budget && { daily_budget: campaignData.daily_budget }),
        ...(campaignData.lifetime_budget && { lifetime_budget: campaignData.lifetime_budget }),
        ...(campaignData.start_time && { start_time: campaignData.start_time }),
        ...(campaignData.stop_time && { stop_time: campaignData.stop_time }),
        ...(campaignData.special_ad_categories && { 
          special_ad_categories: campaignData.special_ad_categories 
        }),
      };

      const campaign = await adAccount.createCampaign([], params);
      
      validateFacebookBusinessResponse(campaign, ['id']);

      // Get full campaign data
      const createdCampaign = await this.getCampaign(campaign.id);

      this.logger.log(`Successfully created campaign: ${createdCampaign.name} (ID: ${createdCampaign.id})`);
      return createdCampaign;
    } catch (error) {
      this.logger.error(`Error creating campaign: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể tạo campaign',
        { adAccountId, campaignData },
      );
    }
  }

  /**
   * Cập nhật campaign
   * @param campaignId ID của campaign
   * @param updateData Dữ liệu cập nhật
   * @returns Campaign đã cập nhật
   */
  async updateCampaign(
    campaignId: string,
    updateData: {
      name?: string;
      status?: string;
      daily_budget?: string;
      lifetime_budget?: string;
      start_time?: string;
      stop_time?: string;
    },
  ): Promise<FacebookCampaign> {
    try {
      validateFacebookBusinessParams({ campaignId }, ['campaignId']);

      this.logger.log(`Updating campaign: ${campaignId}`);

      const campaign = new Campaign(campaignId);
      
      // Remove undefined values
      const params = Object.fromEntries(
        Object.entries(updateData).filter(([_, value]) => value !== undefined)
      );

      if (Object.keys(params).length === 0) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_PARAMETERS,
          'No valid update parameters provided',
        );
      }

      await campaign.update([], params);

      // Get updated campaign data
      const updatedCampaign = await this.getCampaign(campaignId);

      this.logger.log(`Successfully updated campaign: ${updatedCampaign.name}`);
      return updatedCampaign;
    } catch (error) {
      this.logger.error(`Error updating campaign: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể cập nhật campaign',
        { campaignId, updateData },
      );
    }
  }

  /**
   * Xóa campaign
   * @param campaignId ID của campaign
   * @returns True nếu xóa thành công
   */
  async deleteCampaign(campaignId: string): Promise<boolean> {
    try {
      validateFacebookBusinessParams({ campaignId }, ['campaignId']);

      this.logger.log(`Deleting campaign: ${campaignId}`);

      const campaign = new Campaign(campaignId);
      await campaign.update([], { status: FACEBOOK_BUSINESS_CONSTANTS.AD_STATUSES.DELETED });

      this.logger.log(`Successfully deleted campaign: ${campaignId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error deleting campaign: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể xóa campaign',
        { campaignId },
      );
    }
  }

  /**
   * Lấy danh sách ad sets của campaign
   * @param campaignId ID của campaign
   * @param limit Số lượng ad sets tối đa
   * @returns Danh sách ad sets
   */
  async getCampaignAdSets(campaignId: string, limit: number = 25): Promise<FacebookAdSet[]> {
    try {
      validateFacebookBusinessParams({ campaignId }, ['campaignId']);

      this.logger.log(`Getting ad sets for campaign: ${campaignId}`);

      const campaign = new Campaign(campaignId);
      
      const adSets = await campaign.getAdSets([
        'id',
        'name',
        'campaign_id',
        'status',
        'configured_status',
        'effective_status',
        'created_time',
        'updated_time',
        'start_time',
        'end_time',
        'daily_budget',
        'lifetime_budget',
        'budget_remaining',
        'billing_event',
        'optimization_goal',
        'bid_amount',
        'targeting',
      ], {
        limit,
      });

      const result: FacebookAdSet[] = adSets.map((adSet: any) => ({
        id: adSet.id,
        name: adSet.name,
        campaign_id: adSet.campaign_id,
        status: adSet.status,
        configured_status: adSet.configured_status,
        effective_status: adSet.effective_status,
        created_time: adSet.created_time,
        updated_time: adSet.updated_time,
        start_time: adSet.start_time,
        end_time: adSet.end_time,
        daily_budget: adSet.daily_budget,
        lifetime_budget: adSet.lifetime_budget,
        budget_remaining: adSet.budget_remaining,
        billing_event: adSet.billing_event,
        optimization_goal: adSet.optimization_goal,
        bid_amount: adSet.bid_amount,
        targeting: adSet.targeting,
      }));

      this.logger.log(`Successfully retrieved ${result.length} ad sets for campaign ${campaignId}`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting campaign ad sets: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy danh sách ad sets của campaign',
        { campaignId },
      );
    }
  }

  /**
   * Lấy danh sách ads của campaign
   * @param campaignId ID của campaign
   * @param limit Số lượng ads tối đa
   * @returns Danh sách ads
   */
  async getCampaignAds(campaignId: string, limit: number = 25): Promise<FacebookAd[]> {
    try {
      validateFacebookBusinessParams({ campaignId }, ['campaignId']);

      this.logger.log(`Getting ads for campaign: ${campaignId}`);

      const campaign = new Campaign(campaignId);
      
      const ads = await campaign.getAds([
        'id',
        'name',
        'adset_id',
        'campaign_id',
        'status',
        'configured_status',
        'effective_status',
        'created_time',
        'updated_time',
        'creative',
      ], {
        limit,
      });

      const result: FacebookAd[] = ads.map((ad: any) => ({
        id: ad.id,
        name: ad.name,
        adset_id: ad.adset_id,
        campaign_id: ad.campaign_id,
        status: ad.status,
        configured_status: ad.configured_status,
        effective_status: ad.effective_status,
        created_time: ad.created_time,
        updated_time: ad.updated_time,
        creative: ad.creative,
      }));

      this.logger.log(`Successfully retrieved ${result.length} ads for campaign ${campaignId}`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting campaign ads: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy danh sách ads của campaign',
        { campaignId },
      );
    }
  }
}
