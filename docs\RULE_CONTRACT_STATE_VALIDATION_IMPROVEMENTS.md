# Cải Thiện State Validation Cho Rule Contract

## 📋 Tổng Quan

Đã cải thiện hệ thống validation state cho Rule Contract XState Controller để ngăn chặn việc nh<PERSON>y bước (step skipping) và đảm bảo luồng xử lý đúng.

## 🔧 Những Gì Đã Cải Thiện

### 1. **Tạo Hàm Validation Chung**

**File**: `src/modules/rule-contract/state-machine/rule-contract-xstate.service.ts`

```typescript
/**
 * Kiểm tra xem event có được phép thực hiện ở state hiện tại không
 */
async validateEventForCurrentState(
  userId: number,
  eventType: RuleContractEvent
): Promise<{
  isValid: boolean;
  currentState?: string;
  allowedEvents?: string[];
  errorMessage?: string;
}> {
  // Logic validation sử dụng getAvailableEventsForState() có sẵn
}
```

### 2. **C<PERSON><PERSON>t Controller APIs**

**Trước đây** (có lỗi):
```typescript
// acceptTerms - THIẾU validation
async acceptTerms(user, dto) {
  if (!dto.accepted) {
    return error;
  }
  // THIẾU: Không check state hiện tại
  const success = await this.xstateService.sendEvent(...);
}
```

**Sau khi sửa** (đã có validation):
```typescript
// acceptTerms - ĐÃ CÓ validation
async acceptTerms(user, dto) {
  if (!dto.accepted) {
    return error;
  }
  
  // ✅ Kiểm tra state hiện tại
  const validation = await this.xstateService.validateEventForCurrentState(
    user.id,
    RuleContractEvent.ACCEPT_TERMS
  );
  
  if (!validation.isValid) {
    return new ApiResponseDto(null, validation.errorMessage, 400);
  }
  
  const success = await this.xstateService.sendEvent(...);
}
```

### 3. **APIs Đã Được Cập Nhật**

✅ **acceptTerms** - Thêm validation cho `ACCEPT_TERMS`
✅ **submitIndividualInfo** - Cải thiện validation cho `SUBMIT_INDIVIDUAL_INFO`
✅ **submitBusinessInfo** - Cải thiện validation cho `SUBMIT_BUSINESS_INFO`
✅ **proceedToSign** - Thêm validation cho `PROCEED_TO_SIGN`
✅ **signatureCompleted** - Thêm validation cho `SIGNATURE_COMPLETED`
✅ **otpVerified** - Thêm validation cho `OTP_VERIFIED`
✅ **fileUploaded** - Thêm validation cho `FILE_UPLOADED`
✅ **back** - Thêm validation cho `BACK`

### 4. **APIs Khác**

✅ **selectContractType** - Có logic đặc biệt (khởi tạo state machine)
✅ **resendOtp** - Đã có validation state cơ bản
✅ **generateUploadUrl** - Đã có validation state cơ bản

## 🚀 Lợi Ích

### **1. Ngăn Chặn Step Skipping**
```typescript
// Trước: User có thể gọi submit-individual-info mà không cần accept-terms
// Sau: Hệ thống sẽ báo lỗi nếu chưa ở đúng state
```

### **2. Error Messages Rõ Ràng**
```typescript
// Trước: "Không thể gửi thông tin cá nhân"
// Sau: "Event 'SUBMIT_INDIVIDUAL_INFO' không được phép ở state 'termsAcceptance'. 
//       Các event được phép: ACCEPT_TERMS, BACK"
```

### **3. Tái Sử Dụng Logic**
- Sử dụng `getAvailableEventsForState()` có sẵn
- Không duplicate code validation
- Dễ maintain và extend

## 🔍 Cách Hoạt Động

### **Flow Validation**
```typescript
1. User gọi API endpoint
2. Controller gọi validateEventForCurrentState()
3. Service kiểm tra:
   - State hiện tại của user
   - Available events cho state đó
   - Event có trong danh sách allowed không
4. Trả về validation result
5. Controller quyết định cho phép hoặc reject
```

### **State Mapping**
```typescript
// Ví dụ: individualTermsAcceptance
allowedEvents: ['ACCEPT_TERMS', 'BACK']

// Ví dụ: individualInfoInput  
allowedEvents: ['SUBMIT_INFO', 'SUBMIT_INDIVIDUAL_INFO', 'BACK']
```

## 🧪 Testing

### **Test Case 1: Valid Flow**
```typescript
1. POST /select-contract-type (INDIVIDUAL) ✅
2. POST /accept-terms ✅
3. POST /submit-individual-info ✅
```

### **Test Case 2: Invalid Flow (Step Skipping)**
```typescript
1. POST /select-contract-type (INDIVIDUAL) ✅
2. POST /submit-individual-info ❌
   Response: {
     "success": false,
     "message": "Event 'SUBMIT_INDIVIDUAL_INFO' không được phép ở state 'individualTermsAcceptance'. Các event được phép: ACCEPT_TERMS, BACK",
     "statusCode": 400
   }
```

## 📝 Các API Endpoints Affected

| Endpoint | Event | Validation Status |
|----------|-------|-------------------|
| `POST /select-contract-type` | `SELECT_INDIVIDUAL/SELECT_BUSINESS` | ⚠️ Logic đặc biệt |
| `POST /accept-terms` | `ACCEPT_TERMS` | ✅ Đã cải thiện |
| `POST /submit-individual-info` | `SUBMIT_INDIVIDUAL_INFO` | ✅ Đã cải thiện |
| `POST /submit-business-info` | `SUBMIT_BUSINESS_INFO` | ✅ Đã cải thiện |
| `POST /proceed-to-sign` | `PROCEED_TO_SIGN` | ✅ Đã cải thiện |
| `POST /signature-completed` | `SIGNATURE_COMPLETED` | ✅ Đã cải thiện |
| `POST /otp-verified` | `OTP_VERIFIED` | ✅ Đã cải thiện |
| `POST /file-uploaded` | `FILE_UPLOADED` | ✅ Đã cải thiện |
| `POST /back` | `BACK` | ✅ Đã cải thiện |

## ⚠️ Breaking Changes

**Không có breaking changes** - chỉ thêm validation nghiêm ngặt hơn.

Các client applications cần đảm bảo gọi APIs theo đúng thứ tự workflow.

## 🔄 Next Steps

1. **Test thoroughly** - Kiểm tra tất cả workflows
2. **Monitor logs** - Theo dõi validation errors
3. **Update documentation** - Cập nhật API docs nếu cần
4. **Consider adding** validation cho các APIs khác nếu cần thiết
