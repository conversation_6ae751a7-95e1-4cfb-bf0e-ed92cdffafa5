import { ApiProperty } from '@nestjs/swagger';
import { CustomFieldResponseDto } from './custom-field-response.dto';
import { TagResponseDto } from '../tag/tag-response.dto';
import { ImportResourceEnum } from '../../enums/import-resource.enum';

/**
 * DTO cho phản hồi thông tin audience
 */
export class AudienceResponseDto {
  /**
   * ID của audience
   * @example 1
   */
  @ApiProperty({
    description: 'ID của audience',
    example: 1,
  })
  id: number;

  /**
   * ID của người dùng
   * @example 123
   */
  @ApiProperty({
    description: 'ID của người dùng',
    example: 123,
    nullable: true,
  })
  userId: number | null;

  /**
   * Tên của khách hàng
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên của khách hàng',
    example: '<PERSON>uyễ<PERSON>ăn <PERSON>',
    nullable: true,
  })
  name: string | null;

  /**
   * <PERSON>ail của khách hàng
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email của khách hàng',
    example: '<EMAIL>',
  })
  email: string;

  /**
   * Mã quốc gia (số)
   * @example 84
   */
  @ApiProperty({
    description: 'Mã quốc gia (số)',
    example: 84,
    nullable: true,
  })
  countryCode: number | null;

  /**
   * Số điện thoại (không bao gồm mã quốc gia)
   * @example "912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại (không bao gồm mã quốc gia)',
    example: '912345678',
    nullable: true,
  })
  phoneNumber: string | null;

  /**
   * URL avatar của khách hàng (CDN URL đầy đủ)
   * @example "https://cdn.redai.vn/customer_avatars/2024/01/1234567890-uuid.jpg?expires=1234567890&signature=abc123"
   */
  @ApiProperty({
    description: 'URL avatar của khách hàng (CDN URL đầy đủ)',
    example: 'https://cdn.redai.vn/customer_avatars/2024/01/1234567890-uuid.jpg?expires=1234567890&signature=abc123',
    nullable: true,
  })
  avatar: string | null;

  /**
   * Địa chỉ của khách hàng
   * @example "123 Đường ABC, Quận 1, TP.HCM"
   */
  @ApiProperty({
    description: 'Địa chỉ của khách hàng',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    nullable: true,
  })
  address: string | null;

  /**
   * Zalo Social ID của khách hàng
   * @example "1234567890123456789"
   */
  @ApiProperty({
    description: 'Zalo Social ID của khách hàng (user_id từ Zalo API)',
    example: '1234567890123456789',
    nullable: true,
  })
  zaloSocialId: string | null;

  /**
   * ID của Integration (UUID foreign key)
   * @example "550e8400-e29b-41d4-a716-************"
   */
  @ApiProperty({
    description: 'ID của Integration (UUID foreign key)',
    example: '550e8400-e29b-41d4-a716-************',
    nullable: true,
  })
  integrationId: string | null;

  /**
   * Danh sách avatar URLs từ các nguồn bên ngoài
   * @example ["https://zalo.me/avatar1.jpg", "https://facebook.com/avatar2.jpg"]
   */
  @ApiProperty({
    description: 'Danh sách avatar URLs từ các nguồn bên ngoài (Zalo, Facebook, etc.)',
    example: ['https://zalo.me/avatar1.jpg', 'https://facebook.com/avatar2.jpg'],
    nullable: true,
    isArray: true,
    type: String,
  })
  avatarsExternal: string[] | null;

  /**
   * Nguồn import của audience
   * @example "ZALO"
   */
  @ApiProperty({
    description: 'Nguồn import của audience (ZALO, WEB, MANUAL, FACEBOOK)',
    example: 'ZALO',
    enum: ImportResourceEnum,
    nullable: true,
  })
  importResource: ImportResourceEnum | null;

  /**
   * ID của Zalo Official Account
   * @example 456
   */
  @ApiProperty({
    description: 'ID của Zalo Official Account (foreign key)',
    example: 456,
    nullable: true,
  })
  zaloOfficialAccountId: number | null;

  /**
   * Trạng thái theo dõi OA của người dùng Zalo
   * @example true
   */
  @ApiProperty({
    description: 'Trạng thái theo dõi OA của người dùng Zalo',
    example: true,
    nullable: true,
  })
  zaloUserIsFollower: boolean | null;

  /**
   * Ngày cuối cùng người dùng có tương tác với OA
   * @example "2024-06-20"
   */
  @ApiProperty({
    description: 'Ngày cuối cùng người dùng có tương tác với OA',
    example: '2024-06-20',
    nullable: true,
  })
  userLastInteractionDate: string | null;

  /**
   * Danh sách các trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Danh sách các trường tùy chỉnh',
    type: [CustomFieldResponseDto],
  })
  customFields: CustomFieldResponseDto[];

  /**
   * Danh sách các tag
   */
  @ApiProperty({
    description: 'Danh sách các tag',
    type: [TagResponseDto],
  })
  tags: TagResponseDto[];

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
