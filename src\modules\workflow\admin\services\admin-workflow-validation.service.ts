import { Injectable, Logger } from '@nestjs/common';
import { WorkflowValidationService } from '../../services/workflow-validation.service';

/**
 * Admin service để manage workflow validation
 * Following existing admin service patterns
 */
@Injectable()
export class AdminWorkflowValidationService {
  private readonly logger = new Logger(AdminWorkflowValidationService.name);

  constructor(
    private readonly workflowValidationService: WorkflowValidationService,
  ) {}

  // Placeholder implementation
  // This will be implemented in future tasks
}
