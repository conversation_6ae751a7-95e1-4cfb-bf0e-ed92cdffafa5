import {
  Controller,
  Get,
  Put,
  Delete,
  Param,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ShipmentUserService } from '../services/shipment-user.service';
import { 
  UpdateShipmentConfigDto, 
  ShipmentConfigResponseDto 
} from '../dto/shipment/shipment-config.dto';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto } from '@/common/response';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { CurrentUser } from '@/modules/auth/decorators';

/**
 * Controller xử lý cấu hình shipment cho agent user
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents/:agentId/shipment')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ShipmentUserController {
  constructor(private readonly shipmentUserService: ShipmentUserService) {}

  /**
   * Lấy cấu hình shipment của agent
   */
  @Get()
  @ApiOperation({ 
    summary: 'Lấy cấu hình shipment của agent',
    description: 'Lấy thông tin cấu hình vận chuyển của agent bao gồm provider và phí vận chuyển'
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy cấu hình shipment thành công',
    type: ApiResponseDto<ShipmentConfigResponseDto>,
  })
  @ApiErrorResponse()
  async getShipmentConfig(
    @Param('agentId') agentId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<ShipmentConfigResponseDto>> {
    return this.shipmentUserService.getShipmentConfig(agentId, userId);
  }

  /**
   * Cập nhật cấu hình shipment của agent
   */
  @Put()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Cập nhật cấu hình shipment của agent',
    description: 'Cập nhật thông tin cấu hình vận chuyển của agent'
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật cấu hình shipment thành công',
    type: ApiResponseDto<{ id: string }>,
  })
  @ApiErrorResponse()
  async updateShipmentConfig(
    @Param('agentId') agentId: string,
    @CurrentUser('id') userId: number,
    @Body() updateDto: UpdateShipmentConfigDto,
  ): Promise<ApiResponseDto<{ id: string }>> {
    return this.shipmentUserService.updateShipmentConfig(agentId, userId, updateDto);
  }

  /**
   * Xóa cấu hình shipment của agent (reset về mặc định)
   */
  @Delete()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Xóa cấu hình shipment của agent',
    description: 'Reset cấu hình vận chuyển của agent về giá trị mặc định'
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa cấu hình shipment thành công',
    type: ApiResponseDto<ShipmentConfigResponseDto>,
  })
  @ApiErrorResponse()
  async removeShipmentConfig(
    @Param('agentId') agentId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<ShipmentConfigResponseDto>> {
    return this.shipmentUserService.removeShipmentConfig(agentId, userId);
  }
}
