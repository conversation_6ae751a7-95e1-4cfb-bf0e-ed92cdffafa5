import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiExtraModels } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { AdminAudienceService } from '../services/admin-audience.service';
import { CreateAudienceDto, UpdateAudienceDto, AudienceResponseDto, UpdateAudienceResponseDto, AudienceQueryDto, CreateAvatarUploadUrlDto, AvatarUploadUrlResponseDto, MergeAudienceDto, MergeAudienceResponseDto } from '../dto/audience';
import { AttachTagsToAudiencesDto, DetachTagsFromAudiencesDto, AudienceTagResponseDto } from '../dto/audience-tag';

import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers/response.helper';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { BulkDeleteAudienceDto, BulkDeleteResponseDto } from '@/modules/marketing/common/dto';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';

/**
 * Controller cho AdminAudience
 */
@RequirePermissionEnum(Permission.MARKETING_VIEW)
@ApiTags(SWAGGER_API_TAGS.ADMIN_AUDIENCE)
@ApiExtraModels(AppApiResponse, PaginatedResult, AudienceResponseDto)
@Controller('admin/marketing/audiences')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminAudienceController {
  constructor(private readonly adminAudienceService: AdminAudienceService) {}

  /**
   * Tạo audience mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo audience mới' })
  @ApiResponse({ status: 201, description: 'Audience đã tạo', type: AudienceResponseDto })
  async create(
    @Body() createAudienceDto: CreateAudienceDto,
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.adminAudienceService.create(createAudienceDto);
    return wrapResponse(result, 'Audience đã được tạo thành công');
  }

  /**
   * Lấy danh sách audience với phân trang và filter
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách audience với phân trang và filter',
    description: `Lấy danh sách audience với các tùy chọn:
    - Phân trang với page và limit
    - Tìm kiếm theo tên, email, phone
    - Filter theo tags
    - Filter theo custom fields
    - Filter theo việc có số điện thoại hay không
    - Sắp xếp theo các trường khác nhau`
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách audience với phân trang',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/AppApiResponse' },
        {
          properties: {
            data: {
              allOf: [
                { $ref: '#/components/schemas/PaginatedResult' },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/AudienceResponseDto' }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  async findAll(
    @Query() query: AudienceQueryDto
  ): Promise<AppApiResponse<PaginatedResult<AudienceResponseDto>>> {
    const result = await this.adminAudienceService.findAll(query);
    return wrapResponse(result, 'Danh sách audience');
  }

  /**
   * Lấy audience theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy audience theo ID' })
  @ApiResponse({ status: 200, description: 'Audience', type: AudienceResponseDto })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async findOne(
    @Param('id') id: string,
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.adminAudienceService.findOne(+id);
    return wrapResponse(result, 'Thông tin audience');
  }

  /**
   * Cập nhật audience
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật audience',
    description: 'Cập nhật thông tin audience. Nếu có trường avatarMediaType, sẽ trả về URL tạm thời để upload avatar.'
  })
  @ApiResponse({
    status: 200,
    description: 'Audience đã được cập nhật thành công (bao gồm thông tin avatar upload nếu có)',
    type: UpdateAudienceResponseDto
  })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.AUDIENCE_EMAIL_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.AUDIENCE_PHONE_ALREADY_EXISTS,
  )
  async update(
    @CurrentEmployee() employee: JwtPayload,
    @Param('id') id: string,
    @Body() updateAudienceDto: UpdateAudienceDto,
  ): Promise<AppApiResponse<UpdateAudienceResponseDto>> {
    const result = await this.adminAudienceService.update(+id, updateAudienceDto);
    return wrapResponse(result, 'Audience đã được cập nhật thành công');
  }

  /**
   * Xóa audience
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa audience' })
  @ApiResponse({ status: 200, description: 'true nếu xóa thành công', type: Boolean })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async remove(
    @Param('id') id: string,
  ): Promise<AppApiResponse<boolean>> {
    const result = await this.adminAudienceService.remove(+id);
    return wrapResponse(result, 'Audience đã được xóa thành công');
  }

  /**
   * Xóa nhiều audience
   */
  @Delete()
  @ApiOperation({ summary: 'Xóa nhiều audience' })
  @ApiResponse({
    status: 200,
    description: 'Xóa audience thành công',
    type: BulkDeleteResponseDto
  })
  @ApiResponse({
    status: 207,
    description: 'Một số audience không thể xóa',
    type: BulkDeleteResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy audience' })
  async bulkDelete(
    @Body() bulkDeleteDto: BulkDeleteAudienceDto
  ): Promise<AppApiResponse<BulkDeleteResponseDto>> {
    const result = await this.adminAudienceService.bulkDelete(bulkDeleteDto.ids);
    return wrapResponse(result, result.message);
  }

  /**
   * Tạo presigned URL để upload avatar
   */
  @Post(':id/avatar/upload-url')
  @ApiOperation({ summary: 'Tạo presigned URL để upload avatar' })
  @ApiResponse({
    status: 201,
    description: 'URL upload đã được tạo thành công',
    type: AvatarUploadUrlResponseDto
  })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async createAvatarUploadUrl(
    @Param('id') id: string,
    @Body() createAvatarUploadUrlDto: CreateAvatarUploadUrlDto
  ): Promise<AppApiResponse<AvatarUploadUrlResponseDto>> {
    const result = await this.adminAudienceService.createAvatarUploadUrl(
      +id,
      createAvatarUploadUrlDto
    );
    return wrapResponse(result, 'URL upload avatar đã được tạo thành công');
  }



  /**
   * Xóa avatar của audience
   */
  @Delete(':id/avatar')
  @ApiOperation({ summary: 'Xóa avatar của audience' })
  @ApiResponse({
    status: 200,
    description: 'Avatar đã được xóa thành công',
    type: AudienceResponseDto
  })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async removeAvatar(
    @Param('id') id: string
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.adminAudienceService.removeAvatar(+id);
    return wrapResponse(result, 'Avatar đã được xóa thành công');
  }

  /**
   * Merge nhiều audience thành một audience mới
   */
  @Post('merge')
  @ApiOperation({
    summary: 'Merge nhiều audience thành một audience mới',
    description: 'API này nhận vào danh sách ID audience, tạo audience mới với thông tin được merge, và xóa các audience cũ'
  })
  @ApiResponse({
    status: 201,
    description: 'Merge audience thành công',
    type: MergeAudienceResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 404, description: 'Một hoặc nhiều audience không tồn tại' })
  @ApiResponse({ status: 500, description: 'Lỗi server khi merge audience' })
  async mergeAudiences(
    @Body() mergeDto: MergeAudienceDto
  ): Promise<AppApiResponse<MergeAudienceResponseDto>> {
    const result = await this.adminAudienceService.mergeAudiences(mergeDto);
    return wrapResponse(result, result.message);
  }

  /**
   * Gắn nhiều tag với nhiều audience
   */
  @Post('attach-tags')
  @ApiOperation({ summary: 'Gắn nhiều tag với nhiều audience' })
  @ApiResponse({ status: 200, description: 'Đã gắn tag với audience thành công', type: AudienceTagResponseDto })
  async attachTagsToAudiences(
    @Body() attachDto: AttachTagsToAudiencesDto,
  ): Promise<AppApiResponse<AudienceTagResponseDto>> {
    const result = await this.adminAudienceService.attachTagsToAudiences(attachDto);
    return wrapResponse(result, 'Đã gắn tag với audience thành công');
  }

  /**
   * Bỏ gắn nhiều tag với nhiều audience
   */
  @Post('detach-tags')
  @ApiOperation({ summary: 'Bỏ gắn nhiều tag với nhiều audience' })
  @ApiResponse({ status: 200, description: 'Đã bỏ gắn tag với audience thành công', type: AudienceTagResponseDto })
  async detachTagsFromAudiences(
    @Body() detachDto: DetachTagsFromAudiencesDto,
  ): Promise<AppApiResponse<AudienceTagResponseDto>> {
    const result = await this.adminAudienceService.detachTagsFromAudiences(detachDto);
    return wrapResponse(result, 'Đã bỏ gắn tag với audience thành công');
  }
}
