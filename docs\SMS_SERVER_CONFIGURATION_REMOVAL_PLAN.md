# Kế hoạch xóa SmsServerConfiguration Entity

## Tổng quan

Tài liệu này mô tả kế hoạch chi tiết để loại bỏ hoàn toàn `SmsServerConfiguration` entity v<PERSON> chuyển sang sử dụng `Integration` entity.

## Lý do loại bỏ

1. **Duplicate functionality**: SmsServerConfiguration và Integration có chức năng tương tự
2. **Inconsistency**: <PERSON><PERSON><PERSON> t<PERSON><PERSON> hợp khác đều sử dụng Integration entity
3. **Security**: Integration entity có encryption tốt hơn
4. **Maintenance**: Giảm complexity và duplicate code

## Phân tích Impact

### Files cần cập nhật/xóa:

#### 1. Entity & Repository
- ❌ `src/modules/integration/entities/sms-server-configuration.entity.ts`
- ❌ `src/modules/integration/repositories/sms-server-configuration.repository.ts`

#### 2. Services
- ❌ `src/modules/integration/admin/services/sms-server-configuration-admin.service.ts`
- ❌ `src/modules/integration/user/services/sms-server-configuration-user.service.ts`
- 🔄 `src/modules/marketing/admin/services/admin-twilio-sms.service.ts`
- 🔄 `src/modules/marketing/user/services/user-twilio-sms.service.ts`

#### 3. Controllers
- ❌ `src/modules/integration/admin/controllers/sms-server-configuration-admin.controller.ts`
- ❌ `src/modules/integration/user/controllers/sms-server-configuration-user.controller.ts`

#### 4. DTOs
- ❌ `src/modules/integration/user/dto/sms/update-sms-server.dto.ts`
- ❌ `src/modules/integration/admin/dto/sms-server-admin-response.dto.ts`

#### 5. Database
- ❌ Table: `sms_server_configurations`
- ❌ Related migrations and scripts

#### 6. Documentation
- ❌ `src/modules/integration/docs/sms-plan.md`
- 🔄 Update related documentation

## Migration Steps

### Phase 1: Verify Data Migration
```bash
# 1. Verify all SMS campaigns are using Integration
SELECT COUNT(*) FROM sms_campaign_user WHERE sms_integration_config IS NULL;
SELECT COUNT(*) FROM sms_campaign_admin WHERE sms_integration_id IS NULL;

# 2. Verify all SmsServerConfiguration data is migrated to Integration
SELECT 
    ssc.id as old_id,
    ssc.provider_name,
    i.id as new_integration_id,
    i.integration_name
FROM sms_server_configurations ssc
LEFT JOIN integration i ON i.metadata->>'old_sms_server_id' = ssc.id::text
WHERE i.id IS NULL;
```

### Phase 2: Update Code Dependencies

#### 2.1 Update Services using SmsServerConfiguration
```typescript
// Before (admin-twilio-sms.service.ts)
import { SmsServerConfiguration } from '@/modules/integration/entities';
import { SmsServerConfigurationRepository } from '@/modules/integration/repositories';

// After
import { Integration } from '@/modules/integration/entities';
import { IntegrationRepository } from '@/modules/integration/repositories';
```

#### 2.2 Replace Repository Calls
```typescript
// Before
const smsConfig = await this.smsServerConfigurationRepository.findOne({
  where: { id: configId, userId }
});

// After
const integration = await this.integrationRepository.findOne({
  where: { id: configId, userId }
});
```

### Phase 3: Remove Files

#### 3.1 Remove Entity & Repository
```bash
rm src/modules/integration/entities/sms-server-configuration.entity.ts
rm src/modules/integration/repositories/sms-server-configuration.repository.ts
```

#### 3.2 Remove Services
```bash
rm src/modules/integration/admin/services/sms-server-configuration-admin.service.ts
rm src/modules/integration/user/services/sms-server-configuration-user.service.ts
```

#### 3.3 Remove Controllers
```bash
rm src/modules/integration/admin/controllers/sms-server-configuration-admin.controller.ts
rm src/modules/integration/user/controllers/sms-server-configuration-user.controller.ts
```

#### 3.4 Remove DTOs
```bash
rm src/modules/integration/user/dto/sms/update-sms-server.dto.ts
rm src/modules/integration/admin/dto/sms-server-admin-response.dto.ts
```

### Phase 4: Update Module Imports

#### 4.1 Integration Module
```typescript
// Remove from entities export
// export * from './sms-server-configuration.entity';

// Remove from repositories
// SmsServerConfigurationRepository,

// Remove from providers
// SmsServerConfigurationRepository,
```

#### 4.2 Marketing Module
```typescript
// Remove SmsServerConfigurationRepository import and usage
// Replace with IntegrationRepository
```

### Phase 5: Database Cleanup

#### 5.1 Drop Table
```sql
-- Backup table first
CREATE TABLE sms_server_configurations_backup AS 
SELECT * FROM sms_server_configurations;

-- Drop the table
DROP TABLE IF EXISTS sms_server_configurations CASCADE;
```

#### 5.2 Remove Migration Files
```bash
rm database/migrations/add-integration-provider-id-to-sms-server-configurations.sql
rm database/migrations/migrate-sms-server-to-integration.sql
rm scripts/run-sms-server-configuration-migration.sh
rm scripts/run-sms-server-configuration-migration.ps1
```

## Detailed Implementation Plan

### Step 1: Update admin-twilio-sms.service.ts
- Replace SmsServerConfiguration with Integration
- Update repository calls
- Update data structure handling

### Step 2: Update user-twilio-sms.service.ts  
- Replace SmsServerConfiguration with Integration
- Update repository calls
- Update data structure handling

### Step 3: Remove unused imports
- Clean up all imports of SmsServerConfiguration
- Clean up all imports of SmsServerConfigurationRepository

### Step 4: Update module configurations
- Remove from TypeORM entities
- Remove from providers
- Remove from exports

### Step 5: Database cleanup
- Verify no foreign key references
- Drop table
- Remove migration files

## Testing Checklist

### Before Removal:
- [ ] All SMS campaigns work with Integration
- [ ] No active references to SmsServerConfiguration
- [ ] All data migrated successfully
- [ ] Backup created

### After Removal:
- [ ] Application starts without errors
- [ ] SMS functionality works
- [ ] No broken imports
- [ ] No database errors
- [ ] All tests pass

## Rollback Plan

If issues occur:

1. **Code Rollback**: Restore files from git
2. **Database Rollback**: 
   ```sql
   CREATE TABLE sms_server_configurations AS 
   SELECT * FROM sms_server_configurations_backup;
   ```
3. **Module Rollback**: Restore module configurations

## Risk Assessment

### High Risk:
- Breaking SMS functionality
- Data loss during migration

### Medium Risk:
- Import errors
- Module configuration issues

### Low Risk:
- Documentation updates
- File cleanup

## Timeline

- **Week 1**: Verify data migration, update services
- **Week 2**: Remove files, update modules  
- **Week 3**: Database cleanup, testing
- **Week 4**: Documentation update, final verification

## Success Criteria

1. ✅ All SmsServerConfiguration references removed
2. ✅ SMS functionality works with Integration only
3. ✅ No compilation errors
4. ✅ All tests pass
5. ✅ Database cleaned up
6. ✅ Documentation updated
