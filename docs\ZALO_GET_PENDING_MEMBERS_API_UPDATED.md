# Zalo Get Pending Members API Documentation (Updated)

## Tổng quan

API này lấy danh sách thành viên chờ duyệt của nhóm chat Zalo trực tiếp từ Zalo API với phân trang chuẩn, trả về format `ApiResponseDto<PaginatedResult>` để tương thích với hệ thống.

## Thay đổi chính

- **DTO**: `ZaloPendingMembersQueryDto` extend từ `QueryDto` thay vì định nghĩa riêng
- **Pagination**: Sử dụng `page/limit` chuẩn thay vì `offset/count`
- **Response**: <PERSON><PERSON><PERSON> về `PaginatedResult` thay vì `ZaloPendingMembersResponseDto`
- **Real-time**: Dữ liệu luôn cập nhật từ Zalo API

## Endpoint

### Lấy danh sách thành viên chờ duyệt

```
GET /v1/zalo-group-management/{integrationId}/{groupId}/pending-members
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| integrationId | string | Yes | ID của Integration Zalo OA (UUID format) |
| groupId | string | Yes | ID của nhóm chat Zalo |

#### Query Parameters (Inherited từ QueryDto)

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| page | integer | No | 1 | Số trang hiện tại (≥ 1) |
| limit | integer | No | 10 | Số lượng bản ghi trên mỗi trang (1-50) |
| search | string | No | - | Từ khóa tìm kiếm |
| sortBy | string | No | createdAt | Trường cần sắp xếp |
| sortDirection | enum | No | DESC | Hướng sắp xếp (ASC/DESC) |

#### Headers

```
Authorization: Bearer {JWT_TOKEN}
```

#### Response

**Success Response (200 OK):**

```json
{
  "success": true,
  "message": "Lấy danh sách thành viên chờ duyệt thành công",
  "data": {
    "items": [
      {
        "id": "8756287263669629130",
        "user_id": "8756287263669629130",
        "name": "Hoàng Trường Phước",
        "isPending": true
      },
      {
        "id": "1234567890123456789",
        "user_id": "1234567890123456789",
        "name": "Nguyễn Văn A",
        "isPending": true
      }
    ],
    "meta": {
      "totalItems": 15,
      "itemCount": 2,
      "itemsPerPage": 10,
      "totalPages": 2,
      "currentPage": 1
    }
  }
}
```

**Error Response (400 Bad Request):**

```json
{
  "success": false,
  "message": "Limit phải từ 1 đến 100",
  "error": "VALIDATION_ERROR"
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| items | array | Danh sách thành viên chờ duyệt |
| meta | object | Thông tin phân trang |

**Pending Member Object Fields:**

| Field | Type | Description |
|-------|------|-------------|
| id | string | ID duy nhất (user_id) |
| user_id | string | User ID của thành viên chờ duyệt |
| name | string | Tên thành viên |
| isPending | boolean | Luôn true (đánh dấu là pending) |

## So sánh trước và sau

### Trước (offset/count)
```bash
GET /pending-members?offset=10&count=5
```

**Response:**
```json
{
  "data": {
    "offset": 10,
    "count": 5,
    "total": 15,
    "member_count": 2,
    "members": [...]
  }
}
```

### Sau (page/limit)
```bash
GET /pending-members?page=3&limit=5
```

**Response:**
```json
{
  "data": {
    "items": [...],
    "meta": {
      "totalItems": 15,
      "itemCount": 2,
      "itemsPerPage": 5,
      "totalPages": 3,
      "currentPage": 3
    }
  }
}
```

## Sử dụng

### 1. Lấy trang đầu tiên (mặc định)

```bash
curl -X GET \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4/pending-members' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

### 2. Lấy với phân trang tùy chỉnh

```bash
curl -X GET \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4/pending-members?page=2&limit=20' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

### 3. Tìm kiếm thành viên chờ duyệt

```bash
curl -X GET \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4/pending-members?search=Hoàng' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

## Code Example (JavaScript)

```javascript
class ZaloPendingMembersClient {
  constructor(apiBaseUrl, authToken) {
    this.apiBaseUrl = apiBaseUrl;
    this.authToken = authToken;
  }

  async getPendingMembers(integrationId, groupId, options = {}) {
    const { page = 1, limit = 10, search, sortBy, sortDirection } = options;
    
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    
    if (search) params.append('search', search);
    if (sortBy) params.append('sortBy', sortBy);
    if (sortDirection) params.append('sortDirection', sortDirection);

    const response = await fetch(
      `${this.apiBaseUrl}/v1/zalo-group-management/${integrationId}/${groupId}/pending-members?${params}`,
      {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      }
    );
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data;
  }

  async getAllPendingMembers(integrationId, groupId) {
    const allMembers = [];
    let page = 1;
    const limit = 50; // Lấy tối đa mỗi lần
    
    while (true) {
      const result = await this.getPendingMembers(integrationId, groupId, { page, limit });
      
      allMembers.push(...result.items);
      
      // Kiểm tra xem còn trang nào không
      if (result.meta.currentPage >= result.meta.totalPages) {
        break;
      }
      
      page++;
    }
    
    return {
      items: allMembers,
      total: allMembers.length,
    };
  }

  async searchPendingMembers(integrationId, groupId, searchTerm) {
    return await this.getPendingMembers(integrationId, groupId, {
      search: searchTerm,
      limit: 50
    });
  }

  async getPendingMembersPaginated(integrationId, groupId, page = 1, limit = 10) {
    const result = await this.getPendingMembers(integrationId, groupId, { page, limit });
    
    return {
      items: result.items,
      pagination: {
        page: page,
        limit: limit,
        total: result.meta.totalItems,
        totalPages: result.meta.totalPages,
        hasNext: page < result.meta.totalPages,
        hasPrev: page > 1,
      },
    };
  }
}

// Sử dụng
const client = new ZaloPendingMembersClient(
  'https://api.example.com',
  'your-jwt-token'
);

// Lấy thành viên chờ duyệt với phân trang
client.getPendingMembers('integration-id', 'group-id', {
  page: 1,
  limit: 10
}).then(result => {
  console.log(`Found ${result.items.length} pending members out of ${result.meta.totalItems} total`);
  result.items.forEach(member => {
    console.log(`- ${member.name} (${member.user_id})`);
  });
});

// Lấy tất cả thành viên chờ duyệt
client.getAllPendingMembers('integration-id', 'group-id')
  .then(result => {
    console.log(`Total pending members: ${result.total}`);
  });

// Tìm kiếm thành viên chờ duyệt
client.searchPendingMembers('integration-id', 'group-id', 'Hoàng')
  .then(result => {
    console.log(`Found ${result.items.length} matching members`);
  });
```

## Workflow tích hợp

### 1. Workflow cơ bản
```
1. Gọi API với page=1, limit=10
   GET /pending-members?page=1&limit=10

2. Xử lý kết quả
   - items: danh sách thành viên chờ duyệt
   - meta: thông tin phân trang

3. Lặp lại với trang tiếp theo nếu cần
   GET /pending-members?page=2&limit=10
```

### 2. Auto-approve workflow
```javascript
async function autoApprovePendingMembers(integrationId, groupId) {
  try {
    // Lấy tất cả thành viên chờ duyệt
    const pendingMembers = await client.getAllPendingMembers(integrationId, groupId);
    
    if (pendingMembers.total === 0) {
      return { message: 'Không có thành viên chờ duyệt' };
    }
    
    // Duyệt tất cả
    const userIds = pendingMembers.items.map(m => m.user_id);
    const result = await acceptPendingMembers(integrationId, groupId, userIds);
    
    return {
      message: `Đã tự động duyệt ${result.acceptedCount}/${pendingMembers.total} thành viên`,
      success: true,
    };
  } catch (error) {
    return {
      message: `Lỗi auto-approve: ${error.message}`,
      success: false,
    };
  }
}
```

## Migration Guide

### Cập nhật Frontend Code

**Trước:**
```javascript
// Old API call
const response = await fetch(`/pending-members?offset=10&count=5`);
const data = response.data;
console.log(`Total: ${data.total}, Count: ${data.member_count}`);
```

**Sau:**
```javascript
// New API call
const response = await fetch(`/pending-members?page=3&limit=5`);
const data = response.data;
console.log(`Total: ${data.meta.totalItems}, Count: ${data.meta.itemCount}`);
```

### Cập nhật Response Handling

**Trước:**
```javascript
data.members.forEach(member => {
  console.log(member.user_id, member.name);
});
```

**Sau:**
```javascript
data.items.forEach(member => {
  console.log(member.user_id, member.name);
});
```

## Lợi ích

1. **Consistency**: Sử dụng pagination chuẩn như các API khác
2. **Clean Code**: Extend từ QueryDto, loại bỏ duplicate fields
3. **Maintainability**: Dễ bảo trì và mở rộng
4. **User-friendly**: API dễ sử dụng với page/limit
5. **Real-time**: Dữ liệu luôn cập nhật từ Zalo API
6. **Standard Response**: Sử dụng PaginatedResult format chuẩn

## Best Practices

1. **Caching**: Cache kết quả trong thời gian ngắn
2. **Batch Loading**: Sử dụng limit=100 để giảm API calls
3. **Error Handling**: Xử lý timeout và rate limiting
4. **Pagination**: Sử dụng page/limit chuẩn
5. **Search**: Kết hợp với search parameter để tìm kiếm
6. **Auto-approve**: Tích hợp với accept API để tự động duyệt
