import { Injectable, Logger } from '@nestjs/common';
import { EmployeeRepository, EmployeeHasRoleRepository, EmployeeRoleRepository, PermissionRepository } from '@modules/employee/repositories';
import { CreateEmployeeDto, EmployeeQueryDto, EmployeeOverviewDto, RoleCountDto, UpdateEmployeeProfileDto, ChangeEmployeePasswordSelfDto, UpdateEmployeeDto, EmployeeResponseDto, SendBulkDeleteOtpDto, SendBulkDeleteOtpResponseDto, BulkDeleteEmployeeDto, BulkDeleteEmployeeResponseDto } from '@modules/employee/dto';
import { Employee, EmployeeRole } from '@modules/employee/entities';
import { EmployeeAvatarUploadDto, UpdateEmployeeAvatarDto } from '@modules/employee/dto';
import { EmployeeCoverImageUploadDto } from '@modules/employee/dto/cover-image-upload.dto';
import { S3Service } from '@/shared/services/s3.service';
import { CdnService } from '@/shared/services/cdn.service';
import { CategoryFolderEnum, TimeIntervalEnum, FileSizeEnum } from '@/shared/utils';
import { generateS3Key } from '@/shared/utils/generators';
import { ChangeEmployeePasswordDto } from '@modules/employee/dto';
import { AssignEmployeeRoleDto } from '@modules/employee/dto';
import { Transactional } from 'typeorm-transactional';
import { EmployeePasswordService } from './employee-password.service';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions';
import { EMPLOYEE_ERROR_CODES } from '../exceptions/employee-error.code';
import { EmailPlaceholderService } from '@/modules/email/services/email-placeholder.service';
import { RedisService } from '@/shared/services/redis.service';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

/**
 * Service xử lý logic liên quan đến nhân viên
 */
@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);
  private readonly AVATAR_FOLDER = 'employee-avatars';
  private readonly BULK_DELETE_OTP_PREFIX = 'employee:bulk_delete_otp:';
  private readonly OTP_EXPIRY_TIME = 300; // 5 phút
  private readonly COOLDOWN_TIME = 60; // 1 phút

  constructor(
    private readonly employeeRepository: EmployeeRepository,
    private readonly employeeHasRoleRepository: EmployeeHasRoleRepository,
    private readonly employeeRoleRepository: EmployeeRoleRepository,
    private readonly permissionRepository: PermissionRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly passwordService: EmployeePasswordService,
    private readonly emailPlaceholderService: EmailPlaceholderService,
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Tạo nhân viên mới
   * @param createEmployeeDto Thông tin nhân viên mới
   * @returns Nhân viên đã được tạo và thông tin URL tạm thời để upload avatar (nếu có)
   */
  @Transactional()
  async createEmployee(createEmployeeDto: CreateEmployeeDto): Promise<{
    id: number;
    fullName: string;
    email: string;
    phoneNumber: string;
    address: string;
    createdAt: number;
    updatedAt: number;
    enable: boolean;
    avatar?: string;
    avatarUploadUrl?: string;
    avatarKey?: string;
    avatarUrlExpiresAt?: number;
  }> {
    try {
      // Kiểm tra email đã tồn tại chưa
      const existingEmployee = await this.employeeRepository.findByEmail(createEmployeeDto.email);
      if (existingEmployee) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMAIL_ALREADY_EXISTS,
          `Email ${createEmployeeDto.email} đã được sử dụng`
        );
      }

      // Kiểm tra số điện thoại đã tồn tại chưa
      const existingEmployeeByPhone = await this.employeeRepository.findByPhoneNumber(createEmployeeDto.phoneNumber);
      if (existingEmployeeByPhone) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.PHONE_NUMBER_ALREADY_EXISTS,
          `Số điện thoại ${createEmployeeDto.phoneNumber} đã được sử dụng`
        );
      }

      // Kiểm tra độ mạnh của mật khẩu
      try {
        this.passwordService.validatePasswordStrength(createEmployeeDto.password);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.PASSWORD_TOO_WEAK,
          error.message || 'Mật khẩu không đủ mạnh'
        );
      }

      // Mã hóa mật khẩu
      let hashedPassword: string;
      try {
        hashedPassword = await this.passwordService.hashPassword(createEmployeeDto.password);
      } catch (error) {
        this.logger.error(`Error hashing password: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_CREATION_FAILED,
          'Không thể mã hóa mật khẩu'
        );
      }

      // Tạo nhân viên mới
      let employee: Employee;
      try {
        employee = await this.employeeRepository.createEmployee(createEmployeeDto, hashedPassword);
      } catch (error) {
        this.logger.error(`Error creating employee in repository: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_CREATION_FAILED,
          'Tạo nhân viên thất bại'
        );
      }

      // Nếu có roleIds, gán vai trò cho nhân viên
      if (createEmployeeDto.roleIds && createEmployeeDto.roleIds.length > 0) {
        try {
          await this.employeeHasRoleRepository.assignRolesToEmployee(employee.id, createEmployeeDto.roleIds);
        } catch (error) {
          this.logger.error(`Error assigning roles to employee: ${error.message}`, error.stack);
          throw new AppException(
            EMPLOYEE_ERROR_CODES.ROLE_ASSIGNMENT_FAILED,
            'Gán vai trò cho nhân viên thất bại'
          );
        }
      }

      // Gửi email thông báo tài khoản mới cho nhân viên
      try {
        await this.emailPlaceholderService.sendEmployeeAccountCreated({
          EMAIL: employee.email,
          EMPLOYEE_NAME: employee.fullName,
          EMPLOYEE_ID: employee.id.toString(),
          EMPLOYEE_EMAIL: employee.email,
          EMPLOYEE_PASSWORD: createEmployeeDto.password,
        });
        this.logger.log(`Đã gửi email thông báo tài khoản mới đến nhân viên: ${employee.email}`);
      } catch (error) {
        // Log lỗi nhưng không throw để không ảnh hưởng đến việc tạo nhân viên
        this.logger.error(`Lỗi khi gửi email thông báo tài khoản mới cho nhân viên ${employee.email}: ${error.message}`, error.stack);
      }

      // Kết quả trả về
      const result = {
        id: employee.id,
        fullName: employee.fullName,
        email: employee.email,
        phoneNumber: employee.phoneNumber,
        address: employee.address,
        createdAt: employee.createdAt,
        updatedAt: employee.updatedAt,
        enable: employee.enable,
      };

      // Nếu có yêu cầu tạo URL tạm thời để upload avatar
      if (createEmployeeDto.avatarImageType && createEmployeeDto.avatarMaxSize) {
        // Tạo key cho avatar trên S3
        const avatarKey = generateS3Key({
          baseFolder: employee.id.toString(),
          categoryFolder: CategoryFolderEnum.EMPLOYEE_AVATAR_FOLDER,
        });

        // Lưu avatarKey vào entity Employee
        try {
          await this.employeeRepository.updateAvatar(employee.id, avatarKey);
          this.logger.log(`Đã lưu avatar key ${avatarKey} cho nhân viên ${employee.id}`);
        } catch (error) {
          this.logger.error(`Lỗi khi lưu avatar key cho nhân viên ${employee.id}: ${error.message}`, error.stack);
          throw new AppException(
            EMPLOYEE_ERROR_CODES.AVATAR_UPDATE_FAILED,
            'Lưu thông tin avatar thất bại'
          );
        }

        // Tạo URL tạm thời để tải lên avatar
        const uploadUrl = await this.s3Service.createPresignedWithID(
          avatarKey,
          TimeIntervalEnum.FIVE_MINUTES,
          createEmployeeDto.avatarImageType,
          createEmployeeDto.avatarMaxSize,
        );

        // Tính toán thời điểm hết hạn thực tế (timestamp)
        const expiresAt = Date.now() + TimeIntervalEnum.FIVE_MINUTES * 1000;

        // Thêm thông tin URL tạm thời vào kết quả
        return {
          ...result,
          avatar: avatarKey, // Thêm avatar key vào response
          avatarUploadUrl: uploadUrl,
          avatarKey,
          avatarUrlExpiresAt: expiresAt,
        };
      }

      return result;
    } catch (error) {
      this.logger.error(`Error creating employee: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.EMPLOYEE_CREATION_FAILED,
        'Tạo nhân viên thất bại'
      );
    }
  }

  /**
   * Tạo URL tạm thời để tải lên avatar nhân viên và lưu key vào database
   * @param employeeId ID của nhân viên
   * @param avatarUploadDto Thông tin về loại và kích thước avatar
   * @returns URL tạm thời và thông tin khóa S3
   * @description API này sẽ:
   * 1. Tạo S3 key mới cho avatar
   * 2. Lưu key vào database ngay lập tức
   * 3. Tạo presigned URL để upload
   * 4. Trả về URL và thông tin key
   * Người dùng không cần gọi API xác nhận riêng sau khi upload
   */
  async createAvatarUploadUrl(employeeId: number, avatarUploadDto: EmployeeAvatarUploadDto): Promise<{
    uploadUrl: string;
    avatarKey: string;
    expiresIn: number;
    expiresAt: number;
  }> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      try {
        await this.employeeRepository.findById(employeeId);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      // Kiểm tra loại hình ảnh
      if (!avatarUploadDto.imageType) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.INVALID_IMAGE_TYPE,
          'Loại hình ảnh không được để trống'
        );
      }

      // Tạo key cho avatar trên S3
      const avatarKey = generateS3Key({
        baseFolder: employeeId.toString(),
        categoryFolder: CategoryFolderEnum.EMPLOYEE_AVATAR_FOLDER,
      });

      try {
        // Lưu avatarKey vào database trước khi tạo URL
        try {
          await this.employeeRepository.updateAvatar(employeeId, avatarKey);
          this.logger.log(`Đã lưu avatar key ${avatarKey} cho nhân viên ${employeeId}`);
        } catch (error) {
          this.logger.error(`Lỗi khi lưu avatar key cho nhân viên ${employeeId}: ${error.message}`, error.stack);
          throw new AppException(
            EMPLOYEE_ERROR_CODES.AVATAR_UPDATE_FAILED,
            'Lưu thông tin avatar thất bại'
          );
        }

        // Tạo URL tạm thời để tải lên avatar
        const uploadUrl = await this.s3Service.createPresignedWithID(
          avatarKey,
          TimeIntervalEnum.FIVE_MINUTES,
          avatarUploadDto.imageType,
          avatarUploadDto.maxSize,
        );

        // Tính toán thời điểm hết hạn thực tế (timestamp)
        const expiresAt = Date.now() + TimeIntervalEnum.FIVE_MINUTES * 1000;

        return {
          uploadUrl,
          avatarKey,
          expiresIn: TimeIntervalEnum.FIVE_MINUTES,
          expiresAt,
        };
      } catch (error) {
        this.logger.error(`Error creating presigned URL: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.AVATAR_URL_CREATION_FAILED,
          'Tạo URL tải lên avatar thất bại'
        );
      }
    } catch (error) {
      this.logger.error(`Error creating avatar upload URL: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.AVATAR_URL_CREATION_FAILED,
        'Tạo URL tải lên avatar thất bại'
      );
    }
  }

  /**
   * Cập nhật avatar cho nhân viên
   * @param employeeId ID của nhân viên
   * @param updateAvatarDto Thông tin avatar mới
   * @returns Nhân viên đã được cập nhật
   */
  @Transactional()
  async updateAvatar(employeeId: number, updateAvatarDto: UpdateEmployeeAvatarDto): Promise<Employee> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      try {
        await this.employeeRepository.findById(employeeId);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      // Kiểm tra avatarKey
      if (!updateAvatarDto.avatarKey) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.AVATAR_UPDATE_FAILED,
          'Khóa avatar không được để trống'
        );
      }

      try {
        return await this.employeeRepository.updateAvatar(employeeId, updateAvatarDto.avatarKey);
      } catch (error) {
        this.logger.error(`Error updating avatar in repository: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.AVATAR_UPDATE_FAILED,
          'Cập nhật avatar thất bại'
        );
      }
    } catch (error) {
      this.logger.error(`Error updating avatar: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.AVATAR_UPDATE_FAILED,
        'Cập nhật avatar thất bại'
      );
    }
  }

  /**
   * Đổi mật khẩu cho nhân viên
   * @param employeeId ID của nhân viên
   * @param changePasswordDto Thông tin mật khẩu mới
   * @returns Thông báo kết quả
   */
  @Transactional()
  async changePassword(employeeId: number, changePasswordDto: ChangeEmployeePasswordDto): Promise<{ message: string }> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      try {
        await this.employeeRepository.findById(employeeId);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      try {
        // Kiểm tra độ mạnh của mật khẩu
        this.passwordService.validatePasswordStrength(changePasswordDto.newPassword);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.PASSWORD_TOO_WEAK,
          error.message || 'Mật khẩu không đủ mạnh'
        );
      }

      try {
        // Mã hóa mật khẩu mới
        const hashedPassword = await this.passwordService.hashPassword(changePasswordDto.newPassword);

        // Cập nhật mật khẩu
        await this.employeeRepository.changePassword(employeeId, hashedPassword);

        return { message: 'Đổi mật khẩu thành công' };
      } catch (error) {
        this.logger.error(`Error in password change process: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.PASSWORD_CHANGE_FAILED,
          'Đổi mật khẩu thất bại'
        );
      }
    } catch (error) {
      this.logger.error(`Error changing password: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.PASSWORD_CHANGE_FAILED,
        'Đổi mật khẩu thất bại'
      );
    }
  }

  /**
   * Gán vai trò cho nhân viên
   * @param employeeId ID của nhân viên
   * @param assignRoleDto Thông tin vai trò cần gán
   * @returns Nhân viên đã được cập nhật
   */
  @Transactional()
  async assignRoles(employeeId: number, assignRoleDto: AssignEmployeeRoleDto): Promise<Employee> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      try {
        await this.employeeRepository.findById(employeeId);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      // Kiểm tra danh sách vai trò
      if (!assignRoleDto.roleIds || assignRoleDto.roleIds.length === 0) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.ROLE_ASSIGNMENT_FAILED,
          'Danh sách vai trò không được để trống'
        );
      }

      try {
        return await this.employeeHasRoleRepository.assignRolesToEmployee(employeeId, assignRoleDto.roleIds);
      } catch (error) {
        this.logger.error(`Error in role assignment process: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.ROLE_ASSIGNMENT_FAILED,
          'Gán vai trò thất bại'
        );
      }
    } catch (error) {
      this.logger.error(`Error assigning roles: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.ROLE_ASSIGNMENT_FAILED,
        'Gán vai trò thất bại'
      );
    }
  }

  /**
   * Lấy danh sách vai trò của nhân viên
   * @param employeeId ID của nhân viên
   * @returns Danh sách vai trò
   */
  async getEmployeeRoles(employeeId: number): Promise<EmployeeRole[]> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      try {
        await this.employeeRepository.findById(employeeId);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      try {
        return await this.employeeHasRoleRepository.findRolesByEmployeeId(employeeId);
      } catch (error) {
        this.logger.error(`Error fetching roles from repository: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.ROLE_NOT_FOUND,
          'Không thể lấy danh sách vai trò'
        );
      }
    } catch (error) {
      this.logger.error(`Error getting employee roles: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.ROLE_NOT_FOUND,
        'Không thể lấy danh sách vai trò'
      );
    }
  }

  /**
   * Lấy danh sách nhân viên với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách nhân viên với phân trang
   */
  async findAll(queryDto: EmployeeQueryDto): Promise<PaginatedResult<Employee>> {
    try {
      this.logger.log(`Finding employees with query: ${JSON.stringify(queryDto)}`);
      try {
        const result = await this.employeeRepository.findAll(queryDto);

        // Xử lý avatar URL và cover image URL cho từng nhân viên
        const employeesWithUrls = result.items.map(employee => ({
          ...employee,
          avatar: this.generateAvatarUrl(employee.avatar || null),
          coverImage: this.generateCoverImageUrl(employee.coverImage || null)
        }));

        return {
          ...result,
          items: employeesWithUrls
        };
      } catch (error) {
        this.logger.error(`Error fetching employees from repository: ${error.message}`, error.stack);
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          'Không thể lấy danh sách nhân viên'
        );
      }
    } catch (error) {
      this.logger.error(`Error finding employees: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
        'Không thể lấy danh sách nhân viên'
      );
    }
  }

  /**
   * Tạo URL CDN cho avatar từ avatar key
   * @param avatarKey Key của avatar trên S3
   * @returns URL CDN đầy đủ hoặc undefined nếu không có avatar
   * @private
   */
  private generateAvatarUrl(avatarKey: string | null): string | undefined {
    if (!avatarKey) {
      return undefined;
    }

    try {
      // Sử dụng CDN service để tạo URL với thời hạn 24 giờ
      return this.cdnService.generateUrlView(avatarKey, TimeIntervalEnum.ONE_DAY) || undefined;
    } catch (error) {
      this.logger.warn(`Không thể tạo URL CDN cho avatar key ${avatarKey}: ${error.message}`);
      return undefined;
    }
  }

  /**
   * Tạo URL CDN cho ảnh bìa từ cover image key
   * @param coverImageKey Key của ảnh bìa trên S3
   * @returns URL CDN đầy đủ hoặc undefined nếu không có ảnh bìa
   * @private
   */
  private generateCoverImageUrl(coverImageKey: string | null): string | undefined {
    if (!coverImageKey) {
      return undefined;
    }

    try {
      // Sử dụng CDN service để tạo URL với thời hạn 24 giờ
      return this.cdnService.generateUrlView(coverImageKey, TimeIntervalEnum.ONE_DAY) || undefined;
    } catch (error) {
      this.logger.warn(`Không thể tạo URL CDN cho cover image key ${coverImageKey}: ${error.message}`);
      return undefined;
    }
  }

  /**
   * Lấy thông tin tổng quan về nhân viên, vai trò và quyền
   * @returns Thông tin tổng quan về nhân viên
   */
  async getEmployeeOverview(): Promise<EmployeeOverviewDto> {
    try {
      this.logger.log('Getting employee overview statistics');
      
      // Lấy tổng số nhân viên
      const employeeCount = await this.employeeRepository.countEmployees();
      
      // Lấy tất cả vai trò
      const roles = await this.employeeRoleRepository.findAll();
      
      // Lấy tổng số quyền
      const permissionCount = await this.permissionRepository.countPermissions();
      
      // Lấy số lượng nhân viên theo từng vai trò
      const roleCountsPromises = roles.map(async (role) => {
        const count = await this.employeeHasRoleRepository.countEmployeesByRoleId(role.id);
        
        const roleCountDto: RoleCountDto = {
          id: role.id,
          name: role.name,
          description: role.description,
          employeeCount: count
        };
        
        return roleCountDto;
      });
      
      const roleCounts = await Promise.all(roleCountsPromises);
      
      // Tạo đối tượng kết quả
      const result: EmployeeOverviewDto = {
        totalEmployees: employeeCount,
        totalRoles: roles.length,
        totalPermissions: permissionCount,
        roles: roleCounts
      };
      
      return result;
    } catch (error) {
      this.logger.error(`Error getting employee overview: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.DATA_FETCH_ERROR,
        'Không thể lấy thông tin tổng quan nhân viên'
      );
    }
  }

  /**
   * Cập nhật thông tin cá nhân của nhân viên
   * Không cho phép thay đổi email và số điện thoại
   * @param employeeId ID của nhân viên
   * @param updateProfileDto Thông tin cần cập nhật
   * @returns Nhân viên đã được cập nhật
   */
  @Transactional()
  async updateEmployeeProfile(employeeId: number, updateProfileDto: UpdateEmployeeProfileDto): Promise<Employee> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      const employee = await this.employeeRepository.findById(employeeId);
      if (!employee) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      // Cập nhật thông tin
      const updateData: Partial<Employee> = {
        updatedAt: Date.now(),
      };

      if (updateProfileDto.fullName !== undefined) {
        updateData.fullName = updateProfileDto.fullName;
      }

      if (updateProfileDto.address !== undefined) {
        updateData.address = updateProfileDto.address;
      }

      // Cập nhật trong database
      await this.employeeRepository.update(employeeId, updateData);

      // Trả về thông tin nhân viên đã cập nhật
      return await this.employeeRepository.findById(employeeId);
    } catch (error) {
      this.logger.error(`Error updating employee profile: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
        'Cập nhật thông tin nhân viên thất bại'
      );
    }
  }

  /**
   * Thay đổi mật khẩu của chính nhân viên đó
   * Yêu cầu xác thực mật khẩu cũ
   * @param employeeId ID của nhân viên
   * @param changePasswordDto Thông tin mật khẩu cũ và mới
   * @returns Thông báo kết quả
   */
  @Transactional()
  async changeEmployeePasswordSelf(
    employeeId: number,
    changePasswordDto: ChangeEmployeePasswordSelfDto
  ): Promise<{ message: string }> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      const employee = await this.employeeRepository.findById(employeeId);
      if (!employee) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      // Xác thực mật khẩu hiện tại
      const isCurrentPasswordValid = await bcrypt.compare(
        changePasswordDto.currentPassword,
        employee.password
      );

      if (!isCurrentPasswordValid) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.INVALID_CURRENT_PASSWORD,
          'Mật khẩu hiện tại không chính xác'
        );
      }

      // Kiểm tra mật khẩu mới không trùng với mật khẩu cũ
      const isSamePassword = await bcrypt.compare(
        changePasswordDto.newPassword,
        employee.password
      );

      if (isSamePassword) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.SAME_PASSWORD,
          'Mật khẩu mới không được trùng với mật khẩu hiện tại'
        );
      }

      // Kiểm tra độ mạnh của mật khẩu mới
      try {
        this.passwordService.validatePasswordStrength(changePasswordDto.newPassword);
      } catch (error) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.PASSWORD_TOO_WEAK,
          error.message || 'Mật khẩu mới không đủ mạnh'
        );
      }

      // Mã hóa mật khẩu mới
      const hashedNewPassword = await this.passwordService.hashPassword(changePasswordDto.newPassword);

      // Cập nhật mật khẩu
      await this.employeeRepository.changePassword(employeeId, hashedNewPassword);

      return { message: 'Đổi mật khẩu thành công' };
    } catch (error) {
      this.logger.error(`Error changing employee password: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.PASSWORD_CHANGE_FAILED,
        'Đổi mật khẩu thất bại'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết nhân viên theo ID
   * @param employeeId ID của nhân viên
   * @returns Thông tin chi tiết nhân viên
   */
  async findById(employeeId: number): Promise<EmployeeResponseDto> {
    try {
      const employee = await this.employeeRepository.findById(employeeId);
      if (!employee) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      const {password, ...rest} = employee;

      // Tạo URL avatar và cover image nếu có
      return {
        ...rest,
        avatar: this.generateAvatarUrl(employee.avatar || null),
        coverImage: this.generateCoverImageUrl(employee.coverImage || null)
      };
    } catch (error) {
      this.logger.error(`Error finding employee by ID: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
        'Không thể lấy thông tin nhân viên'
      );
    }
  }

  /**
   * Cập nhật thông tin nhân viên (dành cho admin)
   * @param employeeId ID của nhân viên
   * @param updateEmployeeDto Thông tin cần cập nhật
   * @returns Nhân viên đã được cập nhật
   */
  @Transactional()
  async updateEmployee(employeeId: number, updateEmployeeDto: UpdateEmployeeDto): Promise<Employee> {
    try {
      // Kiểm tra xem nhân viên có tồn tại không
      const employee = await this.employeeRepository.findById(employeeId);
      if (!employee) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${employeeId}`
        );
      }

      // Kiểm tra email đã tồn tại chưa (nếu có thay đổi email)
      if (updateEmployeeDto.email && updateEmployeeDto.email !== employee.email) {
        const existingEmployee = await this.employeeRepository.findByEmail(updateEmployeeDto.email);
        if (existingEmployee) {
          throw new AppException(
            EMPLOYEE_ERROR_CODES.EMAIL_ALREADY_EXISTS,
            `Email ${updateEmployeeDto.email} đã được sử dụng`
          );
        }
      }

      // Kiểm tra số điện thoại đã tồn tại chưa (nếu có thay đổi)
      if (updateEmployeeDto.phoneNumber && updateEmployeeDto.phoneNumber !== employee.phoneNumber) {
        const existingEmployeeByPhone = await this.employeeRepository.findByPhoneNumber(updateEmployeeDto.phoneNumber);
        if (existingEmployeeByPhone) {
          throw new AppException(
            EMPLOYEE_ERROR_CODES.PHONE_NUMBER_ALREADY_EXISTS,
            `Số điện thoại ${updateEmployeeDto.phoneNumber} đã được sử dụng`
          );
        }
      }

      // Chuẩn bị dữ liệu cập nhật
      const updateData: Partial<Employee> = {
        updatedAt: Date.now(),
      };

      if (updateEmployeeDto.fullName !== undefined) {
        updateData.fullName = updateEmployeeDto.fullName;
      }
      if (updateEmployeeDto.email !== undefined) {
        updateData.email = updateEmployeeDto.email;
      }
      if (updateEmployeeDto.phoneNumber !== undefined) {
        updateData.phoneNumber = updateEmployeeDto.phoneNumber;
      }
      if (updateEmployeeDto.address !== undefined) {
        updateData.address = updateEmployeeDto.address;
      }
      if (updateEmployeeDto.enable !== undefined) {
        updateData.enable = updateEmployeeDto.enable;
      }

      // Xử lý mật khẩu nếu có
      if (updateEmployeeDto.password) {
        try {
          this.passwordService.validatePasswordStrength(updateEmployeeDto.password);
          updateData.password = await this.passwordService.hashPassword(updateEmployeeDto.password);
        } catch (error) {
          throw new AppException(
            EMPLOYEE_ERROR_CODES.PASSWORD_TOO_WEAK,
            error.message
          );
        }
      }

      // Cập nhật trong database
      await this.employeeRepository.update(employeeId, updateData);

      // Xử lý vai trò nếu có
      if (updateEmployeeDto.roleIds !== undefined) {
        try {
          await this.employeeHasRoleRepository.assignRolesToEmployee(employeeId, updateEmployeeDto.roleIds);
        } catch (error) {
          this.logger.error(`Error assigning roles to employee: ${error.message}`, error.stack);
          throw new AppException(
            EMPLOYEE_ERROR_CODES.ROLE_ASSIGNMENT_FAILED,
            'Gán vai trò cho nhân viên thất bại'
          );
        }
      }

      // Trả về thông tin nhân viên đã cập nhật
      const updatedEmployee = await this.employeeRepository.findById(employeeId);
      return {
        ...updatedEmployee,
        avatar: this.generateAvatarUrl(updatedEmployee.avatar || null),
        coverImage: this.generateCoverImageUrl(updatedEmployee.coverImage || null)
      };
    } catch (error) {
      this.logger.error(`Error updating employee: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
        'Cập nhật thông tin nhân viên thất bại'
      );
    }
  }

  /**
   * Gửi OTP để xóa nhiều nhân viên
   * @param sendOtpDto Thông tin yêu cầu gửi OTP
   * @param currentEmployeeId ID nhân viên hiện tại từ JWT
   * @returns Thông tin OTP đã gửi
   */
  async sendBulkDeleteOtp(
    sendOtpDto: SendBulkDeleteOtpDto,
    currentEmployeeId: number
  ): Promise<SendBulkDeleteOtpResponseDto> {
    try {
      // Lấy thông tin nhân viên hiện tại từ database
      const currentEmployee = await this.employeeRepository.findById(currentEmployeeId);
      if (!currentEmployee) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          'Không tìm thấy thông tin nhân viên hiện tại'
        );
      }

      if (!currentEmployee.email) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.BULK_DELETE_OTP_SEND_FAILED,
          'Nhân viên hiện tại không có email để gửi OTP'
        );
      }

      // Kiểm tra cooldown
      const cooldownKey = `${this.BULK_DELETE_OTP_PREFIX}cooldown:${currentEmployeeId}`;
      const cooldownExists = await this.redisService.get(cooldownKey);
      if (cooldownExists) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.BULK_DELETE_OTP_SEND_FAILED,
          `Vui lòng đợi ${this.COOLDOWN_TIME} giây trước khi gửi OTP mới`
        );
      }

      // Kiểm tra tất cả nhân viên có tồn tại không
      const employees = await this.employeeRepository.findByIds(sendOtpDto.employeeIds);
      if (employees.length !== sendOtpDto.employeeIds.length) {
        const foundIds = employees.map(emp => emp.id);
        const notFoundIds = sendOtpDto.employeeIds.filter(id => !foundIds.includes(id));
        throw new AppException(
          EMPLOYEE_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID: ${notFoundIds.join(', ')}`
        );
      }

      // Tạo OTP 6 số
      const otp = this.generateOTP();

      // Tạo token OTP
      const otpToken = this.generateOtpToken();

      // Lưu thông tin OTP và dữ liệu vào Redis
      const otpData = {
        employeeId: currentEmployeeId,
        employeeEmail: currentEmployee.email,
        employeeName: currentEmployee.fullName,
        otp,
        employeeIds: sendOtpDto.employeeIds,
        action: 'BULK_DELETE',
        createdAt: Date.now(),
      };

      const redisKey = `${this.BULK_DELETE_OTP_PREFIX}${otpToken}`;
      await this.redisService.setWithExpiry(
        redisKey,
        JSON.stringify(otpData),
        this.OTP_EXPIRY_TIME,
      );

      // Thiết lập cooldown
      await this.redisService.setWithExpiry(
        cooldownKey,
        'true',
        this.COOLDOWN_TIME,
      );

      // Gửi email OTP
      await this.emailPlaceholderService.sendTwoFAVerification(currentEmployee.email, {
        TWO_FA_CODE: otp,
        NAME: currentEmployee.fullName,
      });

      this.logger.log(`Đã gửi OTP xóa nhiều nhân viên đến email ${currentEmployee.email}`);

      // Tính toán thời điểm hết hạn
      const expiresAt = Date.now() + this.OTP_EXPIRY_TIME * 1000;

      // Che một phần email
      const maskedEmail = this.maskEmail(currentEmployee.email);

      return {
        otpToken,
        message: `Đã gửi mã OTP đến email ${maskedEmail}`,
        expiresAt,
        maskedEmail,
        employeeCount: sendOtpDto.employeeIds.length,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi OTP xóa nhiều nhân viên: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.BULK_DELETE_OTP_SEND_FAILED,
        'Không thể gửi mã OTP'
      );
    }
  }

  /**
   * Xóa nhiều nhân viên với xác thực OTP
   * @param bulkDeleteDto Thông tin xóa nhiều nhân viên
   * @param currentEmployeeId ID nhân viên hiện tại từ JWT
   * @returns Kết quả xóa nhiều nhân viên
   */
  @Transactional()
  async bulkDeleteEmployees(
    bulkDeleteDto: BulkDeleteEmployeeDto,
    currentEmployeeId: number
  ): Promise<BulkDeleteEmployeeResponseDto> {
    try {
      // Xác thực OTP
      const redisKey = `${this.BULK_DELETE_OTP_PREFIX}${bulkDeleteDto.otpToken}`;
      const otpDataStr = await this.redisService.get(redisKey);

      if (!otpDataStr) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.BULK_DELETE_INVALID_TOKEN,
          'Token OTP không hợp lệ hoặc đã hết hạn'
        );
      }

      const otpData = JSON.parse(otpDataStr);

      // Kiểm tra OTP
      if (otpData.otp !== bulkDeleteDto.otp) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.BULK_DELETE_INVALID_OTP,
          'Mã OTP không chính xác'
        );
      }

      // Kiểm tra nhân viên thực hiện
      if (otpData.employeeId !== currentEmployeeId) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.BULK_DELETE_INVALID_TOKEN,
          'Token OTP không thuộc về nhân viên hiện tại'
        );
      }

      // Kiểm tra danh sách ID nhân viên có khớp không
      const sortedOriginalIds = [...otpData.employeeIds].sort();
      const sortedRequestIds = [...bulkDeleteDto.employeeIds].sort();
      if (JSON.stringify(sortedOriginalIds) !== JSON.stringify(sortedRequestIds)) {
        throw new AppException(
          EMPLOYEE_ERROR_CODES.BULK_DELETE_INVALID_TOKEN,
          'Danh sách nhân viên không khớp với yêu cầu OTP'
        );
      }

      // Xóa OTP khỏi Redis
      await this.redisService.del(redisKey);

      // Kiểm tra tất cả nhân viên có tồn tại không
      const employees = await this.employeeRepository.findByIds(bulkDeleteDto.employeeIds);
      const foundIds = employees.map(emp => emp.id);
      const notFoundIds = bulkDeleteDto.employeeIds.filter(id => !foundIds.includes(id));

      if (notFoundIds.length > 0) {
        this.logger.warn(`Một số nhân viên không tồn tại: ${notFoundIds.join(', ')}`);
      }

      // Thực hiện xóa nhân viên (soft delete)
      const deletedEmployeeIds: number[] = [];
      const failedEmployeeIds: number[] = [];

      for (const employee of employees) {
        try {
          // Không cho phép xóa chính mình
          if (employee.id === currentEmployeeId) {
            failedEmployeeIds.push(employee.id);
            this.logger.warn(`Không thể xóa chính mình: ${employee.id}`);
            continue;
          }

          // Soft delete bằng cách disable nhân viên
          employee.enable = false;
          employee.updatedAt = Date.now();
          await this.employeeRepository.save(employee);
          deletedEmployeeIds.push(employee.id);

          this.logger.log(`Đã xóa nhân viên ID: ${employee.id} - ${employee.fullName}`);
        } catch (error) {
          failedEmployeeIds.push(employee.id);
          this.logger.error(`Lỗi khi xóa nhân viên ID ${employee.id}: ${error.message}`);
        }
      }

      // Thêm các ID không tồn tại vào danh sách thất bại
      failedEmployeeIds.push(...notFoundIds);

      const result: BulkDeleteEmployeeResponseDto = {
        deletedCount: deletedEmployeeIds.length,
        deletedEmployeeIds,
        failedEmployeeIds: failedEmployeeIds.length > 0 ? failedEmployeeIds : undefined,
        message: `Đã xóa thành công ${deletedEmployeeIds.length} nhân viên`,
      };

      if (failedEmployeeIds.length > 0) {
        result.message += `. ${failedEmployeeIds.length} nhân viên không thể xóa`;
      }

      this.logger.log(`Bulk delete completed: ${deletedEmployeeIds.length} deleted, ${failedEmployeeIds.length} failed`);

      return result;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa nhiều nhân viên: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.BULK_DELETE_FAILED,
        'Xóa nhiều nhân viên thất bại'
      );
    }
  }

  /**
   * Tạo mã OTP 6 số ngẫu nhiên
   * @returns Mã OTP 6 số
   */
  private generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Tạo token OTP ngẫu nhiên
   * @returns Token OTP
   */
  private generateOtpToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Che một phần email để bảo mật
   * @param email Email cần che
   * @returns Email đã che
   */
  private maskEmail(email: string): string {
    const [localPart, domain] = email.split('@');
    if (localPart.length <= 2) {
      return `${localPart[0]}***@${domain}`;
    }
    const maskedLocal = `${localPart.substring(0, 2)}***${localPart.slice(-1)}`;
    return `${maskedLocal}@${domain}`;
  }

  /**
   * Tạo URL tạm thời để tải lên ảnh bìa nhân viên
   * @param employeeId ID của nhân viên
   * @param coverImageUploadDto Thông tin về loại và kích thước ảnh bìa
   * @returns URL tạm thời và thông tin khóa S3
   */
  async createCoverImageUploadUrl(
    employeeId: number,
    coverImageUploadDto: EmployeeCoverImageUploadDto,
  ) {
    try {
      // Kiểm tra nhân viên tồn tại (findById sẽ throw exception nếu không tìm thấy)
      await this.employeeRepository.findById(employeeId);

      // Tạo key cho ảnh bìa trên S3
      const coverImageKey = generateS3Key({
        baseFolder: employeeId.toString(),
        categoryFolder: CategoryFolderEnum.EMPLOYEE_COVER_IMAGE,
        useTimeFolder: true,
      });

      // Kiểm tra và giới hạn kích thước tối đa nếu cần
      const maxSize = Math.min(coverImageUploadDto.maxSize, FileSizeEnum.TEN_MB);

      // Tạo URL tạm thời để tải lên ảnh bìa
      const uploadUrl = await this.s3Service.createPresignedWithID(
        coverImageKey,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        coverImageUploadDto.imageType,
        maxSize,
      );

      // Lưu key ảnh bìa vào database ngay lập tức
      await this.employeeRepository.update(employeeId, {
        coverImage: coverImageKey,
        updatedAt: Date.now()
      });

      return {
        uploadUrl,
        coverImageKey,
        expiresIn: TimeIntervalEnum.FIFTEEN_MINUTES,
      };
    } catch (error) {
      this.logger.error(
        `Error creating employee cover image upload URL: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        EMPLOYEE_ERROR_CODES.COVER_IMAGE_UPLOAD_URL_CREATION_FAILED,
        'Tạo URL tải lên ảnh bìa thất bại'
      );
    }
  }


}
