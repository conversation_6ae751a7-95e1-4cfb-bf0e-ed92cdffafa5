import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration to add performance indexes for workflow module
 * Optimizes common query patterns for better performance
 */
export class AddWorkflowIndexes1640995200000 implements MigrationInterface {
  name = 'AddWorkflowIndexes1640995200000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Workflows table indexes
    
    // Index for user workflows query (most common)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_user_id_updated_at" 
      ON "workflows" ("user_id", "updated_at" DESC)
    `);

    // Index for active workflows filter
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_user_id_is_active" 
      ON "workflows" ("user_id", "is_active")
    `);

    // Index for workflow search by name and description
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_name_gin" 
      ON "workflows" USING gin(to_tsvector('english', "name"))
    `);

    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_description_gin" 
      ON "workflows" USING gin(to_tsvector('english', "description"))
    `);

    // Index for tags search (GIN index for array operations)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_tags_gin" 
      ON "workflows" USING gin("tags")
    `);

    // Index for created date range queries
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_created_at" 
      ON "workflows" ("created_at")
    `);

    // Workflow Executions table indexes

    // Index for user executions query (using JSONB metadata)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_user_id_started_at" 
      ON "workflow_executions" ((metadata->>'userId'), "started_at" DESC)
    `);

    // Index for workflow executions
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_workflow_id_started_at" 
      ON "workflow_executions" ("workflow_id", "started_at" DESC)
    `);

    // Index for execution status queries
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_status_started_at" 
      ON "workflow_executions" ("status", "started_at" DESC)
    `);

    // Index for execution duration queries (for statistics)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_duration" 
      ON "workflow_executions" ("duration") 
      WHERE "duration" IS NOT NULL
    `);

    // Composite index for user + workflow + status queries
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_user_workflow_status" 
      ON "workflow_executions" ((metadata->>'userId'), "workflow_id", "status")
    `);

    // Index for finished executions (for statistics)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_finished_at" 
      ON "workflow_executions" ("finished_at") 
      WHERE "finished_at" IS NOT NULL
    `);

    // Node Definitions table indexes

    // Index for category-based queries
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_node_definition_category_name" 
      ON "node_definitions" ("category", "name")
    `);

    // Index for active node definitions
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_node_definition_is_deprecated" 
      ON "node_definitions" ("is_deprecated")
    `);

    // Index for version queries
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_node_definition_type_version" 
      ON "node_definitions" ("type", "version")
    `);

    // Index for tags search in node definitions
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_node_definition_tags_gin" 
      ON "node_definitions" USING gin("tags")
    `);

    // Workflow Nodes table indexes

    // Index for workflow nodes by workflow
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_node_workflow_id" 
      ON "workflow_nodes" ("workflow_id")
    `);

    // Index for node type queries
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_node_type" 
      ON "workflow_nodes" ("type")
    `);

    // Index for position-based queries (for canvas rendering)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_node_position" 
      ON "workflow_nodes" USING gist(
        (("position"->>'x')::numeric), 
        (("position"->>'y')::numeric)
      )
    `);

    // Workflow Edges table indexes

    // Index for workflow edges by workflow
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_edge_workflow_id" 
      ON "workflow_edges" ("workflow_id")
    `);

    // Index for source node queries
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_edge_source_node_id" 
      ON "workflow_edges" ("source_node_id")
    `);

    // Index for target node queries
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_edge_target_node_id" 
      ON "workflow_edges" ("target_node_id")
    `);

    // Workflow Execution Logs table indexes

    // Index for execution logs by execution
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_log_execution_id_timestamp" 
      ON "workflow_execution_logs" ("execution_id", "timestamp" DESC)
    `);

    // Index for log level queries
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_log_level" 
      ON "workflow_execution_logs" ("level")
    `);

    // Index for node-specific logs
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_log_node_id" 
      ON "workflow_execution_logs" ("node_id") 
      WHERE "node_id" IS NOT NULL
    `);

    // Index for error logs (for debugging)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_log_error" 
      ON "workflow_execution_logs" ("level", "timestamp" DESC) 
      WHERE "level" IN ('error', 'fatal')
    `);

    // Partial indexes for performance optimization

    // Index only for active workflows
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_active_updated_at" 
      ON "workflows" ("updated_at" DESC) 
      WHERE "is_active" = true
    `);

    // Index only for running executions
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_running" 
      ON "workflow_executions" ("started_at" DESC) 
      WHERE "status" IN ('queued', 'running', 'paused')
    `);

    // Index only for failed executions (for monitoring)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_failed" 
      ON "workflow_executions" ("started_at" DESC) 
      WHERE "status" = 'failed'
    `);

    // Composite indexes for complex queries

    // Index for user workflow statistics
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_stats" 
      ON "workflow_executions" (
        (metadata->>'userId'), 
        "status", 
        "started_at" DESC
      )
    `);

    // Index for workflow performance analysis
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_performance" 
      ON "workflow_executions" (
        "workflow_id", 
        "status", 
        "duration"
      ) 
      WHERE "duration" IS NOT NULL
    `);

    // JSONB indexes for metadata queries

    // Index for trigger event type
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_trigger_type" 
      ON "workflow_executions" USING gin((trigger_event->>'type'))
    `);

    // Index for execution metadata
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_execution_metadata_gin" 
      ON "workflow_executions" USING gin("metadata")
    `);

    // Index for node configuration searches
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_workflow_node_config_gin" 
      ON "workflow_nodes" USING gin("config")
    `);

    // Add comments for documentation
    await queryRunner.query(`
      COMMENT ON INDEX "IDX_workflow_user_id_updated_at" IS 
      'Optimizes user workflow list queries with sorting'
    `);

    await queryRunner.query(`
      COMMENT ON INDEX "IDX_workflow_execution_user_id_started_at" IS 
      'Optimizes user execution list queries using JSONB metadata'
    `);

    await queryRunner.query(`
      COMMENT ON INDEX "IDX_workflow_execution_stats" IS 
      'Optimizes execution statistics aggregation queries'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop all indexes in reverse order
    
    // JSONB indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_node_config_gin"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_metadata_gin"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_trigger_type"`);

    // Composite indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_performance"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_stats"`);

    // Partial indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_failed"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_running"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_active_updated_at"`);

    // Workflow Execution Logs indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_log_error"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_log_node_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_log_level"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_log_execution_id_timestamp"`);

    // Workflow Edges indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_edge_target_node_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_edge_source_node_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_edge_workflow_id"`);

    // Workflow Nodes indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_node_position"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_node_type"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_node_workflow_id"`);

    // Node Definitions indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definition_tags_gin"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definition_type_version"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definition_is_deprecated"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_node_definition_category_name"`);

    // Workflow Executions indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_finished_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_user_workflow_status"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_duration"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_status_started_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_workflow_id_started_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_execution_user_id_started_at"`);

    // Workflows indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_created_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_tags_gin"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_description_gin"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_name_gin"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_user_id_is_active"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_workflow_user_id_updated_at"`);
  }
}
