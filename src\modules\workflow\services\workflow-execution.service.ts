import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, FindManyOptions } from 'typeorm';
import { AppException } from '@/common';
import { WorkflowExecution } from '../entities';
import { ExecutionStatus } from '../constants';
import { WORKFLOW_ERROR_CODES } from '../constants/workflow-error-codes';

/**
 * Service quản lý workflow execution
 * Handles CRUD operations và business logic cho workflow executions
 */
@Injectable()
export class WorkflowExecutionService {
  private readonly logger = new Logger(WorkflowExecutionService.name);

  constructor(
    @InjectRepository(WorkflowExecution)
    private readonly workflowExecutionRepository: Repository<WorkflowExecution>,
  ) {}

  /**
   * Tạo mới workflow execution
   * @param data Dữ liệu execution
   * @returns Promise với execution đã tạo
   */
  async create(data: Partial<WorkflowExecution>): Promise<WorkflowExecution> {
    this.logger.log(`Creating new workflow execution for workflow: ${data.workflowId}`);

    const execution = this.workflowExecutionRepository.create({
      ...data,
      status: data.status || ExecutionStatus.QUEUED,
      startedAt: data.startedAt || Date.now(),
      retryCount: data.retryCount || 0,
      maxRetries: data.maxRetries || 3,
    });

    const savedExecution = await this.workflowExecutionRepository.save(execution);
    this.logger.log(`Created workflow execution: ${savedExecution.id}`);

    return savedExecution;
  }

  /**
   * Lấy execution theo ID
   * @param id ID của execution
   * @returns Promise với execution
   */
  async findById(id: string): Promise<WorkflowExecution> {
    const execution = await this.workflowExecutionRepository.findOne({
      where: { id },
    });

    if (!execution) {
      throw new NotFoundException(`Workflow execution ${id} not found`);
    }

    return execution;
  }

  /**
   * Lấy danh sách executions với pagination
   * @param options Tùy chọn query
   * @returns Promise với danh sách executions
   */
  async findMany(options: FindManyOptions<WorkflowExecution> = {}): Promise<{
    executions: WorkflowExecution[];
    total: number;
  }> {
    const [executions, total] = await this.workflowExecutionRepository.findAndCount({
      ...options,
      order: {
        startedAt: 'DESC',
        ...options.order,
      },
    });

    return { executions, total };
  }

  /**
   * Lấy executions theo workflow ID
   * @param workflowId ID của workflow
   * @param options Tùy chọn query
   * @returns Promise với danh sách executions
   */
  async findByWorkflowId(
    workflowId: string,
    options: Omit<FindManyOptions<WorkflowExecution>, 'where'> = {},
  ): Promise<{
    executions: WorkflowExecution[];
    total: number;
  }> {
    return this.findMany({
      ...options,
      where: {
        workflowId,
      },
    });
  }

  /**
   * Lấy executions theo status
   * @param status Status cần filter
   * @param options Tùy chọn query
   * @returns Promise với danh sách executions
   */
  async findByStatus(
    status: ExecutionStatus,
    options: Omit<FindManyOptions<WorkflowExecution>, 'where'> = {},
  ): Promise<{
    executions: WorkflowExecution[];
    total: number;
  }> {
    return this.findMany({
      ...options,
      where: {
        status,
      },
    });
  }

  /**
   * Cập nhật execution status
   * @param id ID của execution
   * @param status Status mới
   * @param additionalData Dữ liệu bổ sung
   * @returns Promise với execution đã cập nhật
   */
  async updateStatus(
    id: string,
    status: ExecutionStatus,
    additionalData?: {
      result?: any;
      error?: any;
      metadata?: any;
    },
  ): Promise<WorkflowExecution> {
    this.logger.log(`Updating execution ${id} status to: ${status}`);

    const execution = await this.findById(id);

    execution.status = status;

    // Cập nhật thời gian kết thúc nếu execution hoàn thành
    if ([ExecutionStatus.COMPLETED, ExecutionStatus.FAILED, ExecutionStatus.CANCELLED].includes(status)) {
      execution.finish(status as any, additionalData?.result, additionalData?.error);
    }

    // Cập nhật metadata nếu có
    if (additionalData?.metadata) {
      execution.metadata = {
        ...execution.metadata,
        ...additionalData.metadata,
      };
    }

    const updatedExecution = await this.workflowExecutionRepository.save(execution);
    this.logger.log(`Updated execution ${id} status to: ${status}`);

    return updatedExecution;
  }

  /**
   * Bắt đầu execution (chuyển từ QUEUED sang RUNNING)
   * @param id ID của execution
   * @returns Promise với execution đã cập nhật
   */
  async start(id: string): Promise<WorkflowExecution> {
    this.logger.log(`Starting execution: ${id}`);

    const execution = await this.findById(id);

    if (execution.status !== ExecutionStatus.QUEUED) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.INVALID_STATUS,
        `Cannot start execution ${id}. Current status: ${execution.status}`,
      );
    }

    return this.updateStatus(id, ExecutionStatus.RUNNING, {
      metadata: {
        ...execution.metadata,
        actualStartedAt: Date.now(),
      },
    });
  }

  /**
   * Hoàn thành execution thành công
   * @param id ID của execution
   * @param result Kết quả execution
   * @returns Promise với execution đã cập nhật
   */
  async complete(id: string, result?: any): Promise<WorkflowExecution> {
    this.logger.log(`Completing execution: ${id}`);

    return this.updateStatus(id, ExecutionStatus.COMPLETED, { result });
  }

  /**
   * Đánh dấu execution thất bại
   * @param id ID của execution
   * @param error Thông tin lỗi
   * @returns Promise với execution đã cập nhật
   */
  async fail(id: string, error: any): Promise<WorkflowExecution> {
    this.logger.error(`Failing execution ${id}:`, error);

    return this.updateStatus(id, ExecutionStatus.FAILED, { error });
  }

  /**
   * Hủy execution
   * @param id ID của execution
   * @param reason Lý do hủy
   * @returns Promise với execution đã cập nhật
   */
  async cancel(id: string, reason?: string): Promise<WorkflowExecution> {
    this.logger.log(`Cancelling execution ${id}. Reason: ${reason || 'No reason provided'}`);

    const execution = await this.findById(id);

    if (execution.isCompleted()) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.INVALID_STATUS,
        `Cannot cancel completed execution ${id}. Current status: ${execution.status}`,
      );
    }

    return this.updateStatus(id, ExecutionStatus.CANCELLED, {
      metadata: {
        ...execution.metadata,
        cancelReason: reason,
        cancelledAt: Date.now(),
      },
    });
  }

  /**
   * Pause execution
   * @param id ID của execution
   * @returns Promise với execution đã cập nhật
   */
  async pause(id: string): Promise<WorkflowExecution> {
    this.logger.log(`Pausing execution: ${id}`);

    const execution = await this.findById(id);

    if (execution.status !== ExecutionStatus.RUNNING) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.INVALID_STATUS,
        `Cannot pause execution ${id}. Current status: ${execution.status}`,
      );
    }

    return this.updateStatus(id, ExecutionStatus.PAUSED, {
      metadata: {
        ...execution.metadata,
        pausedAt: Date.now(),
      },
    });
  }

  /**
   * Resume execution từ PAUSED
   * @param id ID của execution
   * @returns Promise với execution đã cập nhật
   */
  async resume(id: string): Promise<WorkflowExecution> {
    this.logger.log(`Resuming execution: ${id}`);

    const execution = await this.findById(id);

    if (execution.status !== ExecutionStatus.PAUSED) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.INVALID_STATUS,
        `Cannot resume execution ${id}. Current status: ${execution.status}`,
      );
    }

    return this.updateStatus(id, ExecutionStatus.RUNNING, {
      metadata: {
        ...execution.metadata,
        resumedAt: Date.now(),
      },
    });
  }

  /**
   * Retry execution đã failed
   * @param id ID của execution
   * @returns Promise với execution mới (retry)
   */
  async retry(id: string): Promise<WorkflowExecution> {
    this.logger.log(`Retrying execution: ${id}`);

    const originalExecution = await this.findById(id);

    if (!originalExecution.canRetry()) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.INVALID_STATUS,
        `Cannot retry execution ${id}. Status: ${originalExecution.status}, Retry count: ${originalExecution.retryCount}/${originalExecution.maxRetries}`,
      );
    }

    // Tạo execution mới cho retry
    const retryExecution = await this.create({
      workflowId: originalExecution.workflowId,
      triggerEvent: originalExecution.triggerEvent,
      parentExecutionId: originalExecution.id,
      retryCount: originalExecution.retryCount + 1,
      maxRetries: originalExecution.maxRetries,
      metadata: {
        ...originalExecution.metadata,
        isRetry: true,
        originalExecutionId: originalExecution.id,
        retryReason: 'Manual retry',
      },
    });

    this.logger.log(`Created retry execution ${retryExecution.id} for original execution ${id}`);

    return retryExecution;
  }

  /**
   * Xóa execution (soft delete bằng cách đánh dấu)
   * @param id ID của execution
   * @returns Promise với execution đã xóa
   */
  async delete(id: string): Promise<WorkflowExecution> {
    this.logger.log(`Deleting execution: ${id}`);

    const execution = await this.findById(id);

    if (execution.isRunning()) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.INVALID_STATUS,
        `Cannot delete running execution ${id}`,
      );
    }

    // Soft delete bằng cách đánh dấu trong metadata
    return this.updateStatus(id, execution.status, {
      metadata: {
        ...execution.metadata,
        deleted: true,
        deletedAt: Date.now(),
      },
    });
  }

  /**
   * Lấy execution statistics
   * @param workflowId ID của workflow (optional)
   * @returns Promise với statistics
   */
  async getStatistics(workflowId?: string): Promise<{
    total: number;
    byStatus: Record<ExecutionStatus, number>;
    avgDuration: number | null;
    successRate: number;
  }> {
    const whereCondition: FindOptionsWhere<WorkflowExecution> = {};
    if (workflowId) {
      whereCondition.workflowId = workflowId;
    }

    const executions = await this.workflowExecutionRepository.find({
      where: whereCondition,
      select: ['status', 'duration'],
    });

    const total = executions.length;
    const byStatus = executions.reduce((acc, execution) => {
      acc[execution.status] = (acc[execution.status] || 0) + 1;
      return acc;
    }, {} as Record<ExecutionStatus, number>);

    const completedExecutions = executions.filter(e => e.duration !== null);
    const avgDuration = completedExecutions.length > 0
      ? completedExecutions.reduce((sum, e) => sum + (e.duration || 0), 0) / completedExecutions.length
      : null;

    const successfulExecutions = executions.filter(e => e.status === ExecutionStatus.COMPLETED).length;
    const successRate = total > 0 ? (successfulExecutions / total) * 100 : 0;

    return {
      total,
      byStatus,
      avgDuration,
      successRate,
    };
  }
}
