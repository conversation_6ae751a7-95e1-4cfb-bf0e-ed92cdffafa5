import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@/common/dto/query.dto';

/**
 * Enum cho các trường có thể sắp xếp
 */
export enum TemplateEmailSortBy {
  ID = 'id',
  NAME = 'name',
  SUBJECT = 'subject',
  CATEGORY = 'category',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho query parameters khi lấy danh sách template email
 */
export class TemplateEmailQueryDto extends QueryDto {

  /**
   * Tìm kiếm theo category
   * @example "ACCOUNT_VERIFICATION"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo category',
    example: 'ACCOUNT_VERIFICATION',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Category phải là chuỗi' })
  category?: string;

  /**
   * Tìm kiếm theo subject
   * @example "Xác thực tài khoản"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo subject',
    example: 'Xác thực tài khoản',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Subject phải là chuỗi' })
  subject?: string;

  /**
   * Trường sắp xếp
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: TemplateEmailSortBy,
    example: TemplateEmailSortBy.CREATED_AT,
    default: TemplateEmailSortBy.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(TemplateEmailSortBy, { message: 'Trường sắp xếp không hợp lệ' })
  sortBy?: TemplateEmailSortBy = TemplateEmailSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection, { message: 'Hướng sắp xếp không hợp lệ' })
  sortDirection?: SortDirection = SortDirection.DESC;
}
