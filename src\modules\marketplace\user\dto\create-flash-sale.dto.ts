import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  Max,
  ValidateNested,
  IsObject,
  IsEnum
} from 'class-validator';

// Reuse the same DTOs from admin since validation logic is identical
import { MaxConfigurationDto } from '../../admin/dto/create-flash-sale.dto';
import { FlashSaleStatus } from '../../enums/flash-sale-status.enum';

/**
 * DTO để tạo flash sale mới (User)
 */
export class CreateFlashSaleDto {
  @ApiProperty({
    description: 'ID sản phẩm áp dụng flash sale',
    example: 123
  })
  @IsNotEmpty()
  @IsNumber()
  productId: number;

  @ApiProperty({
    description: 'Phần trăm giảm giá (1-99%)',
    example: 20,
    minimum: 1,
    maximum: 99
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(99)
  discountPercentage: number;

  @ApiProperty({
    description: 'Thời gian hiển thị sản phẩm flash sale cho user (giây, 1-60). Tạo cảm giác khan hiếm - sản phẩm chỉ hiển thị trong khoảng thời gian này',
    example: 30,
    minimum: 1,
    maximum: 60
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(60)
  displayTime: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu flash sale (timestamp milliseconds)',
    example: 1641081600000
  })
  @IsNotEmpty()
  @IsNumber()
  startTime: number;

  @ApiProperty({
    description: 'Thời gian kết thúc flash sale (timestamp milliseconds)',
    example: 1641168000000
  })
  @IsNotEmpty()
  @IsNumber()
  endTime: number;

  @ApiPropertyOptional({
    description: 'Trạng thái flash sale (mặc định: DRAFT). User có thể chọn DRAFT để lưu nháp hoặc SCHEDULED để lên lịch ngay',
    enum: FlashSaleStatus,
    example: FlashSaleStatus.DRAFT,
    default: FlashSaleStatus.DRAFT
  })
  @IsOptional()
  @IsEnum(FlashSaleStatus, {
    message: 'Trạng thái phải là DRAFT, SCHEDULED hoặc CANCELLED'
  })
  status?: FlashSaleStatus;

  @ApiPropertyOptional({
    description: 'Cấu hình giới hạn flash sale',
    type: MaxConfigurationDto
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => MaxConfigurationDto)
  maxConfiguration?: MaxConfigurationDto;
}
