# Cập Nhật Zalo OAuth API Hỗ Trợ PKCE

## Tổng Quan

Đã cập nhật API Zalo OAuth để tuân thủ đầy đủ tài liệu Zalo OAuth v4 và hỗ trợ PKCE (Proof Key for Code Exchange) để tăng cường bảo mật.

## Các Thay Đổi Chính

### 1. Hỗ Trợ PKCE (Proof Key for Code Exchange)

#### Thêm Utility Functions
- `generateCodeVerifier()`: Tạo code verifier ngẫu nhiên
- `generateCodeChallenge(codeVerifier)`: Tạo code challenge từ code verifier bằng SHA-256
- `generatePKCEPair()`: Tạo cặp code verifier và code challenge

#### Cập Nhật API Endpoints

**GET /auth/zalo/auth-url**
- Thêm query parameter `enablePKCE` (boolean, mặc định: false)
- Khi `enablePKCE=true`, response bao gồm cả `codeVerifier` cần lưu trữ

```typescript
// Response không PKCE
{
  "code": 200,
  "message": "Lấy URL xác thực Zalo thành công",
  "result": {
    "url": "https://oauth.zaloapp.com/v4/permission?app_id=..."
  }
}

// Response với PKCE
{
  "code": 200,
  "message": "Lấy URL xác thực Zalo với PKCE thành công",
  "result": {
    "url": "https://oauth.zaloapp.com/v4/permission?app_id=...&code_challenge=...",
    "codeVerifier": "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk"
  }
}
```

**POST /auth/zalo/login**
- Thêm field `codeVerifier` (string, tùy chọn) trong request body

### 2. Tuân Thủ Tài Liệu Zalo OAuth v4

#### URL Authorization Endpoint
```
https://oauth.zaloapp.com/v4/permission?app_id=<APP_ID>&redirect_uri=<CALLBACK_URL>&code_challenge=<CODE_CHALLENGE>&state=<STATE>
```

#### Các Tham Số Hỗ Trợ
- `app_id` (bắt buộc): ID của ứng dụng
- `redirect_uri` (bắt buộc): URL chuyển hướng
- `state` (bắt buộc): Token bảo mật chống CSRF
- `code_challenge` (tùy chọn): Code challenge cho PKCE
- `scope` (mặc định): id,name,picture

### 3. Cập Nhật Cấu Trúc Code

#### Files Được Cập Nhật
- `src/shared/services/zalo/zalo-social.service.ts`: Thêm PKCE utilities và cập nhật createAuthUrl
- `src/shared/services/zalo/zalo.service.ts`: Cập nhật getSocialAccessToken hỗ trợ code_verifier
- `src/modules/auth/service/oauth2.service.ts`: Cập nhật generateZaloAuthUrl và handleZaloLogin
- `src/modules/auth/service/auth.service.ts`: Cập nhật generateZaloAuthUrl và loginWithZalo
- `src/modules/auth/controller/auth.controller.ts`: Cập nhật getZaloAuthUrl endpoint
- `src/modules/auth/dto/zalo-auth.dto.ts`: Thêm codeVerifier field

## Cách Sử Dụng

### Flow Thông Thường (Không PKCE)
```typescript
// 1. Lấy URL xác thực
GET /auth/zalo/auth-url

// 2. Chuyển hướng user đến URL
// 3. User quay lại với authorization code
// 4. Đăng nhập
POST /auth/zalo/login
{
  "code": "authorization_code_from_zalo",
  "redirectUri": "https://example.com/callback"
}
```

### Flow PKCE (Khuyến Nghị)
```typescript
// 1. Lấy URL xác thực với PKCE
GET /auth/zalo/auth-url?enablePKCE=true

// 2. Lưu trữ codeVerifier từ response
// 3. Chuyển hướng user đến URL
// 4. User quay lại với authorization code
// 5. Đăng nhập với codeVerifier
POST /auth/zalo/login
{
  "code": "authorization_code_from_zalo",
  "redirectUri": "https://example.com/callback",
  "codeVerifier": "saved_code_verifier_from_step_1"
}
```

## Lợi Ích PKCE

1. **Bảo Mật Cao Hơn**: Ngăn chặn authorization code interception attacks
2. **Không Cần Client Secret**: Phù hợp cho mobile apps và SPAs
3. **Tuân Thủ OAuth 2.1**: Chuẩn bảo mật mới nhất

## Backward Compatibility

API vẫn hỗ trợ flow cũ (không PKCE) để đảm bảo tương thích ngược. PKCE chỉ được kích hoạt khi `enablePKCE=true`.

## Testing

Sử dụng file test: `src/modules/auth/test-examples/test-zalo-oauth-pkce.http`
