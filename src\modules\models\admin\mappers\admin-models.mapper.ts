import { ModelData } from '../../repositories/models.repository';
import { AdminModelsResponseDto } from '../dto/admin-models';

/**
 * Mapper cho Admin Models
 */
export class AdminModelsMapper {
    /**
     * Convert ModelData sang AdminModelsResponseDto
     */
    static toResponseDto(modelData: ModelData): AdminModelsResponseDto {
        return {
            id: modelData.id,
            modelName: modelData.modelName,
            inputModalities: modelData.inputModalities,
            outputModalities: modelData.outputModalities,
            samplingParameters: modelData.samplingParameters,
            features: modelData.features,
            basePricing: modelData.basePricing,
            fineTunePricing: modelData.fineTunePricing,
            trainingPricing: modelData.trainingPricing,
            maxTokens: modelData.maxTokens,
            contextWindow: modelData.contextWindow
        };
    }

    /**
     * Convert danh sách ModelData sang AdminModelsResponseDto
     */
    static toResponseDtoList(modelDataList: ModelData[]): AdminModelsResponseDto[] {
        return modelDataList.map(modelData => this.toResponseDto(modelData));
    }
}
