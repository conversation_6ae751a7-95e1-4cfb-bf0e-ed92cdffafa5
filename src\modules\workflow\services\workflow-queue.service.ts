import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { AppException, ErrorCode } from '@/common';
import {
  QueueName,
  WorkflowExecutionJobName,
  // WorkflowNodeTestJobName removed - focusing on real execution only
  DEFAULT_JOB_OPTIONS,
  HIGH_PRIORITY_JOB_OPTIONS,
} from '@/shared/queue/queue.constants';
import {
  JobOptions,
  WorkflowExecutionJobData,
  WorkflowNodeExecutionJobData,
  // WorkflowNodeTestJobData removed - focusing on real execution only
} from '@/shared/queue/queue.types';

/**
 * Service quản lý workflow queue operations
 * Handles workflow execution jobs only - node testing removed
 */
@Injectable()
export class WorkflowQueueService {
  private readonly logger = new Logger(WorkflowQueueService.name);

  constructor(
    @InjectQueue(QueueName.WORKFLOW_EXECUTION)
    private readonly workflowExecutionQueue: Queue,
    // Node test queue removed - focusing on real execution only
  ) {}

  /**
   * Thêm job thực thi workflow vào queue
   * @param data Dữ liệu workflow execution
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addWorkflowExecutionJob(
    data: WorkflowExecutionJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      this.validateWorkflowExecutionJobData(data);

      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.workflowExecutionQueue.add(
        WorkflowExecutionJobName.EXECUTE_WORKFLOW,
        data,
        options,
      );

      this.logger.log(
        `Added workflow execution job to queue: ${job.id} - Workflow: ${data.workflowId} - User: ${data.userId}`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Failed to add workflow execution job to queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Cannot add workflow execution job to queue',
      );
    }
  }

  /**
   * Thêm job thực thi workflow với priority cao
   * @param data Dữ liệu workflow execution
   * @returns Promise với ID của job đã tạo
   */
  async addHighPriorityWorkflowExecutionJob(
    data: WorkflowExecutionJobData,
  ): Promise<string | undefined> {
    return this.addWorkflowExecutionJob(data, HIGH_PRIORITY_JOB_OPTIONS);
  }

  /**
   * Thêm job thực thi workflow với delay
   * @param data Dữ liệu workflow execution
   * @param delayMs Thời gian delay (milliseconds)
   * @returns Promise với ID của job đã tạo
   */
  async addDelayedWorkflowExecutionJob(
    data: WorkflowExecutionJobData,
    delayMs: number,
  ): Promise<string | undefined> {
    const delayedOptions: JobOptions = {
      ...DEFAULT_JOB_OPTIONS,
      delay: delayMs,
    };
    return this.addWorkflowExecutionJob(data, delayedOptions);
  }

  /**
   * Thêm job thực thi node đơn lẻ vào queue
   * @param data Dữ liệu node execution
   * @param opts Tùy chọn job
   * @returns Promise với ID của job đã tạo
   */
  async addNodeExecutionJob(
    data: WorkflowNodeExecutionJobData,
    opts?: JobOptions,
  ): Promise<string | undefined> {
    try {
      this.validateNodeExecutionJobData(data);

      const options = opts || DEFAULT_JOB_OPTIONS;
      const job = await this.workflowExecutionQueue.add(
        WorkflowExecutionJobName.EXECUTE_NODE,
        data,
        options,
      );

      this.logger.log(
        `Added node execution job to queue: ${job.id} - Node: ${data.nodeId} - Type: ${data.nodeType}`,
      );
      return job.id;
    } catch (error) {
      this.logger.error(
        `Failed to add node execution job to queue: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Cannot add node execution job to queue',
      );
    }
  }

  // Node test methods removed - focusing on real execution only

  /**
   * Lấy trạng thái job workflow execution
   * @param jobId ID của job
   * @returns Promise với thông tin job
   */
  async getWorkflowExecutionJobStatus(jobId: string): Promise<any> {
    try {
      return await this.workflowExecutionQueue.getJob(jobId);
    } catch (error) {
      this.logger.error(`Failed to get workflow execution job ${jobId}: ${error.message}`, error.stack);
      return null;
    }
  }

  // Node test job status method removed

  /**
   * Hủy job workflow execution
   * @param jobId ID của job
   * @returns Promise<boolean>
   */
  async cancelWorkflowExecutionJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.workflowExecutionQueue.getJob(jobId);
      if (job) {
        await job.remove();
        this.logger.log(`Cancelled workflow execution job: ${jobId}`);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`Failed to cancel workflow execution job ${jobId}: ${error.message}`, error.stack);
      return false;
    }
  }

  // Node test job cancellation method removed

  /**
   * Lấy thống kê workflow execution queue
   * @returns Promise với thống kê queue
   */
  async getWorkflowExecutionQueueStats(): Promise<any> {
    try {
      const waiting = await this.workflowExecutionQueue.getWaiting();
      const active = await this.workflowExecutionQueue.getActive();
      const completed = await this.workflowExecutionQueue.getCompleted();
      const failed = await this.workflowExecutionQueue.getFailed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        total: waiting.length + active.length + completed.length + failed.length,
      };
    } catch (error) {
      this.logger.error(`Failed to get workflow execution queue stats: ${error.message}`, error.stack);
      return null;
    }
  }

  // Node test queue stats method removed

  /**
   * Validate workflow execution job data
   * @private
   */
  private validateWorkflowExecutionJobData(data: WorkflowExecutionJobData): void {
    if (!data.executionId) {
      throw new Error('executionId is required');
    }
    if (!data.workflowId) {
      throw new Error('workflowId is required');
    }
    if (!data.userId) {
      throw new Error('userId is required');
    }
    if (!data.triggerType) {
      throw new Error('triggerType is required');
    }
  }

  /**
   * Validate node execution job data
   * @private
   */
  private validateNodeExecutionJobData(data: WorkflowNodeExecutionJobData): void {
    if (!data.executionId) {
      throw new Error('executionId is required');
    }
    if (!data.nodeId) {
      throw new Error('nodeId is required');
    }
    if (!data.nodeType) {
      throw new Error('nodeType is required');
    }
  }

  // Node test validation method removed
}
