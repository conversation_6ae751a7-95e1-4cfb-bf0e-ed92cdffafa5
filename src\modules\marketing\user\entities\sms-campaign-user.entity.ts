import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { SmsCampaignType } from '../../enums/sms-campaign-type.enum';

/**
 * Interface cho cấu hình SMS integration
 */
export interface SmsIntegrationConfig {
  id: string;
  integrationName: string;
  typeId: number;
  metadata?: Record<string, any>;
  provider?: string; // Legacy field for backward compatibility
  providerName?: string; // Primary provider field
  endpoint?: string;
  additionalSettings?: Record<string, any>;
}

/**
 * Interface cho cấu hình SMS template
 */
export interface SmsTemplateConfig {
  id: number;
  name: string;
  content: string;
  customContent?: string;
  variables?: Record<string, any>;
  isActive: boolean;
}

/**
 * Interface cho cấu hình segment
 */
export interface SegmentConfig {
  id: number;
  name: string;
  description?: string;
  conditions?: Record<string, any>;
  audienceCount?: number;
}

/**
 * Enum trạng thái SMS campaign
 */
export enum SmsCampaignStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  SENDING = 'SENDING',
  SENT = 'SENT',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  PENDING = 'PENDING',
}

/**
 * Entity đại diện cho bảng sms_campaign_user trong cơ sở dữ liệu
 * Lưu trữ các chiến dịch SMS marketing của user
 */
@Entity('sms_campaign_user')
export class SmsCampaignUser {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của user tạo campaign
   */
  @Column({ name: 'user_id', type: 'bigint' })
  userId: number;

  /**
   * Tên campaign
   */
  @Column({ name: 'name', type: 'varchar', length: 255 })
  name: string;

  /**
   * Mô tả campaign
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Nội dung SMS
   */
  @Column({ name: 'content', type: 'text' })
  content: string;

  /**
   * Cấu hình SMS integration dạng JSON
   */
  @Column({ name: 'sms_integration_config', type: 'jsonb', nullable: true })
  smsIntegrationConfig: SmsIntegrationConfig | null;

  /**
   * Cấu hình template SMS dạng JSON
   */
  @Column({ name: 'template_config', type: 'jsonb', nullable: true })
  templateConfig: SmsTemplateConfig | null;

  /**
   * 
   * Cấu hình segment dạng JSON
   */
  @Column({ name: 'segment_config', type: 'jsonb', nullable: true })
  segmentConfig: SegmentConfig | null;

  /**
   * Danh sách đối tượng nhận SMS (lưu trữ trực tiếp dưới dạng JSON)
   * Bao gồm: name, phoneNumber, countryCode
   */
  @Column({ name: 'audiences', type: 'jsonb', nullable: true })
  audiences: Array<{
    name: string;
    phoneNumber: string;
    countryCode: number;
  }> | null;

  /**
   * Template variables
   */
  @Column({ name: 'template_variables', type: 'jsonb', nullable: true })
  templateVariables: Record<string, any> | null;

  /**
   * Loại chiến dịch SMS (OTP hoặc ADS)
   */
  @Column({ name: 'campaign_type', type: 'varchar', length: 10, default: SmsCampaignType.ADS })
  campaignType: SmsCampaignType;

  /**
   * Trạng thái campaign
   */
  @Column({ name: 'status', type: 'varchar', length: 50, default: SmsCampaignStatus.DRAFT })
  status: SmsCampaignStatus;

  /**
   * Thời gian lên lịch gửi (Unix timestamp)
   */
  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true })
  scheduledAt: number | null;

  /**
   * Thời gian bắt đầu gửi (Unix timestamp)
   */
  @Column({ name: 'started_at', type: 'bigint', nullable: true })
  startedAt: number | null;

  /**
   * Thời gian hoàn thành (Unix timestamp)
   */
  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt: number | null;

  /**
   * Tổng số người nhận
   */
  @Column({ name: 'total_recipients', type: 'integer', default: 0 })
  totalRecipients: number;

  /**
   * Số SMS đã gửi thành công
   */
  @Column({ name: 'sent_count', type: 'integer', default: 0 })
  sentCount: number;

  /**
   * Số SMS gửi thất bại
   */
  @Column({ name: 'failed_count', type: 'integer', default: 0 })
  failedCount: number;

  /**
   * Danh sách job IDs trong queue
   */
  @Column({ name: 'job_ids', type: 'jsonb', nullable: true })
  jobIds: string[] | null;

  /**
   * Mã campaign từ hệ thống bên ngoài (FPT SMS, etc.)
   */
  @Column({ name: 'external_campaign_code', type: 'varchar', length: 255, nullable: true })
  externalCampaignCode: string | null;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
