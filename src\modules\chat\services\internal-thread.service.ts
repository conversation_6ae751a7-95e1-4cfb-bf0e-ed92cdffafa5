import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, In, Raw, ILike } from 'typeorm';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions';

// Import entities
import { InternalConversationThread } from '../entities/internal-conversation-thread.entity';

// Import DTOs
import {
  QueryThreadsDto,
  CreateThreadDto,
  ThreadResponseDto,
  ThreadSortBy,
} from '../dto';
import {
  DeleteThreadsDto,
  DeleteThreadsResponseDto,
} from '../dto/delete-threads.dto';

// Import error codes
import { CHAT_ERROR_CODES } from '../exceptions';

/**
 * Service for managing internal conversation threads
 * Supports both user and employee ownership
 */
@Injectable()
export class InternalThreadService {
  private readonly logger = new Logger(InternalThreadService.name);

  constructor(
    @InjectRepository(InternalConversationThread)
    private readonly threadRepository: Repository<InternalConversationThread>,
  ) {}

  /**
   * Get threads for a user with pagination
   */
  async getUserThreads(
    userId: number,
    queryDto: QueryThreadsDto,
  ): Promise<PaginatedResult<ThreadResponseDto>> {
    this.logger.debug(`Getting user threads for userId: ${userId}`, {
      queryDto,
    });

    const {
      page = 1,
      limit = 10,
      sortBy = ThreadSortBy.UPDATED_AT,
      sortDirection = 'DESC',
    } = queryDto;
    const skip = (page - 1) * limit;

    const whereClause = {
      userId,
      deletedAt: IsNull(),
      ...(queryDto.search && {
        title: Raw(
          (alias) => `f_unaccent(${alias}) ILIKE f_unaccent(:search)`,
          {
            search: `%${queryDto.search.trim()}%`,
          },
        ),
      }),
    };

    this.logger.debug(
      `Query parameters: page=${page}, limit=${limit}, sortBy=${sortBy}, sortDirection=${sortDirection}, skip=${skip}`,
    );
    const [threads, totalItems] = await this.threadRepository.findAndCount({
      where: whereClause,
      order: {
        [sortBy]: sortDirection as 'ASC' | 'DESC',
      },
      skip,
      take: limit,
    });

    this.logger.debug(
      `Found ${threads.length} threads out of ${totalItems} total for user ${userId}`,
    );

    const items = threads.map((thread) => {
      return {
        id: thread.id,
        title: thread.title,
        updatedAt: parseInt(thread.updatedAt),
      };
    });

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Get threads for an employee with pagination
   */
  async getEmployeeThreads(
    employeeId: number,
    queryDto: QueryThreadsDto,
  ): Promise<PaginatedResult<ThreadResponseDto>> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'updatedAt',
      sortDirection = 'DESC',
    } = queryDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.threadRepository
      .createQueryBuilder('thread')
      .where('thread.employeeId = :employeeId', { employeeId })
      .andWhere('thread.deletedAt IS NULL') // Filter out soft deleted threads
      .orderBy(`thread.${sortBy}`, sortDirection as 'ASC' | 'DESC')
      .skip(skip)
      .take(limit);

    const [threads, totalItems] = await queryBuilder.getManyAndCount();

    const items = threads.map((thread) => {
      return {
        id: thread.id,
        title: thread.title,
        updatedAt: parseInt(thread.updatedAt),
      };
    });

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Create a new thread for a user
   */
  async createUserThread(
    userId: number,
    createThreadDto: CreateThreadDto,
  ): Promise<ThreadResponseDto> {
    this.logger.debug(`Creating new thread for userId: ${userId}`, {
      title: createThreadDto.title,
    });

    const thread = this.threadRepository.create({
      title: createThreadDto.title,
      userId,
      updatedAt: `${Date.now()}`,
    });

    const savedThread = await this.threadRepository.save(thread);

    this.logger.debug(
      `Successfully created thread ${savedThread.id} for user ${userId}`,
    );

    return {
      id: savedThread.id,
      title: savedThread.title,
      updatedAt: parseInt(savedThread.updatedAt),
    };
  }

  /**
   * Create a new thread for an employee
   */
  async createEmployeeThread(
    employeeId: number,
    createThreadDto: CreateThreadDto,
  ): Promise<ThreadResponseDto> {
    const thread = this.threadRepository.create({
      title: createThreadDto.title,
      employeeId,
      updatedAt: `${Date.now()}`,
    });

    const savedThread = await this.threadRepository.save(thread);

    return {
      id: savedThread.id,
      title: savedThread.title,
      updatedAt: parseInt(savedThread.updatedAt),
    };
  }

  /**
   * Get a specific thread by ID with ownership validation
   */
  async getThreadById(
    threadId: string,
    userId?: number,
    employeeId?: number,
  ): Promise<ThreadResponseDto> {
    this.logger.debug(
      `Getting thread by ID: ${threadId} for userId: ${userId}, employeeId: ${employeeId}`,
    );

    const thread = await this.threadRepository.findOne({
      where: {
        id: threadId,
        deletedAt: IsNull(), // Only find non-deleted threads
      },
    });

    if (!thread) {
      this.logger.warn(`Thread not found: ${threadId}`);
      throw new AppException(CHAT_ERROR_CODES.THREAD_NOT_FOUND);
    }

    this.logger.debug(
      `Found thread: ${thread.id}, userId: ${thread.userId}, employeeId: ${thread.employeeId}`,
    );

    // Check ownership
    const isUserOwner = userId && thread.userId === userId;
    const isEmployeeOwner = employeeId && thread.employeeId === employeeId;

    if (!isUserOwner && !isEmployeeOwner) {
      this.logger.warn(
        `Access denied for thread ${threadId}. User ${userId} or Employee ${employeeId} does not own this thread`,
      );
      throw new AppException(CHAT_ERROR_CODES.THREAD_ACCESS_DENIED);
    }

    this.logger.debug(`Successfully retrieved thread ${threadId}`);

    return {
      id: thread.id,
      title: thread.title,
      updatedAt: parseInt(thread.updatedAt),
    };
  }

  /**
   * Update a thread with ownership validation
   */
  async updateThread(
    threadId: string,
    updateThreadDto: CreateThreadDto,
    userId?: number,
    employeeId?: number,
  ): Promise<ThreadResponseDto> {
    const thread = await this.threadRepository.findOne({
      where: {
        id: threadId,
        deletedAt: IsNull(), // Only find non-deleted threads
      },
    });

    if (!thread) {
      throw new AppException(CHAT_ERROR_CODES.THREAD_NOT_FOUND);
    }

    // Check ownership
    const isUserOwner = userId && thread.userId === userId;
    const isEmployeeOwner = employeeId && thread.employeeId === employeeId;

    if (!isUserOwner && !isEmployeeOwner) {
      throw new AppException(CHAT_ERROR_CODES.THREAD_ACCESS_DENIED);
    }

    // Update thread
    thread.title = updateThreadDto.title;
    thread.updatedAt = `${Date.now()}`;

    const updatedThread = await this.threadRepository.save(thread);

    return {
      id: updatedThread.id,
      title: updatedThread.title,
      updatedAt: parseInt(updatedThread.updatedAt),
    };
  }

  /**
   * Delete a thread with ownership validation
   */
  async deleteThread(
    threadId: string,
    userId?: number,
    employeeId?: number,
  ): Promise<void> {
    this.logger.debug(
      `Deleting thread ${threadId} for userId: ${userId}, employeeId: ${employeeId}`,
    );

    const thread = await this.threadRepository.findOne({
      where: {
        id: threadId,
        deletedAt: IsNull(), // Only find non-deleted threads
      },
    });

    if (!thread) {
      this.logger.warn(`Thread not found: ${threadId}`);
      throw new AppException(CHAT_ERROR_CODES.THREAD_NOT_FOUND);
    }

    this.logger.debug(
      `Found thread: ${thread.id}, userId: ${thread.userId}, employeeId: ${thread.employeeId}`,
    );

    // Check ownership
    const isUserOwner = userId && thread.userId === userId;
    const isEmployeeOwner = employeeId && thread.employeeId === employeeId;

    if (!isUserOwner && !isEmployeeOwner) {
      this.logger.warn(
        `Access denied for thread ${threadId}. User ${userId} or Employee ${employeeId} does not own this thread`,
      );
      throw new AppException(CHAT_ERROR_CODES.THREAD_ACCESS_DENIED);
    }

    // Soft delete: set deletedAt timestamp
    thread.deletedAt = `${Date.now()}`;
    await this.threadRepository.save(thread);

    this.logger.debug(`Successfully soft deleted thread ${threadId}`);
  }

  /**
   * Batch delete threads with ownership validation
   */
  async deleteThreads(
    deleteThreadsDto: DeleteThreadsDto,
    userId?: number,
    employeeId?: number,
  ): Promise<DeleteThreadsResponseDto> {
    const { threadIds } = deleteThreadsDto;

    this.logger.debug(
      `Batch deleting ${threadIds.length} threads for userId: ${userId}, employeeId: ${employeeId}`,
      {
        threadIds,
      },
    );

    const deletedThreadIds: string[] = [];
    const failedThreadIds: string[] = [];

    // Build ownership condition
    const ownershipCondition = userId
      ? { userId }
      : employeeId
        ? { employeeId }
        : {};

    this.logger.debug(`Ownership condition:`, ownershipCondition);

    // First, let's check if the threads exist at all (without ownership filter)
    const allThreads = await this.threadRepository.find({
      where: {
        id: In(threadIds),
      },
    });

    this.logger.debug(
      `Found ${allThreads.length} threads total (ignoring ownership and deletion status):`,
      allThreads.map((t) => ({
        id: t.id,
        userId: t.userId,
        employeeId: t.employeeId,
        deletedAt: t.deletedAt,
      })),
    );

    // Find all threads that exist and belong to the user/employee
    const threads = await this.threadRepository.find({
      where: {
        id: In(threadIds),
        deletedAt: IsNull(),
        ...ownershipCondition,
      },
    });

    this.logger.debug(
      `Found ${threads.length} threads out of ${threadIds.length} requested for deletion`,
    );

    // Track which threads were found
    const foundThreadIds = threads.map((thread) => thread.id);

    this.logger.debug(`Found thread IDs:`, foundThreadIds);

    // Identify failed thread IDs (not found or no access)
    threadIds.forEach((id) => {
      if (!foundThreadIds.includes(id)) {
        failedThreadIds.push(id);
      }
    });

    if (failedThreadIds.length > 0) {
      this.logger.warn(
        `Failed to find or access ${failedThreadIds.length} threads:`,
        failedThreadIds,
      );
    }

    // Batch soft delete found threads
    if (threads.length > 0) {
      const currentTimestamp = Date.now();

      this.logger.debug(
        `Performing batch soft delete with timestamp: ${currentTimestamp}`,
      );

      // Update all threads in a single query
      const updateResult = await this.threadRepository.update(
        { id: In(foundThreadIds) },
        { deletedAt: `${currentTimestamp}` },
      );

      this.logger.debug(`Batch update result:`, updateResult);

      deletedThreadIds.push(...foundThreadIds);
    } else {
      this.logger.warn(`No threads found for deletion`);
    }

    const result = {
      deletedCount: deletedThreadIds.length,
      deletedThreadIds,
      failedThreadIds: failedThreadIds.length > 0 ? failedThreadIds : undefined,
    };

    this.logger.debug(`Batch delete completed:`, result);

    return result;
  }
}
