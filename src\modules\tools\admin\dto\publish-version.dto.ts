import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc xuất bản version
 */
export class PublishVersionDto {
  @ApiProperty({
    description: 'Thông báo khi xuất bản version',
    example: 'Version đã được xuất bản và sẵn sàng sử dụng',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  message?: string;
}
