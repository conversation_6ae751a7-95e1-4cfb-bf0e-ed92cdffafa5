/**
 * Interface cho Facebook Business API configuration
 */
export interface FacebookBusinessConfig {
  /**
   * App ID từ Facebook Developer Console
   */
  appId: string;

  /**
   * App Secret từ Facebook Developer Console
   */
  appSecret: string;

  /**
   * Access Token cho Business API
   */
  accessToken: string;

  /**
   * API Version
   */
  apiVersion: string;

  /**
   * Business Account ID
   */
  businessAccountId?: string;

  /**
   * Ad Account ID
   */
  adAccountId?: string;
}

/**
 * Interface cho Facebook Business Account
 */
export interface FacebookBusinessAccount {
  /**
   * ID của business account
   */
  id: string;

  /**
   * Tên business account
   */
  name: string;

  /**
   * Timezone của business
   */
  timezone_name?: string;

  /**
   * Primary page ID
   */
  primary_page?: string;

  /**
   * Thời gian tạo
   */
  created_time?: string;

  /**
   * Thời gian cập nhật
   */
  updated_time?: string;

  /**
   * Verification status
   */
  verification_status?: string;

  /**
   * Business type
   */
  business_type?: string;
}

/**
 * Interface cho Facebook Ad Account
 */
export interface FacebookAdAccount {
  /**
   * ID của ad account
   */
  id: string;

  /**
   * Tên ad account
   */
  name: string;

  /**
   * Account status
   */
  account_status: number;

  /**
   * Currency
   */
  currency: string;

  /**
   * Timezone ID
   */
  timezone_id: number;

  /**
   * Timezone name
   */
  timezone_name: string;

  /**
   * Business account ID
   */
  business?: string;

  /**
   * Balance
   */
  balance?: string;

  /**
   * Amount spent
   */
  amount_spent?: string;

  /**
   * Spend cap
   */
  spend_cap?: string;

  /**
   * Capabilities
   */
  capabilities?: string[];

  /**
   * Created time
   */
  created_time?: string;
}

/**
 * Interface cho Facebook Campaign
 */
export interface FacebookCampaign {
  /**
   * ID của campaign
   */
  id: string;

  /**
   * Tên campaign
   */
  name: string;

  /**
   * Objective của campaign
   */
  objective: string;

  /**
   * Status của campaign
   */
  status: 'ACTIVE' | 'PAUSED' | 'DELETED' | 'ARCHIVED';

  /**
   * Configured status
   */
  configured_status?: 'ACTIVE' | 'PAUSED' | 'DELETED' | 'ARCHIVED';

  /**
   * Effective status
   */
  effective_status?: string;

  /**
   * Created time
   */
  created_time?: string;

  /**
   * Updated time
   */
  updated_time?: string;

  /**
   * Start time
   */
  start_time?: string;

  /**
   * Stop time
   */
  stop_time?: string;

  /**
   * Daily budget
   */
  daily_budget?: string;

  /**
   * Lifetime budget
   */
  lifetime_budget?: string;

  /**
   * Budget remaining
   */
  budget_remaining?: string;

  /**
   * Buying type
   */
  buying_type?: string;

  /**
   * Bid strategy
   */
  bid_strategy?: string;

  /**
   * Special ad categories
   */
  special_ad_categories?: string[];
}

/**
 * Interface cho Facebook Ad Set
 */
export interface FacebookAdSet {
  /**
   * ID của ad set
   */
  id: string;

  /**
   * Tên ad set
   */
  name: string;

  /**
   * Campaign ID
   */
  campaign_id: string;

  /**
   * Status của ad set
   */
  status: 'ACTIVE' | 'PAUSED' | 'DELETED' | 'ARCHIVED';

  /**
   * Configured status
   */
  configured_status?: 'ACTIVE' | 'PAUSED' | 'DELETED' | 'ARCHIVED';

  /**
   * Effective status
   */
  effective_status?: string;

  /**
   * Created time
   */
  created_time?: string;

  /**
   * Updated time
   */
  updated_time?: string;

  /**
   * Start time
   */
  start_time?: string;

  /**
   * End time
   */
  end_time?: string;

  /**
   * Daily budget
   */
  daily_budget?: string;

  /**
   * Lifetime budget
   */
  lifetime_budget?: string;

  /**
   * Budget remaining
   */
  budget_remaining?: string;

  /**
   * Billing event
   */
  billing_event?: string;

  /**
   * Optimization goal
   */
  optimization_goal?: string;

  /**
   * Bid amount
   */
  bid_amount?: string;

  /**
   * Targeting
   */
  targeting?: FacebookTargeting;

  /**
   * Attribution spec
   */
  attribution_spec?: FacebookAttributionSpec[];
}

/**
 * Interface cho Facebook Ad
 */
export interface FacebookAd {
  /**
   * ID của ad
   */
  id: string;

  /**
   * Tên ad
   */
  name: string;

  /**
   * Ad set ID
   */
  adset_id: string;

  /**
   * Campaign ID
   */
  campaign_id: string;

  /**
   * Status của ad
   */
  status: 'ACTIVE' | 'PAUSED' | 'DELETED' | 'ARCHIVED';

  /**
   * Configured status
   */
  configured_status?: 'ACTIVE' | 'PAUSED' | 'DELETED' | 'ARCHIVED';

  /**
   * Effective status
   */
  effective_status?: string;

  /**
   * Created time
   */
  created_time?: string;

  /**
   * Updated time
   */
  updated_time?: string;

  /**
   * Creative
   */
  creative?: FacebookAdCreative;

  /**
   * Tracking specs
   */
  tracking_specs?: FacebookTrackingSpec[];

  /**
   * Conversion specs
   */
  conversion_specs?: FacebookConversionSpec[];
}

/**
 * Interface cho Facebook Targeting
 */
export interface FacebookTargeting {
  /**
   * Age min
   */
  age_min?: number;

  /**
   * Age max
   */
  age_max?: number;

  /**
   * Genders
   */
  genders?: number[];

  /**
   * Geo locations
   */
  geo_locations?: {
    countries?: string[];
    regions?: Array<{
      key: string;
      name?: string;
    }>;
    cities?: Array<{
      key: string;
      name?: string;
      radius?: number;
      distance_unit?: string;
    }>;
    location_types?: string[];
  };

  /**
   * Interests
   */
  interests?: Array<{
    id: string;
    name?: string;
  }>;

  /**
   * Behaviors
   */
  behaviors?: Array<{
    id: string;
    name?: string;
  }>;

  /**
   * Demographics
   */
  demographics?: Array<{
    id: string;
    name?: string;
  }>;

  /**
   * Custom audiences
   */
  custom_audiences?: Array<{
    id: string;
    name?: string;
  }>;

  /**
   * Excluded custom audiences
   */
  excluded_custom_audiences?: Array<{
    id: string;
    name?: string;
  }>;

  /**
   * Lookalike audiences
   */
  lookalike_audiences?: Array<{
    id: string;
    name?: string;
  }>;

  /**
   * Device platforms
   */
  device_platforms?: string[];

  /**
   * Publisher platforms
   */
  publisher_platforms?: string[];

  /**
   * Facebook positions
   */
  facebook_positions?: string[];

  /**
   * Instagram positions
   */
  instagram_positions?: string[];

  /**
   * Audience network positions
   */
  audience_network_positions?: string[];

  /**
   * Messenger positions
   */
  messenger_positions?: string[];
}

/**
 * Interface cho Facebook Attribution Spec
 */
export interface FacebookAttributionSpec {
  /**
   * Event type
   */
  event_type: string;

  /**
   * Window days
   */
  window_days: number;
}

/**
 * Interface cho Facebook Ad Creative
 */
export interface FacebookAdCreative {
  /**
   * ID của creative
   */
  id: string;

  /**
   * Tên creative
   */
  name: string;

  /**
   * Object story spec
   */
  object_story_spec?: FacebookObjectStorySpec;

  /**
   * Object type
   */
  object_type?: string;

  /**
   * Status
   */
  status?: string;

  /**
   * Created time
   */
  created_time?: string;

  /**
   * Updated time
   */
  updated_time?: string;
}

/**
 * Interface cho Facebook Object Story Spec
 */
export interface FacebookObjectStorySpec {
  /**
   * Page ID
   */
  page_id: string;

  /**
   * Link data
   */
  link_data?: {
    /**
     * Call to action
     */
    call_to_action?: {
      type: string;
      value?: {
        link?: string;
        application?: string;
        lead_gen_form_id?: string;
      };
    };

    /**
     * Caption
     */
    caption?: string;

    /**
     * Description
     */
    description?: string;

    /**
     * Image hash
     */
    image_hash?: string;

    /**
     * Link
     */
    link?: string;

    /**
     * Message
     */
    message?: string;

    /**
     * Name
     */
    name?: string;

    /**
     * Picture
     */
    picture?: string;
  };

  /**
   * Video data
   */
  video_data?: {
    /**
     * Video ID
     */
    video_id: string;

    /**
     * Call to action
     */
    call_to_action?: {
      type: string;
      value?: {
        link?: string;
        application?: string;
        lead_gen_form_id?: string;
      };
    };

    /**
     * Message
     */
    message?: string;

    /**
     * Title
     */
    title?: string;
  };
}

/**
 * Interface cho Facebook Tracking Spec
 */
export interface FacebookTrackingSpec {
  /**
   * Action type
   */
  action_type: string;

  /**
   * Facebook pixel
   */
  fb_pixel?: string;
}

/**
 * Interface cho Facebook Conversion Spec
 */
export interface FacebookConversionSpec {
  /**
   * Action type
   */
  action_type: string;

  /**
   * Facebook pixel
   */
  fb_pixel?: string;
}

/**
 * Interface cho Facebook Custom Audience
 */
export interface FacebookCustomAudience {
  /**
   * ID của audience
   */
  id: string;

  /**
   * Tên audience
   */
  name: string;

  /**
   * Description
   */
  description?: string;

  /**
   * Subtype
   */
  subtype: string;

  /**
   * Approximate count
   */
  approximate_count?: number;

  /**
   * Data source
   */
  data_source?: {
    type: string;
    sub_type?: string;
  };

  /**
   * Created time
   */
  created_time?: string;

  /**
   * Updated time
   */
  updated_time?: string;

  /**
   * Delivery status
   */
  delivery_status?: {
    code: number;
    description: string;
  };

  /**
   * Operation status
   */
  operation_status?: {
    code: number;
    description: string;
  };
}

/**
 * Interface cho Facebook Insights
 */
export interface FacebookInsights {
  /**
   * Date start
   */
  date_start: string;

  /**
   * Date stop
   */
  date_stop: string;

  /**
   * Impressions
   */
  impressions?: string;

  /**
   * Clicks
   */
  clicks?: string;

  /**
   * Spend
   */
  spend?: string;

  /**
   * Reach
   */
  reach?: string;

  /**
   * Frequency
   */
  frequency?: string;

  /**
   * CPM (Cost per 1000 impressions)
   */
  cpm?: string;

  /**
   * CPC (Cost per click)
   */
  cpc?: string;

  /**
   * CTR (Click-through rate)
   */
  ctr?: string;

  /**
   * Actions
   */
  actions?: Array<{
    action_type: string;
    value: string;
  }>;

  /**
   * Action values
   */
  action_values?: Array<{
    action_type: string;
    value: string;
  }>;

  /**
   * Cost per action
   */
  cost_per_action_type?: Array<{
    action_type: string;
    value: string;
  }>;

  /**
   * Video views
   */
  video_views?: string;

  /**
   * Video play actions
   */
  video_play_actions?: Array<{
    action_type: string;
    value: string;
  }>;
}

/**
 * Interface cho Facebook API Response
 */
export interface FacebookBusinessApiResponse<T> {
  /**
   * Data
   */
  data: T[];

  /**
   * Paging
   */
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
    previous?: string;
  };

  /**
   * Summary
   */
  summary?: Record<string, unknown>;
}

/**
 * Interface cho Facebook Error Response
 */
export interface FacebookBusinessError {
  /**
   * Error message
   */
  message: string;

  /**
   * Error type
   */
  type: string;

  /**
   * Error code
   */
  code: number;

  /**
   * Error subcode
   */
  error_subcode?: number;

  /**
   * Error user title
   */
  error_user_title?: string;

  /**
   * Error user msg
   */
  error_user_msg?: string;

  /**
   * Facebook trace ID
   */
  fbtrace_id: string;
}

/**
 * Facebook Business API constants
 */
export const FACEBOOK_BUSINESS_CONSTANTS = {
  /**
   * Campaign objectives
   */
  CAMPAIGN_OBJECTIVES: {
    AWARENESS: 'BRAND_AWARENESS',
    TRAFFIC: 'LINK_CLICKS',
    ENGAGEMENT: 'ENGAGEMENT',
    LEADS: 'LEAD_GENERATION',
    APP_PROMOTION: 'APP_INSTALLS',
    SALES: 'CONVERSIONS',
    REACH: 'REACH',
    VIDEO_VIEWS: 'VIDEO_VIEWS',
    MESSAGES: 'MESSAGES',
    STORE_VISITS: 'STORE_VISITS',
  },

  /**
   * Ad statuses
   */
  AD_STATUSES: {
    ACTIVE: 'ACTIVE',
    PAUSED: 'PAUSED',
    DELETED: 'DELETED',
    ARCHIVED: 'ARCHIVED',
  },

  /**
   * Optimization goals
   */
  OPTIMIZATION_GOALS: {
    IMPRESSIONS: 'IMPRESSIONS',
    CLICKS: 'LINK_CLICKS',
    REACH: 'REACH',
    LANDING_PAGE_VIEWS: 'LANDING_PAGE_VIEWS',
    POST_ENGAGEMENT: 'POST_ENGAGEMENT',
    PAGE_LIKES: 'PAGE_LIKES',
    LEAD_GENERATION: 'LEAD_GENERATION',
    CONVERSIONS: 'OFFSITE_CONVERSIONS',
    APP_INSTALLS: 'APP_INSTALLS',
    VIDEO_VIEWS: 'VIDEO_VIEWS',
    MESSAGES: 'MESSAGES',
  },

  /**
   * Billing events
   */
  BILLING_EVENTS: {
    IMPRESSIONS: 'IMPRESSIONS',
    CLICKS: 'LINK_CLICKS',
    APP_INSTALLS: 'APP_INSTALLS',
    VIDEO_VIEWS: 'VIDEO_VIEWS',
    POST_ENGAGEMENT: 'POST_ENGAGEMENT',
  },

  /**
   * Device platforms
   */
  DEVICE_PLATFORMS: {
    MOBILE: 'mobile',
    DESKTOP: 'desktop',
  },

  /**
   * Publisher platforms
   */
  PUBLISHER_PLATFORMS: {
    FACEBOOK: 'facebook',
    INSTAGRAM: 'instagram',
    AUDIENCE_NETWORK: 'audience_network',
    MESSENGER: 'messenger',
  },
} as const;
