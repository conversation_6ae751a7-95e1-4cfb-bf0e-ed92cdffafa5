import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsBoolean,
  IsOptional,
  IsObject,
  MaxLength,
  <PERSON><PERSON>ength,
  IsNumber,
  ValidateNested
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for creating a new workflow
 * Follows existing validation patterns from the codebase
 */
export class CreateWorkflowDto {
  /**
   * Workflow name - must be unique per user
   */
  @ApiProperty({
    description: 'Workflow name (unique per user)',
    example: 'Customer Onboarding Flow',
    minLength: 1,
    maxLength: 255
  })
  @IsString()
  @MinLength(1, { message: 'Workflow name cannot be empty' })
  @MaxLength(255, { message: 'Workflow name cannot exceed 255 characters' })
  name: string;

  /**
   * User ID who owns this workflow (optional for admin creation)
   */
  @ApiPropertyOptional({
    description: 'User ID who owns this workflow',
    example: 123
  })
  @IsOptional()
  @IsNumber()
  userId?: number;

  /**
   * Employee ID responsible for this workflow (optional)
   */
  @ApiPropertyOptional({
    description: 'Employee ID responsible for this workflow',
    example: 456
  })
  @IsOptional()
  @IsNumber()
  employeeId?: number;

  /**
   * Whether the workflow is active
   */
  @ApiPropertyOptional({
    description: 'Whether the workflow is active',
    example: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean = false;

  /**
   * Workflow definition structure (JSONB)
   */
  @ApiPropertyOptional({
    description: 'Workflow definition structure',
    example: {
      nodes: [],
      edges: [],
      version: '1.0.0'
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  definition?: Record<string, any> = {};
}
