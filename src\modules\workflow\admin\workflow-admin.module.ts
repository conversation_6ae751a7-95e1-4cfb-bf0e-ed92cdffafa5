import { Module } from '@nestjs/common';
import {
  AdminWorkflowService,
  AdminWorkflowDefinitionService,
  AdminWorkflowValidationService,
  AdminWorkflowImportExportService,
} from './services';
import {
  AdminWorkflowController,
  AdminWorkflowDefinitionController,
  AdminNodeDefinitionController,
} from './controllers';
import { AdminWorkflowQueueController } from './controllers/admin-workflow-queue.controller';
import { WorkflowSharedModule } from '../shared/workflow-shared.module';
import { WorkflowService } from '../services/workflow.service';

@Module({
  imports: [
    WorkflowSharedModule,
  ],
  controllers: [
    AdminWorkflowController,
    AdminWorkflowDefinitionController,
    AdminNodeDefinitionController,
    AdminWorkflowQueueController,
  ],
  providers: [
    AdminWorkflowService,
    AdminWorkflowDefinitionService,
    AdminWorkflowValidationService,
    AdminWorkflowImportExportService,
    WorkflowService,
  ],
  exports: [
    AdminWorkflowService,
    AdminWorkflowDefinitionService,
    AdminWorkflowValidationService,
    AdminWorkflowImportExportService,
  ],
})
export class WorkflowAdminModule {}
