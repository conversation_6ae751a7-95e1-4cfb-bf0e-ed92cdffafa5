import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { FlashSaleStatus } from '../enums/flash-sale-status.enum';
import { MaxConfiguration } from '../interfaces/max-configuration.interface';

/**
 * Entity đại diện cho bảng flash_sales trong cơ sở dữ liệu
 * Flash sale - chương trình giảm giá có thời hạn
 */
@Entity('flash_sales')
export class FlashSale {
  /**
   * Mã định danh flash sale
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Mã sản phẩm áp dụng flash sale
   */
  @Column({
    name: 'product_id',
    type: 'bigint',
    comment: 'Mã sản phẩm áp dụng flash sale'
  })
  productId: number;

  /**
   * Mã người dùng tạo flash sale (cho user APIs)
   */
  @Column({
    name: 'user_id',
    type: 'bigint',
    nullable: true,
    comment: 'Mã người dùng tạo flash sale'
  })
  userId: number | null;

  /**
   * Mã nhân viên tạo flash sale (cho admin APIs)
   */
  @Column({
    name: 'employee_id',
    type: 'bigint',
    nullable: true,
    comment: 'Mã nhân viên tạo flash sale'
  })
  employeeId: number | null;

  /**
   * Phần trăm giảm giá (1-99%)
   */
  @Column({
    name: 'discount_percentage',
    type: 'integer',
    comment: 'Phần trăm giảm giá (1-99%)'
  })
  discountPercentage: number;

  /**
   * Thời gian hiển thị sản phẩm flash sale cho user (giây)
   * Tạo cảm giác khan hiếm - sản phẩm chỉ hiển thị trong khoảng thời gian này
   */
  @Column({
    name: 'display_time',
    type: 'integer',
    comment: 'Thời gian hiển thị sản phẩm flash sale cho user (giây)'
  })
  displayTime: number;

  /**
   * Thời gian bắt đầu flash sale (timestamp milliseconds)
   */
  @Column({
    name: 'start_time',
    type: 'bigint',
    comment: 'Thời gian bắt đầu flash sale (timestamp ms)'
  })
  startTime: number;

  /**
   * Thời gian kết thúc flash sale (timestamp milliseconds)
   */
  @Column({
    name: 'end_time',
    type: 'bigint',
    comment: 'Thời gian kết thúc flash sale (timestamp ms)'
  })
  endTime: number;

  /**
   * Cấu hình giới hạn flash sale
   */
  @Column({
    name: 'max_configuration',
    type: 'jsonb',
    default: () => "'{}'::jsonb",
    comment: 'Cấu hình giới hạn flash sale (JSONB)'
  })
  maxConfiguration: MaxConfiguration;

  /**
   * Trạng thái flash sale
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: FlashSaleStatus,
    default: FlashSaleStatus.DRAFT,
    comment: 'Trạng thái flash sale'
  })
  status: FlashSaleStatus;

  /**
   * Flash sale có đang hoạt động không
   */
  @Column({
    name: 'is_active',
    type: 'boolean',
    default: true,
    comment: 'Flash sale có đang hoạt động không'
  })
  isActive: boolean;

  /**
   * Thời gian tạo (timestamp milliseconds)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    comment: 'Thời gian tạo (timestamp ms)'
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (timestamp milliseconds)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    comment: 'Thời gian cập nhật (timestamp ms)'
  })
  updatedAt: number;

  // Các trường không lưu trong database, chỉ dùng để chứa dữ liệu từ query
  /** Thông tin sản phẩm (từ join) */
  product?: any;

  /** Giá sau khi áp dụng flash sale */
  salePrice?: number;

  /** Thời gian còn lại của flash sale (tính bằng giây) */
  timeRemaining?: number;

  /** Số lượng đã bán (calculated field, không lưu trong DB) */
  soldCount?: number;
}
