# Key LLM Validation Implementation

## <PERSON><PERSON> tả
Triển khai validation cho key LL<PERSON> khi tạo agent, đ<PERSON><PERSON> bảo key thuộc về user và hoạt động bình thường.

## 🎯 Chức năng chính

### Workflow Validation
```typescript
let useSystemKey = true;
if (createDto.keyLlmId) {
  // 1. <PERSON><PERSON><PERSON> tra key LLM tồn tại và thuộc về user
  const keyLlm = await this.integrationLlmKeyRepository.findByIdAndUserId(createDto.keyLlmId, userId);
  
  // 2. Validate key LLM hoạt động
  await this.validateKeyLlmActive(keyLlm);
  
  useSystemKey = false;
}
```

## 🔧 Implementation Details

### 1. Repository Integration
**File**: `src/modules/agent/user/services/agent-user.service.ts`

**Import Repository**:
```typescript
import {
  IntegrationLlmKeyRepository,
  // ... other repositories
} from '@modules/integration/repositories';
```

**Constructor Injection**:
```typescript
constructor(
  // ... other dependencies
  private readonly integrationLlmKeyRepository: IntegrationLlmKeyRepository,
  // ... other dependencies
) {}
```

### 2. Key LLM Existence Validation
**Method**: `findByIdAndUserId(keyLlmId, userId)`

**Validation Logic**:
```typescript
const keyLlm = await this.integrationLlmKeyRepository.findByIdAndUserId(createDto.keyLlmId, userId);
if (!keyLlm) {
  throw new AppException(
    AGENT_ERROR_CODES.RELATION_NOT_FOUND,
    `Không tìm thấy key LLM với ID ${createDto.keyLlmId} thuộc về user ${userId}`,
  );
}
```

**Security Benefits**:
- ✅ **User Isolation**: Chỉ cho phép sử dụng key thuộc về user
- ✅ **Existence Check**: Đảm bảo key tồn tại trong database
- ✅ **Authorization**: Ngăn chặn sử dụng key của user khác

### 3. Key LLM Active Validation
**Method**: `validateKeyLlmActive(keyLlm)`

**Validation Checks**:

#### a) Configuration Validation
```typescript
if (!keyLlm.encryptedConfig || !keyLlm.secretKey) {
  throw new AppException(
    AGENT_ERROR_CODES.RELATION_NOT_FOUND,
    `Key LLM ${keyLlm.integrationName} không có cấu hình hợp lệ`
  );
}
```

#### b) Status Validation
```typescript
const metadata = keyLlm.metadata as any;
if (metadata && metadata.status === 'INACTIVE') {
  throw new AppException(
    AGENT_ERROR_CODES.RELATION_NOT_FOUND,
    `Key LLM ${keyLlm.integrationName} đã bị vô hiệu hóa`
  );
}
```

#### c) Age Warning
```typescript
const createdAt = new Date(keyLlm.createdAt);
const now = new Date();
const daysDiff = Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24));

if (daysDiff > 365) {
  this.logger.warn(`Key LLM ${keyLlm.integrationName} đã được tạo ${daysDiff} ngày trước, có thể cần kiểm tra lại`);
}
```

## 🏗️ Database Schema

### Integration Entity
```sql
-- Bảng integrations lưu trữ key LLM
CREATE TABLE integrations (
  id UUID PRIMARY KEY,
  integration_name VARCHAR(255) NOT NULL,
  user_id INTEGER NOT NULL,
  type_id INTEGER NOT NULL, -- Reference to integration_providers
  encrypted_config TEXT NOT NULL, -- Encrypted API key
  secret_key VARCHAR(255) NOT NULL, -- Encryption key
  metadata JSONB, -- Additional configuration
  created_at BIGINT NOT NULL,
  updated_at BIGINT
);
```

### Integration Providers
```sql
-- Bảng integration_providers định nghĩa các AI providers
CREATE TABLE integration_providers (
  id SERIAL PRIMARY KEY,
  type VARCHAR(50) NOT NULL, -- 'OPENAI', 'GEMINI', 'ANTHROPIC', etc.
  name VARCHAR(255) NOT NULL,
  description TEXT,
  active BOOLEAN DEFAULT true
);
```

## ✅ Validation Rules

### 1. Ownership Validation
- **Rule**: Key LLM phải thuộc về user hiện tại
- **Check**: `user_id = userId` trong query
- **Error**: `RELATION_NOT_FOUND` nếu không tìm thấy

### 2. Configuration Validation
- **Rule**: Key phải có encrypted config và secret key
- **Check**: `encryptedConfig IS NOT NULL AND secretKey IS NOT NULL`
- **Error**: `RELATION_NOT_FOUND` nếu thiếu config

### 3. Status Validation
- **Rule**: Key không được bị vô hiệu hóa
- **Check**: `metadata.status !== 'INACTIVE'`
- **Error**: `RELATION_NOT_FOUND` nếu inactive

### 4. Age Warning
- **Rule**: Cảnh báo nếu key quá cũ (>365 ngày)
- **Action**: Log warning, không throw error
- **Purpose**: Nhắc nhở kiểm tra key cũ

## 🔒 Security Considerations

### 1. User Isolation
```typescript
// Chỉ query key thuộc về user hiện tại
.where('llm_key.user_id = :userId', { userId })
```

### 2. Encrypted Storage
- API keys được encrypt với `encryptedConfig`
- Sử dụng `secretKey` riêng cho mỗi integration
- Không expose raw API key trong logs

### 3. Error Messages
- Không leak thông tin sensitive trong error messages
- Sử dụng generic error codes
- Log chi tiết chỉ ở server side

## 📊 Error Handling

### Error Codes Used
```typescript
AGENT_ERROR_CODES.RELATION_NOT_FOUND
```

### Error Scenarios
1. **Key Not Found**: Key LLM không tồn tại hoặc không thuộc về user
2. **Invalid Config**: Key thiếu encrypted config hoặc secret key
3. **Inactive Status**: Key đã bị vô hiệu hóa
4. **Database Error**: Lỗi kết nối database

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "RELATION_NOT_FOUND",
    "message": "Không tìm thấy key LLM với ID xxx thuộc về user yyy"
  }
}
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('Key LLM Validation', () => {
  it('should validate existing key LLM', async () => {
    // Mock valid key LLM
    mockRepository.findByIdAndUserId.mockResolvedValue(validKeyLlm);
    
    // Should not throw error
    await service.validateKeyLlmActive(validKeyLlm);
  });
  
  it('should throw error for non-existent key', async () => {
    // Mock null response
    mockRepository.findByIdAndUserId.mockResolvedValue(null);
    
    // Should throw RELATION_NOT_FOUND
    await expect(service.create(userId, createDto)).rejects.toThrow();
  });
  
  it('should throw error for inactive key', async () => {
    // Mock inactive key
    const inactiveKey = { ...validKeyLlm, metadata: { status: 'INACTIVE' } };
    
    // Should throw error
    await expect(service.validateKeyLlmActive(inactiveKey)).rejects.toThrow();
  });
});
```

### Integration Tests
- Test với real database
- Test với different user scenarios
- Test với various key states

## 🚀 Performance Considerations

### 1. Database Queries
- Single query để check existence và ownership
- Index trên `(user_id, id)` cho performance
- Avoid N+1 queries

### 2. Caching Opportunities
- Cache validation results cho short period
- Cache user permissions
- Cache provider configurations

### 3. Async Operations
- Validation chạy async để không block
- Parallel validation cho multiple keys
- Timeout cho external API calls

## 📝 Usage Examples

### Valid Scenario
```typescript
const createDto = {
  name: 'My Agent',
  keyLlmId: 'valid-key-uuid',
  // ... other fields
};

// Key validation sẽ pass
const result = await agentService.create(userId, createDto);
```

### Invalid Scenario
```typescript
const createDto = {
  name: 'My Agent',
  keyLlmId: 'invalid-key-uuid', // Key không tồn tại
  // ... other fields
};

// Sẽ throw RELATION_NOT_FOUND error
await expect(agentService.create(userId, createDto)).rejects.toThrow();
```

## 🔄 Future Enhancements

### 1. Advanced Validation
- [ ] Test API key connectivity
- [ ] Validate API key permissions
- [ ] Check rate limits và quotas
- [ ] Validate supported models

### 2. Monitoring & Analytics
- [ ] Track key usage frequency
- [ ] Monitor key performance
- [ ] Alert cho keys sắp expire
- [ ] Usage analytics dashboard

### 3. Security Improvements
- [ ] Key rotation automation
- [ ] Multi-factor authentication
- [ ] Audit trail cho key usage
- [ ] Anomaly detection

## 📚 Related Documentation
- [Agent Creation Flow](./agent-creation-flow.md)
- [Integration Management](../../integration/docs/integration-management.md)
- [Security Guidelines](../../../docs/security-guidelines.md)
