import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  UseGuards,
  HttpStatus
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiExtraModels
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@modules/auth/interfaces';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { WORKFLOW_ERROR_CODES } from '../../constants/workflow-error-codes';
import { WorkflowService } from '../../services/workflow.service';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  QueryWorkflowDto,
  WorkflowResponseDto
} from '../../dto/workflow';

/**
 * Admin controller for workflow CRUD operations
 * Following existing admin controller patterns from the codebase
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_WORKFLOW)
@Controller('api/v1/admin/workflows')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, WorkflowResponseDto, PaginatedResult)
export class AdminWorkflowController {
  constructor(
    private readonly workflowService: WorkflowService,
  ) { }

  /**
   * Get all workflows with pagination and filtering
   */
  @Get()
  @ApiOperation({ summary: 'Get all workflows with pagination' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (1-based)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page', example: 10 })
  @ApiQuery({ name: 'search', required: false, description: 'Search by workflow name', example: 'customer' })
  @ApiQuery({ name: 'isActive', required: false, description: 'Filter by active status', example: true })
  @ApiQuery({ name: 'userId', required: false, description: 'Filter by user ID', example: 123 })
  @ApiQuery({ name: 'employeeId', required: false, description: 'Filter by employee ID', example: 456 })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field', example: 'createdAt' })
  @ApiQuery({ name: 'sortDirection', required: false, description: 'Sort direction', example: 'DESC' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflows retrieved successfully',
    schema: ApiResponseDto.getPaginatedSchema(WorkflowResponseDto)
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.DATA_FETCH_ERROR
  )
  async findAll(@Query() queryDto: QueryWorkflowDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<PaginatedResult<WorkflowResponseDto>>> {
    const result = await this.workflowService.findAll(queryDto, employeeId);
    return ApiResponseDto.paginated(result, 'Workflows retrieved successfully');
  }

  /**
   * Get workflow details by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get workflow details by ID' })
  @ApiParam({ name: 'id', description: 'Workflow ID', example: '123e4567-e89b-12d3-a456-426614174000' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow details retrieved successfully',
    schema: ApiResponseDto.getSchema(WorkflowResponseDto)
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.NOT_FOUND,
    WORKFLOW_ERROR_CODES.DATA_FETCH_ERROR
  )
  async findOne(@Param('id') id: string): Promise<ApiResponseDto<WorkflowResponseDto>> {
    const result = await this.workflowService.findOne(id);
    return ApiResponseDto.success(result, 'Workflow details retrieved successfully');
  }

  /**
   * Create new workflow
   */
  @Post()
  @ApiOperation({ summary: 'Create new workflow' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Workflow created successfully',
    schema: ApiResponseDto.getSchema(WorkflowResponseDto)
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.CREATION_FAILED,
    WORKFLOW_ERROR_CODES.MISSING_REQUIRED_FIELDS,
    WORKFLOW_ERROR_CODES.INVALID_DATA,
    WORKFLOW_ERROR_CODES.DUPLICATE_NAME
  )
  async create(
    @Body() createWorkflowDto: CreateWorkflowDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<WorkflowResponseDto>> {
    const result = await this.workflowService.create(createWorkflowDto);
    return ApiResponseDto.created(result, 'Workflow created successfully');
  }

  /**
   * Update workflow
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update workflow' })
  @ApiParam({ name: 'id', description: 'Workflow ID', example: '123e4567-e89b-12d3-a456-426614174000' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow updated successfully',
    schema: ApiResponseDto.getSchema(WorkflowResponseDto)
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.NOT_FOUND,
    WORKFLOW_ERROR_CODES.UPDATE_FAILED,
    WORKFLOW_ERROR_CODES.INVALID_DATA,
    WORKFLOW_ERROR_CODES.DUPLICATE_NAME
  )
  async update(
    @Param('id') id: string,
    @Body() updateWorkflowDto: UpdateWorkflowDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<WorkflowResponseDto>> {
    const result = await this.workflowService.update(id, updateWorkflowDto);
    return ApiResponseDto.success(result, 'Workflow updated successfully');
  }

  /**
   * Delete workflow
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete workflow' })
  @ApiParam({ name: 'id', description: 'Workflow ID', example: '123e4567-e89b-12d3-a456-426614174000' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow deleted successfully',
    schema: ApiResponseDto.getSchema(null)
  })
  @ApiErrorResponse(
    WORKFLOW_ERROR_CODES.NOT_FOUND,
    WORKFLOW_ERROR_CODES.DELETE_FAILED
  )
  async remove(
    @Param('id') id: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<null>> {
    await this.workflowService.remove(id);
    return ApiResponseDto.success(null, 'Workflow deleted successfully');
  }
}
