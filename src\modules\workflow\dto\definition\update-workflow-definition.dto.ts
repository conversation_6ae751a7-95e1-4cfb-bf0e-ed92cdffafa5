import { 
  <PERSON>O<PERSON>, 
  IsOptional, 
  <PERSON><PERSON>teNested, 
  <PERSON><PERSON>rray, 
  IsString,
  <PERSON><PERSON>eng<PERSON> 
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho workflow node trong definition
 */
export class WorkflowNodeDto {
  @ApiProperty({
    description: 'Unique identifier for the node within workflow',
    example: 'start_node_1'
  })
  @IsString()
  @MaxLength(255)
  id: string;

  @ApiProperty({
    description: 'Node type following category.subcategory.action pattern',
    example: 'system.start'
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Human-readable name for the node',
    example: 'Start Node'
  })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({
    description: 'Optional description of node functionality',
    example: 'This node starts the workflow execution'
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({
    description: 'Node position on canvas',
    example: { x: 100, y: 200 }
  })
  @IsObject()
  position: {
    x: number;
    y: number;
  };

  @ApiPropertyOptional({
    description: 'Node size on canvas',
    example: { width: 150, height: 80 }
  })
  @IsOptional()
  @IsObject()
  size?: {
    width: number;
    height: number;
  };

  @ApiPropertyOptional({
    description: 'Node input configuration and values',
    example: { message: 'Hello World', delay: 1000 }
  })
  @IsOptional()
  @IsObject()
  inputs?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Node output configuration',
    example: { result: 'success', data: {} }
  })
  @IsOptional()
  @IsObject()
  outputs?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Node-specific configuration',
    example: { timeout: 30000, retries: 3 }
  })
  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'UI and display metadata',
    example: { color: '#FF5733', icon: 'play', category: 'system' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho workflow edge trong definition
 */
export class WorkflowEdgeDto {
  @ApiProperty({
    description: 'Unique identifier for the edge within workflow',
    example: 'edge_1'
  })
  @IsString()
  @MaxLength(255)
  id: string;

  @ApiProperty({
    description: 'ID of the source node',
    example: 'start_node_1'
  })
  @IsString()
  sourceNodeId: string;

  @ApiPropertyOptional({
    description: 'Output port of source node',
    example: 'success'
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  sourcePort?: string;

  @ApiProperty({
    description: 'ID of the target node',
    example: 'action_node_1'
  })
  @IsString()
  targetNodeId: string;

  @ApiPropertyOptional({
    description: 'Input port of target node',
    example: 'input'
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  targetPort?: string;

  @ApiPropertyOptional({
    description: 'Type of edge connection',
    example: 'normal',
    enum: ['normal', 'conditional', 'error', 'success', 'loop']
  })
  @IsOptional()
  @IsString()
  edgeType?: string;

  @ApiPropertyOptional({
    description: 'Condition for conditional edges',
    example: { expression: '{{result}} === "success"', operator: 'equals', value: 'success' }
  })
  @IsOptional()
  @IsObject()
  condition?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'UI and display metadata',
    example: { label: 'Success Path', color: '#00FF00', style: 'solid' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho workflow definition metadata
 */
export class WorkflowDefinitionMetadataDto {
  @ApiPropertyOptional({
    description: 'Semantic version of workflow definition',
    example: '1.0.0'
  })
  @IsOptional()
  @IsString()
  version?: string;

  @ApiPropertyOptional({
    description: 'Version of workflow schema used',
    example: '1.0'
  })
  @IsOptional()
  @IsString()
  schemaVersion?: string;

  @ApiPropertyOptional({
    description: 'Canvas display settings',
    example: { 
      viewport: { x: 0, y: 0, zoom: 1 },
      grid: { enabled: true, size: 20, snapToGrid: true }
    }
  })
  @IsOptional()
  @IsObject()
  canvas?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Workflow-level variables',
    example: { 
      apiKey: { type: 'string', value: '', description: 'API key for external service' }
    }
  })
  @IsOptional()
  @IsObject()
  variables?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Workflow trigger configurations',
    example: [{ type: 'webhook', config: { url: '/webhook/trigger' }, enabled: true }]
  })
  @IsOptional()
  @IsArray()
  triggers?: Array<Record<string, any>>;

  @ApiPropertyOptional({
    description: 'Workflow execution settings',
    example: { 
      timeout: 300000, 
      retryPolicy: { enabled: true, maxRetries: 3, retryDelay: 5000 }
    }
  })
  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Workflow tags for categorization',
    example: ['automation', 'marketing', 'email']
  })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Workflow category',
    example: 'Marketing Automation'
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  category?: string;

  @ApiPropertyOptional({
    description: 'Workflow author information',
    example: { userId: 1, name: 'John Doe', email: '<EMAIL>' }
  })
  @IsOptional()
  @IsObject()
  author?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Last modification information',
    example: { timestamp: 1640995200000, userId: 1, changes: 'Updated node configuration' }
  })
  @IsOptional()
  @IsObject()
  lastModified?: Record<string, any>;
}

/**
 * DTO cho update workflow definition request
 */
export class UpdateWorkflowDefinitionDto {
  @ApiProperty({
    description: 'Array of workflow nodes',
    type: [WorkflowNodeDto],
    example: [
      {
        id: 'start_node_1',
        type: 'system.start',
        name: 'Start Node',
        position: { x: 100, y: 200 },
        inputs: {},
        outputs: {}
      }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowNodeDto)
  nodes: WorkflowNodeDto[];

  @ApiPropertyOptional({
    description: 'Array of workflow edges',
    type: [WorkflowEdgeDto],
    example: [
      {
        id: 'edge_1',
        sourceNodeId: 'start_node_1',
        targetNodeId: 'action_node_1',
        edgeType: 'normal'
      }
    ]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowEdgeDto)
  edges?: WorkflowEdgeDto[];

  @ApiPropertyOptional({
    description: 'Workflow metadata and settings',
    type: WorkflowDefinitionMetadataDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowDefinitionMetadataDto)
  metadata?: WorkflowDefinitionMetadataDto;
}
