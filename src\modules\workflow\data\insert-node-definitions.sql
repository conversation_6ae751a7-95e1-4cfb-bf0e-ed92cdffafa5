-- Insert Node Definitions SQL Script
-- Based on node-definitions.data.ts and NodeDefinition entity schema

-- Clear existing data (optional - remove if you want to keep existing data)
-- DELETE FROM node_definitions;

-- Insert System Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'system.start',
  'Start Node',
  'Workflow start point',
  'system',
  '{"type": "object", "properties": {"triggerData": {"type": "object"}}}',
  '{"type": "object", "properties": {"output": {"type": "object"}}}',
  '1.0.0'
),
(
  'system.end',
  'End Node',
  'Workflow end point',
  'system',
  '{"type": "object", "properties": {"input": {"type": "object"}}}',
  '{"type": "object", "properties": {}}',
  '1.0.0'
),
(
  'system.condition',
  'Condition Node',
  'Conditional branching',
  'system',
  '{"type": "object", "properties": {"condition": {"type": "string"}, "input": {"type": "object"}}, "required": ["condition"]}',
  '{"type": "object", "properties": {"true": {"type": "object"}, "false": {"type": "object"}}}',
  '1.0.0'
),
(
  'system.loop',
  'Loop Node',
  'Loop through items',
  'system',
  '{"type": "object", "properties": {"items": {"type": "array"}, "maxIterations": {"type": "number"}}, "required": ["items"]}',
  '{"type": "object", "properties": {"results": {"type": "array"}}}',
  '1.0.0'
),
(
  'system.delay',
  'Delay Node',
  'Pause workflow execution',
  'system',
  '{"type": "object", "properties": {"duration": {"type": "number"}, "unit": {"type": "string", "enum": ["seconds", "minutes", "hours"]}}, "required": ["duration", "unit"]}',
  '{"type": "object", "properties": {"delayed": {"type": "boolean"}}}',
  '1.0.0'
);

-- Insert Google Sheets Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'google.sheet.getRows',
  'Get Google Sheets Rows',
  'Retrieve data from Google Sheets',
  'google_sheets',
  '{"type": "object", "properties": {"spreadsheetId": {"type": "string"}, "range": {"type": "string"}}, "required": ["spreadsheetId", "range"]}',
  '{"type": "object", "properties": {"rows": {"type": "array"}}}',
  '1.0.0'
),
(
  'google.sheet.addRow',
  'Add Google Sheets Row',
  'Add new row to Google Sheets',
  'google_sheets',
  '{"type": "object", "properties": {"spreadsheetId": {"type": "string"}, "range": {"type": "string"}, "values": {"type": "array"}}, "required": ["spreadsheetId", "range", "values"]}',
  '{"type": "object", "properties": {"updatedRange": {"type": "string"}, "updatedRows": {"type": "number"}}}',
  '1.0.0'
),
(
  'google.sheet.updateRow',
  'Update Google Sheets Row',
  'Update existing row in Google Sheets',
  'google_sheets',
  '{"type": "object", "properties": {"spreadsheetId": {"type": "string"}, "range": {"type": "string"}, "values": {"type": "array"}}, "required": ["spreadsheetId", "range", "values"]}',
  '{"type": "object", "properties": {"updatedRange": {"type": "string"}, "updatedCells": {"type": "number"}}}',
  '1.0.0'
);

-- Insert Google Docs Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'google.docs.create',
  'Create Google Doc',
  'Create new Google Document',
  'google_docs',
  '{"type": "object", "properties": {"title": {"type": "string"}, "content": {"type": "string"}}, "required": ["title"]}',
  '{"type": "object", "properties": {"documentId": {"type": "string"}, "documentUrl": {"type": "string"}}}',
  '1.0.0'
),
(
  'google.docs.read',
  'Read Google Doc',
  'Read content from Google Document',
  'google_docs',
  '{"type": "object", "properties": {"documentId": {"type": "string"}}, "required": ["documentId"]}',
  '{"type": "object", "properties": {"content": {"type": "string"}, "title": {"type": "string"}}}',
  '1.0.0'
);

-- Insert Google Gmail Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'google.gmail.send',
  'Send Gmail',
  'Send email via Gmail',
  'google_gmail',
  '{"type": "object", "properties": {"to": {"type": "string"}, "subject": {"type": "string"}, "body": {"type": "string"}, "cc": {"type": "string"}, "bcc": {"type": "string"}}, "required": ["to", "subject", "body"]}',
  '{"type": "object", "properties": {"messageId": {"type": "string"}, "threadId": {"type": "string"}}}',
  '1.0.0'
),
(
  'google.gmail.read',
  'Read Gmail',
  'Read emails from Gmail',
  'google_gmail',
  '{"type": "object", "properties": {"query": {"type": "string"}, "maxResults": {"type": "number"}}}',
  '{"type": "object", "properties": {"messages": {"type": "array"}, "totalCount": {"type": "number"}}}',
  '1.0.0'
);

-- Insert Facebook Page Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'facebook.page.post',
  'Facebook Page Post',
  'Post to Facebook page',
  'facebook_page',
  '{"type": "object", "properties": {"pageId": {"type": "string"}, "message": {"type": "string"}, "link": {"type": "string"}, "imageUrl": {"type": "string"}}, "required": ["pageId", "message"]}',
  '{"type": "object", "properties": {"postId": {"type": "string"}, "postUrl": {"type": "string"}}}',
  '1.0.0'
),
(
  'facebook.page.getInsights',
  'Get Facebook Page Insights',
  'Retrieve Facebook page analytics',
  'facebook_page',
  '{"type": "object", "properties": {"pageId": {"type": "string"}, "metrics": {"type": "array"}, "period": {"type": "string"}}, "required": ["pageId", "metrics"]}',
  '{"type": "object", "properties": {"insights": {"type": "array"}, "period": {"type": "string"}}}',
  '1.0.0'
);

-- Insert Facebook Messenger Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'facebook.messenger.send',
  'Send Facebook Messenger',
  'Send message via Facebook Messenger',
  'facebook_messenger',
  '{"type": "object", "properties": {"recipientId": {"type": "string"}, "message": {"type": "string"}, "messageType": {"type": "string"}}, "required": ["recipientId", "message"]}',
  '{"type": "object", "properties": {"messageId": {"type": "string"}, "recipientId": {"type": "string"}}}',
  '1.0.0'
);

-- Insert Zalo OA Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'zalo.oa.sendMessage',
  'Zalo OA Send Message',
  'Send message via Zalo OA',
  'zalo_oa',
  '{"type": "object", "properties": {"userId": {"type": "string"}, "message": {"type": "string"}, "messageType": {"type": "string"}}, "required": ["userId", "message"]}',
  '{"type": "object", "properties": {"messageId": {"type": "string"}, "status": {"type": "string"}}}',
  '1.0.0'
),
(
  'zalo.oa.getUserInfo',
  'Get Zalo OA User Info',
  'Get user information from Zalo OA',
  'zalo_oa',
  '{"type": "object", "properties": {"userId": {"type": "string"}}, "required": ["userId"]}',
  '{"type": "object", "properties": {"userInfo": {"type": "object"}, "userId": {"type": "string"}}}',
  '1.0.0'
);

-- Insert Zalo ZNS Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'zalo.zns.send',
  'Send Zalo ZNS',
  'Send ZNS message via Zalo',
  'zalo_zns',
  '{"type": "object", "properties": {"phone": {"type": "string"}, "templateId": {"type": "string"}, "templateData": {"type": "object"}}, "required": ["phone", "templateId"]}',
  '{"type": "object", "properties": {"messageId": {"type": "string"}, "status": {"type": "string"}}}',
  '1.0.0'
);

-- Insert additional Google Drive Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'google.drive.upload',
  'Upload to Google Drive',
  'Upload file to Google Drive',
  'google_drive',
  '{"type": "object", "properties": {"fileName": {"type": "string"}, "fileContent": {"type": "string"}, "folderId": {"type": "string"}, "mimeType": {"type": "string"}}, "required": ["fileName", "fileContent"]}',
  '{"type": "object", "properties": {"fileId": {"type": "string"}, "fileUrl": {"type": "string"}}}',
  '1.0.0'
),
(
  'google.drive.download',
  'Download from Google Drive',
  'Download file from Google Drive',
  'google_drive',
  '{"type": "object", "properties": {"fileId": {"type": "string"}}, "required": ["fileId"]}',
  '{"type": "object", "properties": {"fileContent": {"type": "string"}, "fileName": {"type": "string"}, "mimeType": {"type": "string"}}}',
  '1.0.0'
);

-- Insert Google Calendar Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'google.calendar.createEvent',
  'Create Calendar Event',
  'Create event in Google Calendar',
  'google_calendar',
  '{"type": "object", "properties": {"summary": {"type": "string"}, "description": {"type": "string"}, "startTime": {"type": "string"}, "endTime": {"type": "string"}, "attendees": {"type": "array"}}, "required": ["summary", "startTime", "endTime"]}',
  '{"type": "object", "properties": {"eventId": {"type": "string"}, "eventUrl": {"type": "string"}}}',
  '1.0.0'
),
(
  'google.calendar.getEvents',
  'Get Calendar Events',
  'Retrieve events from Google Calendar',
  'google_calendar',
  '{"type": "object", "properties": {"calendarId": {"type": "string"}, "timeMin": {"type": "string"}, "timeMax": {"type": "string"}, "maxResults": {"type": "number"}}}',
  '{"type": "object", "properties": {"events": {"type": "array"}, "nextPageToken": {"type": "string"}}}',
  '1.0.0'
);

-- Insert additional system nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'system.webhook',
  'Webhook Trigger',
  'Receive webhook data from external services',
  'system',
  '{"type": "object", "properties": {"webhookUrl": {"type": "string"}, "method": {"type": "string"}, "headers": {"type": "object"}}}',
  '{"type": "object", "properties": {"data": {"type": "object"}, "headers": {"type": "object"}, "timestamp": {"type": "string"}}}',
  '1.0.0'
),
(
  'system.http.request',
  'HTTP Request',
  'Make HTTP requests to external APIs',
  'system',
  '{"type": "object", "properties": {"url": {"type": "string"}, "method": {"type": "string"}, "headers": {"type": "object"}, "body": {"type": "object"}}, "required": ["url", "method"]}',
  '{"type": "object", "properties": {"response": {"type": "object"}, "status": {"type": "number"}, "headers": {"type": "object"}}}',
  '1.0.0'
),
(
  'system.transform',
  'Data Transform',
  'Transform and manipulate data',
  'system',
  '{"type": "object", "properties": {"inputData": {"type": "object"}, "transformations": {"type": "array"}}, "required": ["inputData", "transformations"]}',
  '{"type": "object", "properties": {"outputData": {"type": "object"}, "transformedFields": {"type": "array"}}}',
  '1.0.0'
);

-- Insert Google Ads Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'google.ads.createCampaign',
  'Create Google Ads Campaign',
  'Create new Google Ads campaign',
  'google_ads',
  '{"type": "object", "properties": {"campaignName": {"type": "string"}, "budget": {"type": "number"}, "targetLocation": {"type": "string"}, "keywords": {"type": "array"}}, "required": ["campaignName", "budget"]}',
  '{"type": "object", "properties": {"campaignId": {"type": "string"}, "status": {"type": "string"}}}',
  '1.0.0'
),
(
  'google.ads.getMetrics',
  'Get Google Ads Metrics',
  'Retrieve Google Ads performance metrics',
  'google_ads',
  '{"type": "object", "properties": {"campaignId": {"type": "string"}, "dateRange": {"type": "object"}, "metrics": {"type": "array"}}, "required": ["campaignId"]}',
  '{"type": "object", "properties": {"metrics": {"type": "object"}, "impressions": {"type": "number"}, "clicks": {"type": "number"}, "cost": {"type": "number"}}}',
  '1.0.0'
);

-- Insert Facebook Ads Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'facebook.ads.createCampaign',
  'Create Facebook Ads Campaign',
  'Create new Facebook advertising campaign',
  'facebook_ads',
  '{"type": "object", "properties": {"campaignName": {"type": "string"}, "objective": {"type": "string"}, "budget": {"type": "number"}, "targeting": {"type": "object"}}, "required": ["campaignName", "objective", "budget"]}',
  '{"type": "object", "properties": {"campaignId": {"type": "string"}, "status": {"type": "string"}}}',
  '1.0.0'
),
(
  'facebook.ads.getInsights',
  'Get Facebook Ads Insights',
  'Retrieve Facebook Ads performance data',
  'facebook_ads',
  '{"type": "object", "properties": {"campaignId": {"type": "string"}, "dateRange": {"type": "object"}, "fields": {"type": "array"}}, "required": ["campaignId"]}',
  '{"type": "object", "properties": {"insights": {"type": "array"}, "spend": {"type": "number"}, "impressions": {"type": "number"}, "clicks": {"type": "number"}}}',
  '1.0.0'
);

-- Insert Instagram Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'instagram.post.create',
  'Create Instagram Post',
  'Create new post on Instagram',
  'instagram',
  '{"type": "object", "properties": {"imageUrl": {"type": "string"}, "caption": {"type": "string"}, "hashtags": {"type": "array"}, "location": {"type": "string"}}, "required": ["imageUrl", "caption"]}',
  '{"type": "object", "properties": {"postId": {"type": "string"}, "postUrl": {"type": "string"}}}',
  '1.0.0'
),
(
  'instagram.story.create',
  'Create Instagram Story',
  'Create new story on Instagram',
  'instagram',
  '{"type": "object", "properties": {"mediaUrl": {"type": "string"}, "mediaType": {"type": "string"}, "stickers": {"type": "array"}}, "required": ["mediaUrl", "mediaType"]}',
  '{"type": "object", "properties": {"storyId": {"type": "string"}, "expiresAt": {"type": "string"}}}',
  '1.0.0'
),
(
  'instagram.insights.get',
  'Get Instagram Insights',
  'Retrieve Instagram account analytics',
  'instagram',
  '{"type": "object", "properties": {"accountId": {"type": "string"}, "metrics": {"type": "array"}, "period": {"type": "string"}}, "required": ["accountId", "metrics"]}',
  '{"type": "object", "properties": {"insights": {"type": "array"}, "followerCount": {"type": "number"}, "engagement": {"type": "number"}}}',
  '1.0.0'
);

-- Insert General Zalo Nodes
INSERT INTO node_definitions (type, name, description, category, input_schema, output_schema, version) VALUES
(
  'zalo.user.getProfile',
  'Get Zalo User Profile',
  'Get user profile information from Zalo',
  'zalo',
  '{"type": "object", "properties": {"userId": {"type": "string"}, "accessToken": {"type": "string"}}, "required": ["userId", "accessToken"]}',
  '{"type": "object", "properties": {"profile": {"type": "object"}, "userId": {"type": "string"}}}',
  '1.0.0'
),
(
  'zalo.payment.create',
  'Create Zalo Payment',
  'Create payment request via Zalo Pay',
  'zalo',
  '{"type": "object", "properties": {"amount": {"type": "number"}, "description": {"type": "string"}, "orderId": {"type": "string"}, "userId": {"type": "string"}}, "required": ["amount", "orderId", "userId"]}',
  '{"type": "object", "properties": {"paymentUrl": {"type": "string"}, "transactionId": {"type": "string"}}}',
  '1.0.0'
);

-- Verify the inserted data
SELECT
  type,
  name,
  category,
  version,
  CASE
    WHEN LENGTH(description) > 50 THEN CONCAT(LEFT(description, 50), '...')
    ELSE description
  END as description_preview
FROM node_definitions
ORDER BY category, type;

-- Get statistics by category
SELECT
  category,
  COUNT(*) as node_count
FROM node_definitions
GROUP BY category
ORDER BY category;

-- Total count
SELECT COUNT(*) as total_nodes FROM node_definitions;

-- Get statistics
SELECT
  category,
  COUNT(*) as node_count
FROM node_definitions
GROUP BY category
ORDER BY category;
