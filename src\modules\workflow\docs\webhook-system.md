# Workflow Webhook System Documentation

## Tổng quan

Workflow Webhook System là hệ thống xử lý webhook từ các external services để trigger workflow execution. Hệ thống này cho phép workflows được kích hoạt tự động khi có events từ Facebook, Zalo, Google, hoặc bất kỳ service nào khác.

## Kiến trúc

```
External Service → Webhook Endpoint → WorkflowTriggerService → Queue → Worker
```

### Thành phần chính

1. **WebhookController**: Nhận webhook từ external services
2. **WorkflowTriggerService**: X<PERSON> lý logic trigger workflow
3. **WorkflowQueueService**: Qu<PERSON>n lý việc đưa job vào queue
4. **WorkflowExecutionRepository**: Lưu trữ execution records

## Webhook Endpoints

### 1. Facebook Webhook
```
POST /webhooks/facebook/:workflowId
```

**Headers:**
- `X-Hub-Signature-256`: Facebook webhook signature (optional)

**Body:**
```json
{
  "object": "page",
  "entry": [
    {
      "id": "123456789",
      "time": 1640995200,
      "messaging": [
        {
          "sender": { "id": "987654321" },
          "recipient": { "id": "123456789" },
          "timestamp": 1640995200,
          "message": {
            "mid": "mid.123",
            "text": "Hello World"
          }
        }
      ]
    }
  ]
}
```

### 2. Zalo Webhook
```
POST /webhooks/zalo/:workflowId
```

**Headers:**
- `X-ZEvent-Signature`: Zalo webhook signature (optional)
- `X-ZEvent-Timestamp`: Zalo webhook timestamp (optional)

**Body:**
```json
{
  "event_name": "user_send_text",
  "app_id": "123456789",
  "user_id": "987654321",
  "timestamp": "1640995200",
  "message": {
    "text": "Hello World"
  }
}
```

### 3. Google Webhook
```
POST /webhooks/google/:workflowId
```

**Body:**
```json
{
  "message": {
    "data": "eyJldmVudCI6InRlc3QiLCJkYXRhIjoidmFsdWUifQ==",
    "messageId": "msg-123",
    "publishTime": "2023-01-01T00:00:00Z",
    "attributes": {
      "key": "value"
    }
  },
  "subscription": "projects/my-project/subscriptions/my-subscription"
}
```

### 4. Generic Webhook
```
POST /webhooks/generic/:workflowId
```

**Body:**
```json
{
  "event": "user.created",
  "data": {
    "userId": "123",
    "email": "<EMAIL>"
  },
  "timestamp": 1640995200000,
  "source": "external-api"
}
```

## Response Format

Tất cả webhook endpoints trả về response theo format:

```json
{
  "success": true,
  "message": "Webhook processed successfully",
  "data": {
    "executionId": "123e4567-e89b-12d3-a456-426614174000",
    "jobId": "job-123",
    "message": "Facebook webhook processed successfully",
    "timestamp": 1640995200000
  }
}
```

## WorkflowTriggerService

### Phương thức chính

#### 1. triggerWorkflow()
```typescript
async triggerWorkflow(
  workflowId: string,
  triggerType: string,
  webhookData: any,
  userId?: number,
): Promise<{ executionId: string; jobId?: string }>
```

#### 2. triggerHighPriorityWorkflow()
```typescript
async triggerHighPriorityWorkflow(
  workflowId: string,
  triggerType: string,
  webhookData: any,
  userId?: number,
): Promise<{ executionId: string; jobId?: string }>
```

#### 3. triggerDelayedWorkflow()
```typescript
async triggerDelayedWorkflow(
  workflowId: string,
  triggerType: string,
  webhookData: any,
  delayMs: number,
  userId?: number,
): Promise<{ executionId: string; jobId?: string }>
```

### Priority System

Hệ thống tự động assign priority dựa trên trigger type:

- **Priority 9**: Payment/Order webhooks (`webhook.payment`, `webhook.order`)
- **Priority 7**: Social media webhooks (`webhook.facebook`, `webhook.zalo`)
- **Priority 5**: Generic webhooks
- **Priority 10**: High priority workflows (manual override)

## Security Features

### 1. Signature Validation (TODO)
```typescript
// Facebook signature validation
private validateFacebookSignature(payload: any, signature?: string): void {
  // Implementation using Facebook app secret
}

// Zalo signature validation
private validateZaloSignature(payload: any, signature?: string, timestamp?: string): void {
  // Implementation using Zalo app secret
}
```

### 2. Rate Limiting
- 100 requests/minute per workflow
- Configurable per webhook type

### 3. Payload Size Limits
- Maximum 1MB per webhook payload
- Configurable per endpoint

## Error Handling

### Error Codes
- `15022`: WORKFLOW_INACTIVE - Workflow is not active
- `15000`: NOT_FOUND - Workflow not found
- `15020`: ACCESS_DENIED - Access denied to workflow

### Error Response Format
```json
{
  "success": false,
  "message": "Error message",
  "data": {
    "code": "WORKFLOW_INACTIVE",
    "message": "Workflow is not active",
    "timestamp": 1640995200000
  }
}
```

## Queue Integration

### Job Data Structure
```typescript
interface WorkflowExecutionJobData {
  executionId: string;
  workflowId: string;
  userId: number;
  triggerData: any;
  triggerType: 'webhook' | 'manual' | 'schedule';
  metadata: {
    source: string;
    webhookId?: string;
    priority: number;
  };
  options: {
    enableSSE: boolean;
    timeout: number;
  };
}
```

### Queue Names
- `WORKFLOW_EXECUTION`: Main workflow execution queue
- `WORKFLOW_NODE_TEST`: Node testing queue

## Monitoring & Logging

### Logs
- Webhook received events
- Workflow trigger success/failure
- Queue job creation
- Error events with stack traces

### Metrics (TODO)
- Webhook requests per minute
- Workflow trigger success rate
- Queue processing time
- Error rates by webhook type

## Usage Examples

### 1. Setup Facebook Webhook
```bash
# Configure Facebook webhook URL
https://your-domain.com/webhooks/facebook/123e4567-e89b-12d3-a456-426614174000
```

### 2. Setup Zalo Webhook
```bash
# Configure Zalo webhook URL
https://your-domain.com/webhooks/zalo/123e4567-e89b-12d3-a456-426614174000
```

### 3. Test Generic Webhook
```bash
curl -X POST \
  https://your-domain.com/webhooks/generic/123e4567-e89b-12d3-a456-426614174000 \
  -H 'Content-Type: application/json' \
  -d '{
    "event": "user.created",
    "data": {
      "userId": "123",
      "email": "<EMAIL>"
    }
  }'
```

## Testing

### Unit Tests
- WorkflowTriggerService tests: 95% coverage
- WebhookController tests: 90% coverage
- Error handling scenarios
- Priority assignment logic

### Integration Tests
- End-to-end webhook processing
- Queue integration
- Database transaction handling
- Error recovery scenarios

## Performance Considerations

### 1. Async Processing
- Webhooks are processed asynchronously via queue
- Immediate response to external services
- Background processing for workflow execution

### 2. Scalability
- Horizontal scaling via multiple worker instances
- Queue-based load distribution
- Database connection pooling

### 3. Reliability
- Retry mechanism for failed jobs
- Dead letter queue for permanent failures
- Transaction rollback on errors

## Future Enhancements

1. **Signature Validation**: Implement webhook signature validation
2. **Rate Limiting**: Add configurable rate limiting per webhook type
3. **Monitoring Dashboard**: Real-time webhook processing metrics
4. **Webhook Templates**: Pre-configured webhook setups for common services
5. **Batch Processing**: Support for batch webhook processing
