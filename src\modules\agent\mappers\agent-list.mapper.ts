import { Injectable, Logger } from '@nestjs/common';
import { AgentListItemDto } from '@modules/agent/user/dto/agent/agent-response.dto';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@/shared/utils';

/**
 * Interface cho dữ liệu agent từ unified architecture
 */
export interface AgentListUnifiedResult {
  id: string;
  name: string;
  avatar: string | null;
  instruction: string | null;
  modelConfig: any;
  config: any;
  createdAt: Date;
  updatedAt: Date;
  typeAgent: {
    id: string;
    name: string;
    type: string;
    avatar: string | null;
  } | null;
}

/**
 * Mapper để chuyển đổi dữ liệu raw từ database sang DTO cho danh sách agent
 */
@Injectable()
export class AgentListMapper {
  private readonly logger = new Logger(AgentListMapper.name);

  constructor(private readonly cdnService: CdnService) {}

  // /**
  //  * Chuyển đổi từ AgentListUnifiedResult sang AgentListItemDto
  //  * @param rawData Dữ liệu từ unified architecture
  //  * @returns AgentListItemDto
  //  */
  // toDto(rawData: AgentListUnifiedResult): AgentListItemDto {
  //   try {
  //     // Chuyển đổi avatar từ S3 key sang URL đầy đủ
  //     const avatarUrl = rawData.avatar
  //       ? this.cdnService.generateUrlView(rawData.avatar, TimeIntervalEnum.FIVE_MINUTES)
  //       : null;

  //     // Chuyển đổi type agent avatar từ S3 key sang URL đầy đủ
  //     const typeAvatarUrl = rawData.typeAgent?.avatar
  //       ? this.cdnService.generateUrlView(rawData.typeAgent.avatar, TimeIntervalEnum.FIVE_MINUTES)
  //       : null;

  //     // Extract model ID từ modelConfig nếu có
  //     let resolvedModelId: string | undefined;
  //     if (rawData.modelConfig) {
  //       // Có thể có systemModelId hoặc userModelId trong modelConfig
  //       resolvedModelId = rawData.modelConfig.systemModelId || rawData.modelConfig.userModelId;
  //     }

  //     return {
  //       id: rawData.id,
  //       name: rawData.name,
  //       avatar: avatarUrl,
  //       typeName: rawData.typeAgent?.name || '',
  //       exp: 0, // TODO: Implement exp system in unified architecture
  //       expMax: 0, // TODO: Implement rank system in unified architecture
  //       level: 1, // TODO: Implement rank system in unified architecture
  //       badgeUrl: null, // TODO: Implement rank system in unified architecture
  //       modelId: resolvedModelId,
  //       active: true, // TODO: Implement active status in unified architecture
  //     };
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi chuyển đổi dữ liệu agent: ${error.message}`, error.stack);
  //     throw error;
  //   }
  // }

  // /**
  //  * Chuyển đổi mảng AgentListUnifiedResult sang mảng AgentListItemDto
  //  * @param rawDataList Mảng dữ liệu từ unified architecture
  //  * @returns Mảng AgentListItemDto
  //  */
  // toDtoList(rawDataList: AgentListUnifiedResult[]): AgentListItemDto[] {
  //   return rawDataList.map(rawData => this.toDto(rawData));
  // }
}
