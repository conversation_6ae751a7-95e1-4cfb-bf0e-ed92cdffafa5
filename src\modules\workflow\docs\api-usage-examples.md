# Workflow API Usage Examples

## 📋 Overview

This document provides comprehensive examples of how to use the Workflow API endpoints. All examples include request/response formats, authentication, and error handling.

## 🔐 Authentication

All API endpoints require JWT authentication via Bearer token:

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 🔄 Workflow Management

### Create a New Workflow

```http
POST /api/v1/user/workflows
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Customer Onboarding Workflow",
  "description": "Automated customer onboarding process",
  "definition": {
    "nodes": [
      {
        "id": "start",
        "type": "trigger",
        "position": { "x": 100, "y": 100 },
        "data": {
          "triggerType": "webhook",
          "webhookPath": "/customer-signup"
        }
      },
      {
        "id": "send-welcome-email",
        "type": "email",
        "position": { "x": 300, "y": 100 },
        "data": {
          "to": "{{customer.email}}",
          "subject": "Welcome to our platform!",
          "template": "welcome-email"
        }
      }
    ],
    "edges": [
      {
        "id": "e1",
        "source": "start",
        "target": "send-welcome-email"
      }
    ]
  },
  "isActive": true,
  "tags": ["onboarding", "email"]
}
```

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "result": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Customer Onboarding Workflow",
    "description": "Automated customer onboarding process",
    "definition": { ... },
    "isActive": true,
    "tags": ["onboarding", "email"],
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Get Workflow List with Pagination

```http
GET /api/v1/user/workflows?page=1&limit=10&isActive=true&search=onboarding
Authorization: Bearer <token>
```

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "result": {
    "workflows": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Customer Onboarding Workflow",
        "description": "Automated customer onboarding process",
        "isActive": true,
        "tags": ["onboarding", "email"],
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10
  }
}
```

## ⚡ Workflow Execution

### Trigger Workflow Execution

```http
POST /api/v1/user/workflow-executions
Authorization: Bearer <token>
Content-Type: application/json

{
  "workflowId": "123e4567-e89b-12d3-a456-426614174000",
  "triggerEvent": {
    "type": "manual",
    "data": {
      "customer": {
        "email": "<EMAIL>",
        "name": "John Doe",
        "plan": "premium"
      }
    }
  },
  "metadata": {
    "source": "admin_panel",
    "priority": "high"
  }
}
```

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "result": {
    "id": "exec-789abc-def012-345678",
    "workflowId": "123e4567-e89b-12d3-a456-426614174000",
    "status": "queued",
    "triggerEvent": { ... },
    "startedAt": 1640995200000,
    "metadata": { ... },
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Monitor Execution Status

```http
GET /api/v1/user/workflow-executions/exec-789abc-def012-345678
Authorization: Bearer <token>
```

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "result": {
    "id": "exec-789abc-def012-345678",
    "workflowId": "123e4567-e89b-12d3-a456-426614174000",
    "status": "completed",
    "startedAt": 1640995200000,
    "finishedAt": 1640995260000,
    "duration": 60000,
    "result": {
      "emailSent": true,
      "emailId": "msg_123456789"
    },
    "progress": {
      "completedNodes": 2,
      "totalNodes": 2,
      "percentage": 100
    }
  }
}
```

## 🧪 Node Testing

### Test Individual Node

```http
POST /api/v1/user/node-tests
Authorization: Bearer <token>
Content-Type: application/json

{
  "nodeDefinitionId": "http-request-node",
  "inputData": {
    "url": "https://jsonplaceholder.typicode.com/posts/1",
    "method": "GET",
    "headers": {
      "Content-Type": "application/json"
    }
  },
  "nodeConfig": {
    "timeout": 5000,
    "retries": 3
  },
  "metadata": {
    "testType": "manual",
    "description": "Test API endpoint connectivity"
  }
}
```

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "result": {
    "id": "test_1640995200000_abc123",
    "nodeDefinitionId": "http-request-node",
    "nodeName": "HTTP Request",
    "nodeType": "http_request",
    "status": "pending",
    "inputData": { ... },
    "startedAt": 1640995200000,
    "userId": 1,
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Generate Mock Data for Node

```http
POST /api/v1/user/node-tests/mock-data
Authorization: Bearer <token>
Content-Type: application/json

{
  "nodeDefinitionId": "http-request-node",
  "template": "realistic",
  "customSchema": {
    "url": {
      "type": "string",
      "example": "https://api.example.com/users"
    },
    "method": {
      "type": "string",
      "example": "POST"
    }
  }
}
```

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "result": {
    "url": "https://jsonplaceholder.typicode.com/posts/1",
    "method": "GET",
    "headers": {
      "Authorization": "Bearer your-token-here",
      "Content-Type": "application/json"
    },
    "timeout": 5000,
    "retries": 3
  }
}
```

## 📡 Real-time Updates (SSE)

### Connect to Execution Events Stream

```javascript
// JavaScript example
const token = 'your-jwt-token';
const executionId = 'exec-789abc-def012-345678';

const eventSource = new EventSource(
  `/api/v1/user/workflow-sse/executions/stream?auth=${token}&executionId=${executionId}`
);

eventSource.onopen = function(event) {
  console.log('SSE connection opened');
};

eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Received event:', data);
  
  switch (data.type) {
    case 'execution_started':
      console.log('Execution started:', data.executionId);
      break;
    case 'execution_completed':
      console.log('Execution completed:', data.executionId);
      break;
    case 'execution_failed':
      console.log('Execution failed:', data.executionId, data.data.error);
      break;
    case 'heartbeat':
      console.log('Heartbeat received');
      break;
  }
};

eventSource.onerror = function(event) {
  console.error('SSE error:', event);
};

// Close connection when done
// eventSource.close();
```

### Node Events Stream

```javascript
const eventSource = new EventSource(
  `/api/v1/user/workflow-sse/nodes/stream?auth=${token}&executionId=${executionId}`
);

eventSource.addEventListener('node_started', function(event) {
  const data = JSON.parse(event.data);
  console.log('Node started:', data.data.nodeId, data.data.nodeType);
});

eventSource.addEventListener('node_completed', function(event) {
  const data = JSON.parse(event.data);
  console.log('Node completed:', data.data.nodeId, data.data.output);
});
```

## 🔗 Webhook Integration

### Register Webhook Endpoint

```http
POST /api/v1/webhooks/workflow/trigger
Content-Type: application/json
X-Webhook-Signature: sha256=<signature>

{
  "workflowId": "123e4567-e89b-12d3-a456-426614174000",
  "event": "customer.created",
  "data": {
    "customer": {
      "id": "cust_123456",
      "email": "<EMAIL>",
      "name": "Jane Doe",
      "plan": "basic"
    }
  },
  "timestamp": "2023-01-01T00:00:00.000Z"
}
```

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "result": {
    "executionId": "exec-webhook-123456",
    "status": "queued",
    "message": "Workflow execution queued successfully"
  }
}
```

## 📊 Statistics and Analytics

### Get Execution Statistics

```http
GET /api/v1/user/workflow-executions/statistics/overview?workflowId=123e4567-e89b-12d3-a456-426614174000
Authorization: Bearer <token>
```

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "result": {
    "total": 150,
    "byStatus": {
      "completed": 120,
      "failed": 20,
      "running": 5,
      "queued": 3,
      "cancelled": 2
    },
    "avgDuration": 45000,
    "successRate": 80.0
  }
}
```

### Get Node Test Statistics

```http
GET /api/v1/user/node-tests/statistics/overview
Authorization: Bearer <token>
```

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "result": {
    "total": 50,
    "byStatus": {
      "completed": 40,
      "failed": 8,
      "running": 1,
      "pending": 1
    },
    "byNodeType": {
      "http_request": 25,
      "condition": 15,
      "email": 10
    },
    "avgDuration": 2500,
    "successRate": 80.0,
    "topTestedNodeTypes": [
      { "nodeType": "http_request", "count": 25 },
      { "nodeType": "condition", "count": 15 }
    ]
  }
}
```

## 🔄 Bulk Operations

### Bulk Node Testing

```http
POST /api/v1/user/node-tests/bulk
Authorization: Bearer <token>
Content-Type: application/json

{
  "tests": [
    {
      "nodeDefinitionId": "http-request-node",
      "inputData": { "url": "https://api1.example.com" }
    },
    {
      "nodeDefinitionId": "condition-node",
      "inputData": { "condition": "x > 5", "variables": { "x": 10 } }
    }
  ],
  "parallel": true,
  "timeoutSeconds": 300
}
```

**Response:**
```json
{
  "code": 0,
  "message": "Success",
  "result": {
    "testIds": [
      "test_1640995200000_abc123",
      "test_1640995200001_def456"
    ],
    "summary": {
      "total": 2,
      "pending": 2,
      "running": 0,
      "completed": 0,
      "failed": 0
    }
  }
}
```

## 🛠️ Error Handling Examples

### Handling Validation Errors

```javascript
async function createWorkflow(workflowData) {
  try {
    const response = await fetch('/api/v1/user/workflows', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(workflowData),
    });

    const data = await response.json();

    if (!response.ok) {
      if (data.code === 11001) {
        // Validation error
        console.error('Validation errors:', data.detail.errors);
        data.detail.errors.forEach(error => {
          console.error(`Field ${error.field}: ${error.constraint}`);
        });
      }
      throw new Error(data.message);
    }

    return data.result;
  } catch (error) {
    console.error('Failed to create workflow:', error);
    throw error;
  }
}
```

## 📚 SDK Examples

### TypeScript SDK Usage

```typescript
import { WorkflowApiClient } from './workflow-api-client';

const client = new WorkflowApiClient({
  baseUrl: 'http://localhost:3000/api/v1',
  token: 'your-jwt-token'
});

// Create workflow
const workflow = await client.workflows.create({
  name: 'My Workflow',
  definition: { ... }
});

// Execute workflow
const execution = await client.executions.create({
  workflowId: workflow.id,
  triggerEvent: { ... }
});

// Monitor execution with SSE
client.sse.onExecutionEvent(execution.id, (event) => {
  console.log('Execution event:', event);
});

// Test node
const test = await client.nodeTests.create({
  nodeDefinitionId: 'http-request-node',
  inputData: { url: 'https://api.example.com' }
});
```

This documentation provides comprehensive examples for all major workflow API operations. For more details, refer to the [Swagger API Documentation](http://localhost:3000/api/docs).
