import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber, IsArray, IsObject, Min, IsEnum, ValidateIf, IsUUID } from 'class-validator';
import { SmsCampaignType } from '../../../enums/sms-campaign-type.enum';

/**
 * DTO cho tạo SMS campaign với template
 */
export class CreateSmsCampaignDto {
  /**
   * Tên campaign
   * @example "Chiến dịch SMS khuyến mãi Black Friday"
   */
  @ApiProperty({
    description: 'Tên campaign',
    example: 'Chiến dịch SMS khuyến mãi Black Friday',
  })
  @IsNotEmpty({ message: 'Tên campaign không được để trống' })
  @IsString({ message: 'Tên campaign phải là chuỗi' })
  name: string;

  /**
   * Loại chiến dịch SMS
   * @example "ADS"
   */
  @ApiProperty({
    description: 'Loại chiến dịch SMS',
    example: SmsCampaignType.ADS,
    enum: SmsCampaignType,
    enumName: 'SmsCampaignType',
  })
  @IsNotEmpty({ message: 'Loại chiến dịch không được để trống' })
  @IsEnum(SmsCampaignType, { message: 'Loại chiến dịch phải là OTP hoặc ADS' })
  campaignType: SmsCampaignType;

  /**
   * Mô tả campaign
   * @example "Chiến dịch SMS marketing cho sự kiện Black Friday"
   */
  @ApiProperty({
    description: 'Mô tả campaign',
    example: 'Chiến dịch SMS marketing cho sự kiện Black Friday',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả campaign phải là chuỗi' })
  description?: string;

  /**
   * ID của template SMS
   * @example 1
   */
  @ApiProperty({
    description: 'ID của template SMS',
    example: 1,
  })
  @IsNotEmpty({ message: 'Template ID không được để trống' })
  @IsNumber({}, { message: 'Template ID phải là số' })
  templateId: number;

  /**
   * ID của SMS server/integration
   * @example "550e8400-e29b-41d4-a716-************"
   */
  @ApiProperty({
    description: 'ID của SMS server/integration',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Server ID không được để trống' })
  @IsUUID('4', { message: 'Server ID phải là UUID hợp lệ' })
  serverId: string;

  /**
   * Danh sách ID của segments
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID của segments để gửi SMS',
    example: [1, 2, 3],
    type: [Number],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Segment IDs phải là mảng' })
  @IsNumber({}, { each: true, message: 'Mỗi segment ID phải là số' })
  segmentIds?: number[];

  /**
   * Danh sách ID của audiences
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID của audiences để gửi SMS',
    example: [1, 2, 3],
    type: [Number],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Audience IDs phải là mảng' })
  @IsNumber({}, { each: true, message: 'Mỗi audience ID phải là số' })
  audienceIds?: number[];

  /**
   * Template variables
   * @example { "customerName": "Nguyễn Văn A", "discountPercent": "50" }
   */
  @ApiProperty({
    description: 'Template variables để thay thế trong nội dung SMS',
    example: {
      customerName: 'Nguyễn Văn A',
      discountPercent: '50',
      validUntil: '31/12/2024'
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Template variables phải là object' })
  templateVariables?: Record<string, any>;

  /**
   * Danh sách số điện thoại (nếu gửi cho số điện thoại cụ thể)
   * @example ["0901234567", "0987654321"]
   */
  @ApiProperty({
    description: 'Danh sách số điện thoại. Hỗ trợ tất cả định dạng số điện thoại quốc tế',
    example: ["0921843966", "+84901234567", "84987654321", "+1234567890"],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Phone numbers phải là mảng' })
  @IsString({ each: true, message: 'Mỗi số điện thoại phải là chuỗi' })
  @IsNotEmpty({ each: true, message: 'Số điện thoại không được để trống' })
  phoneNumbers?: string[];

  /**
   * Thời gian lên lịch gửi (Unix timestamp)
   * @example 1703980800
   */
  @ApiProperty({
    description: 'Thời gian lên lịch gửi (Unix timestamp). Bắt buộc đối với ADS campaign',
    example: 1703980800,
    required: false,
  })
  @ValidateIf((obj) => obj.campaignType === SmsCampaignType.ADS)
  @IsNotEmpty({ message: 'scheduledAt là bắt buộc đối với ADS campaign' })
  @IsNumber({}, { message: 'scheduledAt phải là số' })
  @Min(Math.floor(Date.now() / 1000), { message: 'scheduledAt phải là thời gian trong tương lai' })
  scheduledAt?: number;
}


