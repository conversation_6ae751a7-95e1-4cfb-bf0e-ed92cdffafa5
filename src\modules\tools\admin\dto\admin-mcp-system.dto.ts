import { QueryDto } from '@common/dto/query.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  Length,
  Max,
  Min,
  ValidateNested,
  Matches,
  IsInt
} from 'class-validator';
import { McpConfig, McpTransportType } from '../../interfaces/mcp-config.interface';

/**
 * DTO cho MCP Configuration (admin version)
 */
export class AdminMcpConfigDto implements McpConfig {
  @ApiProperty({
    description: 'URL endpoint của MCP server',
    example: 'http://redai-affiliate-server:8004/mcp'
  })
  @Matches(/^https?:\/\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=%]+$/, {
    message: 'url must be a valid URL address'
  })
  url: string;

  @ApiProperty({
    description: 'Loại transport để kết nối',
    enum: ['http', 'sse'],
    example: 'http'
  })
  @IsEnum(['http', 'sse'])
  transport: McpTransportType;

  @ApiPropertyOptional({
    description: 'Header cho MCP server (JSON string)',
    example: '{"Authorization": "Bearer token"}'
  })
  @IsOptional()
  @IsObject()
  headers?: Record<string, string>;

  @ApiPropertyOptional({
    description: 'Tự động fallback sang SSE nếu transport chính thất bại',
    example: true,
    default: true
  })
  @IsOptional()
  automaticSSEFallback?: boolean = true;
}

/**
 * DTO cho việc tạo MCP System mới (admin)
 */
export class CreateMcpSystemDto {
  @ApiProperty({
    description: 'Tên MCP server (unique)',
    example: 'redai-affiliate-server',
    maxLength: 255
  })
  @IsString()
  @Length(1, 255)
  nameServer: string;

  @ApiPropertyOptional({
    description: 'Mô tả MCP system',
    example: 'MCP server for affiliate management'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: 'Cấu hình MCP system',
    type: AdminMcpConfigDto
  })
  @IsObject()
  @ValidateNested()
  @Type(() => AdminMcpConfigDto)
  config: AdminMcpConfigDto;
}

/**
 * DTO cho việc cập nhật MCP System
 */
export class UpdateMcpSystemDto {
  @ApiPropertyOptional({
    description: 'Tên MCP server (unique)',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  nameServer?: string;

  @ApiPropertyOptional({ 
    description: 'Mô tả MCP system' 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ 
    description: 'Cấu hình MCP system',
    type: AdminMcpConfigDto
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AdminMcpConfigDto)
  config?: AdminMcpConfigDto;

  @ApiPropertyOptional({
    description: 'Header mặc định cho tất cả các request đến MCP server (JSON string)',
    example: '{"Authorization": "Bearer token", "Content-Type": "application/json"}'
  })
  @IsOptional()
  @IsString()
  headers?: string;
}

/**
 * DTO cho query MCP Systems
 */
export class QueryMcpSystemDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Lọc theo transport type',
    enum: ['http', 'sse']
  })
  @IsOptional()
  @IsEnum(['http', 'sse'])
  transport?: McpTransportType;

  @ApiPropertyOptional({
    description: 'Lọc theo employee tạo'
  })
  @IsOptional()
  @IsInt()
  createdBy?: number;

  @ApiPropertyOptional({
    description: 'Lọc theo employee cập nhật'
  })
  @IsOptional()
  @IsInt()
  updatedBy?: number;
}

/**
 * Response DTO cho MCP System
 */
export class McpSystemResponseDto {
  @ApiProperty({ description: 'ID của MCP System' })
  id: string;

  @ApiProperty({ description: 'Tên MCP server' })
  nameServer: string;

  @ApiPropertyOptional({ description: 'Mô tả MCP system' })
  description?: string;

  @ApiProperty({ description: 'Thời điểm tạo' })
  createdAt: number;

  @ApiProperty({ description: 'Thời điểm cập nhật' })
  updatedAt: number;
}

/**
 * Response DTO chi tiết cho MCP System
 */
export class McpSystemDetailResponseDto extends McpSystemResponseDto {
  @ApiProperty({ 
    description: 'Cấu hình MCP system',
    type: AdminMcpConfigDto
  })
  config: AdminMcpConfigDto;
}

/**
 * DTO cho việc test connection MCP System
 */
export class TestMcpSystemConnectionDto {
  @ApiProperty({ 
    description: 'Cấu hình MCP system để test',
    type: AdminMcpConfigDto
  })
  @IsObject()
  @ValidateNested()
  @Type(() => AdminMcpConfigDto)
  config: AdminMcpConfigDto;

  @ApiPropertyOptional({
    description: 'Header để test (JSON string)',
    example: '{"Authorization": "Bearer token"}'
  })
  @IsOptional()
  @IsString()
  headers?: string;

  @ApiPropertyOptional({
    description: 'Timeout cho test (ms)',
    default: 10000,
    minimum: 1000,
    maximum: 60000
  })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(60000)
  timeout?: number;
}

/**
 * Response DTO cho test connection
 */
export class TestMcpSystemConnectionResponseDto {
  @ApiProperty({ description: 'Kết quả test connection' })
  success: boolean;

  @ApiPropertyOptional({ description: 'Thông báo lỗi (nếu có)' })
  error?: string;

  @ApiPropertyOptional({ description: 'Thời gian response (ms)' })
  responseTime?: number;

  @ApiPropertyOptional({ description: 'Server info (nếu có)' })
  serverInfo?: {
    name?: string;
    version?: string;
    capabilities?: Record<string, any>;
  };
}
