import { ApiProperty } from '@nestjs/swagger';
import { ProviderLlmEnum } from '@/modules/models/constants/provider.enum';

/**
 * Interface cho pricing information
 */
export interface ModelPricingInterface {
  inputRate: number;
  outputRate: number;
}

/**
 * DTO cho response của user models
 */
export class UserModelsResponseDto {
  /**
   * ID của user model
   */
  @ApiProperty({
    description: 'ID của user model',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  /**
   * ID định danh của model
   */
  @ApiProperty({
    description: 'ID định danh của model',
    example: 'gpt-4-turbo'
  })
  modelId: string;
  
  /**
   * Các phương thức input được hỗ trợ
   */
  @ApiProperty({
    description: 'Các phương thức input được hỗ trợ',
    example: ['text', 'image'],
    type: [String]
  })
  inputModalities: string[];

  /**
   * <PERSON><PERSON><PERSON> phương thức output được hỗ trợ
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> phương thức output được hỗ trợ',
    example: ['text'],
    type: [String]
  })
  outputModalities: string[];

  /**
   * Các tham số sampling được hỗ trợ
   */
  @ApiProperty({
    description: 'Các tham số sampling được hỗ trợ',
    example: ['temperature', 'top_p', 'max_tokens'],
    type: [String]
  })
  samplingParameters: string[];

  /**
   * Các tính năng được hỗ trợ
   */
  @ApiProperty({
    description: 'Các tính năng được hỗ trợ',
    example: ['function_calling', 'streaming'],
    type: [String]
  })
  features: string[];

  /**
   * Giá cơ bản cho model
   */
  @ApiProperty({
    description: 'Giá cơ bản cho model (input/output rate)',
    example: { inputRate: 0.01, outputRate: 0.03 }
  })
  basePricing: ModelPricingInterface;

  /**
   * Giá fine-tune cho model
   */
  @ApiProperty({
    description: 'Giá fine-tune cho model (input/output rate)',
    example: { inputRate: 0.02, outputRate: 0.06 }
  })
  fineTunePricing: ModelPricingInterface;

  /**
   * Giá training cho model
   */
  @ApiProperty({
    description: 'Giá training cho model',
    example: 100
  })
  trainingPricing: number;

  @ApiProperty({
    description: 'Số tokens tối đa có thể sinh ra',
    example: 1000,
  })
  maxTokens: number;

  @ApiProperty({
    description: 'Độ dài context tối đa',
    example: 1000,
  })
  contextWindow: number;
}
