import { ErrorCode } from '@common/exceptions';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi cho module Integration
 * Phạm vi: 11000-11999
 */
export const INTEGRATION_ERROR_CODES = {
  // Email Server Configuration (11000-11099)
  EMAIL_SERVER_NOT_FOUND: new ErrorCode(11000, 'Không tìm thấy cấu hình máy chủ email', HttpStatus.NOT_FOUND),
  EMAIL_SERVER_LIST_FAILED: new ErrorCode(11001, 'Không thể lấy danh sách cấu hình máy chủ email', HttpStatus.INTERNAL_SERVER_ERROR),
  EMAIL_SERVER_DETAIL_FAILED: new ErrorCode(11002, 'Không thể lấy thông tin chi tiết cấu hình máy chủ email', HttpStatus.INTERNAL_SERVER_ERROR),
  EMAIL_SERVER_CREATE_FAILED: new ErrorCode(11003, 'Không thể tạo cấu hình máy chủ email', HttpStatus.BAD_REQUEST),
  EMAIL_SERVER_UPDATE_FAILED: new ErrorCode(11004, 'Không thể cập nhật cấu hình máy chủ email', HttpStatus.BAD_REQUEST),
  EMAIL_SERVER_DELETE_FAILED: new ErrorCode(11005, 'Không thể xóa cấu hình máy chủ email', HttpStatus.INTERNAL_SERVER_ERROR),
  EMAIL_SERVER_TEST_FAILED: new ErrorCode(11006, 'Không thể kiểm tra kết nối máy chủ email', HttpStatus.BAD_REQUEST),
  EMAIL_SERVER_INVALID_UUID: new ErrorCode(11007, 'ID cấu hình máy chủ email không đúng định dạng UUID', HttpStatus.BAD_REQUEST),

  // User Key (11100-11199)
  USER_KEY_NOT_FOUND: new ErrorCode(11100, 'Không tìm thấy API key', HttpStatus.NOT_FOUND),
  USER_KEY_LIST_FAILED: new ErrorCode(11101, 'Không thể lấy danh sách API key', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_KEY_DETAIL_FAILED: new ErrorCode(11102, 'Không thể lấy thông tin chi tiết API key', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_KEY_CREATE_FAILED: new ErrorCode(11103, 'Không thể tạo API key', HttpStatus.BAD_REQUEST),
  USER_KEY_UPDATE_FAILED: new ErrorCode(11104, 'Không thể cập nhật API key', HttpStatus.BAD_REQUEST),
  USER_KEY_DELETE_FAILED: new ErrorCode(11105, 'Không thể xóa API key', HttpStatus.INTERNAL_SERVER_ERROR),
  USER_KEY_TEST_FAILED: new ErrorCode(11106, 'Không thể kiểm tra API key', HttpStatus.BAD_REQUEST),

  // Payment Gateway (11200-11299)
  PAYMENT_GATEWAY_NOT_FOUND: new ErrorCode(11200, 'Không tìm thấy cổng thanh toán', HttpStatus.NOT_FOUND),
  PAYMENT_GATEWAY_LIST_FAILED: new ErrorCode(11201, 'Không thể lấy danh sách cổng thanh toán', HttpStatus.INTERNAL_SERVER_ERROR),
  PAYMENT_GATEWAY_DETAIL_FAILED: new ErrorCode(11202, 'Không thể lấy thông tin chi tiết cổng thanh toán', HttpStatus.INTERNAL_SERVER_ERROR),
  PAYMENT_GATEWAY_CREATE_FAILED: new ErrorCode(11203, 'Không thể tạo cổng thanh toán', HttpStatus.BAD_REQUEST),
  PAYMENT_GATEWAY_UPDATE_FAILED: new ErrorCode(11204, 'Không thể cập nhật cổng thanh toán', HttpStatus.BAD_REQUEST),
  PAYMENT_GATEWAY_DELETE_FAILED: new ErrorCode(11205, 'Không thể xóa cổng thanh toán', HttpStatus.INTERNAL_SERVER_ERROR),

  // Facebook Page (11300-11399)
  FACEBOOK_PAGE_NOT_FOUND: new ErrorCode(11300, 'Không tìm thấy trang Facebook', HttpStatus.NOT_FOUND),
  FACEBOOK_PAGE_LIST_FAILED: new ErrorCode(11301, 'Không thể lấy danh sách trang Facebook', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_DETAIL_FAILED: new ErrorCode(11302, 'Không thể lấy thông tin chi tiết trang Facebook', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_CREATE_FAILED: new ErrorCode(11303, 'Không thể tạo trang Facebook', HttpStatus.BAD_REQUEST),
  FACEBOOK_PAGE_UPDATE_FAILED: new ErrorCode(11304, 'Không thể cập nhật trang Facebook', HttpStatus.BAD_REQUEST),
  FACEBOOK_PAGE_DELETE_FAILED: new ErrorCode(11305, 'Không thể xóa trang Facebook', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_UNSUBSCRIBE_FAILED: new ErrorCode(11306, 'Không thể hủy đăng ký webhook cho trang Facebook', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_ALREADY_INTEGRATED: new ErrorCode(11307, 'Trang Facebook đã được tích hợp với agent khác', HttpStatus.CONFLICT),
  FACEBOOK_PAGE_NOT_INTEGRATED: new ErrorCode(11308, 'Trang Facebook chưa được tích hợp với agent', HttpStatus.BAD_REQUEST),
  FACEBOOK_PAGE_INTEGRATION_FAILED: new ErrorCode(11309, 'Lỗi khi tích hợp trang Facebook với agent', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_SUBSCRIBE_FAILED: new ErrorCode(11310, 'Không thể đăng ký webhook cho trang Facebook', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_REMOVE_FAILED: new ErrorCode(11311, 'Lỗi khi gỡ trang Facebook khỏi agent', HttpStatus.INTERNAL_SERVER_ERROR),
  FACEBOOK_PAGE_AUTH_INVALID: new ErrorCode(11312, 'State không hợp lệ', HttpStatus.BAD_REQUEST),
  FACEBOOK_PAGE_AUTH_CODE_USED: new ErrorCode(11313, 'Mã xác thực Facebook đã được sử dụng', HttpStatus.BAD_REQUEST),
  FACEBOOK_PAGE_AUTH_CODE_EXPIRED: new ErrorCode(11314, 'Mã xác thực Facebook đã hết hạn', HttpStatus.BAD_REQUEST),

  // SMS Server Configuration (11400-11499)
  SMS_SERVER_NOT_FOUND: new ErrorCode(11400, 'Không tìm thấy cấu hình máy chủ SMS', HttpStatus.NOT_FOUND),
  SMS_SERVER_LIST_FAILED: new ErrorCode(11401, 'Không thể lấy danh sách cấu hình máy chủ SMS', HttpStatus.INTERNAL_SERVER_ERROR),
  SMS_SERVER_DETAIL_FAILED: new ErrorCode(11402, 'Không thể lấy thông tin chi tiết cấu hình máy chủ SMS', HttpStatus.INTERNAL_SERVER_ERROR),
  SMS_SERVER_CREATE_FAILED: new ErrorCode(11403, 'Không thể tạo cấu hình máy chủ SMS', HttpStatus.BAD_REQUEST),
  SMS_SERVER_UPDATE_FAILED: new ErrorCode(11404, 'Không thể cập nhật cấu hình máy chủ SMS', HttpStatus.BAD_REQUEST),
  SMS_SERVER_DELETE_FAILED: new ErrorCode(11405, 'Không thể xóa cấu hình máy chủ SMS', HttpStatus.INTERNAL_SERVER_ERROR),
  SMS_SERVER_TEST_FAILED: new ErrorCode(11406, 'Không thể kiểm tra kết nối máy chủ SMS', HttpStatus.BAD_REQUEST),

  // WhatsApp Integration (11500-11549)
  WHATSAPP_ACCOUNT_NOT_FOUND: new ErrorCode(11500, 'Không tìm thấy tài khoản WhatsApp', HttpStatus.NOT_FOUND),
  WHATSAPP_ACCOUNT_LIST_ERROR: new ErrorCode(11501, 'Lỗi khi lấy danh sách tài khoản WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_ACCOUNT_FIND_ERROR: new ErrorCode(11502, 'Lỗi khi lấy thông tin tài khoản WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_ACCOUNT_CREATE_ERROR: new ErrorCode(11503, 'Lỗi khi tạo tài khoản WhatsApp', HttpStatus.BAD_REQUEST),
  WHATSAPP_ACCOUNT_UPDATE_ERROR: new ErrorCode(11504, 'Lỗi khi cập nhật tài khoản WhatsApp', HttpStatus.BAD_REQUEST),
  WHATSAPP_ACCOUNT_DELETE_ERROR: new ErrorCode(11505, 'Lỗi khi xóa tài khoản WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_ACCOUNT_ALREADY_EXISTS: new ErrorCode(11506, 'Tài khoản WhatsApp đã tồn tại', HttpStatus.CONFLICT),
  WHATSAPP_ACCOUNT_ACCESS_DENIED: new ErrorCode(11507, 'Không có quyền truy cập tài khoản WhatsApp này', HttpStatus.FORBIDDEN),
  WHATSAPP_ACCOUNT_CONNECT_ERROR: new ErrorCode(11508, 'Lỗi khi kết nối tài khoản WhatsApp với agent', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_ACCOUNT_DISCONNECT_ERROR: new ErrorCode(11509, 'Lỗi khi ngắt kết nối tài khoản WhatsApp với agent', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_TEMPLATE_NOT_FOUND: new ErrorCode(11510, 'Không tìm thấy mẫu tin nhắn WhatsApp', HttpStatus.NOT_FOUND),
  WHATSAPP_TEMPLATE_CREATE_ERROR: new ErrorCode(11511, 'Lỗi khi tạo mẫu tin nhắn WhatsApp', HttpStatus.BAD_REQUEST),
  WHATSAPP_TEMPLATE_UPDATE_ERROR: new ErrorCode(11512, 'Lỗi khi cập nhật mẫu tin nhắn WhatsApp', HttpStatus.BAD_REQUEST),
  WHATSAPP_TEMPLATE_DELETE_ERROR: new ErrorCode(11513, 'Lỗi khi xóa mẫu tin nhắn WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_MESSAGE_SEND_ERROR: new ErrorCode(11514, 'Lỗi khi gửi tin nhắn WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),
  WHATSAPP_MESSAGE_INVALID: new ErrorCode(11515, 'Tin nhắn WhatsApp không hợp lệ', HttpStatus.BAD_REQUEST),
  WHATSAPP_WEBHOOK_ERROR: new ErrorCode(11516, 'Lỗi khi xử lý webhook WhatsApp', HttpStatus.INTERNAL_SERVER_ERROR),

  // Gmail Integration (11550-11599)
  GMAIL_INTEGRATION_NOT_FOUND: new ErrorCode(11550, 'Không tìm thấy tích hợp Gmail', HttpStatus.NOT_FOUND),
  GMAIL_INTEGRATION_ALREADY_EXISTS: new ErrorCode(11551, 'Tích hợp Gmail với email này đã tồn tại', HttpStatus.CONFLICT),
  GMAIL_INTEGRATION_CREATE_FAILED: new ErrorCode(11552, 'Không thể tạo tích hợp Gmail', HttpStatus.BAD_REQUEST),
  GMAIL_INTEGRATION_UPDATE_FAILED: new ErrorCode(11553, 'Không thể cập nhật tích hợp Gmail', HttpStatus.BAD_REQUEST),
  GMAIL_INTEGRATION_DELETE_FAILED: new ErrorCode(11554, 'Không thể xóa tích hợp Gmail', HttpStatus.INTERNAL_SERVER_ERROR),
  GMAIL_INTEGRATION_LIST_FAILED: new ErrorCode(11555, 'Không thể lấy danh sách tích hợp Gmail', HttpStatus.INTERNAL_SERVER_ERROR),
  GMAIL_INTEGRATION_ACCESS_DENIED: new ErrorCode(11556, 'Không có quyền truy cập tích hợp Gmail này', HttpStatus.FORBIDDEN),
  GMAIL_TOKEN_UPDATE_FAILED: new ErrorCode(11557, 'Không thể cập nhật token Gmail', HttpStatus.BAD_REQUEST),
  GMAIL_CONNECTION_STATUS_UPDATE_FAILED: new ErrorCode(11558, 'Không thể cập nhật trạng thái kết nối Gmail', HttpStatus.BAD_REQUEST),
  GMAIL_ENCRYPTION_FAILED: new ErrorCode(11559, 'Lỗi mã hóa thông tin Gmail', HttpStatus.INTERNAL_SERVER_ERROR),
  GMAIL_DECRYPTION_FAILED: new ErrorCode(11560, 'Lỗi giải mã thông tin Gmail', HttpStatus.INTERNAL_SERVER_ERROR),

  // Website Related Errors (11800-11899)
  WEBSITE_NOT_FOUND: new ErrorCode(11800, 'Không tìm thấy website', HttpStatus.NOT_FOUND),
  WEBSITE_UPDATE_FAILED: new ErrorCode(11801, 'Không thể cập nhật website', HttpStatus.BAD_REQUEST),
  WEBSITE_DELETE_FAILED: new ErrorCode(11802, 'Lỗi khi xóa website', HttpStatus.INTERNAL_SERVER_ERROR),
  WEBSITE_ACCESS_DENIED: new ErrorCode(11803, 'Không có quyền truy cập website này', HttpStatus.FORBIDDEN),

  // Integration CRUD (11600-11699)
  INTEGRATION_NOT_FOUND: new ErrorCode(11600, 'Không tìm thấy tích hợp', HttpStatus.NOT_FOUND),
  INTEGRATION_CREATE_FAILED: new ErrorCode(11601, 'Không thể tạo tích hợp', HttpStatus.BAD_REQUEST),
  INTEGRATION_UPDATE_FAILED: new ErrorCode(11602, 'Không thể cập nhật tích hợp', HttpStatus.BAD_REQUEST),
  INTEGRATION_DELETE_FAILED: new ErrorCode(11603, 'Không thể xóa tích hợp', HttpStatus.INTERNAL_SERVER_ERROR),
  INTEGRATION_LIST_FAILED: new ErrorCode(11604, 'Không thể lấy danh sách tích hợp', HttpStatus.INTERNAL_SERVER_ERROR),
  INTEGRATION_ACCESS_DENIED: new ErrorCode(11605, 'Không có quyền truy cập tích hợp này', HttpStatus.FORBIDDEN),

  // Google Docs Integration (11700-11719)
  GOOGLE_DOCS_AUTH_FAILED: new ErrorCode(11700, 'Xác thực Google Docs thất bại', HttpStatus.UNAUTHORIZED),
  GOOGLE_DOCS_TOKEN_REFRESH_FAILED: new ErrorCode(11701, 'Làm mới token Google Docs thất bại', HttpStatus.UNAUTHORIZED),
  GOOGLE_DOCS_REVOKE_FAILED: new ErrorCode(11702, 'Thu hồi quyền truy cập Google Docs thất bại', HttpStatus.BAD_REQUEST),
  GOOGLE_DOCS_API_ERROR: new ErrorCode(11703, 'Lỗi API Google Docs', HttpStatus.BAD_REQUEST),
  GOOGLE_DOCS_DOCUMENT_NOT_FOUND: new ErrorCode(11704, 'Không tìm thấy document', HttpStatus.NOT_FOUND),
  GOOGLE_DOCS_PERMISSION_DENIED: new ErrorCode(11705, 'Không có quyền truy cập document', HttpStatus.FORBIDDEN),

  // Box Chat Config (11700-11799)
  BOX_CHAT_CONFIG_NOT_FOUND: new ErrorCode(11700, 'Không tìm thấy cấu hình box chat', HttpStatus.NOT_FOUND),
  BOX_CHAT_CONFIG_CREATE_FAILED: new ErrorCode(11701, 'Không thể tạo cấu hình box chat', HttpStatus.BAD_REQUEST),
  BOX_CHAT_CONFIG_UPDATE_FAILED: new ErrorCode(11702, 'Không thể cập nhật cấu hình box chat', HttpStatus.BAD_REQUEST),
  BOX_CHAT_CONFIG_DELETE_FAILED: new ErrorCode(11703, 'Không thể xóa cấu hình box chat', HttpStatus.INTERNAL_SERVER_ERROR),
  BOX_CHAT_CONFIG_ACCESS_DENIED: new ErrorCode(11704, 'Không có quyền truy cập cấu hình box chat này', HttpStatus.FORBIDDEN),

  // SePay API Errors (11710-11799)
  SEPAY_ACCOUNT_ALREADY_EXISTS: new ErrorCode(11710, 'Số tài khoản đã tồn tại trên hệ thống SePay', HttpStatus.CONFLICT),
  SEPAY_INVALID_INPUT: new ErrorCode(11711, 'Thông tin đầu vào không hợp lệ', HttpStatus.BAD_REQUEST),
  SEPAY_ACCOUNT_NOT_FOUND: new ErrorCode(11712, 'Số tài khoản không tồn tại trên hệ thống ngân hàng', HttpStatus.NOT_FOUND),
  SEPAY_INVALID_CREDENTIALS: new ErrorCode(11713, 'Số CCCD/CMND hoặc số điện thoại không được đăng ký cho tài khoản ngân hàng', HttpStatus.BAD_REQUEST),
  SEPAY_SERVICE_UNAVAILABLE: new ErrorCode(11714, 'Hệ thống ngân hàng đang bận, vui lòng thử lại sau', HttpStatus.SERVICE_UNAVAILABLE),
  SEPAY_OTP_INVALID: new ErrorCode(11715, 'OTP không chính xác', HttpStatus.BAD_REQUEST),
  SEPAY_OTP_EXPIRED: new ErrorCode(11716, 'OTP đã hết hạn', HttpStatus.BAD_REQUEST),
  SEPAY_OTP_LIMIT_EXCEEDED: new ErrorCode(11717, 'Số lần nhập OTP sai đã vượt quá giới hạn', HttpStatus.BAD_REQUEST),
  SEPAY_REQUEST_ID_INVALID: new ErrorCode(11718, 'Request ID không tồn tại hoặc đã hết hạn', HttpStatus.BAD_REQUEST),
  SEPAY_API_ERROR: new ErrorCode(11719, 'Lỗi không xác định từ SePay API', HttpStatus.INTERNAL_SERVER_ERROR),

  // ACB Bank specific errors (11720-11729)
  SEPAY_ACB_ACCOUNT_LOCKED: new ErrorCode(11720, 'Số tài khoản không tồn tại hoặc đã bị khóa trên hệ thống ngân hàng ACB', HttpStatus.BAD_REQUEST),
  SEPAY_ACB_NOT_INDIVIDUAL: new ErrorCode(11721, 'Số tài khoản này không thuộc nhóm khách hàng cá nhân', HttpStatus.BAD_REQUEST),
  SEPAY_ACB_PHONE_NOT_REGISTERED: new ErrorCode(11722, 'Số điện thoại không được đăng ký cho tài khoản ngân hàng ACB', HttpStatus.BAD_REQUEST),
  SEPAY_ACB_RATE_LIMIT: new ErrorCode(11723, 'Yêu cầu quá nhiều lần, vui lòng thử lại sau 2 phút', HttpStatus.TOO_MANY_REQUESTS),

  // Agent Related Errors
  AGENT_NOT_FOUND: new ErrorCode(11900, 'Không tìm thấy agent', HttpStatus.NOT_FOUND),
  AGENT_ACCESS_DENIED: new ErrorCode(11901, 'Không có quyền truy cập agent này', HttpStatus.FORBIDDEN),

  // Shipping Integration Errors (11950-11999)
  PROVIDER_NOT_FOUND: new ErrorCode(11950, 'Không tìm thấy nhà cung cấp vận chuyển', HttpStatus.NOT_FOUND),
  INVALID_PROVIDER_TYPE: new ErrorCode(11951, 'Loại nhà cung cấp vận chuyển không hợp lệ', HttpStatus.BAD_REQUEST),
  DUPLICATE_INTEGRATION: new ErrorCode(11952, 'Đã tồn tại tích hợp cho nhà cung cấp này', HttpStatus.CONFLICT),
  INVALID_CONFIG: new ErrorCode(11953, 'Cấu hình không hợp lệ', HttpStatus.BAD_REQUEST),
  INVALID_USER: new ErrorCode(11954, 'User không hợp lệ', HttpStatus.BAD_REQUEST),
  INVALID_INPUT: new ErrorCode(11955, 'Dữ liệu đầu vào không hợp lệ', HttpStatus.BAD_REQUEST),
  CREATION_FAILED: new ErrorCode(11956, 'Không thể tạo shipping integration', HttpStatus.INTERNAL_SERVER_ERROR),
  UPDATE_FAILED: new ErrorCode(11957, 'Không thể cập nhật shipping integration', HttpStatus.INTERNAL_SERVER_ERROR),
  DELETE_FAILED: new ErrorCode(11958, 'Không thể xóa shipping integration', HttpStatus.INTERNAL_SERVER_ERROR),
  FETCH_FAILED: new ErrorCode(11959, 'Không thể lấy thông tin shipping integration', HttpStatus.INTERNAL_SERVER_ERROR),
  NOT_FOUND: new ErrorCode(11960, 'Không tìm thấy shipping integration', HttpStatus.NOT_FOUND),
  ENCRYPTION_FAILED: new ErrorCode(11961, 'Lỗi mã hóa dữ liệu', HttpStatus.INTERNAL_SERVER_ERROR),
  DECRYPTION_FAILED: new ErrorCode(11962, 'Lỗi giải mã dữ liệu', HttpStatus.INTERNAL_SERVER_ERROR),
};
