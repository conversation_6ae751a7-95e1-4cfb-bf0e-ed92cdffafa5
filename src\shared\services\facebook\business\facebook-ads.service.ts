import { AppException } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { Ad, AdSet } from 'facebook-nodejs-business-sdk';
import {
  FACEBOOK_BUSINESS_ERROR_CODES,
  createFacebookBusinessException,
  validateFacebookBusinessParams,
  validateFacebookBusinessResponse,
} from '../exceptions/facebook-business.exception';
import {
  FACEBOOK_BUSINESS_CONSTANTS,
  FacebookAd,
  FacebookAdSet,
  FacebookTargeting
} from '../interfaces/facebook-business.interface';
import { FacebookBusinessApiService } from './facebook-business-api.service';

/**
 * Service quản lý Facebook Ads và Ad Sets
 */
@Injectable()
export class FacebookAdsService {
  private readonly logger = new Logger(FacebookAdsService.name);

  constructor(private readonly facebookApiService: FacebookBusinessApiService) { }

  /**
   * <PERSON><PERSON><PERSON> da<PERSON> sách ad sets
   * @param accessToken Access token
   * @param adAccountId ID của ad account
   * @param limit Số lượng ad sets tối đa
   * @returns Danh sách ad sets
   */
  async getAdSets(accessToken: string, adAccountId?: string, limit: number = 25): Promise<FacebookAdSet[]> {
    try {
      this.logger.log(`Getting ad sets for ad account: ${adAccountId || 'default'}`);

      const adAccount = this.facebookApiService.getAdAccountInstance(accessToken, adAccountId);

      const adSets = await adAccount.getAdSets([
        'id',
        'name',
        'campaign_id',
        'status',
        'configured_status',
        'effective_status',
        'created_time',
        'updated_time',
        'start_time',
        'end_time',
        'daily_budget',
        'lifetime_budget',
        'budget_remaining',
        'billing_event',
        'optimization_goal',
        'bid_amount',
        'targeting',
        'attribution_spec',
      ], {
        limit,
      });

      const result: FacebookAdSet[] = adSets.map((adSet: any) => ({
        id: adSet.id,
        name: adSet.name,
        campaign_id: adSet.campaign_id,
        status: adSet.status,
        configured_status: adSet.configured_status,
        effective_status: adSet.effective_status,
        created_time: adSet.created_time,
        updated_time: adSet.updated_time,
        start_time: adSet.start_time,
        end_time: adSet.end_time,
        daily_budget: adSet.daily_budget,
        lifetime_budget: adSet.lifetime_budget,
        budget_remaining: adSet.budget_remaining,
        billing_event: adSet.billing_event,
        optimization_goal: adSet.optimization_goal,
        bid_amount: adSet.bid_amount,
        targeting: adSet.targeting,
        attribution_spec: adSet.attribution_spec,
      }));

      this.logger.log(`Successfully retrieved ${result.length} ad sets`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting ad sets: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy danh sách ad sets',
        { adAccountId },
      );
    }
  }

  /**
   * Lấy thông tin ad set cụ thể
   * @param adSetId ID của ad set
   * @returns Thông tin ad set
   */
  async getAdSet(adSetId: string): Promise<FacebookAdSet> {
    try {
      validateFacebookBusinessParams({ adSetId }, ['adSetId']);

      this.logger.log(`Getting ad set info for ID: ${adSetId}`);

      const adSet = new AdSet(adSetId);
      const adSetData = await adSet.get([
        'id',
        'name',
        'campaign_id',
        'status',
        'configured_status',
        'effective_status',
        'created_time',
        'updated_time',
        'start_time',
        'end_time',
        'daily_budget',
        'lifetime_budget',
        'budget_remaining',
        'billing_event',
        'optimization_goal',
        'bid_amount',
        'targeting',
        'attribution_spec',
      ]);

      validateFacebookBusinessResponse(adSetData, ['id', 'name']);

      const result: FacebookAdSet = {
        id: adSetData.id,
        name: adSetData.name,
        campaign_id: adSetData.campaign_id,
        status: adSetData.status,
        configured_status: adSetData.configured_status,
        effective_status: adSetData.effective_status,
        created_time: adSetData.created_time,
        updated_time: adSetData.updated_time,
        start_time: adSetData.start_time,
        end_time: adSetData.end_time,
        daily_budget: adSetData.daily_budget,
        lifetime_budget: adSetData.lifetime_budget,
        budget_remaining: adSetData.budget_remaining,
        billing_event: adSetData.billing_event,
        optimization_goal: adSetData.optimization_goal,
        bid_amount: adSetData.bid_amount,
        targeting: adSetData.targeting,
        attribution_spec: adSetData.attribution_spec,
      };

      this.logger.log(`Successfully retrieved ad set: ${result.name}`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting ad set: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy thông tin ad set',
        { adSetId },
      );
    }
  }

  /**
   * Tạo ad set mới
   * @param adAccountId ID của ad account
   * @param adSetData Dữ liệu ad set
   * @returns Ad set đã tạo
   */
  async createAdSet(
    adAccountId: string,
    adSetData: {
      name: string;
      campaign_id: string;
      targeting: FacebookTargeting;
      optimization_goal: string;
      billing_event: string;
      bid_amount?: string;
      daily_budget?: string;
      lifetime_budget?: string;
      start_time?: string;
      end_time?: string;
      status?: string;
    },
  ): Promise<FacebookAdSet> {
    try {
      validateFacebookBusinessParams(adSetData, [
        'name',
        'campaign_id',
        'targeting',
        'optimization_goal',
        'billing_event',
      ]);

      this.logger.log(`Creating ad set: ${adSetData.name}`);

      const adAccount = this.facebookApiService.getAdAccountInstance(adAccountId);

      const params = {
        name: adSetData.name,
        campaign_id: adSetData.campaign_id,
        targeting: adSetData.targeting,
        optimization_goal: adSetData.optimization_goal,
        billing_event: adSetData.billing_event,
        status: adSetData.status || FACEBOOK_BUSINESS_CONSTANTS.AD_STATUSES.PAUSED,
        ...(adSetData.bid_amount && { bid_amount: adSetData.bid_amount }),
        ...(adSetData.daily_budget && { daily_budget: adSetData.daily_budget }),
        ...(adSetData.lifetime_budget && { lifetime_budget: adSetData.lifetime_budget }),
        ...(adSetData.start_time && { start_time: adSetData.start_time }),
        ...(adSetData.end_time && { end_time: adSetData.end_time }),
      };

      const adSet = await adAccount.createAdSet([], params);

      validateFacebookBusinessResponse(adSet, ['id']);

      // Get full ad set data
      const createdAdSet = await this.getAdSet(adSet.id);

      this.logger.log(`Successfully created ad set: ${createdAdSet.name} (ID: ${createdAdSet.id})`);
      return createdAdSet;
    } catch (error) {
      this.logger.error(`Error creating ad set: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể tạo ad set',
        { adAccountId, adSetData },
      );
    }
  }

  /**
   * Cập nhật ad set
   * @param adSetId ID của ad set
   * @param updateData Dữ liệu cập nhật
   * @returns Ad set đã cập nhật
   */
  async updateAdSet(
    adSetId: string,
    updateData: {
      name?: string;
      status?: string;
      targeting?: FacebookTargeting;
      bid_amount?: string;
      daily_budget?: string;
      lifetime_budget?: string;
      start_time?: string;
      end_time?: string;
    },
  ): Promise<FacebookAdSet> {
    try {
      validateFacebookBusinessParams({ adSetId }, ['adSetId']);

      this.logger.log(`Updating ad set: ${adSetId}`);

      const adSet = new AdSet(adSetId);

      // Remove undefined values
      const params = Object.fromEntries(
        Object.entries(updateData).filter(([_, value]) => value !== undefined)
      );

      if (Object.keys(params).length === 0) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_PARAMETERS,
          'No valid update parameters provided',
        );
      }

      await adSet.update([], params);

      // Get updated ad set data
      const updatedAdSet = await this.getAdSet(adSetId);

      this.logger.log(`Successfully updated ad set: ${updatedAdSet.name}`);
      return updatedAdSet;
    } catch (error) {
      this.logger.error(`Error updating ad set: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể cập nhật ad set',
        { adSetId, updateData },
      );
    }
  }

  /**
   * Lấy danh sách ads
   * @param accessToken Access token
   * @param adAccountId ID của ad account
   * @param limit Số lượng ads tối đa
   * @returns Danh sách ads
   */
  async getAds(accessToken: string, adAccountId?: string, limit: number = 25): Promise<FacebookAd[]> {
    try {
      this.logger.log(`Getting ads for ad account: ${adAccountId || 'default'}`);

      const adAccount = this.facebookApiService.getAdAccountInstance(accessToken, adAccountId);

      const ads = await adAccount.getAds([
        'id',
        'name',
        'adset_id',
        'campaign_id',
        'status',
        'configured_status',
        'effective_status',
        'created_time',
        'updated_time',
        'creative',
        'tracking_specs',
        'conversion_specs',
      ], {
        limit,
      });

      const result: FacebookAd[] = ads.map((ad: any) => ({
        id: ad.id,
        name: ad.name,
        adset_id: ad.adset_id,
        campaign_id: ad.campaign_id,
        status: ad.status,
        configured_status: ad.configured_status,
        effective_status: ad.effective_status,
        created_time: ad.created_time,
        updated_time: ad.updated_time,
        creative: ad.creative,
        tracking_specs: ad.tracking_specs,
        conversion_specs: ad.conversion_specs,
      }));

      this.logger.log(`Successfully retrieved ${result.length} ads`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting ads: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy danh sách ads',
        { adAccountId },
      );
    }
  }

  /**
   * Lấy thông tin ad cụ thể
   * @param adId ID của ad
   * @returns Thông tin ad
   */
  async getAd(adId: string): Promise<FacebookAd> {
    try {
      validateFacebookBusinessParams({ adId }, ['adId']);

      this.logger.log(`Getting ad info for ID: ${adId}`);

      const ad = new Ad(adId);
      const adData = await ad.get([
        'id',
        'name',
        'adset_id',
        'campaign_id',
        'status',
        'configured_status',
        'effective_status',
        'created_time',
        'updated_time',
        'creative',
        'tracking_specs',
        'conversion_specs',
      ]);

      validateFacebookBusinessResponse(adData, ['id', 'name']);

      const result: FacebookAd = {
        id: adData.id,
        name: adData.name,
        adset_id: adData.adset_id,
        campaign_id: adData.campaign_id,
        status: adData.status,
        configured_status: adData.configured_status,
        effective_status: adData.effective_status,
        created_time: adData.created_time,
        updated_time: adData.updated_time,
        creative: adData.creative,
        tracking_specs: adData.tracking_specs,
        conversion_specs: adData.conversion_specs,
      };

      this.logger.log(`Successfully retrieved ad: ${result.name}`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting ad: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy thông tin ad',
        { adId },
      );
    }
  }

  /**
   * Tạo ad mới
   * @param adAccountId ID của ad account
   * @param adData Dữ liệu ad
   * @returns Ad đã tạo
   */
  async createAd(
    adAccountId: string,
    adData: {
      name: string;
      adset_id: string;
      creative: {
        creative_id: string;
      };
      status?: string;
    },
  ): Promise<FacebookAd> {
    try {
      validateFacebookBusinessParams(adData, ['name', 'adset_id', 'creative']);

      this.logger.log(`Creating ad: ${adData.name}`);

      const adAccount = this.facebookApiService.getAdAccountInstance(adAccountId);

      const params = {
        name: adData.name,
        adset_id: adData.adset_id,
        creative: adData.creative,
        status: adData.status || FACEBOOK_BUSINESS_CONSTANTS.AD_STATUSES.PAUSED,
      };

      const ad = await adAccount.createAd([], params);

      validateFacebookBusinessResponse(ad, ['id']);

      // Get full ad data
      const createdAd = await this.getAd(ad.id);

      this.logger.log(`Successfully created ad: ${createdAd.name} (ID: ${createdAd.id})`);
      return createdAd;
    } catch (error) {
      this.logger.error(`Error creating ad: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể tạo ad',
        { adAccountId, adData },
      );
    }
  }

  /**
   * Cập nhật ad
   * @param adId ID của ad
   * @param updateData Dữ liệu cập nhật
   * @returns Ad đã cập nhật
   */
  async updateAd(
    adId: string,
    updateData: {
      name?: string;
      status?: string;
    },
  ): Promise<FacebookAd> {
    try {
      validateFacebookBusinessParams({ adId }, ['adId']);

      this.logger.log(`Updating ad: ${adId}`);

      const ad = new Ad(adId);

      // Remove undefined values
      const params = Object.fromEntries(
        Object.entries(updateData).filter(([_, value]) => value !== undefined)
      );

      if (Object.keys(params).length === 0) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_PARAMETERS,
          'No valid update parameters provided',
        );
      }

      await ad.update([], params);

      // Get updated ad data
      const updatedAd = await this.getAd(adId);

      this.logger.log(`Successfully updated ad: ${updatedAd.name}`);
      return updatedAd;
    } catch (error) {
      this.logger.error(`Error updating ad: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể cập nhật ad',
        { adId, updateData },
      );
    }
  }

  /**
   * Xóa ad
   * @param adId ID của ad
   * @returns True nếu xóa thành công
   */
  async deleteAd(adId: string): Promise<boolean> {
    try {
      validateFacebookBusinessParams({ adId }, ['adId']);

      this.logger.log(`Deleting ad: ${adId}`);

      const ad = new Ad(adId);
      await ad.update([], { status: FACEBOOK_BUSINESS_CONSTANTS.AD_STATUSES.DELETED });

      this.logger.log(`Successfully deleted ad: ${adId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error deleting ad: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể xóa ad',
        { adId },
      );
    }
  }
}
