import { ConfigType } from '@/config';
import { CategoryFolderEnum, generateS3Key } from '@/shared/utils';
import { AppException, ErrorCode } from '@common/exceptions';
import { ConfigService } from '@config/config.service';
import { FacebookConfig } from '@config/interfaces';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { S3Service } from '../../s3.service';
import {
  FacebookUserInfo,
  FacebookPictureResponse,
} from '../interfaces/facebook.interface';

/**
 * Service để xử lý các API liên quan đến Facebook Personal/User
 */
@Injectable()
export class FacebookPersonalService {
  private readonly logger = new Logger(FacebookPersonalService.name);
  private readonly facebookConfig: FacebookConfig;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly s3Service: S3Service,
  ) {
    this.facebookConfig = this.configService.getConfig<FacebookConfig>(
      ConfigType.Facebook,
    );
  }

  /**
   * Lấy avatar của user Facebook, upload lên S3 và trả về key
   * @param userId ID của user Facebook
   * @param accessToken Access token của user
   * @param keyOld Key cũ để ghi đè (tùy chọn)
   * @returns S3 key của avatar đã upload
   */
  async getUserAvatarAndUploadToS3(
    userId: string,
    accessToken: string,
    keyOld?: string,
  ): Promise<{ key: string }> {
    try {
      this.logger.log(`Bắt đầu lấy avatar của user Facebook ${userId}`);

      // Lấy trực tiếp ảnh avatar của user Facebook với kích thước lớn
      const pictureResponse = await firstValueFrom(
        this.httpService.get<FacebookPictureResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${userId}/picture`,
          {
            params: {
              access_token: accessToken,
              type: 'large',
              redirect: 'false',
            },
          },
        ),
      );

      // Kiểm tra xem có phải là ảnh silhouette (ảnh mặc định) không
      if (pictureResponse.data.data.is_silhouette) {
        this.logger.log(`User Facebook ${userId} đang sử dụng ảnh mặc định, bỏ qua upload`);
        return { key: '' }; // Trả về key rỗng nếu là ảnh mặc định
      }

      // Tải avatar từ URL
      const response = await firstValueFrom(
        this.httpService.get(pictureResponse.data.data.url, {
          responseType: 'arraybuffer',
        }),
      );

      // Tạo buffer từ dữ liệu nhận được
      const buffer = Buffer.from(response.data);

      // Xác định content type từ header hoặc mặc định là image/jpeg
      const contentType = response.headers['content-type'] || 'image/jpeg';

      // Tạo S3 key cho avatar user
      const key = keyOld || generateS3Key({
        baseFolder: 'facebook',
        categoryFolder: CategoryFolderEnum.PROFILE,
        fileName: `user-${userId}-avatar.jpg`,
        useTimeFolder: true,
      });

      // Upload avatar lên S3
      await this.s3Service.uploadFile(key, buffer, contentType);

      this.logger.log(
        `Đã upload avatar của user Facebook ${userId} lên S3 với key: ${key}`,
      );

      return { key };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy và upload avatar của user Facebook ${userId}: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy và upload avatar của user Facebook',
        { userId },
      );
    }
  }

  /**
   * Lấy thông tin người dùng Facebook
   * @param accessToken Access token của người dùng
   * @returns Thông tin người dùng Facebook
   */
  async getUserInfo(accessToken: string): Promise<FacebookUserInfo> {
    try {
      // Gọi API để lấy thông tin người dùng
      const response = await firstValueFrom(
        this.httpService.get<FacebookUserInfo>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/me`,
          {
            params: {
              access_token: accessToken,
              fields: 'id,name,email,picture',
            },
          },
        ),
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Error getting Facebook user info: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin người dùng Facebook',
      );
    }
  }
}
