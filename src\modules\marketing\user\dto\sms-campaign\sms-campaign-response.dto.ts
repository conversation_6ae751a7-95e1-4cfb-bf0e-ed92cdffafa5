import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsNumber, Min, IsIn } from 'class-validator';
import { Type } from 'class-transformer';
import { SmsCampaignStatus } from '../../entities/sms-campaign-user.entity';
import { SmsCampaignType } from '@/modules/marketing/enums/sms-campaign-type.enum';
import { QueryDto } from '@/common/dto';

/**
 * DTO cho response tạo SMS campaign
 */
export class CreateSmsCampaignResponseDto {
  /**
   * ID của campaign đã tạo
   * @example 1
   */
  @ApiProperty({
    description: 'ID của campaign đã tạo',
    example: 1,
  })
  campaignId: number;

  /**
   * Số lượng job đã tạo
   * @example 1
   */
  @ApiProperty({
    description: 'Số lượng job đã tạo',
    example: 1,
  })
  jobCount: number;

  /**
   * Danh sách ID của các job đã tạo
   * @example ["job_1"]
   */
  @ApiProperty({
    description: 'Danh sách ID của các job đã tạo',
    example: ['job_1'],
    type: [String],
  })
  jobIds: string[];

  /**
   * Thời gian dự kiến gửi (Unix timestamp)
   * @example 1703980800
   */
  @ApiProperty({
    description: 'Thời gian dự kiến gửi (Unix timestamp)',
    example: 1703980800,
    required: false,
  })
  scheduledAt?: number;

  /**
   * Trạng thái campaign
   * @example "SCHEDULED"
   */
  @ApiProperty({
    description: 'Trạng thái campaign',
    example: 'SCHEDULED',
    enum: SmsCampaignStatus,
  })
  status: SmsCampaignStatus;

  /**
   * Tổng số người nhận
   * @example 150
   */
  @ApiProperty({
    description: 'Tổng số người nhận',
    example: 150,
  })
  totalRecipients: number;
}

/**
 * DTO cho SMS campaign item trong danh sách
 */
export class SmsCampaignItemDto {
  /**
   * ID của campaign
   * @example 1
   */
  @ApiProperty({
    description: 'ID của campaign',
    example: 1,
  })
  id: number;

  /**
   * Tên campaign
   * @example "Chiến dịch SMS khuyến mãi Black Friday"
   */
  @ApiProperty({
    description: 'Tên campaign',
    example: 'Chiến dịch SMS khuyến mãi Black Friday',
  })
  name: string;

  /**
   * Mô tả campaign
   * @example "Chiến dịch SMS marketing cho sự kiện Black Friday"
   */
  @ApiProperty({
    description: 'Mô tả campaign',
    example: 'Chiến dịch SMS marketing cho sự kiện Black Friday',
    required: false,
  })
  description?: string;

  /**
   * Loại chiến dịch SMS
   * @example "ADS"
   */
  @ApiProperty({
    description: 'Loại chiến dịch SMS',
    example: SmsCampaignType.ADS,
    enum: SmsCampaignType,
    enumName: 'SmsCampaignType',
  })
  campaignType: SmsCampaignType;

  /**
   * Trạng thái campaign
   * @example "SENT"
   */
  @ApiProperty({
    description: 'Trạng thái campaign',
    example: 'SENT',
    enum: SmsCampaignStatus,
  })
  status: SmsCampaignStatus;

  /**
   * Tổng số người nhận
   * @example 150
   */
  @ApiProperty({
    description: 'Tổng số người nhận',
    example: 150,
  })
  totalRecipients: number;

  /**
   * Số SMS đã gửi thành công
   * @example 145
   */
  @ApiProperty({
    description: 'Số SMS đã gửi thành công',
    example: 145,
  })
  sentCount: number;

  /**
   * Số SMS gửi thất bại
   * @example 5
   */
  @ApiProperty({
    description: 'Số SMS gửi thất bại',
    example: 5,
  })
  failedCount: number;

  /**
   * Tỷ lệ gửi thành công (%)
   * @example 96.7
   */
  @ApiProperty({
    description: 'Tỷ lệ gửi thành công (%)',
    example: 96.7,
    required: false,
  })
  successRate?: number;

  /**
   * Thời gian lên lịch gửi (Unix timestamp)
   * @example 1703980800
   */
  @ApiProperty({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: 1703980800,
    required: false,
  })
  scheduledAt?: number;

  /**
   * Thời gian bắt đầu gửi (Unix timestamp)
   * @example 1703980800
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu gửi (Unix timestamp)',
    example: 1703980800,
    required: false,
  })
  startedAt?: number;

  /**
   * Thời gian hoàn thành (Unix timestamp)
   * @example 1703981200
   */
  @ApiProperty({
    description: 'Thời gian hoàn thành (Unix timestamp)',
    example: 1703981200,
    required: false,
  })
  completedAt?: number;

  /**
   * Thời gian tạo (Unix timestamp)
   * @example 1703980000
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1703980000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   * @example 1703981200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1703981200,
  })
  updatedAt: number;
}

/**
 * DTO cho query SMS campaign
 */
export class SmsCampaignQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái
   * @example "SENT"
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái campaign',
    example: 'SENT',
    enum: SmsCampaignStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(SmsCampaignStatus, { message: 'Trạng thái không hợp lệ' })
  status?: SmsCampaignStatus;

  /**
   * Trường cần sắp xếp (override từ QueryDto để có options cụ thể)
   * @example "createdAt"
   */
  @ApiProperty({
    description: 'Trường cần sắp xếp',
    example: 'createdAt',
    enum: ['createdAt', 'updatedAt', 'name', 'status', 'scheduledAt', 'totalRecipients'],
    required: false,
  })
  @IsOptional()
  @IsIn(['createdAt', 'updatedAt', 'name', 'status', 'scheduledAt', 'totalRecipients'], {
    message: 'sortBy phải là một trong: createdAt, updatedAt, name, status, scheduledAt, totalRecipients'
  })
  declare sortBy?: string;
}

/**
 * DTO cho thống kê tổng quan SMS campaign
 */
export class SmsCampaignOverviewDto {
  /**
   * Tổng số campaign
   * @example 25
   */
  @ApiProperty({
    description: 'Tổng số campaign',
    example: 25,
  })
  totalCampaigns: number;

  /**
   * Số campaign nháp
   * @example 3
   */
  @ApiProperty({
    description: 'Số campaign nháp',
    example: 3,
  })
  draftCampaigns: number;

  /**
   * Số campaign đã lên lịch
   * @example 2
   */
  @ApiProperty({
    description: 'Số campaign đã lên lịch',
    example: 2,
  })
  scheduledCampaigns: number;

  /**
   * Số campaign đang gửi
   * @example 1
   */
  @ApiProperty({
    description: 'Số campaign đang gửi',
    example: 1,
  })
  sendingCampaigns: number;

  /**
   * Số campaign đã gửi
   * @example 18
   */
  @ApiProperty({
    description: 'Số campaign đã gửi',
    example: 18,
  })
  sentCampaigns: number;

  /**
   * Số campaign thất bại
   * @example 1
   */
  @ApiProperty({
    description: 'Số campaign thất bại',
    example: 1,
  })
  failedCampaigns: number;

  /**
   * Tổng số SMS đã gửi
   * @example 5420
   */
  @ApiProperty({
    description: 'Tổng số SMS đã gửi',
    example: 5420,
  })
  totalSent: number;

  /**
   * Tổng số SMS thất bại
   * @example 125
   */
  @ApiProperty({
    description: 'Tổng số SMS thất bại',
    example: 125,
  })
  totalFailed: number;

  /**
   * Tỷ lệ thành công tổng thể (%)
   * @example 97.7
   */
  @ApiProperty({
    description: 'Tỷ lệ thành công tổng thể (%)',
    example: 97.7,
  })
  overallSuccessRate: number;

  /**
   * Số campaign đang hoạt động (SENT, SENDING, SCHEDULED)
   * @example 3
   */
  @ApiProperty({
    description: 'Số campaign đang hoạt động (SENT, SENDING, SCHEDULED)',
    example: 3,
  })
  activeProviders: number;

  /**
   * Tổng chi phí SMS (VND)
   * @example 162600
   */
  @ApiProperty({
    description: 'Tổng chi phí SMS (VND)',
    example: 162600,
  })
  totalCost: number;

  /**
   * Chi phí trung bình mỗi tin nhắn (VND)
   * @example 30
   */
  @ApiProperty({
    description: 'Chi phí trung bình mỗi tin nhắn (VND)',
    example: 30,
  })
  averageCostPerMessage: number;
}
