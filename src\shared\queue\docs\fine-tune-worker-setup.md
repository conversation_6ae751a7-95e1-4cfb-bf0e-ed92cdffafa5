# Fine-Tune Worker Setup Guide

## Tổng quan

Worker này xử lý monitoring fine-tuning jobs từ Redis queue. Worker chạy ở backend process riêng biệt và liên tục kiểm tra trạng thái fine-tuning jobs từ các providers (OpenAI, Gemini).

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Execute API   │───▶│   Redis Queue   │───▶│ Worker Process  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                              ┌─────────────────┐
                                              │ Provider APIs   │
                                              │ (OpenAI/Gemini) │
                                              └─────────────────┘
```

## Job Types

### 1. FINE_TUNE_MONITOR
- **Purpose**: Monitor fine-tuning job status từ provider
- **Frequency**: Every 60 seconds (configurable)
- **Payload**:
```typescript
{
  historyId: string,        // Model ID
  providerJobId: string,    // Job ID từ provider (ft-xxx)
  provider: string,         // 'OPENAI' | 'GEMINI'
  userId: number           // User ID
}
```

### 2. FINE_TUNE_POLLING (Simplified)
- **Purpose**: Lightweight polling cho simple monitoring
- **Payload**:
```typescript
{
  userId: number,
  modelId: string,
  costDeducted: number
}
```

## Worker Implementation

### Setup Worker Module

```typescript
// src/workers/fine-tune-worker.module.ts
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '@shared/queue/queue.constants';
import { FineTuneMonitoringWorker } from '@shared/queue/workers/fine-tune-monitoring.worker';

@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.FINE_TUNE,
    }),
  ],
  providers: [FineTuneMonitoringWorker],
})
export class FineTuneWorkerModule {}
```

### Worker Process Lifecycle

1. **Job Received**: Worker nhận job từ Redis queue
2. **Status Check**: Call provider API để check job status
3. **Database Update**: Update model status và metadata
4. **Action Decision**:
   - **Completed**: Activate model, remove monitoring
   - **Failed**: Refund points, mark as failed
   - **Running**: Continue monitoring

## Provider Integration

### OpenAI Status Mapping
```typescript
const statusMap = {
  'validating_files': 'validating',
  'queued': 'queued', 
  'running': 'training',
  'succeeded': 'completed',
  'failed': 'failed',
  'cancelled': 'cancelled'
};
```

### Gemini Status Mapping
```typescript
const statusMap = {
  'PENDING': 'queued',
  'RUNNING': 'training', 
  'COMPLETED': 'completed',
  'FAILED': 'failed',
  'CANCELLED': 'cancelled'
};
```

## Database Operations

### Model Activation (Success)
```sql
UPDATE models 
SET active = true, 
    model_id = 'ft-final-model-id'  -- Update với final model ID
WHERE id = :modelId;

UPDATE model_detail 
SET metadata = jsonb_set(metadata, '{status}', '"completed"')
WHERE id = :detailId;
```

### Point Refund (Failure)
```sql
UPDATE users 
SET points_balance = points_balance + :refundAmount
WHERE id = :userId;

UPDATE models 
SET active = false
WHERE id = :modelId;
```

## Configuration

### Queue Options
```typescript
const monitoringJobOptions = {
  delay: 30000,           // Delay 30s trước khi bắt đầu
  attempts: 10,           // Retry 10 lần nếu thất bại
  backoff: {
    type: 'exponential',
    delay: 5000           // Exponential backoff
  },
  repeat: {
    every: 60000          // Repeat every 60s
  },
  removeOnComplete: 50,   // Giữ 50 job completed
  removeOnFail: 100       // Giữ 100 job failed
};
```

### Environment Variables
```env
# Redis Configuration
REDIS_URL=redis://localhost:6379

# Fine-tuning Configuration  
FINE_TUNE_MONITOR_INTERVAL=60000    # 60 seconds
FINE_TUNE_MAX_RETRIES=10
FINE_TUNE_TIMEOUT=300000            # 5 minutes timeout

# Provider API Keys (for system integration)
OPENAI_API_KEY=sk-xxx
GEMINI_API_KEY=xxx
```

## Monitoring & Logging

### Log Levels
- **INFO**: Job start/completion, status changes
- **DEBUG**: Detailed job data, API responses
- **WARN**: Unknown statuses, retries
- **ERROR**: API failures, database errors

### Metrics to Track
- Job processing time
- Success/failure rates
- Provider API response times
- Queue depth and processing lag

## Error Handling

### Retry Strategy
1. **Transient Errors**: Retry with exponential backoff
2. **API Rate Limits**: Respect rate limits, increase delay
3. **Authentication Errors**: Alert admin, stop processing
4. **Permanent Failures**: Mark job as failed, refund points

### Failure Scenarios
- Provider API down
- Invalid API keys
- Job not found on provider
- Database connection issues
- Network timeouts

## Deployment

### Worker Process
```bash
# Start worker process
npm run start:worker

# Or with PM2
pm2 start ecosystem.config.js --only fine-tune-worker
```

### Docker Setup
```dockerfile
# Dockerfile.worker
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist/ ./dist/
CMD ["node", "dist/workers/fine-tune-worker.js"]
```

### Health Checks
```typescript
// Health check endpoint
@Get('/health/fine-tune-worker')
async checkWorkerHealth() {
  const queueHealth = await this.fineTuneQueue.getWaiting();
  return {
    status: 'healthy',
    queueDepth: queueHealth.length,
    timestamp: Date.now()
  };
}
```

## Testing

### Unit Tests
```typescript
describe('FineTuneMonitoringWorker', () => {
  it('should process monitoring job successfully', async () => {
    const job = createMockJob({
      historyId: 'model-123',
      providerJobId: 'ft-abc123',
      provider: 'OPENAI',
      userId: 1
    });
    
    const result = await worker.process(job);
    expect(result.success).toBe(true);
  });
});
```

### Integration Tests
```typescript
describe('Fine-Tune Worker Integration', () => {
  it('should complete full monitoring cycle', async () => {
    // 1. Create monitoring job
    // 2. Process job with mock provider response
    // 3. Verify database updates
    // 4. Check job completion
  });
});
```

## Best Practices

1. **Idempotency**: Ensure operations can be safely retried
2. **Graceful Shutdown**: Handle SIGTERM properly
3. **Resource Cleanup**: Clean up resources on job completion
4. **Rate Limiting**: Respect provider API limits
5. **Monitoring**: Track job metrics and performance
6. **Error Recovery**: Implement proper error handling and recovery

## Troubleshooting

### Common Issues
- **Jobs stuck in queue**: Check worker process status
- **High failure rate**: Verify API keys and provider status
- **Memory leaks**: Monitor worker memory usage
- **Database locks**: Check for long-running transactions

### Debug Commands
```bash
# Check queue status
redis-cli LLEN bull:fine-tune:waiting
redis-cli LLEN bull:fine-tune:active

# Monitor job processing
tail -f logs/fine-tune-worker.log | grep "Processing job"
```
