# SMS Campaign API Update

## Tổng quan

API `createSmsCampaign` đã được cập nhật để sử dụng các trường mới thay vì truyền toàn bộ object config. Điều này giúp API đơn giản hơn và backend sẽ tự truy vấn dữ liệu cần thiết.

## Thay đổi DTO

### Trước (Cũ)
```typescript
{
  "name": "Campaign Name",
  "campaignType": "OTP",
  "smsIntegrationConfig": {
    "id": "uuid",
    "integrationName": "FPT SMS",
    "typeId": 1,
    "metadata": {...}
  },
  "segmentConfig": {
    "id": 1,
    "name": "Segment Name",
    "audienceCount": 100
  },
  "audiences": [
    {
      "name": "User 1",
      "phoneNumber": "0901234567",
      "countryCode": 84
    }
  ],
  "templateId": 15,
  "templateVariables": {...}
}
```

### <PERSON>u (<PERSON><PERSON><PERSON>)
```typescript
{
  "name": "Campaign Name",
  "campaignType": "OTP",
  "serverId": "uuid",           // Thay thế smsIntegrationConfig
  "segmentIds": [1, 2],         // Thay thế segmentConfig
  "audienceIds": [1, 2, 3],     // Thay thế audiences
  "phoneNumbers": ["0901234567"], // Vẫn giữ nguyên
  "templateId": 15,
  "templateVariables": {...}
}
```

## Các trường mới

### `serverId` (bắt buộc)
- **Kiểu**: `string` (UUID)
- **Mô tả**: ID của SMS server/integration
- **Validation**: Phải là UUID hợp lệ và tồn tại trong database
- **Thay thế**: `smsIntegrationConfig`

### `segmentIds` (tùy chọn)
- **Kiểu**: `number[]`
- **Mô tả**: Danh sách ID của segments để gửi SMS
- **Validation**: Mỗi ID phải là số và segment phải tồn tại
- **Thay thế**: `segmentConfig`

### `audienceIds` (tùy chọn)
- **Kiểu**: `number[]`
- **Mô tả**: Danh sách ID của audiences để gửi SMS
- **Validation**: Mỗi ID phải là số và audience phải tồn tại
- **Thay thế**: `audiences`

## Logic xử lý

### Backend sẽ tự động:
1. **Lấy SMS Integration Config** từ `serverId`
2. **Lấy danh sách audiences** từ `segmentIds` và `audienceIds`
3. **Loại bỏ duplicate audiences** nếu có
4. **Validate** tất cả dữ liệu trước khi tạo campaign
5. **Chuẩn bị dữ liệu** và đẩy sang worker

### Validation Rules:
- Phải có ít nhất một trong: `segmentIds`, `audienceIds`, hoặc `phoneNumbers`
- `serverId` phải tồn tại và thuộc về user
- Tất cả `segmentIds` phải tồn tại và thuộc về user
- Tất cả `audienceIds` phải tồn tại và thuộc về user
- ADS campaign bắt buộc phải có `scheduledAt`

## Ví dụ sử dụng

### 1. OTP Campaign với audiences
```json
{
  "name": "OTP Verification",
  "campaignType": "OTP",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "audienceIds": [1, 2, 3],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}
```

### 2. ADS Campaign với segments
```json
{
  "name": "Promotion Campaign",
  "campaignType": "ADS",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "segmentIds": [1, 2],
  "templateId": 20,
  "templateVariables": {
    "PROMOTION_CODE": "SALE50"
  },
  "scheduledAt": 1735689600
}
```

### 3. Campaign với phone numbers
```json
{
  "name": "Direct SMS",
  "campaignType": "OTP",
  "serverId": "9404b492-358b-4f03-906d-f122018347bf",
  "phoneNumbers": ["0901234567", "0987654321"],
  "templateId": 15,
  "templateVariables": {
    "TWO_FA_CODE": "123456"
  }
}
```

## Lợi ích

1. **API đơn giản hơn**: Client chỉ cần truyền IDs thay vì toàn bộ object
2. **Dữ liệu luôn mới**: Backend tự truy vấn dữ liệu mới nhất từ database
3. **Bảo mật tốt hơn**: Client không cần biết chi tiết config của integration
4. **Dễ maintain**: Logic xử lý tập trung ở backend
5. **Validation tốt hơn**: Backend có thể validate đầy đủ quyền sở hữu

## Migration

Để migrate từ API cũ sang API mới:

1. Thay `smsIntegrationConfig.id` → `serverId`
2. Thay `segmentConfig.id` → `segmentIds: [id]`
3. Thay `audiences` → `audienceIds` (cần mapping từ audience objects sang IDs)
4. Giữ nguyên các trường khác: `name`, `campaignType`, `templateId`, `templateVariables`, `phoneNumbers`, `scheduledAt`

## Test Cases

Xem file `test-create-sms-campaign-updated.http` để có các test cases đầy đủ.
