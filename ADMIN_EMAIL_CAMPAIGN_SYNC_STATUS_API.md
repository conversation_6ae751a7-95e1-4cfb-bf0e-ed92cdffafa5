# Admin Email Campaign - Sync Status API

## Tổng quan

Đã tạo thành công API để cập nhật trạng thái chiến dịch email admin dựa trên trạng thái job trong queue và thời gian lên lịch.

## API Endpoint

```
POST /admin/email-campaigns/sync-status
```

**Headers:**
- `Authorization: Bearer <JWT_TOKEN>`
- `Content-Type: application/json`

**Permissions:** Cần quyền `MARKETING_VIEW`

## Logic hoạt động

### 1. Kiểm tra Campaign SCHEDULED
- **Điều kiện**: Campaign có trạng thái `SCHEDULED` và `scheduledAt < currentTime`
- **Hành động**: Kiểm tra xem còn job nào đang active trong queue không
- **Kết quả**: Nếu không còn job active → cập nhật thành `FAILED`

### 2. <PERSON><PERSON><PERSON> tra Campaign SENDING
- **Đi<PERSON>u kiện**: Campaign có trạng thái `SENDING`
- **Hành động**: Kiểm tra trạng thái tất cả job trong queue
- **Kết quả**: 
  - Nếu tất cả job `completed` → cập nhật thành `COMPLETED` + set `completedAt`
  - Nếu tất cả job `failed` hoặc không còn job nào → cập nhật thành `FAILED`

### 3. Trạng thái Job trong Queue
- `waiting`, `active`, `delayed`: Job đang hoạt động
- `completed`: Job hoàn thành thành công
- `failed`: Job thất bại
- Job không tồn tại: Coi như `failed`

## Response Format

```json
{
  "success": true,
  "message": "Đã cập nhật trạng thái campaign thành công",
  "data": {
    "totalCampaignsChecked": 25,
    "updatedCampaigns": [
      {
        "campaignId": 1,
        "campaignName": "Welcome Email",
        "previousStatus": "SCHEDULED",
        "currentStatus": "FAILED",
        "reason": "Quá thời gian lên lịch và không còn job trong queue"
      }
    ],
    "summary": {
      "scheduledToFailed": 1,
      "sendingToCompleted": 0,
      "sendingToFailed": 0
    }
  }
}
```

## Files đã tạo/cập nhật

### 1. Controller
- **File**: `src/modules/marketing/admin/controllers/admin-email-campaign.controller.ts`
- **Method**: `syncCampaignStatus()`
- **Route**: `POST /admin/email-campaigns/sync-status`

### 2. Service
- **File**: `src/modules/marketing/admin/services/admin-email-campaign.service.ts`
- **Method**: `syncCampaignStatus(employeeId: number)`
- **Helper Methods**: 
  - `checkActiveJobs(jobIds: string[])`
  - `checkJobsStatus(jobIds: string[])`

### 3. DTOs
- **File**: `src/modules/marketing/admin/dto/admin-email-campaign/admin-email-campaign-response.dto.ts`
- **Classes**:
  - `SyncCampaignUpdateDto`
  - `SyncStatusSummaryDto`
  - `SyncCampaignStatusResponseDto`

### 4. Module Update
- **File**: `src/modules/marketing/admin/marketing-admin.module.ts`
- **Changes**: Không cần thay đổi gì thêm

### 5. Test Files
- **Unit Test**: `src/modules/marketing/admin/services/admin-email-campaign-sync.test.ts`
- **API Test Script**: `scripts/test-sync-campaign-status.js`
- **Documentation**: `test-sync-campaign-status.md`

## Cách sử dụng

### 1. Manual API Call
```bash
curl -X POST http://localhost:3000/admin/email-campaigns/sync-status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 2. Test với Script
```bash
# Cập nhật JWT_TOKEN trong file trước
node scripts/test-sync-campaign-status.js
```

## Lưu ý quan trọng

1. **Performance**: API kiểm tra tất cả campaign có trạng thái `SCHEDULED` và `SENDING`
2. **Queue Connection**: Cần đảm bảo Redis/Queue hoạt động bình thường
3. **Error Handling**: Có xử lý lỗi và logging chi tiết
4. **Permissions**: Cần quyền `MARKETING_VIEW`
5. **Transaction Safety**: Các cập nhật database được thực hiện an toàn

## Monitoring & Logging

- Service ghi log chi tiết quá trình sync
- Error handling với AppException
- Metrics về số campaign được kiểm tra và cập nhật

## Tích hợp với hệ thống

API này có thể được tích hợp với:
- Dashboard admin để hiển thị trạng thái real-time
- Alert system để cảnh báo campaign có vấn đề
- Reporting system để thống kê hiệu suất campaign
- Monitoring tools để theo dõi queue health

## Next Steps

1. Test API với data thực tế
2. Cấu hình monitoring và alerting
3. Tối ưu performance nếu cần
4. Thêm metrics và analytics
5. Tích hợp với dashboard
