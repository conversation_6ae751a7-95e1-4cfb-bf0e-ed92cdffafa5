# Test API GET /v1/employees/info/me với coverImage

## M<PERSON> tả thay đổi

Đã thêm trường `coverImage` vào API GET `/v1/employees/info/me` để trả về thông tin ảnh bìa của nhân viên đang đăng nhập hiện tại.

## C<PERSON>c thay đổi đã thực hiện

### 1. <PERSON><PERSON><PERSON> nhật EmployeeResponseDto
- Thêm trường `coverImage`: URL ảnh bìa có thời hạn (24 giờ)

### 2. Cập nhật EmployeeService
- Thêm method `generateCoverImageUrl()` tương tự như `generateAvatarUrl()`
- Cập nhật method `findById()` để bao gồm `coverImage`
- Cập nhật method `findAll()` để bao gồm `coverImage` 
- C<PERSON><PERSON> nhật method `updateEmployee()` để bao gồm `coverImage`

## Cách test API

### Request
```http
GET /v1/employees/info/me
Authorization: Bearer <EMPLOYEE_JWT_TOKEN>
```

### Expected Response
```json
{
  "success": true,
  "code": 200,
  "message": "Lấy thông tin nhân viên hiện tại thành công",
  "result": {
    "id": 1,
    "fullName": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "phoneNumber": "0987654321",
    "address": "Hà Nội",
    "createdAt": 1682506092000,
    "updatedAt": 1682506092000,
    "enable": true,
    "avatar": "https://cdn.example.com/employees/1/avatar.jpg?token=abc123",
    "coverImage": "https://cdn.example.com/employees/1/cover_images/2024/01/cover.jpg?token=def456"
  }
}
```

## Test Cases

### Case 1: Employee có coverImage
- Employee đã upload ảnh bìa
- Expect: `coverImage` có URL có thời hạn 24 giờ

### Case 2: Employee chưa có coverImage  
- Employee chưa upload ảnh bìa
- Expect: `coverImage` = undefined

### Case 3: CdnService generateUrlView thất bại
- `coverImage` key có giá trị nhưng CDN service không tạo được URL
- Expect: `coverImage` = undefined, log warning

## Swagger Documentation

API documentation sẽ tự động cập nhật vì sử dụng `EmployeeResponseDto` schema trong `@ApiResponse`.

## Tương thích ngược

- API vẫn trả về tất cả các trường cũ
- Chỉ thêm 1 trường mới: `coverImage`
- Không ảnh hưởng đến client hiện tại

## Các API khác cũng được cập nhật

Do sử dụng chung `EmployeeResponseDto`, các API sau cũng sẽ trả về `coverImage`:
- GET `/v1/employees` (danh sách nhân viên)
- GET `/v1/employees/{id}` (chi tiết nhân viên theo ID)
- PUT `/v1/employees/{id}` (cập nhật nhân viên)

## Thời hạn URL

- Avatar URL: 24 giờ (TimeIntervalEnum.ONE_DAY)
- Cover Image URL: 24 giờ (TimeIntervalEnum.ONE_DAY)
- Tương tự như avatar để đảm bảo tính nhất quán
