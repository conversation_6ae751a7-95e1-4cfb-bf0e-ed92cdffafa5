import { AppException } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import {
  FACEBOOK_BUSINESS_ERROR_CODES,
  createFacebookBusinessException,
  validateDateRange,
  validateFacebookBusinessParams
} from '../exceptions/facebook-business.exception';
import { FacebookInsights } from '../interfaces/facebook-business.interface';
import { FacebookBusinessApiService } from './facebook-business-api.service';
import { Campaign, AdSet, Ad } from 'facebook-nodejs-business-sdk';

/**
 * Service xử lý Facebook Insights và Analytics
 */
@Injectable()
export class FacebookInsightsService {
  private readonly logger = new Logger(FacebookInsightsService.name);

  constructor(private readonly facebookApiService: FacebookBusinessApiService) { }

  /**
   * Lấy insights cho ad account
   * @param adAccountId ID của ad account
   * @param dateStart <PERSON><PERSON><PERSON> bắt đầu (YYYY-MM-DD)
   * @param dateStop Ng<PERSON>y kết thúc (YYYY-MM-DD)
   * @param metrics Danh sách metrics cần lấy
   * @returns Insights data
   */
  async getAdAccountInsights(
    adAccountId: string,
    dateStart: string,
    dateStop: string,
    metrics: string[] = [
      'impressions',
      'clicks',
      'spend',
      'reach',
      'frequency',
      'cpm',
      'cpc',
      'ctr',
      'actions',
      'action_values',
      'cost_per_action_type',
    ],
  ): Promise<FacebookInsights[]> {
    try {
      validateFacebookBusinessParams({ adAccountId, dateStart, dateStop }, [
        'adAccountId',
        'dateStart',
        'dateStop',
      ]);
      validateDateRange(dateStart, dateStop);

      this.logger.log(
        `Getting ad account insights for ${adAccountId} from ${dateStart} to ${dateStop}`,
      );

      const adAccount = this.facebookApiService.getAdAccountInstance(adAccountId);

      const insights = await adAccount.getInsights(metrics, {
        time_range: {
          since: dateStart,
          until: dateStop,
        },
        level: 'account',
        time_increment: 1, // Daily breakdown
      });

      const result: FacebookInsights[] = insights.map((insight: any) => ({
        date_start: insight.date_start,
        date_stop: insight.date_stop,
        impressions: insight.impressions,
        clicks: insight.clicks,
        spend: insight.spend,
        reach: insight.reach,
        frequency: insight.frequency,
        cpm: insight.cpm,
        cpc: insight.cpc,
        ctr: insight.ctr,
        actions: insight.actions,
        action_values: insight.action_values,
        cost_per_action_type: insight.cost_per_action_type,
        video_views: insight.video_views,
        video_play_actions: insight.video_play_actions,
      }));

      this.logger.log(`Successfully retrieved ${result.length} insights records`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting ad account insights: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy insights cho ad account',
        { adAccountId, dateStart, dateStop },
      );
    }
  }

  /**
   * Lấy insights cho campaign
   * @param campaignId ID của campaign
   * @param dateStart Ngày bắt đầu (YYYY-MM-DD)
   * @param dateStop Ngày kết thúc (YYYY-MM-DD)
   * @param metrics Danh sách metrics cần lấy
   * @returns Insights data
   */
  async getCampaignInsights(
    campaignId: string,
    dateStart: string,
    dateStop: string,
    metrics: string[] = [
      'impressions',
      'clicks',
      'spend',
      'reach',
      'frequency',
      'cpm',
      'cpc',
      'ctr',
      'actions',
      'action_values',
      'cost_per_action_type',
    ],
  ): Promise<FacebookInsights[]> {
    try {
      validateFacebookBusinessParams({ campaignId, dateStart, dateStop }, [
        'campaignId',
        'dateStart',
        'dateStop',
      ]);
      validateDateRange(dateStart, dateStop);

      this.logger.log(
        `Getting campaign insights for ${campaignId} from ${dateStart} to ${dateStop}`,
      );

      const campaign = new Campaign(campaignId);

      const insights = await campaign.getInsights(metrics, {
        time_range: {
          since: dateStart,
          until: dateStop,
        },
        time_increment: 1, // Daily breakdown
      });

      const result: FacebookInsights[] = insights.map((insight: any) => ({
        date_start: insight.date_start,
        date_stop: insight.date_stop,
        impressions: insight.impressions,
        clicks: insight.clicks,
        spend: insight.spend,
        reach: insight.reach,
        frequency: insight.frequency,
        cpm: insight.cpm,
        cpc: insight.cpc,
        ctr: insight.ctr,
        actions: insight.actions,
        action_values: insight.action_values,
        cost_per_action_type: insight.cost_per_action_type,
        video_views: insight.video_views,
        video_play_actions: insight.video_play_actions,
      }));

      this.logger.log(`Successfully retrieved ${result.length} campaign insights records`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting campaign insights: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy insights cho campaign',
        { campaignId, dateStart, dateStop },
      );
    }
  }

  /**
   * Lấy insights cho ad set
   * @param adSetId ID của ad set
   * @param dateStart Ngày bắt đầu (YYYY-MM-DD)
   * @param dateStop Ngày kết thúc (YYYY-MM-DD)
   * @param metrics Danh sách metrics cần lấy
   * @returns Insights data
   */
  async getAdSetInsights(
    adSetId: string,
    dateStart: string,
    dateStop: string,
    metrics: string[] = [
      'impressions',
      'clicks',
      'spend',
      'reach',
      'frequency',
      'cpm',
      'cpc',
      'ctr',
      'actions',
      'action_values',
      'cost_per_action_type',
    ],
  ): Promise<FacebookInsights[]> {
    try {
      validateFacebookBusinessParams({ adSetId, dateStart, dateStop }, [
        'adSetId',
        'dateStart',
        'dateStop',
      ]);
      validateDateRange(dateStart, dateStop);

      this.logger.log(
        `Getting ad set insights for ${adSetId} from ${dateStart} to ${dateStop}`,
      );

      const adSet = new AdSet(adSetId);

      const insights = await adSet.getInsights(metrics, {
        time_range: {
          since: dateStart,
          until: dateStop,
        },
        time_increment: 1, // Daily breakdown
      });

      const result: FacebookInsights[] = insights.map((insight: any) => ({
        date_start: insight.date_start,
        date_stop: insight.date_stop,
        impressions: insight.impressions,
        clicks: insight.clicks,
        spend: insight.spend,
        reach: insight.reach,
        frequency: insight.frequency,
        cpm: insight.cpm,
        cpc: insight.cpc,
        ctr: insight.ctr,
        actions: insight.actions,
        action_values: insight.action_values,
        cost_per_action_type: insight.cost_per_action_type,
        video_views: insight.video_views,
        video_play_actions: insight.video_play_actions,
      }));

      this.logger.log(`Successfully retrieved ${result.length} ad set insights records`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting ad set insights: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy insights cho ad set',
        { adSetId, dateStart, dateStop },
      );
    }
  }

  /**
   * Lấy insights cho ad
   * @param adId ID của ad
   * @param dateStart Ngày bắt đầu (YYYY-MM-DD)
   * @param dateStop Ngày kết thúc (YYYY-MM-DD)
   * @param metrics Danh sách metrics cần lấy
   * @returns Insights data
   */
  async getAdInsights(
    adId: string,
    dateStart: string,
    dateStop: string,
    metrics: string[] = [
      'impressions',
      'clicks',
      'spend',
      'reach',
      'frequency',
      'cpm',
      'cpc',
      'ctr',
      'actions',
      'action_values',
      'cost_per_action_type',
    ],
  ): Promise<FacebookInsights[]> {
    try {
      validateFacebookBusinessParams({ adId, dateStart, dateStop }, [
        'adId',
        'dateStart',
        'dateStop',
      ]);
      validateDateRange(dateStart, dateStop);

      this.logger.log(`Getting ad insights for ${adId} from ${dateStart} to ${dateStop}`);

      const ad = new Ad(adId);

      const insights = await ad.getInsights(metrics, {
        time_range: {
          since: dateStart,
          until: dateStop,
        },
        time_increment: 1, // Daily breakdown
      });

      const result: FacebookInsights[] = insights.map((insight: any) => ({
        date_start: insight.date_start,
        date_stop: insight.date_stop,
        impressions: insight.impressions,
        clicks: insight.clicks,
        spend: insight.spend,
        reach: insight.reach,
        frequency: insight.frequency,
        cpm: insight.cpm,
        cpc: insight.cpc,
        ctr: insight.ctr,
        actions: insight.actions,
        action_values: insight.action_values,
        cost_per_action_type: insight.cost_per_action_type,
        video_views: insight.video_views,
        video_play_actions: insight.video_play_actions,
      }));

      this.logger.log(`Successfully retrieved ${result.length} ad insights records`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting ad insights: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy insights cho ad',
        { adId, dateStart, dateStop },
      );
    }
  }

  /**
   * Lấy insights tổng hợp cho nhiều campaigns
   * @param campaignIds Danh sách campaign IDs
   * @param dateStart Ngày bắt đầu (YYYY-MM-DD)
   * @param dateStop Ngày kết thúc (YYYY-MM-DD)
   * @param metrics Danh sách metrics cần lấy
   * @returns Insights data tổng hợp
   */
  async getMultipleCampaignInsights(
    campaignIds: string[],
    dateStart: string,
    dateStop: string,
    metrics: string[] = [
      'impressions',
      'clicks',
      'spend',
      'reach',
      'frequency',
      'cpm',
      'cpc',
      'ctr',
      'actions',
      'action_values',
      'cost_per_action_type',
    ],
  ): Promise<Record<string, FacebookInsights[]>> {
    try {
      validateFacebookBusinessParams({ campaignIds, dateStart, dateStop }, [
        'campaignIds',
        'dateStart',
        'dateStop',
      ]);
      validateDateRange(dateStart, dateStop);

      if (!Array.isArray(campaignIds) || campaignIds.length === 0) {
        throw new AppException(
          FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_PARAMETERS,
          'Campaign IDs must be a non-empty array',
        );
      }

      this.logger.log(
        `Getting insights for ${campaignIds.length} campaigns from ${dateStart} to ${dateStop}`,
      );

      const results: Record<string, FacebookInsights[]> = {};

      // Process campaigns in parallel
      const promises = campaignIds.map(async (campaignId) => {
        try {
          const insights = await this.getCampaignInsights(
            campaignId,
            dateStart,
            dateStop,
            metrics,
          );
          results[campaignId] = insights;
        } catch (error) {
          this.logger.warn(`Failed to get insights for campaign ${campaignId}: ${error.message}`);
          results[campaignId] = [];
        }
      });

      await Promise.all(promises);

      this.logger.log(`Successfully retrieved insights for ${Object.keys(results).length} campaigns`);
      return results;
    } catch (error) {
      this.logger.error(`Error getting multiple campaign insights: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy insights cho nhiều campaigns',
        { campaignIds, dateStart, dateStop },
      );
    }
  }

  /**
   * Lấy insights với breakdown theo demographics
   * @param objectId ID của object (campaign, ad set, ad)
   * @param objectType Loại object ('campaign', 'adset', 'ad')
   * @param dateStart Ngày bắt đầu (YYYY-MM-DD)
   * @param dateStop Ngày kết thúc (YYYY-MM-DD)
   * @param breakdowns Danh sách breakdowns
   * @returns Insights data với breakdown
   */
  async getInsightsWithBreakdown(
    objectId: string,
    objectType: 'campaign' | 'adset' | 'ad',
    dateStart: string,
    dateStop: string,
    breakdowns: string[] = ['age', 'gender'],
    metrics: string[] = ['impressions', 'clicks', 'spend', 'cpm', 'cpc', 'ctr'],
  ): Promise<FacebookInsights[]> {
    try {
      validateFacebookBusinessParams({ objectId, objectType, dateStart, dateStop }, [
        'objectId',
        'objectType',
        'dateStart',
        'dateStop',
      ]);
      validateDateRange(dateStart, dateStop);

      this.logger.log(
        `Getting insights with breakdown for ${objectType} ${objectId} from ${dateStart} to ${dateStop}`,
      );

      let objectInstance: any;
      switch (objectType) {
        case 'campaign':
          objectInstance = new Campaign(objectId);
          break;
        case 'adset':
          objectInstance = new AdSet(objectId);
          break;
        case 'ad':
          objectInstance = new Ad(objectId);
          break;
        default:
          throw new AppException(
            FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_PARAMETERS,
            'Invalid object type. Must be campaign, adset, or ad',
          );
      }

      const insights = await objectInstance.getInsights(metrics, {
        time_range: {
          since: dateStart,
          until: dateStop,
        },
        breakdowns,
      });

      const result: FacebookInsights[] = insights.map((insight: any) => ({
        date_start: insight.date_start,
        date_stop: insight.date_stop,
        impressions: insight.impressions,
        clicks: insight.clicks,
        spend: insight.spend,
        reach: insight.reach,
        frequency: insight.frequency,
        cpm: insight.cpm,
        cpc: insight.cpc,
        ctr: insight.ctr,
        actions: insight.actions,
        action_values: insight.action_values,
        cost_per_action_type: insight.cost_per_action_type,
        video_views: insight.video_views,
        video_play_actions: insight.video_play_actions,
        // Include breakdown data
        ...Object.fromEntries(
          breakdowns.map(breakdown => [breakdown, insight[breakdown]])
        ),
      }));

      this.logger.log(`Successfully retrieved ${result.length} insights records with breakdown`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting insights with breakdown: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw createFacebookBusinessException(
        error,
        'Không thể lấy insights với breakdown',
        { objectId, objectType, dateStart, dateStop, breakdowns },
      );
    }
  }

  /**
   * Lấy available metrics cho insights
   * @returns Danh sách metrics có sẵn
   */
  getAvailableMetrics(): string[] {
    return [
      'impressions',
      'clicks',
      'spend',
      'reach',
      'frequency',
      'cpm',
      'cpc',
      'ctr',
      'cpp',
      'cost_per_unique_click',
      'unique_clicks',
      'unique_ctr',
      'unique_link_clicks_ctr',
      'actions',
      'action_values',
      'cost_per_action_type',
      'video_views',
      'video_play_actions',
      'video_p25_watched_actions',
      'video_p50_watched_actions',
      'video_p75_watched_actions',
      'video_p100_watched_actions',
      'video_avg_time_watched_actions',
      'website_ctr',
      'inline_link_clicks',
      'inline_link_click_ctr',
      'inline_post_engagement',
      'social_spend',
      'quality_ranking',
      'engagement_rate_ranking',
      'conversion_rate_ranking',
    ];
  }

  /**
   * Lấy available breakdowns cho insights
   * @returns Danh sách breakdowns có sẵn
   */
  getAvailableBreakdowns(): string[] {
    return [
      'age',
      'gender',
      'country',
      'region',
      'impression_device',
      'publisher_platform',
      'platform_position',
      'device_platform',
      'product_id',
      'hourly_stats_aggregated_by_advertiser_time_zone',
      'hourly_stats_aggregated_by_audience_time_zone',
    ];
  }
}
