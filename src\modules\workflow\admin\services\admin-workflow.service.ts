import { Injectable, Logger } from '@nestjs/common';
import { WorkflowRepository } from '../../repositories/workflow.repository';

/**
 * Admin service để manage workflows
 * Following existing admin service patterns
 */
@Injectable()
export class AdminWorkflowService {
  private readonly logger = new Logger(AdminWorkflowService.name);

  constructor(
    private readonly workflowRepository: WorkflowRepository,
  ) {}

  // Placeholder implementation
  // This will be implemented in future tasks
}
