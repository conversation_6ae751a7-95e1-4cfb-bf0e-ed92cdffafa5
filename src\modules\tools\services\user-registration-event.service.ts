import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UserRegisteredEvent } from '../events';

/**
 * Service để phát event khi có người dùng mới đăng ký
 * <PERSON><PERSON> thể được inject vào module auth để sử dụng
 */
@Injectable()
export class UserRegistrationEventService {
  constructor(private readonly eventEmitter: EventEmitter2) {}

  /**
   * Phát event khi có người dùng mới đăng ký
   * @param userId ID của người dùng mới
   * @param namePrefix Tiền tố tên tool (tùy chọn)
   */
  async emitUserRegistered(userId: number, namePrefix?: string): Promise<void> {
    const event = new UserRegisteredEvent(userId, namePrefix);
    this.eventEmitter.emit('user.registered', event);
  }
}
