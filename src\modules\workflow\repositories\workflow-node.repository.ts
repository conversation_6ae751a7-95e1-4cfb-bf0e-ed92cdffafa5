import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { WorkflowNode } from '../entities';

/**
 * Repository cho WorkflowNode entity
 * Xử lý các thao tác database liên quan đến workflow nodes
 */
@Injectable()
export class WorkflowNodeRepository extends Repository<WorkflowNode> {
  constructor(private dataSource: DataSource) {
    super(WorkflowNode, dataSource.createEntityManager());
  }

  /**
   * Tạo base query cho WorkflowNode
   */
  private createBaseQuery() {
    return this.createQueryBuilder('workflow_node');
  }

  /**
   * L<PERSON>y tất cả nodes của một workflow
   * @param workflowId - ID của workflow
   * @returns Promise<WorkflowNode[]>
   */
  async findByWorkflowId(workflowId: string): Promise<WorkflowNode[]> {
    return this.createBaseQuery()
      .where('workflow_node.workflowId = :workflowId', { workflowId })
      .orderBy('workflow_node.createdAt', 'ASC')
      .getMany();
  }

  /**
   * Lấy node theo workflow ID và node ID
   * @param workflowId - ID của workflow
   * @param nodeId - ID của node trong workflow
   * @returns Promise<WorkflowNode | null>
   */
  async findByWorkflowAndNodeId(workflowId: string, nodeId: string): Promise<WorkflowNode | null> {
    return this.createBaseQuery()
      .where('workflow_node.workflowId = :workflowId', { workflowId })
      .andWhere('workflow_node.nodeId = :nodeId', { nodeId })
      .getOne();
  }

  /**
   * Lấy tất cả nodes theo node type
   * @param nodeType - Type của node
   * @returns Promise<WorkflowNode[]>
   */
  async findByNodeType(nodeType: string): Promise<WorkflowNode[]> {
    return this.createBaseQuery()
      .where('workflow_node.nodeType = :nodeType', { nodeType })
      .orderBy('workflow_node.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Đếm số lượng nodes trong một workflow
   * @param workflowId - ID của workflow
   * @returns Promise<number>
   */
  async countByWorkflowId(workflowId: string): Promise<number> {
    return this.createBaseQuery()
      .where('workflow_node.workflowId = :workflowId', { workflowId })
      .getCount();
  }

  /**
   * Lấy nodes theo workflow với join node definition
   * @param workflowId - ID của workflow
   * @returns Promise<WorkflowNode[]>
   */
  async findByWorkflowIdWithDefinition(workflowId: string): Promise<WorkflowNode[]> {
    return this.createBaseQuery()
      .leftJoinAndSelect('workflow_node.nodeDefinition', 'node_def')
      .where('workflow_node.workflowId = :workflowId', { workflowId })
      .orderBy('workflow_node.createdAt', 'ASC')
      .getMany();
  }

  /**
   * Cập nhật position của node
   * @param id - ID của workflow node
   * @param position - Position mới
   * @returns Promise<void>
   */
  async updatePosition(id: string, position: { x: number; y: number }): Promise<void> {
    await this.createBaseQuery()
      .update()
      .set({ 
        position,
        updatedAt: Date.now()
      })
      .where('id = :id', { id })
      .execute();
  }

  /**
   * Cập nhật config của node
   * @param id - ID của workflow node
   * @param config - Config mới
   * @returns Promise<void>
   */
  async updateConfig(id: string, config: Record<string, any>): Promise<void> {
    await this.createBaseQuery()
      .update()
      .set({ 
        config,
        updatedAt: Date.now()
      })
      .where('id = :id', { id })
      .execute();
  }

  /**
   * Xóa tất cả nodes của một workflow
   * @param workflowId - ID của workflow
   * @returns Promise<void>
   */
  async deleteByWorkflowId(workflowId: string): Promise<void> {
    await this.createBaseQuery()
      .delete()
      .where('workflowId = :workflowId', { workflowId })
      .execute();
  }

  /**
   * Kiểm tra node có tồn tại trong workflow
   * @param workflowId - ID của workflow
   * @param nodeId - ID của node
   * @returns Promise<boolean>
   */
  async existsInWorkflow(workflowId: string, nodeId: string): Promise<boolean> {
    const count = await this.createBaseQuery()
      .where('workflow_node.workflowId = :workflowId', { workflowId })
      .andWhere('workflow_node.nodeId = :nodeId', { nodeId })
      .getCount();

    return count > 0;
  }

  /**
   * Lấy statistics về node types trong workflow
   * @param workflowId - ID của workflow
   * @returns Promise<{nodeType: string, count: number}[]>
   */
  async getNodeTypeStats(workflowId: string): Promise<{nodeType: string, count: number}[]> {
    const result = await this.createBaseQuery()
      .select('workflow_node.nodeType', 'nodeType')
      .addSelect('COUNT(*)', 'count')
      .where('workflow_node.workflowId = :workflowId', { workflowId })
      .groupBy('workflow_node.nodeType')
      .getRawMany();

    return result.map(row => ({
      nodeType: row.nodeType,
      count: parseInt(row.count)
    }));
  }

  /**
   * Bulk insert nodes
   * @param nodes - Array of nodes to insert
   * @returns Promise with insert result
   */
  async bulkInsert(nodes: Partial<WorkflowNode>[]): Promise<WorkflowNode[]> {
    const entities = nodes.map(node => this.create(node));
    return this.save(entities);
  }

  /**
   * Get statistics
   * @returns Promise with statistics
   */
  async getStatistics(): Promise<any> {
    const total = await this.count();
    return {
      total,
      byType: {}
    };
  }
}
