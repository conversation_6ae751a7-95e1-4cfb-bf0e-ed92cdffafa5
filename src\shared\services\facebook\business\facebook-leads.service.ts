import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@config/config.service';
import { ConfigType } from '@/config';
import { FacebookConfig } from '@config/interfaces';
import { firstValueFrom } from 'rxjs';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  LeadGenForm,
  Lead,
  CreateLeadGenFormRequest,
  UpdateLeadGenFormRequest,
  GetLeadGenFormsResponse,
  GetLeadsResponse,
  LeadGenFormStats,
  LeadExport,
  ExportLeadsRequest,
  LeadGenFormTest,
  LeadGenFormPreview,
  LeadGenFormAnalytics,
  GetLeadGenFormTemplatesResponse,
} from '../interfaces/facebook-leads.interface';

/**
 * Service để quản lý Lead Generation trong Facebook Business API
 */
@Injectable()
export class FacebookLeadsService {
  private readonly logger = new Logger(FacebookLeadsService.name);
  private readonly facebookConfig: FacebookConfig;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.facebookConfig = this.configService.getConfig<FacebookConfig>(
      ConfigType.Facebook,
    );
  }

  /**
   * Lấy danh sách Lead Gen Forms của Page
   * @param pageId ID của Page
   * @param accessToken Access token
   * @param fields Các trường cần lấy
   * @param limit Số lượng kết quả
   * @returns Danh sách Lead Gen Forms
   */
  async getLeadGenForms(
    pageId: string,
    accessToken: string,
    fields?: string,
    limit?: number,
  ): Promise<GetLeadGenFormsResponse> {
    try {
      this.logger.log(`Lấy danh sách Lead Gen Forms cho Page ${pageId}`);

      const params: any = {
        access_token: accessToken,
      };

      if (fields) {
        params.fields = fields;
      } else {
        params.fields = 'id,name,status,locale,questions,privacy_policy_url,follow_up_action_url,is_continued_flow,leadgen_export_csv_url,leads_count,page_id,created_time,updated_time,context_card,creator,creator_id,cusomized_tcpa_content,expired_leads_count,follow_up_action_text,is_optimized_for_quality,leadgen_tos_acceptance_time,leadgen_tos_accepted,legal_content,organic_leads_count,page,qualifiers,question_page_custom_headline,tcpa_compliance,thank_you_page';
      }

      if (limit) {
        params.limit = limit;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetLeadGenFormsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/leadgen_forms`,
          { params },
        ),
      );

      this.logger.log(
        `Đã lấy ${response.data.data.length} Lead Gen Forms cho Page ${pageId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách Lead Gen Forms: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách Lead Gen Forms',
        { pageId },
      );
    }
  }

  /**
   * Tạo Lead Gen Form mới
   * @param pageId ID của Page
   * @param accessToken Access token
   * @param formData Dữ liệu Lead Gen Form
   * @returns Lead Gen Form đã tạo
   */
  async createLeadGenForm(
    pageId: string,
    accessToken: string,
    formData: CreateLeadGenFormRequest,
  ): Promise<LeadGenForm> {
    try {
      this.logger.log(`Tạo Lead Gen Form mới cho Page ${pageId}`);

      const requestData = {
        ...formData,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post<LeadGenForm>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/leadgen_forms`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(
        `Đã tạo Lead Gen Form ${response.data.id} cho Page ${pageId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo Lead Gen Form cho Page ${pageId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo Lead Gen Form',
        { pageId, formData },
      );
    }
  }

  /**
   * Cập nhật Lead Gen Form
   * @param formId ID của Lead Gen Form
   * @param accessToken Access token
   * @param updateData Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  async updateLeadGenForm(
    formId: string,
    accessToken: string,
    updateData: UpdateLeadGenFormRequest,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Cập nhật Lead Gen Form ${formId}`);

      const requestData = {
        ...updateData,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${formId}`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(`Đã cập nhật Lead Gen Form ${formId}`);

      return { success: response.data.success || true };
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật Lead Gen Form ${formId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật Lead Gen Form',
        { formId, updateData },
      );
    }
  }

  /**
   * Xóa Lead Gen Form
   * @param formId ID của Lead Gen Form
   * @param accessToken Access token
   * @returns Kết quả xóa
   */
  async deleteLeadGenForm(
    formId: string,
    accessToken: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(`Xóa Lead Gen Form ${formId}`);

      const response = await firstValueFrom(
        this.httpService.delete(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${formId}`,
          {
            params: {
              access_token: accessToken,
            },
          },
        ),
      );

      this.logger.log(`Đã xóa Lead Gen Form ${formId}`);

      return { success: response.data.success || true };
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa Lead Gen Form ${formId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa Lead Gen Form',
        { formId },
      );
    }
  }

  /**
   * Lấy danh sách Leads
   * @param formId ID của Lead Gen Form
   * @param accessToken Access token
   * @param fields Các trường cần lấy
   * @param limit Số lượng kết quả
   * @param filtering Bộ lọc
   * @returns Danh sách Leads
   */
  async getLeads(
    formId: string,
    accessToken: string,
    fields?: string,
    limit?: number,
    filtering?: any,
  ): Promise<GetLeadsResponse> {
    try {
      this.logger.log(`Lấy danh sách Leads cho Form ${formId}`);

      const params: any = {
        access_token: accessToken,
      };

      if (fields) {
        params.fields = fields;
      } else {
        params.fields = 'id,created_time,ad_id,ad_name,adset_id,adset_name,campaign_id,campaign_name,form_id,is_organic,field_data,partner_name,platform,post,retailer_item_id';
      }

      if (limit) {
        params.limit = limit;
      }

      if (filtering) {
        params.filtering = JSON.stringify(filtering);
      }

      const response = await firstValueFrom(
        this.httpService.get<GetLeadsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${formId}/leads`,
          { params },
        ),
      );

      this.logger.log(
        `Đã lấy ${response.data.data.length} Leads cho Form ${formId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách Leads: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách Leads',
        { formId },
      );
    }
  }

  /**
   * Lấy Leads từ Page (tất cả forms)
   * @param pageId ID của Page
   * @param accessToken Access token
   * @param fields Các trường cần lấy
   * @param limit Số lượng kết quả
   * @returns Danh sách Leads
   */
  async getLeadGenFormLeads(
    pageId: string,
    accessToken: string,
    fields?: string,
    limit?: number,
  ): Promise<GetLeadsResponse> {
    try {
      this.logger.log(`Lấy danh sách Leads cho Page ${pageId}`);

      const params: any = {
        access_token: accessToken,
      };

      if (fields) {
        params.fields = fields;
      } else {
        params.fields = 'id,created_time,ad_id,ad_name,adset_id,adset_name,campaign_id,campaign_name,form_id,is_organic,field_data,partner_name,platform,post,retailer_item_id';
      }

      if (limit) {
        params.limit = limit;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetLeadsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pageId}/leads`,
          { params },
        ),
      );

      this.logger.log(
        `Đã lấy ${response.data.data.length} Leads cho Page ${pageId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách Leads cho Page: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách Leads cho Page',
        { pageId },
      );
    }
  }

  /**
   * Lấy thống kê Lead Gen Form
   * @param formId ID của Lead Gen Form
   * @param accessToken Access token
   * @param dateStart Ngày bắt đầu
   * @param dateStop Ngày kết thúc
   * @returns Thống kê Lead Gen Form
   */
  async getLeadGenFormStats(
    formId: string,
    accessToken: string,
    dateStart?: string,
    dateStop?: string,
  ): Promise<LeadGenFormStats> {
    try {
      this.logger.log(`Lấy thống kê Lead Gen Form ${formId}`);

      const params: any = {
        access_token: accessToken,
        metric: 'impressions,clicks,leads,cost_per_lead,cost_per_click,click_through_rate,conversion_rate,spend',
      };

      if (dateStart) {
        params.date_start = dateStart;
      }
      if (dateStop) {
        params.date_stop = dateStop;
      }

      const response = await firstValueFrom(
        this.httpService.get<{ data: any[] }>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${formId}/insights`,
          { params },
        ),
      );

      // Chuyển đổi dữ liệu insights thành LeadGenFormStats
      const stats: LeadGenFormStats = {
        form_id: formId,
        date_start: dateStart,
        date_stop: dateStop,
      };

      response.data.data.forEach((insight: any) => {
        const metricName = insight.name;
        const value = insight.values?.[0]?.value || 0;
        (stats as any)[metricName] = value;
      });

      this.logger.log(`Đã lấy thống kê Lead Gen Form ${formId}`);

      return stats;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thống kê Lead Gen Form ${formId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thống kê Lead Gen Form',
        { formId },
      );
    }
  }

  /**
   * Export Leads
   * @param exportRequest Yêu cầu export
   * @param accessToken Access token
   * @returns Thông tin export
   */
  async exportLeads(
    exportRequest: ExportLeadsRequest,
    accessToken: string,
  ): Promise<LeadExport> {
    try {
      this.logger.log(`Export Leads cho Form ${exportRequest.form_id}`);

      const requestData = {
        ...exportRequest,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post<LeadExport>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${exportRequest.form_id}/leads_export`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(
        `Đã tạo export Leads ${response.data.export_id} cho Form ${exportRequest.form_id}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi export Leads cho Form ${exportRequest.form_id}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi export Leads',
        { exportRequest },
      );
    }
  }

  /**
   * Test Lead Gen Form
   * @param formId ID của Lead Gen Form
   * @param accessToken Access token
   * @param testData Dữ liệu test
   * @returns Kết quả test
   */
  async testLeadGenForm(
    formId: string,
    accessToken: string,
    testData: any,
  ): Promise<LeadGenFormTest> {
    try {
      this.logger.log(`Test Lead Gen Form ${formId}`);

      const requestData = {
        test_data: testData,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post<LeadGenFormTest>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${formId}/test_leads`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(`Đã test Lead Gen Form ${formId}`);

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi test Lead Gen Form ${formId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi test Lead Gen Form',
        { formId, testData },
      );
    }
  }

  /**
   * Lấy preview Lead Gen Form
   * @param formId ID của Lead Gen Form
   * @param accessToken Access token
   * @returns Preview URL
   */
  async getLeadGenFormPreview(
    formId: string,
    accessToken: string,
  ): Promise<LeadGenFormPreview> {
    try {
      this.logger.log(`Lấy preview Lead Gen Form ${formId}`);

      const params = {
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.get<LeadGenFormPreview>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${formId}/preview`,
          { params },
        ),
      );

      this.logger.log(`Đã lấy preview Lead Gen Form ${formId}`);

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy preview Lead Gen Form ${formId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy preview Lead Gen Form',
        { formId },
      );
    }
  }

  /**
   * Lấy danh sách Lead Gen Form Templates
   * @param accessToken Access token
   * @param category Danh mục template
   * @param limit Số lượng kết quả
   * @returns Danh sách templates
   */
  async getLeadGenFormTemplates(
    accessToken: string,
    category?: string,
    limit?: number,
  ): Promise<GetLeadGenFormTemplatesResponse> {
    try {
      this.logger.log(`Lấy danh sách Lead Gen Form Templates`);

      const params: any = {
        access_token: accessToken,
        fields: 'id,name,category,description,questions,context_card,thank_you_page,is_default,created_time,updated_time',
      };

      if (category) {
        params.category = category;
      }

      if (limit) {
        params.limit = limit;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetLeadGenFormTemplatesResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/leadgen_form_templates`,
          { params },
        ),
      );

      this.logger.log(
        `Đã lấy ${response.data.data.length} Lead Gen Form Templates`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách Lead Gen Form Templates: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách Lead Gen Form Templates',
        { category },
      );
    }
  }
}
