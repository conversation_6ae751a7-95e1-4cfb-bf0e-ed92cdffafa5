# Test API Lấy Đ<PERSON>ch Hàng ZNS

## Chuẩn bị test

### 1. Thông tin cần có
- JWT Token hợp lệ
- Integration ID của Zalo OA
- Template ID của template đánh giá dịch vụ
- <PERSON>hoảng thời gian có dữ liệu đánh giá

### 2. Endpoint
```
GET /v1/marketing/zalo/zns/{integrationId}/ratings
```

## Test Cases

### Test Case 1: L<PERSON>y đ<PERSON>h giá thành công

**Request:**
```bash
curl -X GET \
  "http://localhost:3000/v1/marketing/zalo/zns/123e4567-e89b-12d3-a456-426614174000/ratings?template_id=203972&from_time=1616673095659&to_time=1616673271320&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "code": 200,
  "message": "L<PERSON>y thông tin đánh giá khách hàng thành công",
  "result": {
    "items": [
      {
        "note": "Tôi rất hài lòng.",
        "rate": 5,
        "submitDate": "1616673095659",
        "msgId": "7e4c33cfc20b05575c18",
        "feedbacks": [
          "Nhân viên vui vẻ",
          "Quy trình đơn giản, hiệu quả"
        ],
        "trackingId": "1956"
      }
    ],
    "meta": {
      "totalItems": 3,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### Test Case 2: Thiếu tham số bắt buộc

**Request:**
```bash
curl -X GET \
  "http://localhost:3000/v1/marketing/zalo/zns/123e4567-e89b-12d3-a456-426614174000/ratings?template_id=203972" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Expected Response:**
```json
{
  "code": 400,
  "message": "Validation failed",
  "errors": [
    "from_time must be a number",
    "to_time must be a number"
  ]
}
```

### Test Case 3: Integration không tồn tại

**Request:**
```bash
curl -X GET \
  "http://localhost:3000/v1/marketing/zalo/zns/invalid-integration-id/ratings?template_id=203972&from_time=1616673095659&to_time=1616673271320&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Expected Response:**
```json
{
  "code": 404,
  "message": "Không tìm thấy Integration"
}
```

### Test Case 4: Không có quyền truy cập

**Request:**
```bash
curl -X GET \
  "http://localhost:3000/v1/marketing/zalo/zns/123e4567-e89b-12d3-a456-426614174000/ratings?template_id=203972&from_time=1616673095659&to_time=1616673271320&page=1&limit=10" \
  -H "Authorization: Bearer INVALID_TOKEN"
```

**Expected Response:**
```json
{
  "code": 401,
  "message": "Unauthorized"
}
```

### Test Case 5: Template không có quyền

**Request:**
```bash
curl -X GET \
  "http://localhost:3000/v1/marketing/zalo/zns/123e4567-e89b-12d3-a456-426614174000/ratings?template_id=INVALID_TEMPLATE&from_time=1616673095659&to_time=1616673271320&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Expected Response:**
```json
{
  "code": 403,
  "message": "Không có quyền truy cập template này"
}
```

## Kiểm tra Swagger Documentation

1. Truy cập: `http://localhost:3000/api-docs`
2. Tìm section: **Zalo ZNS**
3. Tìm endpoint: `GET /v1/marketing/zalo/zns/{integrationId}/ratings`
4. Kiểm tra:
   - ✅ Có đầy đủ tham số
   - ✅ Có example request/response
   - ✅ Có mô tả chi tiết
   - ✅ Có error codes

## Checklist hoàn thành

- [ ] API endpoint hoạt động
- [ ] Validation tham số đầu vào
- [ ] Authentication/Authorization
- [ ] Error handling
- [ ] Swagger documentation
- [ ] Response format đúng chuẩn
- [ ] Logging errors
- [ ] Performance acceptable

## Lưu ý khi test

1. **Template ID**: Phải là template đánh giá dịch vụ thực tế
2. **Thời gian**: Phải có dữ liệu đánh giá trong khoảng thời gian test
3. **Access Token**: Phải có quyền truy cập OA và template
4. **Rate Limiting**: Zalo có thể có giới hạn số lần gọi API

## Debug

Nếu gặp lỗi, kiểm tra:

1. **Logs**: Xem logs trong console/file
2. **Database**: Kiểm tra Integration có tồn tại
3. **Zalo Token**: Kiểm tra access token còn hạn
4. **Network**: Kiểm tra kết nối đến Zalo API
5. **Permissions**: Kiểm tra quyền OA và template
