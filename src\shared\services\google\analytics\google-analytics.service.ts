import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OAuth2Client } from 'google-auth-library';
import { analyticsdata_v1beta, google } from 'googleapis';
import {
  AnalyticsDimension,
  AnalyticsMetric,
  BatchRunReportsRequest,
  BatchRunReportsResponse,
  DateRange,
  FilterExpression,
  GoogleAnalyticsConfig,
  GoogleAnalyticsCredentials,
  Metadata,
  OrderBy,
  RunRealtimeReportRequest,
  RunReportRequest,
  RunReportResponse
} from '../interfaces/google-analytics.interface';

/**
 * Service để tương tác với Google Analytics Data API
 */
@Injectable()
export class GoogleAnalyticsService {
  private readonly logger = new Logger(GoogleAnalyticsService.name);
  private oauth2Client: OAuth2Client;
  private analyticsConfig: GoogleAnalyticsConfig;

  constructor(private readonly configService: ConfigService) {
    this.initializeConfig();
    this.initializeOAuth2Client();
  }

  /**
   * Khởi tạo cấu hình từ environment variables
   */
  private initializeConfig(): void {
    this.analyticsConfig = {
      clientId: this.configService.get<string>('GOOGLE_CLIENT_ID') || '',
      clientSecret: this.configService.get<string>('GOOGLE_CLIENT_SECRET') || '',
      redirectUri: this.configService.get<string>('GOOGLE_REDIRECT_URI') || '',
      defaultPropertyId: this.configService.get<string>('GOOGLE_ANALYTICS_PROPERTY_ID'),
    };

    if (!this.analyticsConfig.clientId || !this.analyticsConfig.clientSecret) {
      this.logger.warn('Google Analytics configuration is incomplete');
    }
  }

  /**
   * Khởi tạo OAuth2 client
   */
  private initializeOAuth2Client(): void {
    this.oauth2Client = new google.auth.OAuth2(
      this.analyticsConfig.clientId,
      this.analyticsConfig.clientSecret,
      this.analyticsConfig.redirectUri,
    );
  }

  /**
   * Thiết lập credentials cho OAuth2 client
   * @param credentials Thông tin xác thực
   */
  private setCredentials(credentials: GoogleAnalyticsCredentials): void {
    this.oauth2Client.setCredentials({
      access_token: credentials.accessToken,
      refresh_token: credentials.refreshToken,
      expiry_date: credentials.expiresAt,
    });
  }

  /**
   * Lấy instance của Analytics Data API
   * @param accessToken Access token
   * @returns Analytics Data API instance
   */
  private getAnalyticsInstance(accessToken: string): analyticsdata_v1beta.Analyticsdata {
    this.setCredentials({ accessToken });
    return google.analyticsdata({ version: 'v1beta', auth: this.oauth2Client });
  }

  /**
   * Chạy report
   * @param accessToken Access token
   * @param request Thông tin request
   * @returns Kết quả report
   */
  async runReport(
    accessToken: string,
    request: RunReportRequest,
  ): Promise<RunReportResponse> {
    try {
      const analytics = this.getAnalyticsInstance(accessToken);

      const propertyId = request.property || this.analyticsConfig.defaultPropertyId;
      if (!propertyId) {
        throw new Error('Property ID is required');
      }

      const response = await analytics.properties.runReport({
        property: `properties/${propertyId}`,
        requestBody: {
          dimensions: request.dimensions,
          metrics: request.metrics,
          dateRanges: request.dateRanges,
          dimensionFilter: request.dimensionFilter,
          metricFilter: request.metricFilter,
          offset: request.offset,
          limit: request.limit,
          metricAggregations: request.metricAggregations,
          orderBys: request.orderBys,
          currencyCode: request.currencyCode,
          cohortSpec: request.cohortSpec,
          keepEmptyRows: request.keepEmptyRows,
          returnPropertyQuota: request.returnPropertyQuota,
        },
      });

      this.logger.log(`Report executed for property ${propertyId}`);

      return response.data as RunReportResponse;
    } catch (error) {
      this.logger.error(`Error running report: ${error.message}`, error.stack);
      throw new Error(`Không thể chạy report: ${error.message}`);
    }
  }

  /**
   * Chạy realtime report
   * @param accessToken Access token
   * @param request Thông tin request
   * @returns Kết quả realtime report
   */
  async runRealtimeReport(
    accessToken: string,
    request: RunRealtimeReportRequest,
  ): Promise<RunReportResponse> {
    try {
      const analytics = this.getAnalyticsInstance(accessToken);

      const propertyId = request.property || this.analyticsConfig.defaultPropertyId;
      if (!propertyId) {
        throw new Error('Property ID is required');
      }

      const response = await analytics.properties.runRealtimeReport({
        property: `properties/${propertyId}`,
        requestBody: {
          dimensions: request.dimensions,
          metrics: request.metrics,
          dimensionFilter: request.dimensionFilter,
          metricFilter: request.metricFilter,
          limit: request.limit,
          metricAggregations: request.metricAggregations,
          orderBys: request.orderBys,
          returnPropertyQuota: request.returnPropertyQuota,
          minuteRanges: request.minuteRanges,
        },
      });

      this.logger.log(`Realtime report executed for property ${propertyId}`);

      return response.data as RunReportResponse;
    } catch (error) {
      this.logger.error(`Error running realtime report: ${error.message}`, error.stack);
      throw new Error(`Không thể chạy realtime report: ${error.message}`);
    }
  }

  /**
   * Chạy batch reports
   * @param accessToken Access token
   * @param request Thông tin batch request
   * @returns Kết quả batch reports
   */
  async batchRunReports(
    accessToken: string,
    request: BatchRunReportsRequest,
  ): Promise<BatchRunReportsResponse> {
    try {
      const analytics = this.getAnalyticsInstance(accessToken);

      const propertyId = request.property || this.analyticsConfig.defaultPropertyId;
      if (!propertyId) {
        throw new Error('Property ID is required');
      }

      const response = await analytics.properties.batchRunReports({
        property: `properties/${propertyId}`,
        requestBody: {
          requests: request.requests,
        },
      });

      this.logger.log(`Batch reports executed for property ${propertyId} (${request.requests.length} reports)`);

      return response.data as BatchRunReportsResponse;
    } catch (error) {
      this.logger.error(`Error running batch reports: ${error.message}`, error.stack);
      throw new Error(`Không thể chạy batch reports: ${error.message}`);
    }
  }

  /**
   * Lấy metadata
   * @param accessToken Access token
   * @param propertyId Property ID (optional, sử dụng default nếu không có)
   * @returns Metadata
   */
  async getMetadata(
    accessToken: string,
    propertyId?: string,
  ): Promise<Metadata> {
    try {
      const analytics = this.getAnalyticsInstance(accessToken);

      const targetPropertyId = propertyId || this.analyticsConfig.defaultPropertyId;
      if (!targetPropertyId) {
        throw new Error('Property ID is required');
      }

      const response = await analytics.properties.getMetadata({
        name: `properties/${targetPropertyId}/metadata`,
      });

      this.logger.log(`Metadata retrieved for property ${targetPropertyId}`);

      return response.data as Metadata;
    } catch (error) {
      this.logger.error(`Error getting metadata: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy metadata: ${error.message}`);
    }
  }

  /**
   * Lấy basic report với các metrics phổ biến
   * @param accessToken Access token
   * @param propertyId Property ID
   * @param startDate Ngày bắt đầu (YYYY-MM-DD)
   * @param endDate Ngày kết thúc (YYYY-MM-DD)
   * @returns Basic report data
   */
  async getBasicReport(
    accessToken: string,
    propertyId: string,
    startDate: string,
    endDate: string,
  ): Promise<RunReportResponse> {
    const request: RunReportRequest = {
      property: propertyId,
      dimensions: [
        { name: 'date' },
        { name: 'country' },
        { name: 'deviceCategory' },
      ],
      metrics: [
        { name: 'sessions' },
        { name: 'users' },
        { name: 'pageviews' },
        { name: 'bounceRate' },
        { name: 'sessionDuration' },
      ],
      dateRanges: [
        {
          startDate,
          endDate,
        },
      ],
      orderBys: [
        {
          dimension: {
            dimensionName: 'date',
          },
          desc: false,
        },
      ],
    };

    return this.runReport(accessToken, request);
  }

  /**
   * Lấy traffic sources report
   * @param accessToken Access token
   * @param propertyId Property ID
   * @param startDate Ngày bắt đầu
   * @param endDate Ngày kết thúc
   * @returns Traffic sources report
   */
  async getTrafficSourcesReport(
    accessToken: string,
    propertyId: string,
    startDate: string,
    endDate: string,
  ): Promise<RunReportResponse> {
    const request: RunReportRequest = {
      property: propertyId,
      dimensions: [
        { name: 'sessionSource' },
        { name: 'sessionMedium' },
        { name: 'sessionCampaignName' },
      ],
      metrics: [
        { name: 'sessions' },
        { name: 'users' },
        { name: 'newUsers' },
        { name: 'conversions' },
      ],
      dateRanges: [
        {
          startDate,
          endDate,
        },
      ],
      orderBys: [
        {
          metric: {
            metricName: 'sessions',
          },
          desc: true,
        },
      ],
    };

    return this.runReport(accessToken, request);
  }

  /**
   * Lấy page views report
   * @param accessToken Access token
   * @param propertyId Property ID
   * @param startDate Ngày bắt đầu
   * @param endDate Ngày kết thúc
   * @returns Page views report
   */
  async getPageViewsReport(
    accessToken: string,
    propertyId: string,
    startDate: string,
    endDate: string,
  ): Promise<RunReportResponse> {
    const request: RunReportRequest = {
      property: propertyId,
      dimensions: [
        { name: 'pagePath' },
        { name: 'pageTitle' },
      ],
      metrics: [
        { name: 'screenPageViews' },
        { name: 'uniquePageViews' },
        { name: 'averageTimeOnPage' },
        { name: 'bounceRate' },
      ],
      dateRanges: [
        {
          startDate,
          endDate,
        },
      ],
      orderBys: [
        {
          metric: {
            metricName: 'screenPageViews',
          },
          desc: true,
        },
      ],
      limit: '50',
    };

    return this.runReport(accessToken, request);
  }

  /**
   * Lấy realtime users
   * @param accessToken Access token
   * @param propertyId Property ID
   * @returns Realtime users data
   */
  async getRealtimeUsers(
    accessToken: string,
    propertyId: string,
  ): Promise<RunReportResponse> {
    const request: RunRealtimeReportRequest = {
      property: propertyId,
      dimensions: [
        { name: 'country' },
        { name: 'city' },
      ],
      metrics: [
        { name: 'activeUsers' },
      ],
      orderBys: [
        {
          metric: {
            metricName: 'activeUsers',
          },
          desc: true,
        },
      ],
      limit: '10',
    };

    return this.runRealtimeReport(accessToken, request);
  }

  /**
   * Lấy conversion report
   * @param accessToken Access token
   * @param propertyId Property ID
   * @param startDate Ngày bắt đầu
   * @param endDate Ngày kết thúc
   * @returns Conversion report
   */
  async getConversionReport(
    accessToken: string,
    propertyId: string,
    startDate: string,
    endDate: string,
  ): Promise<RunReportResponse> {
    const request: RunReportRequest = {
      property: propertyId,
      dimensions: [
        { name: 'eventName' },
        { name: 'sessionSource' },
        { name: 'sessionMedium' },
      ],
      metrics: [
        { name: 'conversions' },
        { name: 'totalRevenue' },
        { name: 'purchaseRevenue' },
      ],
      dateRanges: [
        {
          startDate,
          endDate,
        },
      ],
      dimensionFilter: {
        filter: {
          fieldName: 'eventName',
          stringFilter: {
            matchType: 'EXACT',
            value: 'purchase',
          },
        },
      },
      orderBys: [
        {
          metric: {
            metricName: 'conversions',
          },
          desc: true,
        },
      ],
    };

    return this.runReport(accessToken, request);
  }

  /**
   * Tạo custom report với dimensions và metrics tùy chỉnh
   * @param accessToken Access token
   * @param propertyId Property ID
   * @param dimensions Danh sách dimensions
   * @param metrics Danh sách metrics
   * @param dateRanges Danh sách date ranges
   * @param filters Filters (optional)
   * @param orderBys Order by (optional)
   * @param limit Limit (optional)
   * @returns Custom report data
   */
  async getCustomReport(
    accessToken: string,
    propertyId: string,
    dimensions: AnalyticsDimension[],
    metrics: AnalyticsMetric[],
    dateRanges: DateRange[],
    filters?: FilterExpression,
    orderBys?: OrderBy[],
    limit?: string,
  ): Promise<RunReportResponse> {
    const request: RunReportRequest = {
      property: propertyId,
      dimensions,
      metrics,
      dateRanges,
      dimensionFilter: filters,
      orderBys,
      limit,
    };

    return this.runReport(accessToken, request);
  }

  /**
   * Kiểm tra kết nối với Google Analytics API
   * @param accessToken Access token
   * @returns True nếu kết nối thành công
   */
  async testConnection(accessToken: string): Promise<boolean> {
    try {
      const analytics = this.getAnalyticsInstance(accessToken);

      const propertyId = this.analyticsConfig.defaultPropertyId;
      if (!propertyId) {
        this.logger.warn('No default property ID configured for connection test');
        return false;
      }

      // Thử lấy metadata để test connection
      await analytics.properties.getMetadata({
        name: `properties/${propertyId}/metadata`,
      });

      this.logger.log('Google Analytics connection test successful');
      return true;
    } catch (error) {
      this.logger.error(`Google Analytics connection test failed: ${error.message}`);
      return false;
    }
  }
}
