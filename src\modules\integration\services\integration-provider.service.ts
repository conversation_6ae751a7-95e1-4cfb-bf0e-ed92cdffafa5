import { Injectable, Logger } from '@nestjs/common';
import { IntegrationProviderRepository } from '../repositories/integration-provider.repository';
import { IntegrationProvider } from '../entities/integration-provider.entity';
import { ProviderEnum } from '../constants/provider.enum';
import { 
  CreateIntegrationProviderDto, 
  UpdateIntegrationProviderDto, 
  IntegrationProviderResponseDto,
  IntegrationProviderQueryDto 
} from '../dto';
import { PaginatedResult } from '@common/response';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý logic nghiệp vụ cho Integration Provider
 */
@Injectable()
export class IntegrationProviderService {
  private readonly logger = new Logger(IntegrationProviderService.name);

  constructor(
    private readonly integrationProviderRepository: IntegrationProviderRepository,
  ) {}

  /**
   * Lấy danh sách integration providers với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách integration providers với phân trang
   */
  async getProviders(queryDto: IntegrationProviderQueryDto): Promise<PaginatedResult<IntegrationProviderResponseDto>> {
    try {
      this.logger.log('Getting integration providers list');

      const result = await this.integrationProviderRepository.findAll({
        page: queryDto.page,
        limit: queryDto.limit,
      });

      // Transform entities to DTOs
      const items = result.items.map(provider => this.toResponseDto(provider));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Error getting integration providers: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy integration provider theo type
   * @param type Loại provider
   * @returns IntegrationProviderResponseDto hoặc null
   */
  async getProviderByType(type: ProviderEnum): Promise<IntegrationProviderResponseDto | null> {
    try {
      this.logger.log(`Getting integration provider by type: ${type}`);

      const provider = await this.integrationProviderRepository.findByType(type);
      if (!provider) {
        return null;
      }

      return this.toResponseDto(provider);
    } catch (error) {
      this.logger.error(`Error getting integration provider by type: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo integration provider mới
   * @param createDto Dữ liệu tạo provider
   * @returns IntegrationProviderResponseDto
   */
  @Transactional()
  async createProvider(createDto: CreateIntegrationProviderDto): Promise<IntegrationProviderResponseDto> {
    try {
      this.logger.log(`Creating integration provider with type: ${createDto.type}`);

      const provider = await this.integrationProviderRepository.upsertProvider({
        type: createDto.type,
        createdBy: createDto.createdBy,
      });

      this.logger.log(`Successfully created integration provider ${provider.id}`);
      return this.toResponseDto(provider);
    } catch (error) {
      this.logger.error(`Error creating integration provider: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật integration provider
   * @param type Loại provider
   * @param updateDto Dữ liệu cập nhật
   * @returns IntegrationProviderResponseDto hoặc null
   */
  @Transactional()
  async updateProvider(
    type: ProviderEnum,
    updateDto: UpdateIntegrationProviderDto
  ): Promise<IntegrationProviderResponseDto | null> {
    try {
      this.logger.log(`Updating integration provider with type: ${type}`);

      // Tìm provider hiện tại
      const existingProvider = await this.integrationProviderRepository.findByType(type);
      if (!existingProvider) {
        this.logger.warn(`Integration provider with type ${type} not found`);
        return null;
      }

      // Cập nhật provider (không xử lý mcpSchema theo yêu cầu)
      const updatedProvider = await this.integrationProviderRepository.updateProvider(
        existingProvider.id,
        {
          updatedBy: updateDto.updatedBy,
        }
      );

      if (!updatedProvider) {
        this.logger.warn(`Failed to update integration provider with type ${type}`);
        return null;
      }

      this.logger.log(`Successfully updated integration provider ${updatedProvider.id}`);
      return this.toResponseDto(updatedProvider);
    } catch (error) {
      this.logger.error(`Error updating integration provider: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa integration provider
   * @param type Loại provider
   * @returns Số lượng bản ghi đã xóa
   */
  @Transactional()
  async deleteProvider(type: ProviderEnum): Promise<number> {
    try {
      this.logger.log(`Deleting integration provider with type: ${type}`);

      const deletedCount = await this.integrationProviderRepository.deleteByType(type);

      this.logger.log(`Successfully deleted ${deletedCount} integration providers`);
      return deletedCount;
    } catch (error) {
      this.logger.error(`Error deleting integration provider: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách providers theo types
   * @param types Danh sách ProviderEnum
   * @returns Danh sách IntegrationProviderResponseDto
   */
  async getProvidersByTypes(types: ProviderEnum[]): Promise<IntegrationProviderResponseDto[]> {
    try {
      this.logger.log(`Getting integration providers by types: ${types.join(', ')}`);

      const providers = await this.integrationProviderRepository.findByTypes(types);
      
      return providers.map(provider => this.toResponseDto(provider));
    } catch (error) {
      this.logger.error(`Error getting integration providers by types: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Khởi tạo providers mặc định
   * @param createdBy ID người tạo
   * @returns Danh sách IntegrationProviderResponseDto đã được tạo
   */
  @Transactional()
  async initializeDefaultProviders(createdBy?: number): Promise<IntegrationProviderResponseDto[]> {
    try {
      this.logger.log('Initializing default integration providers');

      const defaultProviders = [
        ProviderEnum.OPENAI,
        ProviderEnum.FACEBOOK_PAGE,
        ProviderEnum.WEBSITE,
        ProviderEnum.ZALO_OA,
        ProviderEnum.GHTK,
        ProviderEnum.GHN,
        ProviderEnum.MB_BANK,
        ProviderEnum.EMAIL_SMTP,
        ProviderEnum.EMAIL_TWILIO_SENDGRID,
        ProviderEnum.EMAIL_GMAIL,
        ProviderEnum.EMAIL_OUTLOOK,
        ProviderEnum.GOOGLE_CALENDAR,
        ProviderEnum.GOOGLE_SHEETS,
        ProviderEnum.GOOGLE_DOCS,
      ];

      const createdProviders: IntegrationProvider[] = [];

      for (const type of defaultProviders) {
        const provider = await this.integrationProviderRepository.upsertProvider({
          type,
          createdBy,
        });
        createdProviders.push(provider);
      }

      this.logger.log(`Successfully initialized ${createdProviders.length} default providers`);
      return createdProviders.map(provider => this.toResponseDto(provider));
    } catch (error) {
      this.logger.error(`Error initializing default providers: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Chuyển đổi entity sang response DTO
   * @param provider IntegrationProvider entity
   * @returns IntegrationProviderResponseDto
   */
  private toResponseDto(provider: IntegrationProvider): IntegrationProviderResponseDto {
    return {
      id: provider.id,
      type: provider.type as ProviderEnum,
      mcpSchema: provider.mcpSchema,
      createdAt: provider.createdAt,
      updatedAt: provider.updatedAt,
      createdBy: provider.createdBy,
      updatedBy: provider.updatedBy,
    };
  }
}
