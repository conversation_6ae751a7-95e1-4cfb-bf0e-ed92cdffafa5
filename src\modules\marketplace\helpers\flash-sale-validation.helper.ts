import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { FLASH_SALE_ERROR_CODES } from '../exceptions/flash-sale.exception';
import { MARKETPLACE_ERROR_CODES } from '../exceptions/marketplace.exception';
import { FlashSale } from '../entities/flash-sale.entity';
import { Product } from '../entities/product.entity';
import { FlashSaleStatus } from '../enums/flash-sale-status.enum';
import { ProductStatus } from '../enums/product-status.enum';
import { MaxConfiguration, DEFAULT_MAX_CONFIGURATION } from '../interfaces/max-configuration.interface';
import { FlashSaleRepository } from '../repositories/flash-sale.repository';

/**
 * Helper cho validation logic của Flash Sale
 */
@Injectable()
export class FlashSaleValidationHelper {
  private readonly logger = new Logger(FlashSaleValidationHelper.name);

  constructor(private readonly flashSaleRepository: FlashSaleRepository) {}

  /**
   * Validate discount percentage
   */
  validateDiscountPercentage(discountPercentage: number): void {
    if (!discountPercentage || discountPercentage < 1 || discountPercentage > 99) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.INVALID_DISCOUNT_PERCENTAGE,
        `Phần trăm giảm giá "${discountPercentage}%" không hợp lệ. Phải từ 1% đến 99%`
      );
    }
  }

  /**
   * Validate time sequence and display duration
   */
  validateTimeSequence(displayTime: number, startTime: number, endTime: number): void {
    const now = Date.now();

    // Kiểm tra thời gian bắt đầu phải lớn hơn hiện tại
    if (startTime <= now) {
      const startDate = new Date(startTime).toLocaleString('vi-VN');
      const currentDate = new Date(now).toLocaleString('vi-VN');
      throw new AppException(
        FLASH_SALE_ERROR_CODES.INVALID_TIME_RANGE,
        `Thời gian bắt đầu "${startDate}" phải lớn hơn thời gian hiện tại "${currentDate}"`
      );
    }

    // ✅ SỬA: displayTime từ 1-60 giây (dưới 1 phút) để tạo cảm giác khan hiếm
    if (displayTime <= 0 || displayTime > 60) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.INVALID_TIME_SEQUENCE,
        `Thời gian hiển thị "${displayTime} giây" không hợp lệ. Phải từ 1-60 giây để tạo hiệu ứng khan hiếm`
      );
    }

    // Kiểm tra startTime < endTime
    if (startTime >= endTime) {
      const startDate = new Date(startTime).toLocaleString('vi-VN');
      const endDate = new Date(endTime).toLocaleString('vi-VN');
      throw new AppException(
        FLASH_SALE_ERROR_CODES.INVALID_TIME_SEQUENCE,
        `Thời gian bắt đầu "${startDate}" phải nhỏ hơn thời gian kết thúc "${endDate}"`
      );
    }

    // Kiểm tra flash sale phải kéo dài ít nhất 1 giờ
    const minDuration = 60 * 60 * 1000; // 1 hour in milliseconds
    const actualDuration = endTime - startTime;
    if (actualDuration < minDuration) {
      const durationHours = Math.round(actualDuration / (60 * 60 * 1000) * 100) / 100;
      throw new AppException(
        FLASH_SALE_ERROR_CODES.INVALID_TIME_SEQUENCE,
        `Flash sale chỉ kéo dài ${durationHours} giờ. Phải kéo dài ít nhất 1 giờ`
      );
    }
  }

  /**
   * Validate product eligibility for flash sale
   */
  validateProductEligibility(product: Product): void {
    if (!product) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
        'Sản phẩm không tồn tại'
      );
    }

    if (product.status !== ProductStatus.APPROVED) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.PRODUCT_NOT_ELIGIBLE,
        `Sản phẩm "${product.name}" có trạng thái "${product.status}". Chỉ có thể tạo flash sale cho sản phẩm đã được phê duyệt (APPROVED)`
      );
    }
  }

  /**
   * Validate ownership (user hoặc admin)
   */
  validateOwnership(flashSale: FlashSale, userId?: number, employeeId?: number): void {
    // Debug logging
    console.log('DEBUG validateOwnership:', {
      flashSaleId: flashSale.id,
      flashSaleUserId: flashSale.userId,
      flashSaleUserIdType: typeof flashSale.userId,
      requestUserId: userId,
      requestUserIdType: typeof userId,
      flashSaleEmployeeId: flashSale.employeeId,
      requestEmployeeId: employeeId,
      comparison: flashSale.userId !== userId
    });

    if (userId !== undefined && flashSale.userId !== null) {
      // Ensure both are numbers for comparison
      const flashSaleUserIdNum = Number(flashSale.userId);
      const requestUserIdNum = Number(userId);

      if (flashSaleUserIdNum !== requestUserIdNum) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
          `Flash sale ID ${flashSale.id} thuộc về user ID ${flashSale.userId}. Bạn (user ID ${userId}) không có quyền truy cập`
        );
      }
    }

    if (employeeId !== undefined && flashSale.employeeId !== null) {
      // Ensure both are numbers for comparison
      const flashSaleEmployeeIdNum = Number(flashSale.employeeId);
      const requestEmployeeIdNum = Number(employeeId);

      if (flashSaleEmployeeIdNum !== requestEmployeeIdNum) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
          `Flash sale ID ${flashSale.id} thuộc về employee ID ${flashSale.employeeId}. Bạn (employee ID ${employeeId}) không có quyền truy cập`
        );
      }
    }
  }

  /**
   * Validate status transition
   */
  validateStatusTransition(currentStatus: FlashSaleStatus, newStatus: FlashSaleStatus): void {
    const validTransitions: Record<FlashSaleStatus, FlashSaleStatus[]> = {
      [FlashSaleStatus.DRAFT]: [FlashSaleStatus.SCHEDULED, FlashSaleStatus.CANCELLED],
      [FlashSaleStatus.SCHEDULED]: [FlashSaleStatus.DRAFT, FlashSaleStatus.ACTIVE, FlashSaleStatus.CANCELLED], // ✅ Allow SCHEDULED → DRAFT
      [FlashSaleStatus.ACTIVE]: [FlashSaleStatus.EXPIRED, FlashSaleStatus.CANCELLED],
      [FlashSaleStatus.EXPIRED]: [], // Không thể chuyển từ EXPIRED
      [FlashSaleStatus.CANCELLED]: [] // Không thể chuyển từ CANCELLED
    };

    const allowedTransitions = validTransitions[currentStatus] || [];

    if (!allowedTransitions.includes(newStatus)) {
      const allowedList = allowedTransitions.length > 0 ? allowedTransitions.join(', ') : 'không có';
      throw new AppException(
        FLASH_SALE_ERROR_CODES.INVALID_STATUS_TRANSITION,
        `Không thể chuyển từ trạng thái "${currentStatus}" sang "${newStatus}". Các trạng thái hợp lệ: ${allowedList}`
      );
    }
  }

  /**
   * Validate max configuration
   */
  validateMaxConfiguration(maxConfiguration: MaxConfiguration): MaxConfiguration {
    // Sử dụng default nếu không có config
    const config = { ...DEFAULT_MAX_CONFIGURATION, ...maxConfiguration };

    // Validate maxPerUser
    if (config.maxPerUser !== null && config.maxPerUser <= 0) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.MAX_CONFIGURATION_INVALID,
        `maxPerUser "${config.maxPerUser}" không hợp lệ. Phải là số nguyên dương hoặc null (không giới hạn)`
      );
    }

    // Validate totalInventory
    if (config.totalInventory !== null && config.totalInventory < 0) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.MAX_CONFIGURATION_INVALID,
        `totalInventory "${config.totalInventory}" không hợp lệ. Phải là số nguyên không âm hoặc null (không giới hạn)`
      );
    }

    // Validate purchaseLimitPerOrder
    if (config.purchaseLimitPerOrder !== null && config.purchaseLimitPerOrder <= 0) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.MAX_CONFIGURATION_INVALID,
        `purchaseLimitPerOrder "${config.purchaseLimitPerOrder}" không hợp lệ. Phải là số nguyên dương hoặc null (không giới hạn)`
      );
    }

    // Validate relationship between fields
    if (
      config.maxPerUser !== null &&
      config.totalInventory !== null &&
      config.totalInventory < config.maxPerUser
    ) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.MAX_CONFIGURATION_INVALID,
        `totalInventory (${config.totalInventory}) phải lớn hơn hoặc bằng maxPerUser (${config.maxPerUser})`
      );
    }

    if (
      config.purchaseLimitPerOrder !== null &&
      config.maxPerUser !== null &&
      config.purchaseLimitPerOrder > config.maxPerUser
    ) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.MAX_CONFIGURATION_INVALID,
        `purchaseLimitPerOrder (${config.purchaseLimitPerOrder}) phải nhỏ hơn hoặc bằng maxPerUser (${config.maxPerUser})`
      );
    }

    // Validate timeWindowLimit
    if (config.timeWindowLimit !== null) {
      const { qty, windowMinutes } = config.timeWindowLimit;
      
      if (!qty || qty <= 0) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.MAX_CONFIGURATION_INVALID,
          'timeWindowLimit.qty phải là số nguyên dương'
        );
      }

      if (!windowMinutes || windowMinutes <= 0) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.MAX_CONFIGURATION_INVALID,
          'timeWindowLimit.windowMinutes phải là số nguyên dương'
        );
      }
    }

    return config;
  }

  /**
   * Check overlapping flash sale
   */
  async checkOverlappingFlashSale(
    productId: number,
    startTime: number,
    endTime: number,
    excludeId?: number
  ): Promise<void> {
    const hasOverlap = await this.flashSaleRepository.checkOverlappingFlashSale(
      productId,
      startTime,
      endTime,
      excludeId
    );

    if (hasOverlap) {
      const startDate = new Date(startTime).toLocaleString('vi-VN');
      const endDate = new Date(endTime).toLocaleString('vi-VN');
      throw new AppException(
        FLASH_SALE_ERROR_CODES.OVERLAPPING_FLASH_SALE,
        `Sản phẩm ID ${productId} đã có flash sale khác trong khoảng thời gian từ ${startDate} đến ${endDate}. Vui lòng chọn thời gian khác`
      );
    }
  }

  /**
   * Validate flash sale is active
   */
  validateFlashSaleActive(flashSale: FlashSale): void {
    const now = Date.now();

    if (flashSale.status !== FlashSaleStatus.ACTIVE) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_ACTIVE,
        'Flash sale không đang hoạt động'
      );
    }

    if (flashSale.startTime > now) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_STARTED,
        'Flash sale chưa bắt đầu'
      );
    }

    if (flashSale.endTime <= now) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.FLASH_SALE_EXPIRED,
        'Flash sale đã hết hạn'
      );
    }

    if (!flashSale.isActive) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_ACTIVE,
        'Flash sale đã bị tắt'
      );
    }
  }

  /**
   * Validate inventory limits
   */
  validateInventoryLimits(flashSale: FlashSale, requestedQuantity: number, soldCount: number = 0): void {
    const { maxConfiguration } = flashSale;

    // Kiểm tra total inventory
    if (maxConfiguration.totalInventory !== null) {
      const remainingInventory = maxConfiguration.totalInventory - soldCount;
      if (requestedQuantity > remainingInventory) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.INVENTORY_EXCEEDED,
          `Chỉ còn ${remainingInventory} sản phẩm trong flash sale`
        );
      }
    }

    // Kiểm tra purchase limit per order
    if (maxConfiguration.purchaseLimitPerOrder !== null) {
      if (requestedQuantity > maxConfiguration.purchaseLimitPerOrder) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.ORDER_LIMIT_EXCEEDED,
          `Chỉ được mua tối đa ${maxConfiguration.purchaseLimitPerOrder} sản phẩm mỗi đơn`
        );
      }
    }
  }
}
