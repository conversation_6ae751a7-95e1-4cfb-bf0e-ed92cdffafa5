# Zalo File Upload Validation - Hướng dẫn chi tiết

## Tổng quan

Zalo API có yêu cầu nghiêm ngặt về định dạng file upload. Hệ thống validation đã được cập nhật để tuân thủ đúng yêu cầu của <PERSON>.

## Định dạng file được hỗ trợ

### **Theo Zalo API Documentation:**
Zalo chỉ hỗ trợ các định dạng sau:
- **PDF** (.pdf)
- **DOC** (.doc, .docx) 
- **CSV** (.csv)

### **Lỗi khi upload sai định dạng:**
```json
{
  "code": 9999,
  "message": "Lỗi từ Zalo API: file is invalid. We only support pdf, doc, csv"
}
```

## Validation Implementation

### **1. MIME Type Validation**
```typescript
const allowedMimeTypes = [
  'application/pdf',                    // PDF
  'application/msword',                 // DOC (Word 97-2003)
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX (Word 2007+)
  'text/csv',                          // CSV
  'application/csv',                   // CSV (alternative MIME type)
];
```

### **2. File Extension Validation**
```typescript
const allowedExtensions = ['.pdf', '.doc', '.docx', '.csv'];
const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
```

### **3. Dual Validation**
Hệ thống kiểm tra cả MIME type và file extension để đảm bảo tính chính xác:

```typescript
if (!allowedMimeTypes.includes(file.mimetype) || !allowedExtensions.includes(fileExtension)) {
  throw new AppException(
    ErrorCode.VALIDATION_ERROR,
    'File phải có định dạng PDF, DOC, DOCX hoặc CSV (theo yêu cầu Zalo API)',
  );
}
```

## API Endpoints

### **Upload File (Sync)**
```
POST /v1/marketing/zalo/upload/{integrationId}/upload/file
```

**Request:**
```bash
curl -X POST \
  'http://localhost:3000/v1/marketing/zalo/upload/d331ae2f-b314-4095-963f-6a7c157658a0/upload/file' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -F 'file=@document.pdf' \
  -F 'description=Important document'
```

**Success Response:**
```json
{
  "success": true,
  "data": {
    "token": "hDtMfuo7XKzhraJb6jm53AiDncIQ1aSOQp9FHOROFisDP7m64BxCKe",
    "originalFilename": "document.pdf",
    "fileSize": 2097152,
    "mimeType": "application/pdf",
    "uploadedAt": 1640995200000,
    "description": "Important document"
  },
  "message": "Upload file thành công"
}
```

**Error Response (Invalid Format):**
```json
{
  "success": false,
  "code": 9999,
  "message": "File phải có định dạng PDF, DOC, DOCX hoặc CSV (theo yêu cầu Zalo API)"
}
```

## File Size Limits

| File Type | Max Size | Note |
|-----------|----------|------|
| PDF | 5MB | Recommended for documents |
| DOC/DOCX | 5MB | Microsoft Word documents |
| CSV | 5MB | Data files |

## Validation Examples

### ✅ **Valid Files**
```bash
# PDF file
curl -F 'file=@report.pdf' ...

# Word document
curl -F 'file=@contract.docx' ...

# CSV data
curl -F 'file=@customers.csv' ...
```

### ❌ **Invalid Files**
```bash
# Excel file (not supported)
curl -F 'file=@data.xlsx' ...
# Error: File phải có định dạng PDF, DOC, DOCX hoặc CSV

# PowerPoint (not supported)
curl -F 'file=@presentation.pptx' ...
# Error: File phải có định dạng PDF, DOC, DOCX hoặc CSV

# Text file (not supported)
curl -F 'file=@readme.txt' ...
# Error: File phải có định dạng PDF, DOC, DOCX hoặc CSV
```

## Queue Support (Optional)

Hệ thống cũng hỗ trợ upload file qua queue để xử lý bất đồng bộ:

### **Add File Upload Job**
```typescript
const jobData: ZaloUploadFileJobData = {
  userId: user.id,
  integrationId: integrationId,
  fileInfo: {
    data: file.buffer,
    filename: file.originalname,
    mimetype: file.mimetype,
    size: file.size,
  },
  description: description,
  timestamp: Date.now(),
  trackingId: `file_${user.id}_${Date.now()}`,
};

const jobId = await queueService.addZaloUploadFileJob(jobData);
```

### **Check Job Status**
```
GET /v1/marketing/zalo/upload/job/{jobId}/status
```

## Error Handling

### **1. Client-side Validation**
```javascript
function validateFile(file) {
  const allowedTypes = ['application/pdf', 'application/msword', 
                       'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                       'text/csv', 'application/csv'];
  
  const allowedExtensions = ['.pdf', '.doc', '.docx', '.csv'];
  const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  
  if (!allowedTypes.includes(file.type) || !allowedExtensions.includes(extension)) {
    throw new Error('Chỉ hỗ trợ file PDF, DOC, DOCX, CSV');
  }
  
  if (file.size > 5 * 1024 * 1024) {
    throw new Error('File không được vượt quá 5MB');
  }
}
```

### **2. Server-side Error Codes**

| Error Code | Message | Cause |
|------------|---------|-------|
| 9999 | File phải có định dạng PDF, DOC, DOCX hoặc CSV | Invalid file format |
| 9999 | Kích thước file không được vượt quá 5MB | File too large |
| 9999 | Tên file không hợp lệ | Missing filename |
| 9999 | File là bắt buộc | No file provided |

## Best Practices

### **1. Frontend Implementation**
- Validate file type before upload
- Show clear error messages
- Display supported formats to users
- Implement file size check

### **2. Backend Implementation**
- Double validation (MIME + extension)
- Clear error messages
- Proper logging for debugging
- Queue support for large files

### **3. User Experience**
- Clear format requirements in UI
- File format icons/examples
- Drag & drop with format hints
- Progress indicators for uploads

## Migration Notes

### **Before (Permissive)**
```typescript
// Cho phép nhiều định dạng
const allowedMimeTypes = [
  'application/pdf', 'application/msword', 'application/vnd.ms-excel',
  'application/vnd.ms-powerpoint', 'text/plain', // ... nhiều định dạng khác
];
```

### **After (Zalo Compliant)**
```typescript
// Chỉ cho phép định dạng Zalo hỗ trợ
const allowedMimeTypes = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/csv',
  'application/csv',
];
```

## Testing

### **Unit Tests**
```typescript
describe('ZaloFileValidation', () => {
  it('should accept PDF files', () => {
    const file = { mimetype: 'application/pdf', originalname: 'test.pdf' };
    expect(() => validateFile(file)).not.toThrow();
  });
  
  it('should reject Excel files', () => {
    const file = { mimetype: 'application/vnd.ms-excel', originalname: 'test.xlsx' };
    expect(() => validateFile(file)).toThrow('File phải có định dạng PDF, DOC, DOCX hoặc CSV');
  });
});
```

### **Integration Tests**
```bash
# Test valid file
curl -X POST ... -F 'file=@test.pdf'
# Expected: 201 Created

# Test invalid file  
curl -X POST ... -F 'file=@test.xlsx'
# Expected: 400 Bad Request
```

Validation đã được cập nhật để tuân thủ nghiêm ngặt yêu cầu của Zalo API! 🎯
