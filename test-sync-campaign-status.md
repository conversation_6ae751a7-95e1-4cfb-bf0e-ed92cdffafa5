# Test API Sync Campaign Status

## Mô tả API

API `POST /admin/email-campaigns/sync-status` đư<PERSON>c tạo để cập nhật trạng thái các chiến dịch email dựa trên trạng thái job trong queue và thời gian lên lịch.

## Logic hoạt động

### 1. Kiểm tra Campaign SCHEDULED
- **Điều kiện**: Campaign có trạng thái `SCHEDULED` và `scheduledAt < currentTime`
- **Hành động**: Kiểm tra xem còn job nào đang active trong queue không
- **Kết quả**: Nếu không còn job active → cập nhật thành `FAILED`

### 2. Kiểm tra Campaign SENDING
- **Điều kiện**: Campaign có trạng thái `SENDING`
- **Hành động**: Kiểm tra trạng thái tất cả job trong queue
- **Kết quả**: 
  - Nế<PERSON> tất cả job `completed` → cập nhật thành `COMPLETED`
  - <PERSON><PERSON><PERSON> tất cả job `failed` hoặc không còn job nào → cập nhật thành `FAILED`

### 3. Trạng thái Job trong Queue
- `waiting`, `active`, `delayed`: Job đang hoạt động
- `completed`: Job hoàn thành thành công
- `failed`: Job thất bại
- Job không tồn tại: Coi như `failed`

## Cấu trúc Response

```json
{
  "success": true,
  "message": "Đã cập nhật trạng thái campaign thành công",
  "data": {
    "totalCampaignsChecked": 25,
    "updatedCampaigns": [
      {
        "campaignId": 1,
        "campaignName": "Welcome Email",
        "previousStatus": "SCHEDULED",
        "currentStatus": "FAILED",
        "reason": "Quá thời gian lên lịch và không còn job trong queue"
      },
      {
        "campaignId": 2,
        "campaignName": "Newsletter",
        "previousStatus": "SENDING",
        "currentStatus": "COMPLETED",
        "reason": "Tất cả job đã hoàn thành thành công"
      }
    ],
    "summary": {
      "scheduledToFailed": 1,
      "sendingToCompleted": 1,
      "sendingToFailed": 0
    }
  }
}
```

## Cách test

### 1. Test với Postman/curl
```bash
curl -X POST http://localhost:3000/admin/email-campaigns/sync-status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 2. Tạo test data
Để test API này, bạn cần:
1. Tạo campaign với trạng thái `SCHEDULED` hoặc `SENDING`
2. Đảm bảo có job IDs trong campaign
3. Chạy API sync để xem kết quả

### 3. Kiểm tra kết quả
- Kiểm tra database xem trạng thái campaign có được cập nhật không
- Kiểm tra log để xem quá trình xử lý
- Kiểm tra response để xem thống kê

## Lưu ý

1. **Performance**: API này sẽ kiểm tra tất cả campaign có trạng thái `SCHEDULED` và `SENDING`, có thể chậm nếu có nhiều campaign
2. **Queue Connection**: Cần đảm bảo kết nối tới Redis/Queue hoạt động bình thường
3. **Error Handling**: API có xử lý lỗi và log chi tiết
4. **Permission**: Cần quyền `MARKETING_VIEW` để sử dụng API

## Sử dụng trong Production

API này nên được gọi:
- Khi admin muốn kiểm tra trạng thái campaign
- Sau khi có sự cố với queue system
- Trong dashboard để refresh trạng thái real-time
- Trước khi tạo báo cáo campaign
