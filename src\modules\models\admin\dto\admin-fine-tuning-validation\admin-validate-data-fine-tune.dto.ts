import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID, IsEnum } from 'class-validator';
import { ProviderFineTuneEnum } from '@modules/models/constants/provider.enum';

/**
 * DTO cho admin validate dataset fine-tuning
 */
export class AdminValidateDataFineTuneDto {
  /**
   * ID của dataset cần validate
   */
  @ApiProperty({
    description: 'UUID của dataset cần validate từ bảng admin_data_fine_tune',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsNotEmpty()
  datasetId: string;

  /**
   * ID của model cơ sở từ bảng models
   */
  @ApiProperty({
    description: 'UUID của model cơ sở để fine-tune từ bảng models',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  modelId: string;

  /**
   * Nhà cung cấp AI
   */
  @ApiProperty({
    description: 'Nhà cung cấp AI',
    enum: ProviderFineTuneEnum,
    example: ProviderFineTuneEnum.OPENAI,
  })
  @IsEnum(ProviderFineTuneEnum)
  provider: ProviderFineTuneEnum;
}

/**
 * DTO cho kết quả validation admin
 */
export class AdminValidationResultDto {
  /**
   * Trạng thái validation
   */
  @ApiProperty({
    description: 'Trạng thái validation',
    example: 'success',
  })
  status: string;

  /**
   * Số lượng token ước tính
   */
  @ApiProperty({
    description: 'Số lượng token ước tính cho fine-tuning',
    example: 15000,
  })
  estimatedTokens: number;

  /**
   * Chi phí ước tính (chỉ để tham khảo, admin không bị tính phí)
   */
  @ApiProperty({
    description: 'Chi phí ước tính (USD) - chỉ để tham khảo',
    example: 45.50,
  })
  estimatedCostUSD: number;

  /**
   * Thông tin dataset được sử dụng
   */
  @ApiProperty({
    description: 'Thông tin dataset được sử dụng',
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'Admin Customer Service Dataset',
      totalExamples: 2000,
      provider: 'OPENAI'
    },
  })
  datasetInfo: {
    id: string;
    name: string;
    totalExamples: number;
    provider: string;
  };

  /**
   * Thông tin model cơ sở
   */
  @ApiProperty({
    description: 'Thông tin model cơ sở',
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      modelId: 'gpt-4o-mini-2024-07-18',
      provider: 'OPENAI'
    },
  })
  baseModelInfo: {
    id: string;
    modelId: string;
    provider: string;
  };

  /**
   * Hyperparameters được đề xuất
   */
  @ApiProperty({
    description: 'Hyperparameters được đề xuất cho fine-tuning',
    example: {
      epochs: 3,
      batchSize: 'auto',
      learningRateMultiplier: 'auto'
    },
  })
  hyperparameters: {
    epochs: number;
    batchSize: number | 'auto';
    learningRateMultiplier: number | 'auto';
  };

  /**
   * Thời gian ước tính hoàn thành (phút)
   */
  @ApiProperty({
    description: 'Thời gian ước tính hoàn thành (phút)',
    example: 60,
  })
  estimatedDurationMinutes: number;

  /**
   * Thông tin validation chi tiết
   */
  @ApiProperty({
    description: 'Thông tin validation chi tiết',
    example: {
      trainFileValid: true,
      validFileValid: true,
      formatValid: true,
      tokenCountValid: true,
      warnings: []
    },
  })
  validationDetails: {
    trainFileValid: boolean;
    validFileValid: boolean;
    formatValid: boolean;
    tokenCountValid: boolean;
    warnings: string[];
  };
}
