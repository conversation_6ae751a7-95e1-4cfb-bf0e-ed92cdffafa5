import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiExtraModels
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JWTPayloadEmployee } from '@modules/auth/interfaces/jwt-payload-employee.interface';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { FlashSaleAdminService } from '../services/flash-sale-admin.service';
// ✅ REMOVED: FlashSaleStatusUpdateService endpoints - service chỉ register ở User Module
import { FLASH_SALE_ERROR_CODES } from '../../exceptions/flash-sale.exception';
import { FlashSaleStatus } from '../../enums/flash-sale-status.enum';
import {
  CreateFlashSaleDto,
  UpdateFlashSaleDto,
  BulkDeleteFlashSaleDto,
  BulkDeleteResponseDto,
  FlashSaleResponseDto,
  QueryFlashSaleAdminDto,
  UpdateFlashSaleStatusDto,
  PaginatedFlashSaleResponseDto
} from '../dto';
import { SwaggerApiTag } from '@/common/swagger/swagger.tags';

/**
 * Controller xử lý các API liên quan đến Flash Sale cho admin
 */
@ApiTags(SwaggerApiTag.ADMIN_MARKETPLACE_FLASH_SALE)
@ApiExtraModels(
  ApiResponseDto,
  FlashSaleResponseDto,
  PaginatedFlashSaleResponseDto,
  BulkDeleteResponseDto,
  CreateFlashSaleDto,
  UpdateFlashSaleDto,
  UpdateFlashSaleStatusDto,
  BulkDeleteFlashSaleDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/marketplace/flash-sale')
export class FlashSaleAdminController {
  constructor(
    private readonly flashSaleAdminService: FlashSaleAdminService
  ) {}

  /**
   * Tạo flash sale mới
   */
  @Post()
  @ApiOperation({ 
    summary: 'Tạo flash sale mới',
    description: 'Admin tạo flash sale mới cho sản phẩm'
  })
  @ApiBody({
    type: CreateFlashSaleDto,
    examples: {
      draft: {
        summary: 'Tạo flash sale ở trạng thái DRAFT',
        description: 'Tạo flash sale và lưu ở trạng thái nháp để chỉnh sửa sau',
        value: {
          "productId": 123,
          "discountPercentage": 20,
          "displayTime": 30,
          "startTime": 1641081600000,
          "endTime": 1641168000000,
          "status": "DRAFT",
          "maxConfiguration": {
            "maxPerUser": 3,
            "totalInventory": 1000,
            "purchaseLimitPerOrder": 2,
            "timeWindowLimit": {
              "qty": 1,
              "windowMinutes": 60
            }
          }
        }
      },
      scheduled: {
        summary: 'Tạo flash sale và lên lịch ngay',
        description: 'Tạo flash sale với trạng thái SCHEDULED để tự động kích hoạt khi đến thời gian',
        value: {
          "productId": 123,
          "discountPercentage": 20,
          "displayTime": 30,
          "startTime": 1641081600000,
          "endTime": 1641168000000,
          "status": "SCHEDULED",
          "maxConfiguration": {
            "maxPerUser": 3,
            "totalInventory": 1000,
            "purchaseLimitPerOrder": 2,
            "timeWindowLimit": {
              "qty": 1,
              "windowMinutes": 60
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo flash sale thành công',
    schema: ApiResponseDto.getSchema(FlashSaleResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.INVALID_DISCOUNT_PERCENTAGE,
    FLASH_SALE_ERROR_CODES.INVALID_TIME_SEQUENCE,
    FLASH_SALE_ERROR_CODES.PRODUCT_NOT_ELIGIBLE,
    FLASH_SALE_ERROR_CODES.OVERLAPPING_FLASH_SALE,
    FLASH_SALE_ERROR_CODES.MAX_CONFIGURATION_INVALID,
    FLASH_SALE_ERROR_CODES.CREATION_FAILED
  )
  async createFlashSale(
    @CurrentEmployee() employee: JWTPayloadEmployee,
    @Body() createDto: CreateFlashSaleDto
  ): Promise<ApiResponseDto<FlashSaleResponseDto>> {
    const flashSale = await this.flashSaleAdminService.createFlashSale(employee.id, createDto);
    return ApiResponseDto.success(flashSale, 'Tạo flash sale thành công');
  }

  /**
   * Lấy danh sách flash sale với phân trang
   */
  @Get()
  @ApiOperation({ 
    summary: 'Lấy danh sách flash sale',
    description: 'Lấy danh sách flash sale với phân trang và bộ lọc'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Trang hiện tại',
    example: 1,
    schema: { minimum: 1, default: 1 }
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Số item mỗi trang',
    example: 10,
    schema: { minimum: 1, default: 10 }
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: FlashSaleStatus,
    description: 'Lọc theo trạng thái flash sale',
    example: FlashSaleStatus.ACTIVE
  })
  @ApiQuery({
    name: 'productId',
    required: false,
    type: Number,
    description: 'Lọc theo ID sản phẩm',
    example: 123
  })
  @ApiQuery({
    name: 'employeeId',
    required: false,
    type: Number,
    description: 'Lọc theo ID nhân viên tạo',
    example: 456
  })
  @ApiQuery({
    name: 'isActive',
    required: false,
    type: Boolean,
    description: 'Lọc flash sale đang hoạt động',
    example: true
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Sắp xếp theo (createdAt, startTime, endTime)',
    example: 'createdAt',
    schema: { default: 'createdAt' }
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Thứ tự sắp xếp',
    example: 'DESC',
    schema: { default: 'DESC' }
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách flash sale thành công',
    schema: ApiResponseDto.getSchema(PaginatedFlashSaleResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.RETRIEVAL_FAILED
  )
  async getFlashSales(
    @Query() queryDto: QueryFlashSaleAdminDto
  ): Promise<ApiResponseDto<PaginatedFlashSaleResponseDto>> {
    const result = await this.flashSaleAdminService.getFlashSales(queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách flash sale thành công');
  }

  /**
   * Lấy chi tiết flash sale
   */
  @Get(':id')
  @ApiOperation({ 
    summary: 'Lấy chi tiết flash sale',
    description: 'Lấy thông tin chi tiết của một flash sale'
  })
  @ApiParam({ name: 'id', description: 'ID của flash sale', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết flash sale thành công',
    schema: ApiResponseDto.getSchema(FlashSaleResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
    FLASH_SALE_ERROR_CODES.RETRIEVAL_FAILED
  )
  async getFlashSaleById(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<FlashSaleResponseDto>> {
    const flashSale = await this.flashSaleAdminService.getFlashSaleById(id);
    return ApiResponseDto.success(flashSale, 'Lấy chi tiết flash sale thành công');
  }

  /**
   * Cập nhật flash sale
   */
  @Put(':id')
  @ApiOperation({ 
    summary: 'Cập nhật flash sale',
    description: 'Cập nhật thông tin flash sale'
  })
  @ApiParam({ name: 'id', description: 'ID của flash sale', type: 'number' })
  @ApiBody({ type: UpdateFlashSaleDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật flash sale thành công',
    schema: ApiResponseDto.getSchema(FlashSaleResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
    FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
    FLASH_SALE_ERROR_CODES.INVALID_DISCOUNT_PERCENTAGE,
    FLASH_SALE_ERROR_CODES.INVALID_TIME_SEQUENCE,
    FLASH_SALE_ERROR_CODES.OVERLAPPING_FLASH_SALE,
    FLASH_SALE_ERROR_CODES.MAX_CONFIGURATION_INVALID,
    FLASH_SALE_ERROR_CODES.UPDATE_FAILED
  )
  async updateFlashSale(
    @CurrentEmployee() employee: JWTPayloadEmployee,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateFlashSaleDto
  ): Promise<ApiResponseDto<FlashSaleResponseDto>> {
    const flashSale = await this.flashSaleAdminService.updateFlashSale(id, employee.id, updateDto);
    return ApiResponseDto.success(flashSale, 'Cập nhật flash sale thành công');
  }

  /**
   * Cập nhật trạng thái flash sale
   */
  @Put(':id/status')
  @ApiOperation({ 
    summary: 'Cập nhật trạng thái flash sale',
    description: 'Cập nhật trạng thái của flash sale'
  })
  @ApiParam({ name: 'id', description: 'ID của flash sale', type: 'number' })
  @ApiBody({
    type: UpdateFlashSaleStatusDto,
    examples: {
      schedule: {
        summary: 'Lên lịch flash sale',
        description: 'Chuyển flash sale từ DRAFT sang SCHEDULED để tự động kích hoạt',
        value: {
          "status": "SCHEDULED"
        }
      },
      draft: {
        summary: 'Chuyển về nháp',
        description: 'Chuyển flash sale về DRAFT để chỉnh sửa',
        value: {
          "status": "DRAFT"
        }
      },
      cancel: {
        summary: 'Hủy flash sale',
        description: 'Hủy flash sale (không thể hoàn tác)',
        value: {
          "status": "CANCELLED"
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái flash sale thành công',
    schema: ApiResponseDto.getSchema(FlashSaleResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
    FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
    FLASH_SALE_ERROR_CODES.INVALID_STATUS_TRANSITION,
    FLASH_SALE_ERROR_CODES.STATUS_UPDATE_FAILED
  )
  async updateFlashSaleStatus(
    @CurrentEmployee() employee: JWTPayloadEmployee,
    @Param('id', ParseIntPipe) id: number,
    @Body() statusDto: UpdateFlashSaleStatusDto
  ): Promise<ApiResponseDto<FlashSaleResponseDto>> {
    const flashSale = await this.flashSaleAdminService.updateFlashSaleStatus(id, employee.id, statusDto);
    return ApiResponseDto.success(flashSale, 'Cập nhật trạng thái flash sale thành công');
  }

  /**
   * ✅ MODIFIED: Bulk delete flash sales (Admin)
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa nhiều flash sales',
    description: 'Xóa nhiều flash sales cùng lúc (có thể xóa flash sale ở mọi trạng thái, miễn là thuộc sở hữu của admin). Tối đa 50 items mỗi lần.'
  })
  @ApiBody({
    type: BulkDeleteFlashSaleDto,
    examples: {
      single: {
        summary: 'Xóa 1 flash sale',
        description: 'Xóa một flash sale duy nhất',
        value: {
          "ids": [1]
        }
      },
      multiple: {
        summary: 'Xóa nhiều flash sales',
        description: 'Xóa nhiều flash sales cùng lúc',
        value: {
          "ids": [1, 2, 3, 4, 5]
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Bulk delete flash sales completed',
    schema: ApiResponseDto.getSchema(BulkDeleteResponseDto)
  })
  @ApiErrorResponse(
    FLASH_SALE_ERROR_CODES.DELETE_FAILED
  )
  async deleteFlashSales(
    @CurrentEmployee() employee: JWTPayloadEmployee,
    @Body() deleteDto: BulkDeleteFlashSaleDto
  ): Promise<ApiResponseDto<BulkDeleteResponseDto>> {
    const result = await this.flashSaleAdminService.deleteFlashSales(employee.id, deleteDto);

    if (result.failureCount === 0) {
      return ApiResponseDto.success(result, `Xóa thành công ${result.successCount} flash sales`);
    } else if (result.successCount === 0) {
      return ApiResponseDto.success(result, `Không thể xóa flash sale nào. ${result.failureCount} thất bại`);
    } else {
      return ApiResponseDto.success(result, `Xóa thành công ${result.successCount} flash sales, ${result.failureCount} thất bại`);
    }
  }

  // ✅ REMOVED: Status update endpoints - service chỉ register ở User Module để tránh duplicate cron jobs
  // Nếu cần trigger manual, có thể gọi qua User API hoặc tạo shared service riêng
}
