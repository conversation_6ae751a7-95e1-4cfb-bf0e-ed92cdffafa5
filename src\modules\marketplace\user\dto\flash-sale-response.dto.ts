import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { FlashSaleStatus } from '../../enums/flash-sale-status.enum';
import { MaxConfiguration } from '../../interfaces/max-configuration.interface';

/**
 * DTO response cho flash sale (User)
 */
export class FlashSaleResponseDto {
  @ApiProperty({
    description: 'ID flash sale',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'ID sản phẩm',
    example: 123
  })
  productId: number;

  @ApiPropertyOptional({
    description: 'ID người dùng tạo flash sale',
    example: 789,
    nullable: true
  })
  userId: number | null;

  @ApiProperty({
    description: 'Phần trăm giảm giá',
    example: 20
  })
  discountPercentage: number;

  @ApiProperty({
    description: 'Thời gian hiển thị sản phẩm flash sale cho user (giây)',
    example: 30
  })
  displayTime: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu flash sale (timestamp)',
    example: 1641081600000
  })
  startTime: number;

  @ApiProperty({
    description: 'Thời gian kết thúc flash sale (timestamp)',
    example: 1641168000000
  })
  endTime: number;

  @ApiProperty({
    description: 'Cấu hình giới hạn flash sale',
    example: {
      max_per_user: 3,
      total_inventory: 1000,
      purchase_limit_per_order: 2,
      time_window_limit: {
        qty: 1,
        window_minutes: 60
      }
    }
  })
  maxConfiguration: MaxConfiguration;

  @ApiProperty({
    description: 'Trạng thái flash sale',
    enum: FlashSaleStatus,
    example: FlashSaleStatus.ACTIVE
  })
  status: FlashSaleStatus;

  @ApiProperty({
    description: 'Flash sale có đang hoạt động không',
    example: true
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1640908800000
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (timestamp)',
    example: 1640995200000
  })
  updatedAt: number;

  @ApiPropertyOptional({
    description: 'Thông tin sản phẩm (nếu có join)',
    example: {
      id: 123,
      name: 'Sản phẩm ABC',
      listedPrice: 100000,
      discountedPrice: 80000,
      images: [
        {
          key: 'marketplace/IMAGE/2025/07/1751968717292-218a4f40-4ca5-4d8d-8777-d571bef0a893',
          url: 'https://cdn.redai.vn/marketplace/IMAGE/2025/07/1751968717292-218a4f40-4ca5-4d8d-8777-d571bef0a893',
          position: 0
        }
      ]
    }
  })
  product?: any;

  @ApiPropertyOptional({
    description: 'Giá sau khi áp dụng flash sale',
    example: 64000
  })
  salePrice?: number;

  @ApiPropertyOptional({
    description: 'Thời gian còn lại (giây)',
    example: 3600
  })
  timeRemaining?: number;

  @ApiPropertyOptional({
    description: 'Số lượng đã bán (calculated field)',
    example: 50
  })
  soldCount?: number;
}

/**
 * DTO cho metadata của pagination
 */
export class PaginationMetaDto {
  @ApiProperty({
    description: 'Tổng số items',
    example: 100
  })
  totalItems: number;

  @ApiProperty({
    description: 'Số items trong trang hiện tại',
    example: 10
  })
  itemCount: number;

  @ApiProperty({
    description: 'Số items mỗi trang',
    example: 10
  })
  itemsPerPage: number;

  @ApiProperty({
    description: 'Tổng số trang',
    example: 10
  })
  totalPages: number;

  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1
  })
  currentPage: number;
}

/**
 * DTO response cho danh sách flash sale với phân trang (User)
 */
export class PaginatedFlashSaleResponseDto {
  @ApiProperty({
    description: 'Danh sách flash sale',
    type: [FlashSaleResponseDto]
  })
  items: FlashSaleResponseDto[];

  @ApiProperty({
    description: 'Metadata phân trang',
    type: PaginationMetaDto
  })
  meta: PaginationMetaDto;
}
