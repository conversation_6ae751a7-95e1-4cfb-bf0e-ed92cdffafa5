import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
// import * as compression from 'compression'; // Commented out - compression package not installed
import * as zlib from 'zlib';

/**
 * Compression middleware for workflow API responses
 * Implements intelligent compression based on content type and size
 */
@Injectable()
export class WorkflowCompressionMiddleware implements NestMiddleware {
  private readonly logger = new Logger(WorkflowCompressionMiddleware.name);
  
  // Compression configuration
  private readonly compressionOptions = {
    // Minimum response size to compress (1KB)
    threshold: 1024,
    
    // Compression level (1-9, 6 is default balance)
    level: 6,
    
    // Memory level (1-9, 8 is default)
    memLevel: 8,
    
    // Window size (9-15, 15 is default)
    windowBits: 15,
    
    // Compression strategy
    strategy: zlib.constants.Z_DEFAULT_STRATEGY,
    
    // Filter function to determine what to compress
    filter: this.shouldCompress.bind(this),
  };

  // private readonly compressionMiddleware = compression(this.compressionOptions); // Commented out - compression package not installed

  use(req: Request, res: Response, next: NextFunction) {
    // Add compression headers for monitoring
    const originalSend = res.send;
    const originalJson = res.json;
    
    // Override res.send to track compression
    res.send = function(body: any) {
      const contentLength = Buffer.byteLength(body || '', 'utf8');
      res.setHeader('X-Original-Size', contentLength.toString());
      
      return originalSend.call(this, body);
    };

    // Override res.json to track compression
    res.json = function(obj: any) {
      const jsonString = JSON.stringify(obj);
      const contentLength = Buffer.byteLength(jsonString, 'utf8');
      res.setHeader('X-Original-Size', contentLength.toString());
      
      return originalJson.call(this, obj);
    };

    // Apply compression middleware (disabled - compression package not installed)
    // this.compressionMiddleware(req, res, (err) => {
    //   if (err) {
    //     this.logger.error(`Compression error: ${err.message}`);
    //   }
    //   next(err);
    // });
    next(); // Skip compression for now
  }

  /**
   * Determine if response should be compressed
   * @param req Request object
   * @param res Response object
   * @returns Whether to compress the response
   */
  private shouldCompress(req: Request, res: Response): boolean {
    // Don't compress if client doesn't support it
    if (!req.headers['accept-encoding']) {
      return false;
    }

    // Don't compress if already compressed
    if (res.getHeader('content-encoding')) {
      return false;
    }

    // Get content type
    const contentType = res.getHeader('content-type') as string;
    
    if (!contentType) {
      return false;
    }

    // Compress JSON responses (API responses)
    if (contentType.includes('application/json')) {
      return true;
    }

    // Compress text responses
    if (contentType.includes('text/')) {
      return true;
    }

    // Compress XML responses
    if (contentType.includes('application/xml') || contentType.includes('text/xml')) {
      return true;
    }

    // Compress JavaScript and CSS
    if (contentType.includes('application/javascript') || contentType.includes('text/css')) {
      return true;
    }

    // Don't compress binary content, images, videos, etc.
    const binaryTypes = [
      'image/',
      'video/',
      'audio/',
      'application/pdf',
      'application/zip',
      'application/gzip',
      'application/octet-stream',
    ];

    if (binaryTypes.some(type => contentType.includes(type))) {
      return false;
    }

    // Default to not compress
    return false;
  }
}

/**
 * Advanced compression middleware with workflow-specific optimizations
 */
@Injectable()
export class WorkflowAdvancedCompressionMiddleware implements NestMiddleware {
  private readonly logger = new Logger(WorkflowAdvancedCompressionMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    // Skip compression for SSE endpoints
    if (req.path.includes('/sse/') || req.path.includes('/stream')) {
      return next();
    }

    // Apply different compression strategies based on endpoint
    const compressionLevel = this.getCompressionLevel(req.path);
    const threshold = this.getCompressionThreshold(req.path);

    const compressionOptions = {
      level: compressionLevel,
      threshold,
      filter: (req: Request, res: Response) => {
        return this.shouldCompressAdvanced(req, res);
      },
    };

    // const compressionMiddleware = compression(compressionOptions); // Commented out - compression package not installed

    // Add performance monitoring
    const startTime = Date.now();
    
    const originalEnd = res.end;
    res.end = function(chunk?: any, encoding?: any) {
      const duration = Date.now() - startTime;
      const originalSize = res.getHeader('X-Original-Size') as string;
      const compressedSize = res.getHeader('content-length') as string;
      
      if (originalSize && compressedSize) {
        const compressionRatio = (1 - parseInt(compressedSize) / parseInt(originalSize)) * 100;
        res.setHeader('X-Compression-Ratio', compressionRatio.toFixed(2));
        res.setHeader('X-Compression-Time', duration.toString());
      }
      
      return originalEnd.call(this, chunk, encoding);
    };

    // compressionMiddleware(req, res, next); // Commented out - compression package not installed
    next(); // Skip compression for now
  }

  /**
   * Get compression level based on endpoint
   * @param path Request path
   * @returns Compression level (1-9)
   */
  private getCompressionLevel(path: string): number {
    // High compression for large data endpoints
    if (path.includes('/executions') || path.includes('/statistics')) {
      return 8; // Higher compression for potentially large responses
    }

    // Medium compression for workflow data
    if (path.includes('/workflows')) {
      return 6; // Balanced compression
    }

    // Lower compression for real-time endpoints
    if (path.includes('/node-tests') || path.includes('/status')) {
      return 4; // Faster compression for real-time data
    }

    // Default compression
    return 6;
  }

  /**
   * Get compression threshold based on endpoint
   * @param path Request path
   * @returns Threshold in bytes
   */
  private getCompressionThreshold(path: string): number {
    // Lower threshold for list endpoints (likely to have more data)
    if (path.includes('/workflows') && !path.match(/\/workflows\/[^\/]+$/)) {
      return 512; // 512 bytes
    }

    // Higher threshold for single item endpoints
    if (path.match(/\/workflows\/[^\/]+$/) || path.match(/\/executions\/[^\/]+$/)) {
      return 2048; // 2KB
    }

    // Default threshold
    return 1024; // 1KB
  }

  /**
   * Advanced compression decision logic
   * @param req Request object
   * @param res Response object
   * @returns Whether to compress
   */
  private shouldCompressAdvanced(req: Request, res: Response): boolean {
    // Basic compression check
    if (!req.headers['accept-encoding']) {
      return false;
    }

    const contentType = res.getHeader('content-type') as string;
    
    if (!contentType) {
      return false;
    }

    // Always compress JSON API responses
    if (contentType.includes('application/json')) {
      return true;
    }

    // Check for workflow-specific content that benefits from compression
    const workflowPaths = [
      '/workflows',
      '/executions',
      '/node-tests',
      '/statistics',
      '/node-definitions',
    ];

    const isWorkflowEndpoint = workflowPaths.some(path => req.path.includes(path));
    
    if (isWorkflowEndpoint && contentType.includes('application/json')) {
      return true;
    }

    // Don't compress small responses from cache
    const cacheHeader = res.getHeader('X-Cache-Status');
    if (cacheHeader === 'HIT') {
      const originalSize = res.getHeader('X-Original-Size') as string;
      if (originalSize && parseInt(originalSize) < 1024) {
        return false; // Don't compress small cached responses
      }
    }

    return false;
  }
}

/**
 * Response size monitoring middleware
 */
@Injectable()
export class ResponseSizeMonitoringMiddleware implements NestMiddleware {
  private readonly logger = new Logger(ResponseSizeMonitoringMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    let responseSize = 0;

    // Override res.write to track response size
    const originalWrite = res.write;
    res.write = function(chunk: any, encoding?: any, callback?: any) {
      if (chunk) {
        responseSize += Buffer.byteLength(chunk);
      }
      return originalWrite.call(this, chunk, encoding, callback);
    };

    // Override res.end to log final metrics
    const originalEnd = res.end;
    res.end = function(chunk?: any, encoding?: any) {
      if (chunk) {
        responseSize += Buffer.byteLength(chunk);
      }

      const duration = Date.now() - startTime;
      const compressionRatio = res.getHeader('X-Compression-Ratio') as string;
      
      // Log performance metrics for monitoring
      if (responseSize > 10240) { // Log responses larger than 10KB
        const logData = {
          method: req.method,
          path: req.path,
          responseSize,
          duration,
          compressionRatio: compressionRatio ? `${compressionRatio}%` : 'none',
          userAgent: req.get('User-Agent'),
        };

        if (responseSize > 102400) { // Warn for responses larger than 100KB
          this.logger.warn(`Large response detected: ${JSON.stringify(logData)}`);
        } else {
          this.logger.debug(`Response metrics: ${JSON.stringify(logData)}`);
        }
      }

      // Add response size header for monitoring
      res.setHeader('X-Response-Size', responseSize.toString());
      res.setHeader('X-Response-Time', duration.toString());

      return originalEnd.call(this, chunk, encoding);
    }.bind(this);

    next();
  }
}
