import { Injectable } from '@nestjs/common';
import { AgentMemories } from '@modules/agent/entities';
import { AdminAgentMemoryResponseDto } from '../dto/agent-memories/admin-agent-memories.dto';

/**
 * Mapper cho Admin Agent Memories
 * Chuyển đổi giữa entity và DTO cho admin operations
 */
@Injectable()
export class AdminAgentMemoriesMapper {
  /**
   * Convert AgentMemories entity sang AdminAgentMemoryResponseDto
   * @param entity AgentMemories entity
   * @returns AdminAgentMemoryResponseDto
   */
  static toResponseDto(entity: AgentMemories): AdminAgentMemoryResponseDto {
    return {
      id: entity.id,
      agentId: entity.agentId,
      structuredContent: entity.structuredContent, // Type assertion để tránh lỗi type
      createdAt: entity.createdAt || Date.now(),
    };
  }

  /**
   * Convert array AgentMemories entities sang array AdminAgentMemoryResponseDto
   * @param entities Array AgentMemories entities
   * @returns Array AdminAgentMemoryResponseDto
   */
  static toResponseDtoArray(entities: AgentMemories[]): AdminAgentMemoryResponseDto[] {
    return entities.map(entity => this.toResponseDto(entity));
  }

  /**
   * Convert AgentMemories entity sang simplified DTO (chỉ thông tin cơ bản cho admin)
   * @param entity AgentMemories entity
   * @returns Simplified DTO
   */
  static toSimpleDto(entity: AgentMemories) {
    return {
      id: entity.id,
      agentId: entity.agentId,
      title: entity.structuredContent?.title || 'Không có tiêu đề',
      content: entity.structuredContent?.content || 'Không có nội dung',
      reason: entity.structuredContent?.reason || 'Không có lý do',
      createdAt: entity.createdAt,
    };
  }

  /**
   * Convert AgentMemories entity sang detailed admin DTO với thông tin mở rộng
   * @param entity AgentMemories entity
   * @returns Detailed admin DTO
   */
  static toDetailedAdminDto(entity: AgentMemories) {
    return {
      id: entity.id,
      agentId: entity.agentId,
      structuredContent: {
        title: entity.structuredContent?.title || 'Không có tiêu đề',
        content: entity.structuredContent?.content || 'Không có nội dung',
        reason: entity.structuredContent?.reason || 'Không có lý do',
      },
      createdAt: entity.createdAt,
    };
  }

  /**
   * Convert AgentMemories entity sang export DTO (cho export data)
   * @param entity AgentMemories entity
   * @returns Export DTO
   */
  static toExportDto(entity: AgentMemories) {
    return {
      memory_id: entity.id,
      agent_id: entity.agentId,
      title: entity.structuredContent?.title || '',
      content: entity.structuredContent?.content || '',
      reason: entity.structuredContent?.reason || '',
      created_at: new Date(entity.createdAt || Date.now()).toISOString(),
    };
  }

  /**
   * Convert AgentMemories entity sang statistics DTO (cho báo cáo thống kê)
   * @param entity AgentMemories entity
   * @returns Statistics DTO
   */
  static toStatisticsDto(entity: AgentMemories) {
    return {
      id: entity.id,
      agentId: entity.agentId,
      contentLength: entity.structuredContent?.content?.length || 0,
      hasTitle: !!entity.structuredContent?.title,
      hasReason: !!entity.structuredContent?.reason,
      daysSinceCreated: entity.createdAt 
        ? Math.floor((Date.now() - entity.createdAt) / (1000 * 60 * 60 * 24))
        : 0,
    };
  }
}
