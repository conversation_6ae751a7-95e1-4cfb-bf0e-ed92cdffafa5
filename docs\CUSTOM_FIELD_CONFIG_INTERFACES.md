# Custom Field Config Interfaces

## Tổng quan

Tài liệu này mô tả các interface và cấu trúc config cho từng loại `dataType` trong hệ thống Custom Field.

## Cấu trúc Config theo DataType

### 1. TEXT (`dataType = 'text'`)

```typescript
interface TextCustomFieldConfig {
  placeholder?: string;      // Placeholder hiển thị
  defaultValue?: string;     // Giá trị mặc định
  pattern?: string;          // Regex pattern validation
  minLength?: number;        // Độ dài tối thiểu
  maxLength?: number;        // Độ dài tối đa
}
```

**Ví dụ:**
```json
{
  "placeholder": "Nhập họ tên...",
  "defaultValue": "",
  "pattern": "^[a-zA-ZÀ-ỹ\\s]+$",
  "minLength": 2,
  "maxLength": 100
}
```

### 2. NUMBER (`dataType = 'number'`)

```typescript
interface NumberCustomFieldConfig {
  placeholder?: string;      // Placeholder hiển thị
  defaultValue?: number;     // Gi<PERSON> trị mặc định
  minValue?: number;         // Giá trị tối thiểu
  maxValue?: number;         // Giá trị tối đa
}
```

**Ví dụ:**
```json
{
  "placeholder": "Nhập tuổi...",
  "defaultValue": 18,
  "minValue": 0,
  "maxValue": 120
}
```

### 3. BOOLEAN (`dataType = 'boolean'`)

```typescript
interface BooleanCustomFieldConfig {
  placeholder?: string;      // Placeholder hiển thị
  defaultValue?: boolean;    // Giá trị mặc định
}
```

**Ví dụ:**
```json
{
  "placeholder": "Đồng ý điều khoản",
  "defaultValue": false
}
```

### 4. DATE (`dataType = 'date'`)

```typescript
interface DateCustomFieldConfig {
  placeholder?: string;      // Placeholder hiển thị
  defaultValue?: string;     // Giá trị mặc định (ISO date string)
}
```

**Ví dụ:**
```json
{
  "placeholder": "Chọn ngày sinh...",
  "defaultValue": "2000-01-01"
}
```

### 5. SELECT (`dataType = 'select'`)

```typescript
interface SelectOption {
  title: string;             // Tiêu đề hiển thị
  value: string;             // Giá trị thực tế
}

interface SelectCustomFieldConfig {
  placeholder?: string;      // Placeholder hiển thị
  options: SelectOption[];   // Danh sách tùy chọn
  defaultValue?: string;     // Giá trị mặc định (phải có trong options)
}
```

**Ví dụ:**
```json
{
  "placeholder": "Chọn giới tính...",
  "options": [
    { "title": "Nam", "value": "male" },
    { "title": "Nữ", "value": "female" },
    { "title": "Khác", "value": "other" }
  ],
  "defaultValue": "male"
}
```

### 6. OBJECT (`dataType = 'object'`)

```typescript
interface ObjectCustomFieldConfig {
  placeholder?: string;              // Placeholder hiển thị
  defaultValue?: Record<string, any>; // Giá trị mặc định
}
```

**Ví dụ:**
```json
{
  "placeholder": "Nhập thông tin địa chỉ...",
  "defaultValue": {
    "street": "",
    "city": "",
    "country": "Vietnam"
  }
}
```

## Cách sử dụng

### 1. Import interfaces

```typescript
import {
  TextCustomFieldConfig,
  NumberCustomFieldConfig,
  BooleanCustomFieldConfig,
  DateCustomFieldConfig,
  SelectCustomFieldConfig,
  ObjectCustomFieldConfig,
  CustomFieldConfig
} from '@/modules/marketing/common';
```

### 2. Validate config

```typescript
import { CustomFieldConfigUtil } from '@/modules/marketing/common';

// Validate config
const errors = await CustomFieldConfigUtil.validateConfig(
  CustomFieldDataType.TEXT,
  {
    placeholder: "Nhập họ tên...",
    minLength: 2,
    maxLength: 100
  }
);

if (errors.length > 0) {
  console.log('Validation errors:', errors);
}
```

### 3. Validate giá trị theo config

```typescript
// Validate giá trị
const valueErrors = CustomFieldConfigUtil.validateValue(
  "John Doe",
  CustomFieldDataType.TEXT,
  {
    minLength: 2,
    maxLength: 100,
    pattern: "^[a-zA-Z\\s]+$"
  }
);
```

### 4. Lấy config mặc định

```typescript
// Lấy config mặc định
const defaultConfig = CustomFieldConfigUtil.getDefaultConfig(
  CustomFieldDataType.SELECT
);
```

## Type Guards

Sử dụng type guards để kiểm tra loại config:

```typescript
import { 
  isTextConfig, 
  isSelectConfig, 
  isNumberConfig 
} from '@/modules/marketing/common';

if (isSelectConfig(config)) {
  // TypeScript biết config là SelectCustomFieldConfig
  console.log(config.options);
}
```

## Validation DTOs

Sử dụng DTOs để validate config trong API:

```typescript
import { 
  TextCustomFieldConfigDto,
  SelectCustomFieldConfigDto 
} from '@/modules/marketing/common';

// Trong controller hoặc service
@Post()
async createCustomField(
  @Body() createDto: CreateAudienceCustomFieldDefinitionDto
) {
  // Validate config theo dataType
  const errors = await CustomFieldConfigUtil.validateConfig(
    createDto.dataType,
    createDto.config
  );
  
  if (errors.length > 0) {
    throw new BadRequestException(errors);
  }
  
  // Tiếp tục xử lý...
}
```

## Examples cho từng DataType

### TEXT Example
```json
{
  "fieldKey": "full_name",
  "displayName": "Họ và tên",
  "dataType": "text",
  "config": {
    "placeholder": "Nhập họ và tên đầy đủ...",
    "defaultValue": "",
    "pattern": "^[a-zA-ZÀ-ỹ\\s]+$",
    "minLength": 2,
    "maxLength": 100
  }
}
```

### NUMBER Example
```json
{
  "fieldKey": "age",
  "displayName": "Tuổi",
  "dataType": "number",
  "config": {
    "placeholder": "Nhập tuổi...",
    "defaultValue": 18,
    "minValue": 0,
    "maxValue": 120
  }
}
```

### SELECT Example
```json
{
  "fieldKey": "gender",
  "displayName": "Giới tính",
  "dataType": "select",
  "config": {
    "placeholder": "Chọn giới tính...",
    "options": [
      { "title": "Nam", "value": "male" },
      { "title": "Nữ", "value": "female" },
      { "title": "Khác", "value": "other" }
    ],
    "defaultValue": "male"
  }
}
```

## Lưu ý quan trọng

1. **Validation**: Luôn validate config trước khi lưu vào database
2. **Type Safety**: Sử dụng TypeScript interfaces để đảm bảo type safety
3. **Default Values**: Sử dụng utility functions để tạo config mặc định
4. **Error Handling**: Xử lý lỗi validation một cách graceful
5. **Documentation**: Luôn document config structure cho frontend team
