# Admin Email Campaign - Sync Status API (Tóm tắt)

## 🎯 Mục đích
Tạo API để cập nhật trạng thái chiến dịch email admin dựa trên trạng thái job trong queue và thời gian lên lịch.

## 📡 API Endpoint
```
POST /admin/email-campaigns/sync-status
Authorization: Bearer <JWT_TOKEN>
Permission: MARKETING_VIEW
```

## 🔄 Logic Cập Nhật

### Campaign SCHEDULED
- **Điều kiện**: `scheduledAt < currentTime` và không còn job active
- **Kết quả**: `SCHEDULED` → `FAILED`

### Campaign SENDING  
- **Tất cả job completed**: `SENDING` → `COMPLETED` + set `completedAt`
- **Tất cả job failed/không tồn tại**: `SENDING` → `FAILED`
- **Còn job active**: Không thay đổi

## 📊 Response Format
```json
{
  "success": true,
  "message": "Đã cập nhật trạng thái campaign thành công",
  "data": {
    "totalCampaignsChecked": 25,
    "updatedCampaigns": [
      {
        "campaignId": 1,
        "campaignName": "Welcome Email",
        "previousStatus": "SCHEDULED", 
        "currentStatus": "FAILED",
        "reason": "Quá thời gian lên lịch và không còn job trong queue"
      }
    ],
    "summary": {
      "scheduledToFailed": 1,
      "sendingToCompleted": 0, 
      "sendingToFailed": 0
    }
  }
}
```

## 📁 Files Đã Tạo/Cập Nhật

### Controller
- `admin-email-campaign.controller.ts`: Thêm endpoint `syncCampaignStatus()`

### Service  
- `admin-email-campaign.service.ts`: Thêm method `syncCampaignStatus()` và helper methods

### DTOs
- `admin-email-campaign-response.dto.ts`: Thêm `SyncCampaignStatusResponseDto` và related DTOs

### Test Files
- `admin-email-campaign-sync.test.ts`: Unit tests
- `test-sync-campaign-status.js`: API test script
- `test-sync-campaign-status.md`: Test documentation

## 🧪 Cách Test
```bash
# 1. Cập nhật JWT_TOKEN trong script
# 2. Chạy test
node scripts/test-sync-campaign-status.js

# Hoặc dùng curl
curl -X POST http://localhost:3000/admin/email-campaigns/sync-status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## ⚡ Tính Năng Chính
- ✅ Kiểm tra trạng thái job trong queue real-time
- ✅ Cập nhật trạng thái campaign chính xác
- ✅ Logging chi tiết quá trình xử lý
- ✅ Error handling toàn diện
- ✅ Response format chuẩn với thống kê
- ✅ Unit test coverage

## 🎯 Khi Nào Sử Dụng
- Admin muốn kiểm tra trạng thái campaign
- Sau sự cố queue system
- Trong dashboard để refresh trạng thái
- Trước khi tạo báo cáo campaign
- Khi phát hiện campaign "kẹt" ở trạng thái SCHEDULED/SENDING

## 🔧 Implementation Details
- Chỉ kiểm tra campaign có trạng thái `SCHEDULED` hoặc `SENDING`
- Sử dụng Bull queue `getJob()` và `getState()` để kiểm tra job
- Cập nhật database an toàn với proper error handling
- Không ảnh hưởng đến performance vì chỉ chạy khi được gọi manual

**✨ API đã sẵn sàng sử dụng!**
