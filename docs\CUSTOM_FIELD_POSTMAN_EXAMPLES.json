{"info": {"name": "Custom Field API Examples", "description": "Collection of API examples for Custom Field system with different dataTypes", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "User Custom Fields", "item": [{"name": "Create TEXT Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"fieldKey\": \"full_name\",\n  \"displayName\": \"Họ và tên\",\n  \"dataType\": \"text\",\n  \"description\": \"Họ và tên đầy đủ của khách hàng\",\n  \"tags\": [\"personal\", \"required\"],\n  \"config\": {\n    \"placeholder\": \"Nhập họ và tên đầy đủ...\",\n    \"defaultValue\": \"\",\n    \"pattern\": \"^[a-zA-ZÀ-ỹ\\\\s]+$\",\n    \"minLength\": 2,\n    \"maxLength\": 100\n  }\n}"}, "url": {"raw": "{{base_url}}/user/marketing/audience-custom-fields", "host": ["{{base_url}}"], "path": ["user", "marketing", "audience-custom-fields"]}}}, {"name": "Create NUMBER Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"fieldKey\": \"age\",\n  \"displayName\": \"Tuổi\",\n  \"dataType\": \"number\",\n  \"description\": \"Tuổi của khách hàng\",\n  \"config\": {\n    \"placeholder\": \"<PERSON>hập tuổi...\",\n    \"defaultValue\": 18,\n    \"minValue\": 0,\n    \"maxValue\": 120\n  }\n}"}, "url": {"raw": "{{base_url}}/user/marketing/audience-custom-fields", "host": ["{{base_url}}"], "path": ["user", "marketing", "audience-custom-fields"]}}}, {"name": "Create SELECT Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"fieldKey\": \"gender\",\n  \"displayName\": \"Giới tính\",\n  \"dataType\": \"select\",\n  \"config\": {\n    \"placeholder\": \"Chọn giới tính...\",\n    \"options\": [\n      { \"title\": \"Nam\", \"value\": \"male\" },\n      { \"title\": \"Nữ\", \"value\": \"female\" },\n      { \"title\": \"Khác\", \"value\": \"other\" }\n    ],\n    \"defaultValue\": \"male\"\n  }\n}"}, "url": {"raw": "{{base_url}}/user/marketing/audience-custom-fields", "host": ["{{base_url}}"], "path": ["user", "marketing", "audience-custom-fields"]}}}, {"name": "Create OBJECT Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"fieldKey\": \"address\",\n  \"displayName\": \"Địa chỉ\",\n  \"dataType\": \"object\",\n  \"config\": {\n    \"placeholder\": \"<PERSON>hập thông tin địa chỉ...\",\n    \"defaultValue\": {\n      \"street\": \"\",\n      \"ward\": \"\",\n      \"district\": \"\",\n      \"city\": \"\",\n      \"country\": \"Vietnam\"\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/user/marketing/audience-custom-fields", "host": ["{{base_url}}"], "path": ["user", "marketing", "audience-custom-fields"]}}}, {"name": "Update TEXT Field", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"displayName\": \"Họ và tên đầy đủ\",\n  \"description\": \"Họ và tên đầy đủ của kh<PERSON>ch hàng (đã cập nhật)\",\n  \"config\": {\n    \"placeholder\": \"Nhập họ và tên đầy đủ...\",\n    \"pattern\": \"^[a-zA-ZÀ-ỹ\\\\s]+$\",\n    \"minLength\": 3,\n    \"maxLength\": 150\n  }\n}"}, "url": {"raw": "{{base_url}}/user/marketing/audience-custom-fields/1", "host": ["{{base_url}}"], "path": ["user", "marketing", "audience-custom-fields", "1"]}}}, {"name": "Update SELECT Field - Add Options", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"displayName\": \"Giớ<PERSON> tính\",\n  \"config\": {\n    \"placeholder\": \"Chọn giới tính...\",\n    \"options\": [\n      { \"title\": \"Nam\", \"value\": \"male\" },\n      { \"title\": \"N<PERSON>\", \"value\": \"female\" },\n      { \"title\": \"<PERSON>h<PERSON><PERSON>\", \"value\": \"other\" },\n      { \"title\": \"Không muốn tiết lộ\", \"value\": \"prefer_not_to_say\" }\n    ],\n    \"defaultValue\": \"prefer_not_to_say\"\n  }\n}"}, "url": {"raw": "{{base_url}}/user/marketing/audience-custom-fields/2", "host": ["{{base_url}}"], "path": ["user", "marketing", "audience-custom-fields", "2"]}}}, {"name": "Change DataType TEXT to SELECT", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"dataType\": \"select\",\n  \"config\": {\n    \"placeholder\": \"Chọn trình độ học vấn...\",\n    \"options\": [\n      { \"title\": \"<PERSON>i<PERSON><PERSON> học\", \"value\": \"primary\" },\n      { \"title\": \"Trung học cơ sở\", \"value\": \"secondary\" },\n      { \"title\": \"Trung học phổ thông\", \"value\": \"high_school\" },\n      { \"title\": \"Cao đẳng\", \"value\": \"college\" },\n      { \"title\": \"Đ<PERSON>i học\", \"value\": \"university\" }\n    ]\n  }\n}"}, "url": {"raw": "{{base_url}}/user/marketing/audience-custom-fields/3", "host": ["{{base_url}}"], "path": ["user", "marketing", "audience-custom-fields", "3"]}}}, {"name": "Get Config Examples", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{base_url}}/user/marketing/audience-custom-fields/config-examples", "host": ["{{base_url}}"], "path": ["user", "marketing", "audience-custom-fields", "config-examples"]}}}]}, {"name": "Admin Custom Fields", "item": [{"name": "Create Business TEXT Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"fieldKey\": \"company_name\",\n  \"displayName\": \"Tên công ty\",\n  \"dataType\": \"text\",\n  \"description\": \"Tên công ty của khách hàng doanh nghiệp\",\n  \"tags\": [\"business\", \"company\"],\n  \"config\": {\n    \"placeholder\": \"Nhập tên công ty...\",\n    \"defaultValue\": \"\",\n    \"minLength\": 2,\n    \"maxLength\": 200\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/marketing/audience-custom-fields", "host": ["{{base_url}}"], "path": ["admin", "marketing", "audience-custom-fields"]}}}, {"name": "Create Customer Type SELECT", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"fieldKey\": \"customer_type\",\n  \"displayName\": \"Loại khách hàng\",\n  \"dataType\": \"select\",\n  \"description\": \"<PERSON><PERSON> loại khách hàng theo tính chất\",\n  \"config\": {\n    \"placeholder\": \"<PERSON>ọn loại khách hàng...\",\n    \"options\": [\n      { \"title\": \"Cá nhân\", \"value\": \"individual\" },\n      { \"title\": \"Doanh nghiệp\", \"value\": \"business\" },\n      { \"title\": \"Đối tác\", \"value\": \"partner\" },\n      { \"title\": \"<PERSON>hà cung cấp\", \"value\": \"supplier\" }\n    ],\n    \"defaultValue\": \"individual\"\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/marketing/audience-custom-fields", "host": ["{{base_url}}"], "path": ["admin", "marketing", "audience-custom-fields"]}}}, {"name": "Update Customer Type - Add More Options", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"displayName\": \"<PERSON><PERSON><PERSON> khách hàng\",\n  \"description\": \"<PERSON><PERSON> loại khách hàng theo t<PERSON> chất (đ<PERSON> cập nhật)\",\n  \"config\": {\n    \"placeholder\": \"<PERSON><PERSON>n loại khách hàng...\",\n    \"options\": [\n      { \"title\": \"Cá nhân\", \"value\": \"individual\" },\n      { \"title\": \"Doanh nghiệp nhỏ\", \"value\": \"small_business\" },\n      { \"title\": \"Doanh nghiệp vừa\", \"value\": \"medium_business\" },\n      { \"title\": \"Doanh nghiệp lớn\", \"value\": \"large_business\" },\n      { \"title\": \"<PERSON><PERSON><PERSON> tác chiến lược\", \"value\": \"strategic_partner\" },\n      { \"title\": \"Nhà cung cấp\", \"value\": \"supplier\" }\n    ],\n    \"defaultValue\": \"individual\"\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/marketing/audience-custom-fields/customer_type", "host": ["{{base_url}}"], "path": ["admin", "marketing", "audience-custom-fields", "customer_type"]}}}, {"name": "Update Company Revenue NUMBER", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"displayName\": \"<PERSON><PERSON><PERSON> thu công ty (VND)\",\n  \"description\": \"<PERSON><PERSON>h thu hàng năm của công ty\",\n  \"config\": {\n    \"placeholder\": \"<PERSON><PERSON><PERSON>p doanh thu hàng năm...\",\n    \"defaultValue\": 1000000000,\n    \"minValue\": 0,\n    \"maxValue\": 1000000000000\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/marketing/audience-custom-fields/company_revenue", "host": ["{{base_url}}"], "path": ["admin", "marketing", "audience-custom-fields", "company_revenue"]}}}, {"name": "Change TEXT to OBJECT - Company Info", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"dataType\": \"object\",\n  \"displayName\": \"Thông tin công ty chi tiết\",\n  \"config\": {\n    \"placeholder\": \"Nhập thông tin công ty...\",\n    \"defaultValue\": {\n      \"name\": \"\",\n      \"taxCode\": \"\",\n      \"address\": \"\",\n      \"phone\": \"\",\n      \"email\": \"\",\n      \"website\": \"\",\n      \"industry\": \"\",\n      \"employeeCount\": 0,\n      \"foundedYear\": 2000\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/marketing/audience-custom-fields/company_info", "host": ["{{base_url}}"], "path": ["admin", "marketing", "audience-custom-fields", "company_info"]}}}]}, {"name": "Real-world Examples", "item": [{"name": "Email Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"fieldKey\": \"email\",\n  \"displayName\": \"Email\",\n  \"dataType\": \"text\",\n  \"description\": \"Địa chỉ email của khách hàng\",\n  \"tags\": [\"contact\", \"required\"],\n  \"config\": {\n    \"placeholder\": \"<PERSON><PERSON>ập địa chỉ email...\",\n    \"pattern\": \"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$\",\n    \"maxLength\": 255\n  }\n}"}, "url": {"raw": "{{base_url}}/user/marketing/audience-custom-fields", "host": ["{{base_url}}"], "path": ["user", "marketing", "audience-custom-fields"]}}}, {"name": "Phone Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"fieldKey\": \"phone\",\n  \"displayName\": \"Số điện thoại\",\n  \"dataType\": \"text\",\n  \"description\": \"<PERSON><PERSON> điện thoại liên hệ\",\n  \"tags\": [\"contact\"],\n  \"config\": {\n    \"placeholder\": \"<PERSON><PERSON><PERSON><PERSON> số điện thoạ<PERSON> (VD: 0912345678)\",\n    \"pattern\": \"^(0|\\\\+84)[0-9]{9,10}$\",\n    \"minLength\": 10,\n    \"maxLength\": 12\n  }\n}"}, "url": {"raw": "{{base_url}}/user/marketing/audience-custom-fields", "host": ["{{base_url}}"], "path": ["user", "marketing", "audience-custom-fields"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "user_token", "value": "your_user_jwt_token_here", "type": "string"}, {"key": "admin_token", "value": "your_admin_jwt_token_here", "type": "string"}]}