import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc xóa knowledge files khỏi agent
 */
export class RemoveKnowledgeFilesDto {
  /**
   * <PERSON><PERSON> sách ID của knowledge files cần xóa
   */
  @ApiProperty({
    description: 'Danh sách ID của knowledge files cần xóa',
    example: ['uuid-knowledge-file-1', 'uuid-knowledge-file-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  fileIds: string[];
}
