# Test API Filter hasPhoneNumber

## M<PERSON> tả
Đã thêm trường filter `hasPhoneNumber` vào API GET `/v1/marketing/audiences` để lọc audience theo việc có số điện thoại hay không.

## C<PERSON><PERSON> thay đổi đã thực hiện

### 1. DTO Updates
- **src/modules/marketing/user/dto/audience/audience-query.dto.ts**: Thêm trường `hasPhoneNumber?: boolean`
- **src/modules/marketing/user/dto/audience/audience-all-query.dto.ts**: Thêm trường `hasPhoneNumber?: boolean`
- **src/modules/marketing/admin/dto/audience/audience-query.dto.ts**: Thêm trường `hasPhoneNumber?: boolean`

### 2. Service Updates
- **src/modules/marketing/user/services/user-audience.service.ts**: 
  - Thêm logic xử lý filter `hasPhoneNumber` trong method `findAll()` và `findAllAudiences()`
  - Sử dụng TypeORM operators `Not(IsNull())` và `IsNull()` để filter
- **src/modules/marketing/admin/services/admin-audience.service.ts**: 
  - Thêm logic xử lý filter `hasPhoneNumber` trong method `findAll()`

### 3. Controller Updates
- **src/modules/marketing/user/controllers/user-audience.controller.ts**: Thêm ApiQuery documentation cho `hasPhoneNumber`
- **src/modules/marketing/admin/controllers/admin-audience.controller.ts**: Cập nhật ApiOperation description

## Cách sử dụng API

### Lấy audience có số điện thoại
```
GET /v1/marketing/audiences?hasPhoneNumber=true
```

### Lấy audience không có số điện thoại
```
GET /v1/marketing/audiences?hasPhoneNumber=false
```

### Kết hợp với các filter khác
```
GET /v1/marketing/audiences?hasPhoneNumber=true&platform=ZALO&page=1&limit=20
```

## Giá trị trả về
- `hasPhoneNumber=true`: Chỉ trả về audience có `phoneNumber` không null và không rỗng (`phoneNumber IS NOT NULL AND phoneNumber != ''`)
- `hasPhoneNumber=false`: Chỉ trả về audience có `phoneNumber` là null hoặc rỗng (`phoneNumber IS NULL OR phoneNumber = ''`)
- Không truyền `hasPhoneNumber`: Trả về tất cả audience (không filter theo điều kiện này)

## Lưu ý quan trọng
Logic đã được cập nhật để xử lý cả trường hợp:
- `phoneNumber = null` (giá trị null trong database)
- `phoneNumber = ""` (chuỗi rỗng trong database)

Cả hai trường hợp trên đều được coi là "không có số điện thoại".

## Validation
- Trường `hasPhoneNumber` là optional boolean
- Hỗ trợ string conversion: "true" -> true, "false" -> false
- Validation error nếu giá trị không phải boolean hợp lệ

## Testing
Để test API này, có thể sử dụng các endpoint sau:

1. **User API**: `GET /v1/marketing/audiences?hasPhoneNumber=true`
2. **Admin API**: `GET /v1/admin/marketing/audiences?hasPhoneNumber=false`
3. **All Audiences API**: `GET /v1/marketing/audiences/all?hasPhoneNumber=true&limit=1000`
