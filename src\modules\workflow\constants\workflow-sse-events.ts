/**
 * Constants cho Workflow SSE Events
 * Định nghĩa các event types và Redis channels cho workflow real-time updates
 */

/**
 * Redis channels cho workflow events
 */
export const WORKFLOW_REDIS_CHANNELS = {
  EXECUTION_EVENTS: 'workflow:execution:events',
  NODE_EVENTS: 'workflow:node:events',
  WORKFLOW_EVENTS: 'workflow:workflow:events',
  TEST_EVENTS: 'workflow:test:events',
} as const;

/**
 * SSE Event types cho workflow
 */
export const WORKFLOW_SSE_EVENTS = {
  // Execution Events
  EXECUTION_STARTED: 'execution_started',
  EXECUTION_COMPLETED: 'execution_completed',
  EXECUTION_FAILED: 'execution_failed',
  EXECUTION_PAUSED: 'execution_paused',
  EXECUTION_RESUMED: 'execution_resumed',
  EXECUTION_CANCELLED: 'execution_cancelled',
  EXECUTION_PROGRESS: 'execution_progress',
  
  // Node Events
  NODE_STARTED: 'node_started',
  NODE_COMPLETED: 'node_completed',
  NODE_FAILED: 'node_failed',
  NODE_SKIPPED: 'node_skipped',
  NODE_PROGRESS: 'node_progress',
  
  // Workflow Events
  WORKFLOW_UPDATED: 'workflow_updated',
  WORKFLOW_DELETED: 'workflow_deleted',
  WORKFLOW_SHARED: 'workflow_shared',
  
  // Test Events
  NODE_TEST_STARTED: 'node_test_started',
  NODE_TEST_COMPLETED: 'node_test_completed',
  NODE_TEST_FAILED: 'node_test_failed',
  
  // Connection Events
  CONNECTION_ESTABLISHED: 'connection_established',
  CONNECTION_ERROR: 'connection_error',
} as const;

/**
 * Interface cho execution event data
 */
export interface WorkflowExecutionEventData {
  executionId: string;
  workflowId: string;
  userId: number;
  status: string;
  timestamp: number;
  progress?: {
    currentNode?: string;
    completedNodes: number;
    totalNodes: number;
    percentage: number;
  };
  result?: any;
  error?: {
    message: string;
    code?: string;
    stack?: string;
    nodeId?: string;
    timestamp?: number;
  } | null;
  metadata?: Record<string, any>;
}

/**
 * Interface cho node event data
 */
export interface WorkflowNodeEventData {
  executionId: string;
  workflowId: string;
  nodeId: string;
  nodeType: string;
  userId: number;
  status: string;
  timestamp: number;
  input?: any;
  output?: any;
  error?: {
    message: string;
    code?: string;
  };
  duration?: number;
  metadata?: Record<string, any>;
}

/**
 * Interface cho workflow event data
 */
export interface WorkflowEventData {
  workflowId: string;
  userId: number;
  action: string;
  timestamp: number;
  data?: any;
  metadata?: Record<string, any>;
}

/**
 * Interface cho test event data
 */
export interface WorkflowTestEventData {
  testId: string;
  nodeId: string;
  nodeType: string;
  userId: number;
  status: string;
  timestamp: number;
  input?: any;
  output?: any;
  error?: {
    message: string;
    code?: string;
  };
  duration?: number;
  metadata?: Record<string, any>;
}

/**
 * Union type cho tất cả workflow event data
 */
export type WorkflowEventDataUnion = 
  | WorkflowExecutionEventData
  | WorkflowNodeEventData
  | WorkflowEventData
  | WorkflowTestEventData;

/**
 * Interface cho SSE message format
 */
export interface WorkflowSseMessage {
  id: string;
  event: string;
  data: string;
  retry?: number;
}

/**
 * Interface cho formatted SSE event
 */
export interface WorkflowFormattedSseEvent {
  type: string;
  executionId?: string;
  workflowId?: string;
  nodeId?: string;
  testId?: string;
  userId: number;
  timestamp: number;
  data: any;
  metadata?: Record<string, any>;
}

/**
 * Event priority levels
 */
export const WORKFLOW_EVENT_PRIORITY = {
  LOW: 1,
  NORMAL: 2,
  HIGH: 3,
  CRITICAL: 4,
} as const;

/**
 * Event retention settings (in seconds)
 */
export const WORKFLOW_EVENT_RETENTION = {
  EXECUTION_EVENTS: 3600, // 1 hour
  NODE_EVENTS: 1800, // 30 minutes
  WORKFLOW_EVENTS: 7200, // 2 hours
  TEST_EVENTS: 900, // 15 minutes
} as const;

/**
 * SSE connection settings
 */
export const WORKFLOW_SSE_CONFIG = {
  HEARTBEAT_INTERVAL: 30000, // 30 seconds
  CONNECTION_TIMEOUT: 300000, // 5 minutes
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 5000, // 5 seconds
  MAX_CONNECTIONS_PER_USER: 10,
} as const;
