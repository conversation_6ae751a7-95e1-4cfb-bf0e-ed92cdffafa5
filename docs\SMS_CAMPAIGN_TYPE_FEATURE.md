# SMS Campaign Type Feature Documentation

## Tổng quan

Đã thêm tính năng phân loại chiến dịch SMS theo mục đích sử dụng, hỗ trợ hai loại chính:
- **OTP**: <PERSON> nhắn xác thực một lần
- **ADS**: Tin nhắn quảng cáo/marketing

## Tính năng mới

### 1. **Campaign Type Enum**

```typescript
export enum SmsCampaignType {
  OTP = 'OTP',  // SMS OTP - Tin nhắn xác thực
  ADS = 'ADS',  // SMS ADS - Tin nhắn quảng cáo
}
```

### 2. **API Updates**

#### Tạo SMS Campaign
```http
POST /api/marketing/sms-campaigns
```

**Request Body**:
```json
{
  "name": "Chiến dịch SMS OTP",
  "campaignType": "OTP",
  "content": "<PERSON><PERSON> xác thực của bạn là: {{otpCode}}",
  "smsServerId": 1,
  "segmentId": 5
}
```

#### Tạo SMS Campaign với Template
```http
POST /api/marketing/sms-campaigns/with-template
```

**Request Body**:
```json
{
  "name": "Chiến dịch SMS quảng cáo",
  "campaignType": "ADS",
  "templateId": 1,
  "smsServerId": 1,
  "audienceIds": [1, 2, 3]
}
```

### 3. **Response Updates**

**Campaign List Response**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 123,
        "name": "Chiến dịch SMS khuyến mãi",
        "campaignType": "ADS",
        "status": "SENT",
        "totalRecipients": 150,
        "sentCount": 145,
        "failedCount": 5,
        "successRate": 96.7
      }
    ]
  }
}
```

## Database Changes

### Schema Update
```sql
-- Thêm cột campaign_type
ALTER TABLE sms_campaign_user 
ADD COLUMN campaign_type VARCHAR(10) DEFAULT 'ADS' NOT NULL;

-- Thêm constraint
ALTER TABLE sms_campaign_user 
ADD CONSTRAINT chk_campaign_type 
CHECK (campaign_type IN ('OTP', 'ADS'));

-- Tạo index
CREATE INDEX idx_sms_campaign_user_campaign_type 
ON sms_campaign_user(campaign_type);
```

### Migration Script
```bash
# Windows
.\scripts\run-add-campaign-type-migration.ps1

# Linux/Mac
./scripts/run-add-campaign-type-migration.sh
```

## Queue Integration

### Job Data Update
```typescript
interface SmsMarketingJobDto {
  campaignId: number;
  campaignType: string;  // 'OTP' hoặc 'ADS'
  templateId: number;
  recipients: SmsRecipientDto[];
  smsServerId: number;
  content?: string;
}
```

### Worker Processing
Worker có thể sử dụng `campaignType` để:
- Áp dụng logic xử lý khác nhau
- Thiết lập độ ưu tiên (OTP có độ ưu tiên cao hơn)
- Logging và monitoring riêng biệt
- Rate limiting khác nhau

## Use Cases

### 1. **OTP Campaigns**
```json
{
  "name": "Xác thực đăng nhập",
  "campaignType": "OTP",
  "content": "Mã OTP của bạn là: {{otpCode}}. Có hiệu lực trong 5 phút.",
  "smsServerId": 1,
  "audienceIds": [123]
}
```

**Đặc điểm**:
- Gửi ngay lập tức
- Nội dung ngắn gọn
- Độ ưu tiên cao
- Thời gian hiệu lực ngắn

### 2. **ADS Campaigns**
```json
{
  "name": "Khuyến mãi Black Friday",
  "campaignType": "ADS",
  "content": "🔥 BLACK FRIDAY - Giảm 50% tất cả sản phẩm! Mã: BF2024",
  "smsServerId": 1,
  "segmentId": 5,
  "scheduledAt": 1703980800
}
```

**Đặc điểm**:
- Có thể lên lịch gửi
- Nội dung phong phú
- Hỗ trợ personalization
- Theo dõi hiệu quả

## Implementation Details

### 1. **Files Updated**

#### Enum
- `src/modules/marketing/enums/sms-campaign-type.enum.ts`

#### DTOs
- `src/modules/marketing/user/dto/sms-campaign/create-sms-campaign.dto.ts`
- `src/modules/marketing/user/dto/sms-campaign/sms-campaign-response.dto.ts`
- `src/modules/marketing/user/dto/sms-campaign/sms-marketing-job.dto.ts`

#### Entity
- `src/modules/marketing/user/entities/sms-campaign-user.entity.ts`

#### Service
- `src/modules/marketing/user/services/sms-campaign.service.ts`

#### Controller
- `src/modules/marketing/user/controllers/sms-campaign.controller.ts`

#### Migration
- `src/modules/marketing/user/migrations/add-campaign-type-to-sms-campaign.sql`

### 2. **Validation Rules**

```typescript
@IsEnum(SmsCampaignType, { 
  message: 'Loại chiến dịch phải là OTP hoặc ADS' 
})
campaignType: SmsCampaignType;
```

### 3. **Default Values**
- Entity default: `SmsCampaignType.ADS`
- Database default: `'ADS'`

## Testing

### 1. **API Testing**
```bash
# Test OTP campaign
curl -X POST http://localhost:3000/api/marketing/sms-campaigns \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Test OTP Campaign",
    "campaignType": "OTP",
    "content": "Your OTP: {{code}}",
    "smsServerId": 1,
    "audienceIds": [1]
  }'

# Test ADS campaign
curl -X POST http://localhost:3000/api/marketing/sms-campaigns \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Test ADS Campaign",
    "campaignType": "ADS",
    "content": "Special offer for you!",
    "smsServerId": 1,
    "segmentId": 1
  }'
```

### 2. **Database Testing**
```sql
-- Kiểm tra constraint
INSERT INTO sms_campaign_user (campaign_type) VALUES ('INVALID'); -- Sẽ lỗi

-- Kiểm tra default value
INSERT INTO sms_campaign_user (name, user_id) VALUES ('Test', 1);
SELECT campaign_type FROM sms_campaign_user WHERE name = 'Test'; -- Trả về 'ADS'
```

## Benefits

### 1. **Phân loại rõ ràng**
- Dễ dàng phân biệt mục đích sử dụng
- Quản lý và báo cáo tách biệt
- Tuân thủ quy định khác nhau

### 2. **Tối ưu hóa xử lý**
- OTP: Độ ưu tiên cao, gửi ngay
- ADS: Có thể batch processing, lên lịch

### 3. **Monitoring & Analytics**
- Thống kê riêng cho từng loại
- Tracking hiệu quả khác nhau
- Alerting phù hợp

### 4. **Compliance**
- Tuân thủ quy định về SMS marketing
- Phân biệt tin nhắn giao dịch vs quảng cáo
- Audit trail rõ ràng

## Future Enhancements

### 1. **Priority Queue**
```typescript
// Trong worker
const priority = campaignType === 'OTP' ? 10 : 1;
await smsQueue.add(jobName, jobData, { priority });
```

### 2. **Rate Limiting**
```typescript
// OTP: Không giới hạn
// ADS: Giới hạn theo thời gian
const rateLimit = campaignType === 'OTP' ? null : { max: 100, duration: 3600 };
```

### 3. **Template Categories**
```typescript
enum TemplateCategory {
  OTP_VERIFICATION = 'OTP_VERIFICATION',
  OTP_TRANSACTION = 'OTP_TRANSACTION',
  ADS_PROMOTION = 'ADS_PROMOTION',
  ADS_NOTIFICATION = 'ADS_NOTIFICATION'
}
```

## Conclusion

Tính năng campaign type đã được implement thành công với:
- ✅ Database schema update
- ✅ API endpoints support
- ✅ Queue integration
- ✅ Validation & constraints
- ✅ Documentation & testing

Hệ thống giờ đây có thể phân biệt và xử lý riêng biệt các loại SMS campaign khác nhau! 🎉
