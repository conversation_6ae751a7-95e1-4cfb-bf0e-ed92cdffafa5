import { ProviderLlmEnum } from '../constants';
import { 
  InputModalityEnum, 
  OutputModalityEnum, 
  SamplingParameterEnum, 
  FeatureEnum 
} from '../constants/model-capabilities.enum';
import { ModelPricingInterface } from './pricing.interface';

/**
 * Interface cho system model kết hợp với thông tin model registry
 */
export interface SystemModelWithRegistryInterface {
  id: string;
  modelId: string;
  active: boolean;
  provider: ProviderLlmEnum;
  modelNamePattern: string;
  inputModalities: InputModalityEnum[];
  outputModalities: OutputModalityEnum[];
  samplingParameters: SamplingParameterEnum[];
  features: FeatureEnum[];
  basePricing: ModelPricingInterface;
  fineTunePricing: ModelPricingInterface;
  trainingPricing: number;
}

/**
 * Interface cho raw result từ database query
 */
export interface SystemModelRawResult {
  systemModels_id: string;
  systemModels_modelId: string;
  systemModels_active: boolean;
  registry_provider: ProviderLlmEnum;
  registry_modelNamePattern: string;
  registry_inputModalities: InputModalityEnum[];
  registry_outputModalities: OutputModalityEnum[];
  registry_samplingParameters: SamplingParameterEnum[];
  registry_features: FeatureEnum[];
  registry_basePricing: ModelPricingInterface;
  registry_fineTunePricing: ModelPricingInterface;
  registry_trainingPricing: number;
}
