export * from './execution-status.enum';
export * from './step-status.enum';
export * from './workflow-error-codes';
export * from './workflow-sse-events';
export { NodeCategory } from '../entities/node-definition.entity';

/**
 * Swagger API Tags cho workflow module
 */
export const SWAGGER_API_TAGS = {
  // Admin tags
  ADMIN_WORKFLOWS: 'Admin - Workflows',
  ADMIN_WORKFLOW_DEFINITIONS: 'Admin - Workflow Definitions',
  ADMIN_NODE_DEFINITIONS: 'Admin - Node Definitions',
  ADMIN_WORKFLOW_EXECUTIONS: 'Admin - Workflow Executions',

  // User tags
  USER_WORKFLOWS: 'User - Workflows',
  USER_WORKFLOW_DEFINITIONS: 'User - Workflow Definitions',
  USER_NODE_DEFINITIONS: 'User - Node Definitions',
  USER_WORKFLOW_EXECUTIONS: 'User - Workflow Executions',
} as const;
