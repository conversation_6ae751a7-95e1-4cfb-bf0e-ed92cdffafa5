import { Test, TestingModule } from '@nestjs/testing';
import { FlashSaleUserService } from '../../services/flash-sale-user.service';
import { FlashSaleRepository } from '../../../repositories/flash-sale.repository';
import { ProductRepository } from '../../../repositories/product.repository';
import { FlashSaleValidationHelper } from '../../../helpers/flash-sale-validation.helper';
import { FlashSale } from '../../../entities/flash-sale.entity';
import { FlashSaleStatus } from '../../../enums/flash-sale-status.enum';

describe('FlashSaleUserService - Image URL Transformation', () => {
  let service: FlashSaleUserService;
  let flashSaleRepository: jest.Mocked<FlashSaleRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FlashSaleUserService,
        {
          provide: FlashSaleRepository,
          useValue: {
            findByIdWithProduct: jest.fn(),
            findByUserId: jest.fn(),
            getRandomFlashSaleProducts: jest.fn(),
          },
        },
        {
          provide: ProductRepository,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: FlashSaleValidationHelper,
          useValue: {
            validateOwnership: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<FlashSaleUserService>(FlashSaleUserService);
    flashSaleRepository = module.get(FlashSaleRepository);
  });

  describe('Image URL Transformation', () => {
    it('should transform product images to include URL field', async () => {
      // Arrange
      const mockFlashSale: FlashSale = {
        id: 1,
        productId: 123,
        userId: 456,
        employeeId: null,
        discountPercentage: 20,
        displayTime: 30,
        startTime: Date.now(),
        endTime: Date.now() + 3600000,
        maxConfiguration: { maxPerUser: 3, totalInventory: 1000 },
        status: FlashSaleStatus.ACTIVE,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        product: {
          id: 123,
          name: 'Test Product',
          description: 'Test Description',
          listedPrice: 1000,
          discountedPrice: 800,
          images: [
            {
              key: 'marketplace/IMAGE/2025/07/1751968717292-218a4f40-4ca5-4d8d-8777-d571bef0a893',
              position: 0
            },
            {
              key: 'marketplace/IMAGE/2025/07/another-image-key',
              position: 1
            }
          ],
          category: 'AGENT',
          status: 'APPROVED',
          userId: 456
        },
        salePrice: 640,
        timeRemaining: 3600,
        soldCount: 0
      } as FlashSale;

      flashSaleRepository.findByIdWithProduct.mockResolvedValue(mockFlashSale);

      // Act
      const result = await service.getUserFlashSaleById(456, 1);

      // Assert
      expect(result.product).toBeDefined();
      expect(result.product.images).toHaveLength(2);
      
      // Check first image
      expect(result.product.images[0]).toEqual({
        key: 'marketplace/IMAGE/2025/07/1751968717292-218a4f40-4ca5-4d8d-8777-d571bef0a893',
        position: 0,
        url: 'https://cdn.redai.vn/marketplace/IMAGE/2025/07/1751968717292-218a4f40-4ca5-4d8d-8777-d571bef0a893'
      });

      // Check second image
      expect(result.product.images[1]).toEqual({
        key: 'marketplace/IMAGE/2025/07/another-image-key',
        position: 1,
        url: 'https://cdn.redai.vn/marketplace/IMAGE/2025/07/another-image-key'
      });
    });

    it('should handle empty or null images array', async () => {
      // Arrange
      const mockFlashSale: FlashSale = {
        id: 1,
        productId: 123,
        userId: 456,
        employeeId: null,
        discountPercentage: 20,
        displayTime: 30,
        startTime: Date.now(),
        endTime: Date.now() + 3600000,
        maxConfiguration: { maxPerUser: 3, totalInventory: 1000 },
        status: FlashSaleStatus.ACTIVE,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        product: {
          id: 123,
          name: 'Test Product',
          description: 'Test Description',
          listedPrice: 1000,
          discountedPrice: 800,
          images: null,
          category: 'AGENT',
          status: 'APPROVED',
          userId: 456
        },
        salePrice: 640,
        timeRemaining: 3600,
        soldCount: 0
      } as FlashSale;

      flashSaleRepository.findByIdWithProduct.mockResolvedValue(mockFlashSale);

      // Act
      const result = await service.getUserFlashSaleById(456, 1);

      // Assert
      expect(result.product).toBeDefined();
      expect(result.product.images).toEqual([]);
    });

    it('should handle images without position field', async () => {
      // Arrange
      const mockFlashSale: FlashSale = {
        id: 1,
        productId: 123,
        userId: 456,
        employeeId: null,
        discountPercentage: 20,
        displayTime: 30,
        startTime: Date.now(),
        endTime: Date.now() + 3600000,
        maxConfiguration: { maxPerUser: 3, totalInventory: 1000 },
        status: FlashSaleStatus.ACTIVE,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        product: {
          id: 123,
          name: 'Test Product',
          description: 'Test Description',
          listedPrice: 1000,
          discountedPrice: 800,
          images: [
            {
              key: 'marketplace/IMAGE/2025/07/image-without-position'
              // No position field
            }
          ],
          category: 'AGENT',
          status: 'APPROVED',
          userId: 456
        },
        salePrice: 640,
        timeRemaining: 3600,
        soldCount: 0
      } as FlashSale;

      flashSaleRepository.findByIdWithProduct.mockResolvedValue(mockFlashSale);

      // Act
      const result = await service.getUserFlashSaleById(456, 1);

      // Assert
      expect(result.product).toBeDefined();
      expect(result.product.images).toHaveLength(1);
      expect(result.product.images[0]).toEqual({
        key: 'marketplace/IMAGE/2025/07/image-without-position',
        position: 0, // Should default to index
        url: 'https://cdn.redai.vn/marketplace/IMAGE/2025/07/image-without-position'
      });
    });
  });
});
