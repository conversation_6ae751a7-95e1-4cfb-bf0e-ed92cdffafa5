import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsUUID } from 'class-validator';

/**
 * DTO cho cấu hình shipment của agent
 */
export class ShipmentConfigDto {
  /**
   * UUID tham chiếu đến bảng user_provider_shipments
   */
  @ApiPropertyOptional({
    description: 'UUID tham chiếu đến bảng user_provider_shipments',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID('4', { message: 'userProviderShipmentId phải là UUID hợp lệ' })
  userProviderShipmentId?: string;

  /**
   * Người nhận có trả phí vận chuyển không (true = người nhận trả, false = người gửi trả)
   */
  @ApiProperty({
    description: 'Người nhận có trả phí vận chuyển không (true = người nhận trả, false = người gửi trả)',
    example: true,
    default: true,
  })
  @IsBoolean({ message: 'receiverPayShippingFee phải là boolean' })
  receiverPayShippingFee: boolean;
}

/**
 * DTO cho cập nhật cấu hình shipment của agent
 */
export class UpdateShipmentConfigDto {
  /**
   * UUID tham chiếu đến bảng user_provider_shipments
   */
  @ApiPropertyOptional({
    description: 'UUID tham chiếu đến bảng user_provider_shipments',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID('4', { message: 'userProviderShipmentId phải là UUID hợp lệ' })
  userProviderShipmentId: string;

  /**
   * Người nhận có trả phí vận chuyển không (true = người nhận trả, false = người gửi trả)
   */
  @ApiPropertyOptional({
    description: 'Người nhận có trả phí vận chuyển không (true = người nhận trả, false = người gửi trả)',
    example: true,
  })
  @IsOptional()
  @IsBoolean({ message: 'receiverPayShippingFee phải là boolean' })
  receiverPayShippingFee: boolean;
}

/**
 * DTO cho thông tin nhà cung cấp vận chuyển
 */
export class ShipmentProviderInfoDto {
  /**
   * UUID của cấu hình nhà cung cấp vận chuyển
   */
  @ApiProperty({
    description: 'UUID của cấu hình nhà cung cấp vận chuyển',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  /**
   * Tên hiển thị của cấu hình tích hợp
   */
  @ApiProperty({
    description: 'Tên hiển thị của cấu hình tích hợp',
    example: 'Cấu hình GHTK chính',
  })
  name: string;

  /**
   * Loại nhà cung cấp vận chuyển
   */
  @ApiProperty({
    description: 'Loại nhà cung cấp vận chuyển',
    example: 'GHTK',
    enum: ['GHN', 'GHTK', 'AHAMOVE', 'JT'],
  })
  type: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1703980800000,
  })
  createdAt: number;
}

/**
 * DTO cho response cấu hình shipment của agent
 */
export class ShipmentConfigResponseDto {
  /**
   * UUID tham chiếu đến bảng user_provider_shipments
   */
  @ApiPropertyOptional({
    description: 'UUID tham chiếu đến bảng user_provider_shipments',
    example: '123e4567-e89b-12d3-a456-************',
  })
  userProviderShipmentId?: string;

  /**
   * Người nhận có trả phí vận chuyển không (true = người nhận trả, false = người gửi trả)
   */
  @ApiProperty({
    description: 'Người nhận có trả phí vận chuyển không (true = người nhận trả, false = người gửi trả)',
    example: true,
  })
  receiverPayShippingFee: boolean;

  /**
   * Thông tin chi tiết nhà cung cấp vận chuyển
   */
  @ApiPropertyOptional({
    description: 'Thông tin chi tiết nhà cung cấp vận chuyển',
    type: ShipmentProviderInfoDto,
  })
  providerInfo?: ShipmentProviderInfoDto | null;
}
