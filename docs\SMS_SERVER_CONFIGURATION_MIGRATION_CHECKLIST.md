# SMS Server Configuration Migration Checklist

## Pre-Migration Verification ✅

### Data Status Check
- [ ] All SMS campaigns migrated to use Integration entity
- [ ] All SmsServerConfiguration data migrated to Integration table
- [ ] No active SMS campaigns using sms_server_id
- [ ] Integration entity working properly for SMS

### Verification Queries
```sql
-- Check unmigrated SMS server configurations
SELECT COUNT(*) FROM sms_server_configurations;
-- Should return 0 or all should be migrated to integration

-- Check SMS campaigns using old structure
SELECT COUNT(*) FROM sms_campaign_user WHERE sms_server_id IS NOT NULL;
SELECT COUNT(*) FROM sms_campaign_admin WHERE sms_server_id IS NOT NULL;
-- Should return 0

-- Check SMS integrations exist
SELECT COUNT(*) FROM integration i
JOIN integration_providers ip ON i.type_id = ip.id
WHERE ip.type IN ('SMS_FPT', 'SMS_TWILIO', 'SMS_VONAGE');
-- Should return > 0
```

## Phase 1: Update Services 🔄

### Update admin-twilio-sms.service.ts
- [ ] Replace SmsServerConfiguration import with Integration
- [ ] Replace SmsServerConfigurationRepository with IntegrationRepository
- [ ] Add KeyPairEncryptionService import and injection
- [ ] Update createTwilioConfig method to create Integration
- [ ] Add encryption logic for sensitive data
- [ ] Update getTwilioConfig method to decrypt data
- [ ] Add testTwilioConfig method
- [ ] Add updateTwilioConfig method
- [ ] Add deleteTwilioConfig method
- [ ] Test Twilio SMS functionality

### Update sms-server-configuration-admin.service.ts (if keeping functionality)
- [ ] Replace SmsServerConfiguration with Integration
- [ ] Update createFptSmsBrandname method
- [ ] Update createTwilioConfig method
- [ ] Add encryption for sensitive data
- [ ] Update metadata structure
- [ ] Test FPT SMS functionality

### Update sms-server-configuration-user.service.ts (if keeping functionality)
- [ ] Replace SmsServerConfiguration with Integration
- [ ] Update user SMS configuration methods
- [ ] Add encryption logic
- [ ] Update response DTOs
- [ ] Test user SMS functionality

## Phase 2: Remove Files 🗑️

### Controllers
- [ ] Remove `src/modules/integration/admin/controllers/sms-server-configuration-admin.controller.ts`
- [ ] Remove `src/modules/integration/user/controllers/sms-server-configuration-user.controller.ts`
- [ ] Move necessary functionality to integration controllers

### Services
- [ ] Remove `src/modules/integration/admin/services/sms-server-configuration-admin.service.ts`
- [ ] Remove `src/modules/integration/user/services/sms-server-configuration-user.service.ts`
- [ ] Ensure functionality moved to integration services

### DTOs
- [ ] Remove `src/modules/integration/user/dto/sms/update-sms-server.dto.ts`
- [ ] Remove `src/modules/integration/admin/dto/sms-server-admin-response.dto.ts`
- [ ] Update integration DTOs if needed

### Entity & Repository
- [ ] Remove `src/modules/integration/entities/sms-server-configuration.entity.ts`
- [ ] Remove `src/modules/integration/repositories/sms-server-configuration.repository.ts`

## Phase 3: Update Module Configurations 📦

### Entity Exports
- [ ] Remove from `src/modules/integration/entities/index.ts`:
  ```typescript
  // Remove: export * from './sms-server-configuration.entity';
  ```

### Repository Exports
- [ ] Remove from `src/modules/integration/repositories/index.ts`:
  ```typescript
  // Remove: export * from './sms-server-configuration.repository';
  ```

### DTO Exports
- [ ] Remove from `src/modules/integration/admin/dto/index.ts`:
  ```typescript
  // Remove: export * from './sms-server-admin-response.dto';
  ```

### Module Providers
- [ ] Remove SmsServerConfigurationRepository from:
  - [ ] `src/modules/integration/integration.module.ts`
  - [ ] `src/modules/integration/admin/integration-admin.module.ts`
  - [ ] `src/modules/integration/user/integration-user.module.ts`
  - [ ] `src/modules/marketing/admin/marketing-admin.module.ts`

### Module Controllers
- [ ] Remove SmsServerConfiguration controllers from:
  - [ ] `src/modules/integration/admin/integration-admin.module.ts`
  - [ ] `src/modules/integration/user/integration-user.module.ts`

### Module Services
- [ ] Remove SmsServerConfiguration services from:
  - [ ] `src/modules/integration/admin/integration-admin.module.ts`
  - [ ] `src/modules/integration/user/integration-user.module.ts`

## Phase 4: Database Cleanup 🗄️

### Pre-Database Cleanup
- [ ] Create full database backup
- [ ] Verify no foreign key constraints reference sms_server_configurations
- [ ] Confirm all data migrated to integration table

### Run Database Migration
- [ ] Execute: `./scripts/remove-sms-server-configuration.sh`
- [ ] Verify backup table created: `sms_server_configurations_backup`
- [ ] Confirm original table dropped
- [ ] Check unused columns removed from other tables

### Post-Database Verification
```sql
-- Verify table is gone
SELECT * FROM information_schema.tables 
WHERE table_name = 'sms_server_configurations';
-- Should return no rows

-- Verify backup exists
SELECT COUNT(*) FROM sms_server_configurations_backup;
-- Should return count of backed up records
```

## Phase 5: File Cleanup 🧹

### Migration Files
- [ ] Remove `database/migrations/migrate-sms-server-to-integration.sql`
- [ ] Remove `database/migrations/add-integration-provider-id-to-sms-server-configurations.sql`
- [ ] Remove `scripts/run-sms-server-configuration-migration.sh`
- [ ] Remove `scripts/run-sms-server-configuration-migration.ps1`

### Documentation
- [ ] Remove `src/modules/integration/docs/sms-plan.md`
- [ ] Update API documentation
- [ ] Update integration guides

### Search for Remaining References
```bash
# Search for any remaining references
grep -r "SmsServerConfiguration" src/ --include="*.ts"
grep -r "SmsServerConfigurationRepository" src/ --include="*.ts"
grep -r "sms-server-configuration" src/ --include="*.ts"
```

## Phase 6: Testing 🧪

### Compilation Testing
- [ ] No TypeScript errors: `npm run build`
- [ ] No ESLint warnings: `npm run lint`
- [ ] Application starts: `npm run start:dev`

### Functional Testing
- [ ] SMS campaigns can be created with Integration
- [ ] SMS campaigns can be sent
- [ ] Twilio SMS integration works
- [ ] FPT SMS integration works
- [ ] SMS configuration management works
- [ ] Encryption/decryption works correctly

### Integration Testing
- [ ] Admin SMS functionality works
- [ ] User SMS functionality works
- [ ] API endpoints respond correctly
- [ ] No errors in application logs

### Performance Testing
- [ ] No performance degradation
- [ ] Database queries optimized
- [ ] No memory leaks

## Phase 7: Documentation Update 📚

### API Documentation
- [ ] Update Swagger documentation
- [ ] Remove SmsServerConfiguration endpoints
- [ ] Update Integration endpoints for SMS

### Code Documentation
- [ ] Update code comments
- [ ] Update README files
- [ ] Update integration guides

### Database Documentation
- [ ] Update schema documentation
- [ ] Update ER diagrams
- [ ] Update migration history

## Rollback Plan 🔄

### If Issues Occur:
1. **Stop application immediately**
2. **Restore database from backup**:
   ```bash
   psql -h $DB_HOST -d $DB_NAME -U $DB_USER < backup_before_sms_cleanup_YYYYMMDD_HHMMSS.sql
   ```
3. **Restore code from git**:
   ```bash
   git checkout HEAD~1  # or specific commit
   ```
4. **Restore backed up files**:
   ```bash
   cp src/modules/marketing/admin/services/admin-twilio-sms.service.ts.backup src/modules/marketing/admin/services/admin-twilio-sms.service.ts
   ```
5. **Restart application and investigate**

## Success Criteria ✅

### Technical Success
- [ ] Application compiles without errors
- [ ] All SMS functionality works through Integration
- [ ] No SmsServerConfiguration references remain
- [ ] Database cleaned up successfully
- [ ] Performance maintained or improved

### Business Success
- [ ] SMS campaigns work normally
- [ ] Users can manage SMS integrations
- [ ] No service disruption
- [ ] All SMS providers functional

### Security Success
- [ ] Sensitive data properly encrypted
- [ ] No security vulnerabilities introduced
- [ ] Access controls maintained

## Final Sign-off 📝

- [ ] **Developer**: Code changes reviewed and tested
- [ ] **QA**: Functional testing completed
- [ ] **DevOps**: Database migration verified
- [ ] **Product**: Business functionality confirmed
- [ ] **Security**: Security review completed

**Completion Date**: ___________
**Completed By**: ___________
**Notes**: ___________

## Quick Commands

### Run Migration Script
```bash
chmod +x scripts/migrate-sms-server-configuration-to-integration.sh
./scripts/migrate-sms-server-configuration-to-integration.sh
```

### Check for References
```bash
grep -r "SmsServerConfiguration" src/ --include="*.ts" | wc -l
```

### Test Compilation
```bash
npm run build && echo "✅ Build successful" || echo "❌ Build failed"
```
