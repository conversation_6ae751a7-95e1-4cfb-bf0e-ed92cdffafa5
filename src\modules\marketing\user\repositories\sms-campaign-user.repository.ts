import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { SmsCampaignUser, SmsCampaignStatus } from '../entities/sms-campaign-user.entity';

/**
 * Repository cho SmsCampaignUser entity
 */
@Injectable()
export class SmsCampaignUserRepository extends Repository<SmsCampaignUser> {
  constructor(private dataSource: DataSource) {
    super(SmsCampaignUser, dataSource.createEntityManager());
  }

  /**
   * Tìm tất cả campaign SMS của user với phân trang
   */
  async findByUserIdWithPagination(
    userId: number,
    page: number = 1,
    limit: number = 20,
    search?: string,
    status?: SmsCampaignStatus,
    sortBy?: string,
    sortDirection?: 'ASC' | 'DESC'
  ): Promise<{ campaigns: SmsCampaignUser[]; total: number }> {
    const queryBuilder = this.createQueryBuilder('campaign')
      .where('campaign.userId = :userId', { userId });

    if (search) {
      queryBuilder.andWhere(
        '(campaign.name ILIKE :search OR campaign.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (status) {
      queryBuilder.andWhere('campaign.status = :status', { status });
    }

    const total = await queryBuilder.getCount();

    // Xử lý sắp xếp
    const validSortFields = ['createdAt', 'updatedAt', 'name', 'status', 'scheduledAt', 'totalRecipients'];
    const finalSortBy = sortBy && validSortFields.includes(sortBy) ? sortBy : 'createdAt';
    const finalSortDirection = sortDirection === 'ASC' ? 'ASC' : 'DESC';

    const campaigns = await queryBuilder
      .orderBy(`campaign.${finalSortBy}`, finalSortDirection)
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    return { campaigns, total };
  }

  /**
   * Tìm campaign SMS theo ID và user ID
   */
  async findByIdAndUserId(id: number, userId: number): Promise<SmsCampaignUser | null> {
    return this.findOne({
      where: { id, userId },
    });
  }

  /**
   * Tìm campaign SMS theo tên và user ID
   */
  async findByNameAndUserId(name: string, userId: number): Promise<SmsCampaignUser | null> {
    return this.findOne({
      where: { name, userId },
    });
  }

  /**
   * Kiểm tra xem campaign có tồn tại không
   */
  async existsByIdAndUserId(id: number, userId: number): Promise<boolean> {
    const count = await this.count({
      where: { id, userId },
    });
    return count > 0;
  }

  /**
   * Kiểm tra xem tên campaign đã tồn tại chưa
   */
  async existsByNameAndUserId(name: string, userId: number, excludeId?: number): Promise<boolean> {
    const queryBuilder = this.createQueryBuilder('campaign')
      .where('campaign.name = :name', { name })
      .andWhere('campaign.userId = :userId', { userId });

    if (excludeId) {
      queryBuilder.andWhere('campaign.id != :excludeId', { excludeId });
    }

    const count = await queryBuilder.getCount();
    return count > 0;
  }

  /**
   * Tạo campaign SMS mới
   */
  async createCampaign(campaignData: Partial<SmsCampaignUser>): Promise<SmsCampaignUser> {
    const now = Math.floor(Date.now() / 1000);
    const campaign = this.create({
      ...campaignData,
      createdAt: now,
      updatedAt: now,
    });
    return this.save(campaign);
  }

  /**
   * Cập nhật campaign SMS
   */
  async updateCampaign(id: number, userId: number, updateData: Partial<SmsCampaignUser>): Promise<SmsCampaignUser | null> {
    const campaign = await this.findByIdAndUserId(id, userId);
    if (!campaign) {
      return null;
    }

    const now = Math.floor(Date.now() / 1000);
    Object.assign(campaign, updateData, { updatedAt: now });
    return this.save(campaign);
  }

  /**
   * Xóa campaign SMS
   */
  async deleteCampaign(id: number, userId: number): Promise<boolean> {
    const result = await this.delete({ id, userId });
    return result.affected ? result.affected > 0 : false;
  }

  /**
   * Tìm campaigns theo trạng thái
   */
  async findByStatus(status: SmsCampaignStatus): Promise<SmsCampaignUser[]> {
    return this.find({
      where: { status },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm campaigns đã lên lịch cần gửi
   */
  async findScheduledCampaignsToSend(currentTime: number): Promise<SmsCampaignUser[]> {
    return this.createQueryBuilder('campaign')
      .where('campaign.status = :status', { status: SmsCampaignStatus.SCHEDULED })
      .andWhere('campaign.scheduledAt <= :currentTime', { currentTime })
      .getMany();
  }

  /**
   * Đếm số lượng campaign theo trạng thái của user
   */
  async countByUserIdAndStatus(userId: number, status?: SmsCampaignStatus): Promise<number> {
    const where: any = { userId };
    if (status) {
      where.status = status;
    }
    return this.count({ where });
  }

  /**
   * Lấy thống kê tổng quan của user với filter
   */
  async getOverviewStats(userId: number, filter?: {
    campaignIds?: number[];
    startTime?: number;
    endTime?: number;
    filterByCreatedAt?: boolean;
    filterByCompletedAt?: boolean;
  }): Promise<{
    totalCampaigns: number;
    draftCampaigns: number;
    scheduledCampaigns: number;
    sendingCampaigns: number;
    sentCampaigns: number;
    failedCampaigns: number;
    totalSent: number;
    totalFailed: number;
    activeCampaigns: number;
    totalCost: number;
  }> {
    const queryBuilder = this.createQueryBuilder('campaign')
      .select([
        'COUNT(*) as totalCampaigns',
        'COUNT(CASE WHEN campaign.status = :draft THEN 1 END) as draftCampaigns',
        'COUNT(CASE WHEN campaign.status = :scheduled THEN 1 END) as scheduledCampaigns',
        'COUNT(CASE WHEN campaign.status = :sending THEN 1 END) as sendingCampaigns',
        'COUNT(CASE WHEN campaign.status = :sent THEN 1 END) as sentCampaigns',
        'COUNT(CASE WHEN campaign.status = :failed THEN 1 END) as failedCampaigns',
        'SUM(campaign.sentCount) as totalSent',
        'SUM(campaign.failedCount) as totalFailed',
      ])
      .where('campaign.userId = :userId', { userId })
      .setParameters({
        draft: SmsCampaignStatus.DRAFT,
        scheduled: SmsCampaignStatus.SCHEDULED,
        sending: SmsCampaignStatus.SENDING,
        sent: SmsCampaignStatus.SENT,
        failed: SmsCampaignStatus.FAILED,
      });

    // Áp dụng filter nếu có
    if (filter) {
      // Filter theo danh sách campaign IDs
      if (filter.campaignIds && filter.campaignIds.length > 0) {
        queryBuilder.andWhere('campaign.id IN (:...campaignIds)', {
          campaignIds: filter.campaignIds
        });
      }

      // Filter theo khoảng thời gian
      if (filter.startTime || filter.endTime) {
        const timeField = filter.filterByCompletedAt ? 'campaign.completedAt' : 'campaign.createdAt';

        if (filter.startTime) {
          queryBuilder.andWhere(`${timeField} >= :startTime`, {
            startTime: filter.startTime
          });
        }

        if (filter.endTime) {
          queryBuilder.andWhere(`${timeField} <= :endTime`, {
            endTime: filter.endTime
          });
        }
      }
    }

    const result = await queryBuilder.getRawOne();

    // Đếm số campaigns đang hoạt động thay vì providers
    const activeCampaignsQuery = this.createQueryBuilder('campaign')
      .select('COUNT(*)', 'activeProviders')
      .where('campaign.userId = :userId', { userId })
      .andWhere('campaign.status IN (:...activeStatuses)', {
        activeStatuses: ['SENT', 'SENDING', 'SCHEDULED']
      });

    // Áp dụng cùng filter cho active campaigns
    if (filter) {
      if (filter.campaignIds && filter.campaignIds.length > 0) {
        activeCampaignsQuery.andWhere('campaign.id IN (:...campaignIds)', {
          campaignIds: filter.campaignIds
        });
      }

      if (filter.startTime || filter.endTime) {
        const timeField = filter.filterByCompletedAt ? 'campaign.completedAt' : 'campaign.createdAt';

        if (filter.startTime) {
          activeCampaignsQuery.andWhere(`${timeField} >= :startTime`, {
            startTime: filter.startTime
          });
        }

        if (filter.endTime) {
          activeCampaignsQuery.andWhere(`${timeField} <= :endTime`, {
            endTime: filter.endTime
          });
        }
      }
    }

    const activeProvidersResult = await activeCampaignsQuery.getRawOne();

    // Tính tổng chi phí (giả sử mỗi SMS có giá 30 VND)
    const costPerSms = 30; // VND
    const totalSent = parseInt(result.totalSent) || 0;
    const totalCost = totalSent * costPerSms;

    return {
      totalCampaigns: parseInt(result.totalCampaigns) || 0,
      draftCampaigns: parseInt(result.draftCampaigns) || 0,
      scheduledCampaigns: parseInt(result.scheduledCampaigns) || 0,
      sendingCampaigns: parseInt(result.sendingCampaigns) || 0,
      sentCampaigns: parseInt(result.sentCampaigns) || 0,
      failedCampaigns: parseInt(result.failedCampaigns) || 0,
      totalSent,
      totalFailed: parseInt(result.totalFailed) || 0,
      activeCampaigns: parseInt(activeProvidersResult.activeProviders) || 0,
      totalCost,
    };
  }
}
