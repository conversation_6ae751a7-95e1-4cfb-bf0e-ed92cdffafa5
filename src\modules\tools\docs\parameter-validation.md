# Tool Parameter Validation

Hệ thống validation cho tool parameters sử dụng thư viện [jsonschema](https://www.npmjs.com/package/jsonschema) để đảm bảo tính hợp lệ của các tham số tool theo chuẩn JSON Schema.

## Cách sử dụng

### 1. Validation tự động

Validation được thực hiện tự động khi:
- Tạo mới tool (trong `AdminToolService.createTool()`)
- Tạo mới version (trong `AdminToolVersionService.createVersion()`)
- Cập nhật version (trong `AdminToolVersionService.updateVersion()`)

### 2. API Endpoints

#### Lấy schema mẫu
```
GET /admin/tools/:toolId/versions/schema/sample
```

#### Validate parameters
```
POST /admin/tools/:toolId/versions/validate-parameters
Content-Type: application/json

{
  "type": "object",
  "properties": {
    "query": {
      "type": "string",
      "description": "<PERSON>âu truy vấn tìm kiếm"
    }
  },
  "required": ["query"]
}
```

## Cấu trúc Schema

Tool parameters phải tuân theo cấu trúc JSON Schema với các quy tắc sau:

### Cấu trúc cơ bản
```json
{
  "type": "object",
  "properties": {
    "parameterName": {
      "type": "string|number|integer|boolean|array|object",
      "description": "Mô tả tham số"
    }
  },
  "required": ["parameterName"],
  "additionalProperties": false
}
```

### Các loại tham số được hỗ trợ

#### String
```json
{
  "type": "string",
  "description": "Mô tả",
  "minLength": 1,
  "maxLength": 1000,
  "pattern": "^[a-zA-Z]+$"
}
```

#### Number/Integer
```json
{
  "type": "integer",
  "description": "Mô tả",
  "minimum": 1,
  "maximum": 100,
  "default": 10
}
```

#### Boolean
```json
{
  "type": "boolean",
  "description": "Mô tả",
  "default": false
}
```

#### Array
```json
{
  "type": "array",
  "description": "Mô tả",
  "items": {
    "type": "string"
  }
}
```

#### Object
```json
{
  "type": "object",
  "description": "Mô tả",
  "properties": {
    "nestedParam": {
      "type": "string"
    }
  },
  "required": ["nestedParam"]
}
```

## Quy tắc validation

### 1. Quy tắc cơ bản
- `type` phải là "object"
- `properties` phải tồn tại và không rỗng
- Tên property phải bắt đầu bằng chữ cái và chỉ chứa chữ cái, số, dấu gạch dưới

### 2. Quy tắc nâng cao
- Các trường trong `required` phải tồn tại trong `properties`
- Nested objects phải tuân theo cùng quy tắc
- Array items phải có schema hợp lệ

## Ví dụ Schema hợp lệ

```json
{
  "type": "object",
  "properties": {
    "query": {
      "type": "string",
      "description": "Câu truy vấn tìm kiếm",
      "minLength": 1,
      "maxLength": 1000
    },
    "limit": {
      "type": "integer",
      "description": "Số lượng kết quả tối đa",
      "minimum": 1,
      "maximum": 100,
      "default": 10
    },
    "includeMetadata": {
      "type": "boolean",
      "description": "Có bao gồm metadata trong kết quả",
      "default": false
    },
    "filters": {
      "type": "object",
      "description": "Các bộ lọc",
      "properties": {
        "category": {
          "type": "string",
          "enum": ["news", "blog", "article"]
        },
        "dateRange": {
          "type": "object",
          "properties": {
            "from": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            },
            "to": {
              "type": "string",
              "pattern": "^\\d{4}-\\d{2}-\\d{2}$"
            }
          }
        }
      }
    },
    "tags": {
      "type": "array",
      "description": "Danh sách tags",
      "items": {
        "type": "string"
      }
    }
  },
  "required": ["query"],
  "additionalProperties": false
}
```

## Xử lý lỗi

Khi validation thất bại, hệ thống sẽ throw `AppException` với:
- Error code: `TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID`
- Message: Mô tả chi tiết lỗi validation

## Testing

Chạy test để kiểm tra validation:
```bash
npm test -- tool-parameter-validator.service.spec.ts
```
