# SMS Campaign User JSON Refactor

## Tổng quan

Tài liệu này mô tả việc refactor bảng `sms_campaign_user` từ sử dụng foreign keys sang lưu trữ dữ liệu dạng JSON để tăng tính linh hoạt và giảm dependency.

## Lý do Refactor

1. **Giảm Dependency**: <PERSON>h<PERSON>ng phụ thuộc vào các bảng khác (sms_server_configurations, user_template_sms, user_segments)
2. **Tính linh hoạt**: Dễ dàng thay đổi cấu trúc dữ liệu mà không cần alter table
3. **Performance**: Giảm số lượng JOIN queries
4. **Data Integrity**: Dữ liệu campaign được lưu trữ độc lập, không bị ảnh hưởng khi xóa template/segment
5. **Versioning**: <PERSON><PERSON> <PERSON>ể lưu trữ nhiều phiên bản cấu hình khác nhau

## Thay đổi Database Schema

### Cột mới được thêm:

```sql
-- <PERSON><PERSON><PERSON> hình SMS integration dạng JSON
sms_integration_config JSONB

-- Cấu hình template SMS dạng JSON  
template_config JSONB

-- Cấu hình segment dạng JSON
segment_config JSONB
```

### Cột cũ sẽ được xóa (sau khi verify):

```sql
sms_server_id INTEGER
template_id INTEGER
segment_id INTEGER
```

## Cấu trúc JSON

### SMS Integration Config
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "integrationName": "FPT SMS - Brand Name",
  "typeId": 1,
  "metadata": {
    "brandName": "REDAI",
    "apiUrl": "https://api01.sms.fpt.net"
  }
}
```

### Template Config
```json
{
  "id": 123,
  "name": "Template khuyến mãi Black Friday",
  "content": "Xin chào {{customerName}}! Khuyến mãi Black Friday - Giảm giá {{discountPercent}}%",
  "customContent": "Custom content if any",
  "variables": {
    "customerName": "string",
    "discountPercent": "number"
  },
  "isActive": true
}
```

### Segment Config
```json
{
  "id": 5,
  "name": "Khách hàng VIP",
  "description": "Segment khách hàng VIP có giá trị đơn hàng cao",
  "conditions": {
    "totalOrderValue": { "gte": 1000000 }
  },
  "audienceCount": 150
}
```

## Thay đổi Code

### Entity Changes

```typescript
// Trước
@Column({ name: 'sms_server_id', type: 'integer' })
smsServerId: number;

@Column({ name: 'template_id', type: 'integer', nullable: true })
templateId: number | null;

@Column({ name: 'segment_id', type: 'integer', nullable: true })
segmentId: number | null;

// Sau
@Column({ name: 'sms_integration_config', type: 'jsonb', nullable: true })
smsIntegrationConfig: SmsIntegrationConfig | null;

@Column({ name: 'template_config', type: 'jsonb', nullable: true })
templateConfig: SmsTemplateConfig | null;

@Column({ name: 'segment_config', type: 'jsonb', nullable: true })
segmentConfig: SegmentConfig | null;
```

### DTO Changes

```typescript
// Trước
smsServerId: number;
templateId?: number;
segmentId?: number;

// Sau
smsIntegrationConfig: Record<string, any>;
templateConfig?: Record<string, any>;
segmentConfig?: Record<string, any>;
```

### API Request Example

```json
// Trước
{
  "name": "Chiến dịch SMS khuyến mãi Black Friday",
  "campaignType": "ADS",
  "content": "Xin chào {{customerName}}! Khuyến mãi Black Friday",
  "smsServerId": 1,
  "templateId": 123,
  "segmentId": 5
}

// Sau
{
  "name": "Chiến dịch SMS khuyến mãi Black Friday",
  "campaignType": "ADS", 
  "content": "Xin chào {{customerName}}! Khuyến mãi Black Friday",
  "smsIntegrationConfig": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "integrationName": "FPT SMS - Brand Name",
    "typeId": 1,
    "metadata": { "brandName": "REDAI" }
  },
  "templateConfig": {
    "id": 123,
    "name": "Template khuyến mãi",
    "content": "Xin chào {{customerName}}! Khuyến mãi {{eventName}}"
  },
  "segmentConfig": {
    "id": 5,
    "name": "Khách hàng VIP",
    "audienceCount": 150
  }
}
```

## Migration Process

### 1. Chạy Migration

```bash
./scripts/run-sms-campaign-user-json-migration.sh
```

### 2. Verify Data

```sql
-- Kiểm tra tổng quan
SELECT 
    COUNT(*) as total_campaigns,
    COUNT(CASE WHEN sms_integration_config IS NOT NULL THEN 1 END) as campaigns_with_sms_config,
    COUNT(CASE WHEN template_config IS NOT NULL THEN 1 END) as campaigns_with_template_config,
    COUNT(CASE WHEN segment_config IS NOT NULL THEN 1 END) as campaigns_with_segment_config
FROM sms_campaign_user;

-- Kiểm tra dữ liệu mẫu
SELECT 
    id, 
    name, 
    sms_integration_config->>'integrationName' as integration_name,
    template_config->>'name' as template_name,
    segment_config->>'name' as segment_name
FROM sms_campaign_user 
WHERE sms_integration_config IS NOT NULL 
LIMIT 5;
```

### 3. Test Application

- Test tạo SMS campaign mới
- Test lấy danh sách campaigns
- Test gửi SMS
- Test các query với JSON data

### 4. Cleanup (sau khi verify)

```sql
-- Xóa cột cũ
ALTER TABLE sms_campaign_user DROP COLUMN IF EXISTS sms_server_id;
ALTER TABLE sms_campaign_user DROP COLUMN IF EXISTS template_id;
ALTER TABLE sms_campaign_user DROP COLUMN IF EXISTS segment_id;
```

## Query Examples

### Tìm campaigns theo integration name
```sql
SELECT * FROM sms_campaign_user 
WHERE sms_integration_config->>'integrationName' = 'FPT SMS - Brand Name';
```

### Tìm campaigns theo template name
```sql
SELECT * FROM sms_campaign_user 
WHERE template_config->>'name' ILIKE '%khuyến mãi%';
```

### Tìm campaigns theo segment
```sql
SELECT * FROM sms_campaign_user 
WHERE segment_config->>'name' = 'Khách hàng VIP';
```

### Index sử dụng
```sql
-- GIN indexes cho JSON queries
CREATE INDEX idx_sms_campaign_user_sms_integration_config ON sms_campaign_user USING GIN (sms_integration_config);
CREATE INDEX idx_sms_campaign_user_template_config ON sms_campaign_user USING GIN (template_config);
CREATE INDEX idx_sms_campaign_user_segment_config ON sms_campaign_user USING GIN (segment_config);

-- Functional indexes cho common queries
CREATE INDEX idx_sms_campaign_user_sms_integration_id ON sms_campaign_user ((sms_integration_config->>'id'));
CREATE INDEX idx_sms_campaign_user_template_id ON sms_campaign_user ((template_config->>'id'));
CREATE INDEX idx_sms_campaign_user_segment_id ON sms_campaign_user ((segment_config->>'id'));
```

## Lợi ích

1. **Độc lập dữ liệu**: Campaign data không bị ảnh hưởng khi xóa template/segment
2. **Flexibility**: Dễ dàng thêm fields mới vào JSON
3. **Performance**: Ít JOIN queries hơn
4. **Versioning**: Có thể lưu nhiều phiên bản config
5. **Backup**: Dữ liệu campaign được backup đầy đủ

## Lưu ý

1. **JSON Validation**: Cần validate JSON structure ở application layer
2. **Migration**: Cần test kỹ migration process
3. **Indexing**: Sử dụng GIN indexes cho JSON queries
4. **Backup**: Luôn backup trước khi migration
5. **Rollback**: Có kế hoạch rollback nếu cần thiết
