import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsDateString } from 'class-validator';
import { QueryDto } from '@common/dto';
import { ProviderFineTuneEnum } from '@modules/models/constants/provider.enum';

/**
 * DTO cho query lịch sử fine-tuning
 */
export class FineTuningHistoryQueryDto extends QueryDto {
}

/**
 * DTO response cho lịch sử fine-tuning
 */
export class FineTuningHistoryResponseDto {
  /**
   * ID của fine-tuning history
   */
  @ApiProperty({
    description: 'ID của fine-tuning history',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  /**
   * Tên model
   */
  @ApiProperty({
    description: 'Tên model',
    example: 'My Custom GPT Model',
  })
  modelName: string;

  /**
   * Mô tả model
   */
  @ApiPropertyOptional({
    description: 'Mô tả model',
    example: 'Model được fine-tune cho tác vụ customer support',
  })
  description?: string;

  /**
   * Provider đã sử dụng
   */
  @ApiProperty({
    description: 'Provider đã sử dụng',
    enum: ProviderFineTuneEnum,
    example: ProviderFineTuneEnum.OPENAI,
  })
  provider: ProviderFineTuneEnum;

  /**
   * Trạng thái job
   */
  @ApiProperty({
    description: 'Trạng thái job',
    example: 'succeeded',
  })
  status: string;

  /**
   * Job ID từ provider
   */
  @ApiProperty({
    description: 'Job ID từ provider',
    example: 'ftjob-abc123',
  })
  jobId: string;

  /**
   * Tên model gốc
   */
  @ApiProperty({
    description: 'Tên model gốc',
    example: 'gpt-4o-2024-08-06',
  })
  baseModel: string;

  /**
   * Tên model sau fine-tune
   */
  @ApiPropertyOptional({
    description: 'Tên model sau fine-tune',
    example: 'ft-abc123-model',
  })
  fineTunedModel?: string;

  /**
   * Số token đã sử dụng
   */
  @ApiProperty({
    description: 'Số token đã sử dụng',
    example: 50000,
  })
  tokens: number;

  /**
   * Số R-Points đã trừ
   */
  @ApiProperty({
    description: 'Số R-Points đã trừ',
    example: 100,
  })
  pointsDeducted: number;

  /**
   * Trạng thái hoàn point
   */
  @ApiProperty({
    description: 'Trạng thái hoàn point (nếu job failed)',
    example: false,
  })
  pointsRefunded: boolean;

  /**
   * Thời gian bắt đầu (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu (epoch millis)',
    example: 1640995200000,
  })
  startDate: number;

  /**
   * Thời gian kết thúc (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian kết thúc (epoch millis)',
    example: 1640995800000,
  })
  endDate: number;

  /**
   * Thời gian training (giây)
   */
  @ApiProperty({
    description: 'Thời gian training (giây)',
    example: 600,
  })
  duration: number;

  /**
   * Hyperparameters đã sử dụng
   */
  @ApiPropertyOptional({
    description: 'Hyperparameters đã sử dụng',
    type: 'object',
    additionalProperties: true,
    example: {
      epochs: 3,
      batchSize: 'auto',
      learningRate: 0.0001,
    },
  })
  hyperparameters?: any;

  /**
   * Thông tin dataset
   */
  @ApiPropertyOptional({
    description: 'ID dataset đã sử dụng',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  datasetId?: string;

  /**
   * Loại key đã sử dụng
   */
  @ApiProperty({
    description: 'Loại key đã sử dụng',
    enum: ['system', 'user'],
    example: 'system',
  })
  keyType: 'system' | 'user';

  /**
   * Tên key đã sử dụng (nếu là user key)
   */
  @ApiPropertyOptional({
    description: 'Tên key đã sử dụng (nếu là user key)',
    example: 'My OpenAI Key',
  })
  keyName?: string;

  /**
   * Thông báo lỗi (nếu có)
   */
  @ApiPropertyOptional({
    description: 'Thông báo lỗi (nếu có)',
    example: 'Training file has insufficient examples',
  })
  errorMessage?: string;
}
