import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { UserRegistrationEventService } from '../services/user-registration-event.service';

/**
 * Module riêng cho event services để tránh circular dependency
 */
@Module({
  imports: [EventEmitterModule],
  providers: [UserRegistrationEventService],
  exports: [UserRegistrationEventService],
})
export class EventsModule {}
