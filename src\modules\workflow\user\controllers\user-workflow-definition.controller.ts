import {
  Controller,
  Post,
  Put,
  Delete,
  Get,
  Param,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { UserWorkflowDefinitionService } from '../services/user-workflow-definition.service';
import { 
  UpdateWorkflowDefinitionDto, 
  WorkflowNodeDto, 
  WorkflowEdgeDto, 
  WorkflowDefinitionUpdateResponseDto,
  WorkflowValidationResponseDto,
  WorkflowNodeOperationResponseDto,
  WorkflowEdgeOperationResponseDto
} from '../../dto/definition';
// Entity imports removed - using DTOs only for API responses

/**
 * Controller để manage workflow definitions cho users
 * Following existing user controller patterns
 */
@ApiTags(SWAGGER_API_TAGS.USER_WORKFLOW)
@Controller('user/workflows')
@UseGuards(JwtUserGuard)
@ApiBearerAuth()
export class UserWorkflowDefinitionController {
  constructor(
    private readonly userWorkflowDefinitionService: UserWorkflowDefinitionService,
  ) {}

  /**
   * Update workflow definition
   */
  @Put(':id/definition')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update workflow definition',
    description: 'Update the complete workflow definition including nodes, edges, and metadata'
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow ID',
    type: 'string',
    format: 'uuid'
  })
  @ApiBody({
    type: UpdateWorkflowDefinitionDto,
    description: 'Workflow definition data'
  })
  @ApiResponse({
    status: 200,
    description: 'Workflow definition updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            workflow: { type: 'object' },
            validation: {
              type: 'object',
              properties: {
                isValid: { type: 'boolean' },
                errors: { type: 'array', items: { type: 'object' } },
                warnings: { type: 'array', items: { type: 'object' } }
              }
            }
          }
        },
        message: { type: 'string' },
        timestamp: { type: 'string' }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid workflow definition or validation failed'
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow not found'
  })
  async updateWorkflowDefinition(
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Body() updateDto: UpdateWorkflowDefinitionDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowDefinitionUpdateResponseDto>> {
    const result = await this.userWorkflowDefinitionService.updateWorkflowDefinition(
      workflowId,
      updateDto,
      userId
    );

    return ApiResponseDto.success(result, 'Workflow definition updated successfully');
  }

  /**
   * Validate workflow definition
   */
  @Post(':id/definition/validate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Validate workflow definition',
    description: 'Validate workflow definition without saving changes'
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow ID',
    type: 'string',
    format: 'uuid'
  })
  @ApiBody({
    description: 'Workflow definition to validate',
    schema: {
      type: 'object',
      properties: {
        nodes: { type: 'array' },
        edges: { type: 'array' },
        metadata: { type: 'object' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Validation completed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            isValid: { type: 'boolean' },
            errors: { type: 'array', items: { type: 'object' } },
            warnings: { type: 'array', items: { type: 'object' } }
          }
        },
        message: { type: 'string' },
        timestamp: { type: 'string' }
      }
    }
  })
  async validateWorkflowDefinition(
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Body() definition: Record<string, any>,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowValidationResponseDto>> {
    const validation = await this.userWorkflowDefinitionService.validateWorkflowDefinition(
      workflowId,
      definition,
      userId
    );

    return ApiResponseDto.success(
      validation,
      validation.isValid ? 'Workflow definition is valid' : 'Workflow definition has validation issues'
    );
  }

  /**
   * Add node to workflow
   */
  @Post(':id/nodes')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Add node to workflow',
    description: 'Add a new node to the workflow definition'
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow ID',
    type: 'string',
    format: 'uuid'
  })
  @ApiBody({
    type: WorkflowNodeDto,
    description: 'Node data'
  })
  @ApiResponse({
    status: 201,
    description: 'Node added successfully'
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid node data or node ID already exists'
  })
  async addNodeToWorkflow(
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Body() nodeDto: WorkflowNodeDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowNodeOperationResponseDto>> {
    const workflow = await this.userWorkflowDefinitionService.addNodeToWorkflow(
      workflowId,
      nodeDto,
      userId
    );

    return ApiResponseDto.success(workflow, 'Node added to workflow successfully');
  }

  /**
   * Update node in workflow
   */
  @Put(':id/nodes/:nodeId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update node in workflow',
    description: 'Update an existing node in the workflow definition'
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow ID',
    type: 'string',
    format: 'uuid'
  })
  @ApiParam({
    name: 'nodeId',
    description: 'Node ID',
    type: 'string'
  })
  @ApiBody({
    description: 'Partial node data to update',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        description: { type: 'string' },
        position: { type: 'object' },
        size: { type: 'object' },
        inputs: { type: 'object' },
        outputs: { type: 'object' },
        config: { type: 'object' },
        metadata: { type: 'object' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Node updated successfully'
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow or node not found'
  })
  async updateNodeInWorkflow(
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Param('nodeId') nodeId: string,
    @Body() nodeDto: Partial<WorkflowNodeDto>,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowNodeOperationResponseDto>> {
    const workflow = await this.userWorkflowDefinitionService.updateNodeInWorkflow(
      workflowId,
      nodeId,
      nodeDto,
      userId
    );

    return ApiResponseDto.success(workflow, 'Node updated successfully');
  }

  /**
   * Remove node from workflow
   */
  @Delete(':id/nodes/:nodeId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Remove node from workflow',
    description: 'Remove a node and its related edges from the workflow definition'
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow ID',
    type: 'string',
    format: 'uuid'
  })
  @ApiParam({
    name: 'nodeId',
    description: 'Node ID',
    type: 'string'
  })
  @ApiResponse({
    status: 200,
    description: 'Node removed successfully'
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow or node not found'
  })
  async removeNodeFromWorkflow(
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Param('nodeId') nodeId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowNodeOperationResponseDto>> {
    const workflow = await this.userWorkflowDefinitionService.removeNodeFromWorkflow(
      workflowId,
      nodeId,
      userId
    );

    return ApiResponseDto.success(workflow, 'Node removed from workflow successfully');
  }

  /**
   * Add edge to workflow
   */
  @Post(':id/edges')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Add edge to workflow',
    description: 'Add a new edge connection to the workflow definition'
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow ID',
    type: 'string',
    format: 'uuid'
  })
  @ApiBody({
    type: WorkflowEdgeDto,
    description: 'Edge data'
  })
  @ApiResponse({
    status: 201,
    description: 'Edge added successfully'
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid edge data or edge ID already exists'
  })
  async addEdgeToWorkflow(
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Body() edgeDto: WorkflowEdgeDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowEdgeOperationResponseDto>> {
    const workflow = await this.userWorkflowDefinitionService.addEdgeToWorkflow(
      workflowId,
      edgeDto,
      userId
    );

    return ApiResponseDto.success(workflow, 'Edge added to workflow successfully');
  }

  /**
   * Remove edge from workflow
   */
  @Delete(':id/edges/:edgeId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Remove edge from workflow',
    description: 'Remove an edge connection from the workflow definition'
  })
  @ApiParam({
    name: 'id',
    description: 'Workflow ID',
    type: 'string',
    format: 'uuid'
  })
  @ApiParam({
    name: 'edgeId',
    description: 'Edge ID',
    type: 'string'
  })
  @ApiResponse({
    status: 200,
    description: 'Edge removed successfully'
  })
  @ApiResponse({
    status: 404,
    description: 'Workflow or edge not found'
  })
  async removeEdgeFromWorkflow(
    @Param('id', ParseUUIDPipe) workflowId: string,
    @Param('edgeId') edgeId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowEdgeOperationResponseDto>> {
    const workflow = await this.userWorkflowDefinitionService.removeEdgeFromWorkflow(
      workflowId,
      edgeId,
      userId
    );

    return ApiResponseDto.success(workflow, 'Edge removed from workflow successfully');
  }
}
