import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AdminTemplateEmailService } from '../services/admin-template-email.service';
import { AdminTemplateEmail } from '../entities/admin-template-email.entity';
import { Employee } from '@modules/employee/entities/employee.entity';
import { CategoryTemplateAutoEnum } from '@modules/email/interface/category-template-auto.enum';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import {
  TemplateEmailQueryDto,
  TemplateEmailResponseDto,
  TemplateEmailListResponseDto,
  TemplateEmailOverviewResponseDto,
  CreateTemplateEmailDto,
  UpdateTemplateEmailDto,
  DeleteMultipleTemplateEmailDto,
  DeleteMultipleTemplateEmailResultDto
} from '../dto/template-email';
import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';

/**
 * Controller xử lý API liên quan đến template email bên admin
 */
@RequirePermissionEnum(Permission.MARKETING_VIEW)
@ApiTags(SWAGGER_API_TAGS.ADMIN_TEMPLATE_EMAIL)
@Controller('admin/template-email')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
export class AdminTemplateEmailController {
  constructor(
    private readonly adminTemplateEmailService: AdminTemplateEmailService,
  ) {}

  /**
   * Lấy thông tin overview về template email
   */
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy thông tin overview về template email',
    description: 'Lấy thống kê tổng quan về template email bao gồm tổng số templates, templates hoạt động, bản nháp, số test gửi tuần này và templates mới tuần này'
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin overview template email',
    type: TemplateEmailOverviewResponseDto
  })
  async getOverview(): Promise<AppApiResponse<TemplateEmailOverviewResponseDto>> {
    const result = await this.adminTemplateEmailService.getOverview();
    return wrapResponse(result, 'Lấy thông tin overview template email thành công');
  }

  /**
   * Lấy danh sách template email với phân trang và filter
   */
  @ApiOperation({ summary: 'Lấy danh sách template email với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách template email với phân trang',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: {
              allOf: [
                { $ref: '#/components/schemas/PaginatedResult' },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: '#/components/schemas/TemplateEmailListResponseDto' }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  @Get()
  async findAll(@Query() query: TemplateEmailQueryDto): Promise<AppApiResponse<PaginatedResult<TemplateEmailListResponseDto>>> {
    const result = await this.adminTemplateEmailService.findAll(query);
    return wrapResponse(result, 'Danh sách template email');
  }

  /**
   * Lấy tất cả template email (không phân trang)
   */
  @ApiOperation({ summary: 'Lấy tất cả template email (không phân trang)' })
  @ApiResponse({ status: 200, description: 'Danh sách template email' })
  @Get('all')
  async findAllTemplates(): Promise<AppApiResponse<AdminTemplateEmail[]>> {
    const result = await this.adminTemplateEmailService.findAllTemplates();
    return wrapResponse(result, 'Danh sách tất cả template email');
  }

  /**
   * Lấy template email theo ID
   */
  @ApiOperation({
    summary: 'Lấy template email theo ID',
    description: 'Lấy thông tin chi tiết của một template email bao gồm cả content'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của template email',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết template email',
    type: TemplateEmailResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Template email không tồn tại'
  })
  @Get(':id')
  async findById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<AppApiResponse<TemplateEmailResponseDto>> {
    const template = await this.adminTemplateEmailService.findById(id);
    const result = await this.adminTemplateEmailService.mapToDetailDto(template);
    return wrapResponse(result, 'Thông tin template email');
  }

  // /**
  //  * Lấy template email theo category
  //  */
  // @ApiOperation({ summary: 'Lấy template email theo category' })
  // @ApiQuery({ name: 'category', description: 'Category của template email' })
  // @ApiResponse({ status: 200, description: 'Template email' })
  // @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  // @Get('category/:category')
  // async findByCategory(@Param('category') category: string): Promise<AppApiResponse<AdminTemplateEmail>> {
  //   const result = await this.adminTemplateEmailService.findByCategory(category);
  //   return wrapResponse(result, 'Template email theo category');
  // }

  // /**
  //  * Lấy template email theo category enum
  //  */
  // @ApiOperation({ summary: 'Lấy template email theo category enum' })
  // @ApiQuery({
  //   name: 'category',
  //   description: 'Category enum của template email',
  //   enum: CategoryTemplateAutoEnum
  // })
  // @ApiResponse({ status: 200, description: 'Template email' })
  // @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  // @Get('category-enum/:category')
  // async findTemplateAutoByCategory(
  //   @Param('category') category: CategoryTemplateAutoEnum
  // ): Promise<AppApiResponse<AdminTemplateEmail>> {
  //   const result = await this.adminTemplateEmailService.findTemplateAutoByCategory(category);
  //   return wrapResponse(result, 'Template email theo category enum');
  // }

  /**
   * Tìm template email theo danh sách category
   */
  @ApiOperation({ summary: 'Tìm template email theo danh sách category' })
  @ApiQuery({
    name: 'categories',
    description: 'Danh sách category của template email, phân cách bởi dấu phẩy',
    type: String
  })
  @ApiResponse({ status: 200, description: 'Danh sách template email' })
  @Get('categories')
  async findByCategories(@Query('categories') categoriesStr: string): Promise<AppApiResponse<AdminTemplateEmail[]>> {
    const categories = categoriesStr.split(',').map(cat => cat.trim());
    const result = await this.adminTemplateEmailService.findByCategories(categories);
    return wrapResponse(result, 'Danh sách template email theo categories');
  }

  /**
   * Tạo mới template email
   */
  @ApiOperation({
    summary: 'Tạo mới template email',
    description: 'Tạo một template email mới với thông tin được cung cấp. Trường category có thể để trống.'
  })
  @ApiBody({
    type: CreateTemplateEmailDto,
    description: 'Thông tin template email cần tạo',
    examples: {
      'account-verification': {
        summary: 'Template xác thực tài khoản',
        description: 'Ví dụ tạo template email xác thực tài khoản',
        value: {
          name: 'Template xác thực tài khoản',
          category: 'ACCOUNT_VERIFICATION',
          subject: 'Xác thực tài khoản của bạn',
          content: '<!DOCTYPE html><html><body><h1>Xin chào {customer_name}!</h1><p>Mã xác thực của bạn là: <strong>{otp_code}</strong></p><p>Mã có hiệu lực trong 5 phút.</p></body></html>',
          placeholders: {
            customer_name: 'Tên khách hàng',
            otp_code: 'Mã OTP xác thực',
            company_name: 'Tên công ty'
          }
        }
      },
      'newsletter': {
        summary: 'Template newsletter',
        description: 'Ví dụ tạo template newsletter',
        value: {
          name: 'Newsletter tháng 12',
          category: 'NEWSLETTER',
          subject: 'Tin tức mới nhất từ RedAI',
          content: '<!DOCTYPE html><html><body><h1>Chào {customer_name}!</h1><p>Đây là bản tin mới nhất từ {company_name}</p></body></html>',
          placeholders: {
            customer_name: 'Tên khách hàng',
            company_name: 'Tên công ty'
          }
        }
      },
      'no-category': {
        summary: 'Template không có category',
        description: 'Ví dụ tạo template email không có category',
        value: {
          name: 'Template email tùy chỉnh',
          subject: 'Thông báo quan trọng',
          content: '<!DOCTYPE html><html><body><h1>Xin chào!</h1><p>Đây là một thông báo quan trọng từ hệ thống.</p></body></html>',
          placeholders: {
            message: 'Nội dung thông báo'
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Template email đã được tạo thành công',
    type: TemplateEmailResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ'
  })
  @Post()
  async create(
    @Body() createDto: CreateTemplateEmailDto,
    @CurrentEmployee() employee: Employee
  ): Promise<AppApiResponse<TemplateEmailResponseDto>> {
    const result = await this.adminTemplateEmailService.create(createDto, employee.id);
    return wrapResponse(result, 'Template email đã được tạo thành công');
  }

  /**
   * Cập nhật template email
   */
  @ApiOperation({
    summary: 'Cập nhật template email',
    description: 'Cập nhật thông tin của một template email theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của template email',
    type: 'number',
    example: 1
  })
  @ApiBody({
    type: UpdateTemplateEmailDto,
    description: 'Thông tin template email cần cập nhật'
  })
  @ApiResponse({
    status: 200,
    description: 'Template email đã được cập nhật thành công',
    type: TemplateEmailResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Template email không tồn tại'
  })
  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateTemplateEmailDto,
    @CurrentEmployee() employee: Employee
  ): Promise<AppApiResponse<TemplateEmailResponseDto>> {
    const result = await this.adminTemplateEmailService.update(id, updateDto, employee.id);
    return wrapResponse(result, 'Template email đã được cập nhật thành công');
  }

  /**
   * Xóa template email
   */
  @ApiOperation({ summary: 'Xóa template email' })
  @ApiParam({ name: 'id', description: 'ID của template email' })
  @ApiResponse({ status: 200, description: 'Template email đã được xóa' })
  @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  @Delete(':id')
  async delete(@Param('id', ParseIntPipe) id: number): Promise<AppApiResponse<{ success: boolean }>> {
    const success = await this.adminTemplateEmailService.delete(id);
    return wrapResponse({ success }, 'Template email đã được xóa thành công');
  }

  /**
   * Xóa nhiều template email
   */
  @Delete('bulk-delete/multiple')
  @ApiOperation({
    summary: 'Xóa nhiều template email',
    description: 'Xóa nhiều template email theo danh sách ID'
  })
  @ApiBody({
    type: DeleteMultipleTemplateEmailDto,
    description: 'Danh sách ID của các template email cần xóa'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa nhiều template email',
    type: DeleteMultipleTemplateEmailResultDto
  })
  async bulkDelete(
    @Body() deleteDto: DeleteMultipleTemplateEmailDto,
  ): Promise<AppApiResponse<DeleteMultipleTemplateEmailResultDto>> {
    const result = await this.adminTemplateEmailService.deleteMultiple(deleteDto.ids);
    return wrapResponse(result, 'Xóa nhiều template email hoàn tất');
  }
}
