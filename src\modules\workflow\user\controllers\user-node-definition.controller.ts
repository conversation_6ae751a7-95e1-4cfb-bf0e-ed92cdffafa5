import { ApiResponseDto } from '@common/response';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from '../../constants';
import {
  NodeDocumentationDto,
  NodeFilterDto,
  NodeRegistryStatsDto,
  SearchOptionsDto,
} from '../../dto/node-definition';
import { NodeCategory, NodeDefinition } from '../../entities';
import { ValidationResult } from '../../interfaces';
import { NodeDefinitionService } from '../../services/node-definition.service';

/**
 * User controller cho Node Definition Registry
 * Cho phép user xem và sử dụng node definitions
 */
@ApiTags(SWAGGER_API_TAGS.USER_NODE_DEFINITIONS)
@ApiBearerAuth()
@UseGuards(JwtUserGuard)
@Controller('user/workflow/node-definitions')
export class UserNodeDefinitionController {
  constructor(
    private readonly nodeDefinitionService: NodeDefinitionService,
  ) { }

  /**
   * Lấy tất cả node definitions (chỉ non-deprecated)
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách node definitions cho user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách node definitions',
    type: [NodeDefinition],
  })
  async getNodeDefinitions(
    @Query() filters: NodeFilterDto,
  ): Promise<ApiResponseDto<{ nodes: NodeDefinition[]; total: number }>> {
    // Force exclude deprecated nodes for users
    const userFilters = { ...filters, isDeprecated: false };
    const [nodes, total] = await this.nodeDefinitionService.getNodesWithFilters(userFilters);

    return ApiResponseDto.success({ nodes, total }, 'Node definitions retrieved successfully');
  }

  /**
   * Lấy node definitions theo category
   */
  @Get('category/:category')
  @ApiOperation({ summary: 'Lấy node definitions theo category' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node definitions theo category',
    type: [NodeDefinition],
  })
  async getNodesByCategory(
    @Param('category') category: NodeCategory,
    @Query() filters: NodeFilterDto,
  ): Promise<ApiResponseDto<NodeDefinition[]>> {
    // Force exclude deprecated nodes for users
    const userFilters = { ...filters, isDeprecated: false };
    const nodes = await this.nodeDefinitionService.getNodesByCategory(category, userFilters);

    return ApiResponseDto.success(nodes, `Nodes for category '${category}' retrieved successfully`);
  }

  /**
   * Lấy node definitions theo multiple categories
   */
  @Post('categories')
  @ApiOperation({ summary: 'Lấy node definitions theo multiple categories' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node definitions theo categories',
    type: [NodeDefinition],
  })
  async getNodesByCategories(
    @Body() body: { categories: NodeCategory[]; filters?: NodeFilterDto },
  ): Promise<ApiResponseDto<NodeDefinition[]>> {
    const nodes = await this.nodeDefinitionService.getNodesByCategories(
      body.categories,
      body.filters // Simplified - no deprecated filtering needed
    );

    return ApiResponseDto.success(nodes, `Found ${nodes.length} nodes in ${body.categories.length} categories`);
  }

  /**
   * Tìm kiếm node definitions
   */
  @Get('search')
  @ApiOperation({ summary: 'Tìm kiếm node definitions' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Kết quả tìm kiếm',
    type: [NodeDefinition],
  })
  async searchNodes(
    @Query('q') query: string,
    @Query() options: SearchOptionsDto,
  ): Promise<ApiResponseDto<NodeDefinition[]>> {
    // Force exclude deprecated nodes for users
    const userOptions = { ...options, includeDeprecated: false };
    const nodes = await this.nodeDefinitionService.searchNodes(query, userOptions);

    return ApiResponseDto.success(nodes, `Found ${nodes.length} nodes matching '${query}'`);
  }

  /**
   * Lấy thống kê registry (public stats)
   */
  @Get('statistics')
  @ApiOperation({ summary: 'Lấy thống kê node registry' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thống kê registry',
    type: NodeRegistryStatsDto,
  })
  async getRegistryStatistics(): Promise<ApiResponseDto<NodeRegistryStatsDto>> {
    const stats = await this.nodeDefinitionService.getRegistryStatistics();

    return ApiResponseDto.success(stats, 'Registry statistics retrieved successfully');
  }

  /**
   * Lấy node definition theo type
   */
  @Get(':type')
  @ApiOperation({ summary: 'Lấy node definition theo type' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node definition details',
    type: NodeDefinition,
  })
  async getNodeByType(
    @Param('type') type: string,
  ): Promise<ApiResponseDto<NodeDefinition>> {
    const node = await this.nodeDefinitionService.getNodeByType(type);

    return ApiResponseDto.success(node, 'Node definition retrieved successfully');
  }

  /**
   * Lấy documentation của node
   */
  @Get(':type/documentation')
  @ApiOperation({ summary: 'Lấy documentation chi tiết của node' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Node documentation',
    type: NodeDocumentationDto,
  })
  async getNodeDocumentation(
    @Param('type') type: string,
  ): Promise<ApiResponseDto<NodeDocumentationDto>> {
    const documentation = await this.nodeDefinitionService.getNodeDocumentation(type);

    return ApiResponseDto.success(documentation, 'Node documentation retrieved successfully');
  }

  /**
   * Validate input data theo node schema
   */
  @Post(':type/validate')
  @ApiOperation({ summary: 'Validate input data theo node schema' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Validation result',
    type: Object,
  })
  async validateNodeInput(
    @Param('type') type: string,
    @Body() inputData: any,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<ValidationResult>> {
    const validationResult = await this.nodeDefinitionService.validateNodeSchema(type, inputData);

    return ApiResponseDto.success(
      validationResult,
      validationResult.isValid
        ? 'Input data is valid'
        : `Validation failed with ${validationResult.errors.length} errors`
    );
  }

  /**
   * Lấy danh sách categories có sẵn
   */
  @Get('meta/categories')
  @ApiOperation({ summary: 'Lấy danh sách categories có sẵn' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách categories',
    type: [String],
  })
  async getAvailableCategories(): Promise<ApiResponseDto<NodeCategory[]>> {
    // Return all available categories from enum
    const categories = Object.values(NodeCategory);

    return ApiResponseDto.success(categories, 'Available categories retrieved successfully');
  }

  /**
   * Lấy node definitions được recommend cho user
   */
  @Get('meta/recommended')
  @ApiOperation({ summary: 'Lấy node definitions được recommend' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommended node definitions',
    type: [NodeDefinition],
  })
  async getRecommendedNodes(
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<NodeDefinition[]>> {
    // Simple recommendation: return popular system nodes and some service nodes
    const systemNodes = await this.nodeDefinitionService.getNodesByCategory(NodeCategory.SYSTEM);
    const googleNodes = await this.nodeDefinitionService.getNodesByCategory(NodeCategory.GOOGLE_SHEETS);

    // Combine and limit to 10 most useful nodes
    const recommendedNodes = [...systemNodes.slice(0, 5), ...googleNodes.slice(0, 5)];

    return ApiResponseDto.success(recommendedNodes, 'Recommended nodes retrieved successfully');
  }
}
