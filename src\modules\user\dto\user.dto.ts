import { ApiProperty } from '@nestjs/swagger';
import { GenderEnum, UserTypeEnum } from '../enums';

/**
 * DTO cho thông tin người dùng
 */
export class UserDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên đầy đủ của người dùng',
    example: 'Nguyễn Văn A'
  })
  fullName: string;

  @ApiProperty({
    description: 'Email của người dùng',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Số điện thoại của người dùng',
    example: '0987654321'
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Mã quốc gia của số điện thoại',
    example: 84
  })
  countryCode: number;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> d<PERSON>u lần thay đổi mật khẩu đầu tiên',
    example: false
  })
  isFirstPasswordChange: boolean;

  @ApiProperty({
    description: 'Trạng thái hoạt động của tài khoản',
    example: true
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Trạng thái xác thực email',
    example: true
  })
  isVerifyEmail: boolean;

  @ApiProperty({
    description: 'Trạng thái xác thực số điện thoại',
    example: true
  })
  isVerifyPhone: boolean;

  @ApiProperty({
    description: 'Thời gian tạo tài khoản (Unix timestamp)',
    example: 1625097600000
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật thông tin (Unix timestamp)',
    example: 1625097600000
  })
  updatedAt: number;

  @ApiProperty({
    description: 'Địa chỉ',
    example: '123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh'
  })
  address: string;

  @ApiProperty({
    description: 'Mã số thuế',
    example: '0123456789'
  })
  taxCode: string;

  @ApiProperty({
    description: 'Số dư points hiện tại',
    example: 1000
  })
  pointsBalance: number;

  @ApiProperty({
    description: 'Loại tài khoản',
    enum: UserTypeEnum,
    example: UserTypeEnum.INDIVIDUAL
  })
  type: UserTypeEnum;

  @ApiProperty({
    description: 'Nền tảng đăng ký',
    example: 'web'
  })
  platform: string;

  @ApiProperty({
    description: 'Số CMND/CCCD',
    example: '0**********1'
  })
  citizenId: string;

  @ApiProperty({
    description: 'Nơi cấp CMND/CCCD',
    example: 'Cục Cảnh sát ĐKQL cư trú và DLQG về dân cư'
  })
  citizenIssuePlace: string;

  @ApiProperty({
    description: 'Ngày cấp CMND/CCCD',
    example: '2020-01-01'
  })
  citizenIssueDate: Date;

  @ApiProperty({
    description: 'Key của avatar trên hệ thống lưu trữ',
    example: 'users/1/avatar.jpg'
  })
  avatar: string;

  @ApiProperty({
    description: 'URL xem avatar (có thời hạn)',
    example: 'https://cdn.example.com/users/1/avatar.jpg?token=abc123'
  })
  avatarUrl?: string;

  @ApiProperty({
    description: 'Key của ảnh bìa trên hệ thống lưu trữ',
    example: 'users/1/cover_images/2024/01/cover.jpg'
  })
  coverImage: string;

  @ApiProperty({
    description: 'URL xem ảnh bìa (có thời hạn)',
    example: 'https://cdn.example.com/users/1/cover_images/2024/01/cover.jpg?token=abc123'
  })
  coverImageUrl?: string;

  @ApiProperty({
    description: 'Ngày sinh',
    example: '1990-01-01'
  })
  dateOfBirth: Date;

  @ApiProperty({
    description: 'Giới tính',
    enum: GenderEnum,
    example: GenderEnum.MALE
  })
  gender: GenderEnum;

  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'VCB'
  })
  bankCode: string;

  @ApiProperty({
    description: 'Số tài khoản ngân hàng',
    example: '**********'
  })
  accountNumber: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản ngân hàng',
    example: 'NGUYEN VAN A'
  })
  accountHolder: string;

  @ApiProperty({
    description: 'Chi nhánh ngân hàng',
    example: 'Chi nhánh Quận 1'
  })
  bankBranch: string;
}
