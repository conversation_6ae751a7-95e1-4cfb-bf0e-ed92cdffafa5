import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ProductResponseDto } from './product-response.dto';

/**
 * DTO cho flash sale info trong product response
 */
export class FlashSaleInfoDto {
  @ApiProperty({
    description: 'Giá sau khi áp dụng flash sale',
    example: 64000
  })
  salePrice: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu flash sale (ISO 8601)',
    example: '2024-01-01T10:00:00.000Z'
  })
  startTime: string;

  @ApiProperty({
    description: 'Thời gian kết thúc flash sale (ISO 8601)',
    example: '2024-01-01T22:00:00.000Z'
  })
  endTime: string;

  @ApiPropertyOptional({
    description: 'Thời gian còn lại của flash sale (giây)',
    example: 3600
  })
  timeRemaining?: number;

  @ApiProperty({
    description: 'Phần trăm giảm giá',
    example: 20
  })
  discountPercentage: number;

  @ApiPropertyOptional({
    description: 'Tổng số lượng inventory cho flash sale',
    example: 1000,
    nullable: true
  })
  totalInventory: number | null;

  @ApiPropertyOptional({
    description: 'Số lượng đã bán trong flash sale (calculated)',
    example: 50
  })
  soldCount?: number;

  @ApiPropertyOptional({
    description: 'Thời gian hiển thị sản phẩm cho user (giây). Frontend sử dụng để tạo hiệu ứng khan hiếm',
    example: 30
  })
  displayTime?: number;
}

/**
 * DTO response cho sản phẩm có flash sale (extends ProductResponseDto)
 */
export class FlashSaleProductResponseDto extends ProductResponseDto {
  @ApiProperty({
    description: 'Thông tin flash sale',
    type: FlashSaleInfoDto
  })
  flashSale: FlashSaleInfoDto;
}

/**
 * DTO response cho danh sách flash sale suggestions
 */
export class FlashSaleSuggestionsResponseDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm flash sale gợi ý',
    type: [FlashSaleProductResponseDto]
  })
  suggestions: FlashSaleProductResponseDto[];

  @ApiProperty({
    description: 'Số lượng gợi ý trả về',
    example: 5
  })
  count: number;

  @ApiProperty({
    description: 'Thời gian tạo response (timestamp)',
    example: 1640995200000
  })
  generatedAt: number;
}
