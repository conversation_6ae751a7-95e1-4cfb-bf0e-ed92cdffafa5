import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean } from 'class-validator';

/**
 * DTO cho việc bật/tắt trạng thái website
 */
export class ToggleWebsiteStatusDto {
  /**
   * Trạng thái hoạt động của website
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của website (true = bật, false = tắt)',
    example: true
  })
  @IsBoolean({
    message: 'active phải là boolean'
  })
  active: boolean;
}

/**
 * DTO cho response của việc bật/tắt website
 */
export class ToggleWebsiteStatusResponseDto {
  /**
   * ID của website
   */
  @ApiProperty({
    description: 'ID của website',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  id: string;

  /**
   * Trạng thái hoạt động mới của website
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động mới của website',
    example: true
  })
  active: boolean;

  /**
   * Thông báo kết quả
   */
  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Website đã được bật thành công'
  })
  message: string;
}
