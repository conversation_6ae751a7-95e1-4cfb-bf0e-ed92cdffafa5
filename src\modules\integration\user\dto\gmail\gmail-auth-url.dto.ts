import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUrl } from 'class-validator';

/**
 * DTO cho request URL xác thực Gmail OAuth2
 */
export class GmailAuthUrlRequestDto {
  @ApiProperty({
    description: 'URL callback sau khi xác thực',
    example: 'https://example.com/callback',
    required: false,
  })
  @IsOptional()
  @IsUrl({}, { message: 'Redirect URI phải là URL hợp lệ' })
  redirectUri?: string;
}

/**
 * DTO cho response URL xác thực Gmail OAuth2
 */
export class GmailAuthUrlDto {
  @ApiProperty({
    description: 'URL xác thực Gmail OAuth2',
    example: 'https://accounts.google.com/o/oauth2/v2/auth?access_type=offline&scope=...'
  })
  authUrl: string;

  @ApiProperty({
    description: 'State token để xác thực callback với tiền tố gmail',
    example: 'gmail_eyJ1c2VySWQiOjEsInRpbWVzdGFtcCI6MTc1MzA4OTkxODE4MiwiYWN0aW9uIjoiY29ubmVjdCJ9'
  })
  state: string;
}
