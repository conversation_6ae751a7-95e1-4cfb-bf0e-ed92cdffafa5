import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO for workflow API responses
 * Follows existing response patterns from the codebase
 */
export class WorkflowResponseDto {
  /**
   * Workflow ID
   */
  @ApiProperty({
    description: 'Workflow ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @Expose()
  id: string;

  /**
   * User ID who owns this workflow
   */
  @ApiPropertyOptional({
    description: 'User ID who owns this workflow',
    example: 123
  })
  @Expose()
  userId?: number | null;

  /**
   * Employee ID responsible for this workflow
   */
  @ApiPropertyOptional({
    description: 'Employee ID responsible for this workflow',
    example: 456
  })
  @Expose()
  employeeId?: number | null;

  /**
   * Workflow name
   */
  @ApiProperty({
    description: 'Workflow name',
    example: 'Customer Onboarding Flow'
  })
  @Expose()
  name: string;

  /**
   * Whether the workflow is active
   */
  @ApiProperty({
    description: 'Whether the workflow is active',
    example: true
  })
  @Expose()
  isActive: boolean;

  /**
   * Creation timestamp (Unix timestamp in milliseconds)
   */
  @ApiProperty({
    description: 'Creation timestamp (Unix timestamp in milliseconds)',
    example: 1705123200000
  })
  @Expose()
  @Type(() => Number)
  createdAt: number;

  /**
   * Last update timestamp (Unix timestamp in milliseconds)
   */
  @ApiProperty({
    description: 'Last update timestamp (Unix timestamp in milliseconds)',
    example: 1705209600000
  })
  @Expose()
  @Type(() => Number)
  updatedAt: number;

  /**
   * Workflow definition structure
   */
  @ApiProperty({
    description: 'Workflow definition structure',
    example: {
      nodes: [
        { id: 'start', type: 'start', data: {} },
        { id: 'end', type: 'end', data: {} }
      ],
      edges: [
        { id: 'e1', source: 'start', target: 'end' }
      ],
      version: '1.0.0'
    }
  })
  @Expose()
  definition: Record<string, any>;

  /**
   * Additional computed fields for UI
   */
  @ApiPropertyOptional({
    description: 'Number of nodes in the workflow',
    example: 5
  })
  @Expose()
  nodeCount?: number;

  /**
   * Last execution timestamp (if any)
   */
  @ApiPropertyOptional({
    description: 'Last execution timestamp (Unix timestamp in milliseconds)',
    example: 1705209600000
  })
  @Expose()
  @Type(() => Number)
  lastExecutedAt?: number;

  /**
   * Workflow status summary
   */
  @ApiPropertyOptional({
    description: 'Workflow status summary',
    example: 'ready'
  })
  @Expose()
  status?: string;
}
