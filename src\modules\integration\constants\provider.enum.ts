import { AppException } from '@common/exceptions';
import { INTEGRATION_ERROR_CODES } from '../exceptions/integration-error.code';

export enum ProviderEnum {
    // Providers Model
    GMAIL = 'GMAIL',
    OPENAI = 'OPENAI',
    XAI = 'XAI',
    GEMINI = 'GEMINI',
    ANTHROPIC = 'ANTHROPIC',
    DEEPSEEK = 'DEEPSEEK',

    // Providers Output Agent
    // Facebook
    FACEBOOK_PAGE = 'FACEBOOK_PAGE',
    FACEBOOK_PERSONAL = 'FACEBOOK_PERSONAL',

    // Website
    WEBSITE = 'WEBSITE',

    // Zalo OA
    ZALO_OA = 'ZALO_OA',

    // Providers Shipping
    GHTK = 'GHTK',
    GHN = 'GHN',
    AHAMOVE = 'AHAMOVE',

    // Providers Payment Gateway
    MB_BANK = 'MB_BANK',
    OCB_BANK = 'OCB_BANK',
    KL_BANK = 'KL_BANK',
    ACB_BANK = 'ACB_BANK',

    // NONE
    NONE = 'NONE',

    // Providers Email
    EMAIL_SMTP = 'EMAIL_SMTP',
    EMAIL_TWILIO_SENDGRID = 'EMAIL_TWILIO_SENDGRID',
    EMAIL_GMAIL = 'EMAIL_GMAIL',
    EMAIL_OUTLOOK = 'EMAIL_OUTLOOK',

    // Providers SMS
    SMS_FPT = 'SMS_FPT',
    SMS_TWILIO = 'SMS_TWILIO',
    SMS_VONAGE = 'SMS_VONAGE',
    SMS_SPEED = 'SMS_SPEED',

    // Google Workspace
    GOOGLE_CALENDAR = 'GOOGLE_CALENDAR',
    GOOGLE_SHEETS = 'GOOGLE_SHEETS',
    GOOGLE_DOCS = 'GOOGLE_DOCS',
}


export const ProviderUtil = {
  /**
   * Lấy giá trị chuỗi của một provider
   * @param type Loại provider
   * @returns Giá trị string tương ứng
   */
  getValue(type: ProviderEnum): string {
    return type;
  },

  /**
   * Lấy enum ProviderEnum từ giá trị chuỗi
   * @param value Giá trị chuỗi
   * @returns Provider enum tương ứng
   * @throws AppException nếu không tìm thấy provider hợp lệ
   */
  getProvider(value: string): ProviderEnum {
    if (Object.values(ProviderEnum).includes(value as ProviderEnum)) {
      return value as ProviderEnum;
    }

    throw new AppException(
      INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_CREATE_FAILED,
      `Provider không hợp lệ: ${value}. Các provider hợp lệ: ${Object.values(ProviderEnum).join(', ')}`
    );
  },

  /**
   * Kiểm tra xem một giá trị có phải là ProviderEnum hợp lệ không
   * @param value Giá trị cần kiểm tra
   * @returns true nếu là ProviderEnum hợp lệ
   */
  isValidProvider(value: string): value is ProviderEnum {
    return Object.values(ProviderEnum).includes(value as ProviderEnum);
  },

  /**
   * Lấy tất cả các provider có sẵn
   * @returns Mảng tất cả các ProviderEnum
   */
  getAllProviders(): ProviderEnum[] {
    return Object.values(ProviderEnum);
  },
};
