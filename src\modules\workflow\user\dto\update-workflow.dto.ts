import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, ValidateNested, MaxLength, MinLength } from 'class-validator';
import { WorkflowDefinitionDto } from './create-workflow.dto';

/**
 * DTO cho việc cập nhật workflow
 * Following agent update-agent.dto patterns
 */
export class UpdateWorkflowDto {
  /**
   * Tên của workflow (phải unique cho mỗi user)
   */
  @ApiPropertyOptional({
    description: 'Tên của workflow (phải unique cho mỗi user)',
    example: 'Updated Workflow Name',
    minLength: 1,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MinLength(1, { message: 'Tên workflow không được để trống' })
  @MaxLength(255, { message: 'Tên workflow không đượ<PERSON> vượt quá 255 ký tự' })
  name?: string;

  /**
   * Trạng thái kích hoạt của workflow
   */
  @ApiPropertyOptional({
    description: 'Trạng thái kích hoạt của workflow',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  /**
   * Định nghĩa workflow (nodes, edges, metadata)
   */
  @ApiPropertyOptional({
    description: 'Định nghĩa workflow (nodes, edges, metadata)',
    type: WorkflowDefinitionDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => WorkflowDefinitionDto)
  definition?: WorkflowDefinitionDto;
}

/**
 * DTO cho việc toggle trạng thái workflow
 */
export class ToggleWorkflowStatusDto {
  /**
   * Trạng thái kích hoạt mới
   */
  @ApiPropertyOptional({
    description: 'Trạng thái kích hoạt mới',
    example: true,
  })
  @IsBoolean()
  isActive: boolean;
}
