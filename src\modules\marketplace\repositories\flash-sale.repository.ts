import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder, LessThanOrEqual } from 'typeorm';
import { FlashSale } from '../entities/flash-sale.entity';
import { FlashSaleStatus } from '../enums/flash-sale-status.enum';
import { ProductStatus } from '../enums/product-status.enum';
import { PaginatedResult } from '@common/response/api-response-dto';

/**
 * Repository cho Flash Sale operations
 */
@Injectable()
export class FlashSaleRepository extends Repository<FlashSale> {
  private readonly logger = new Logger(FlashSaleRepository.name);

  constructor(private dataSource: DataSource) {
    super(FlashSale, dataSource.createEntityManager());
  }

  /**
   * Tìm flash sale theo ID
   */
  async findById(id: number): Promise<FlashSale | null> {
    try {
      return await this.findOne({ where: { id } });
    } catch (error) {
      this.logger.error(`Error finding flash sale by ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm flash sale theo ID với thông tin sản phẩm
   */
  async findByIdWithProduct(id: number): Promise<FlashSale | null> {
    try {
      const queryBuilder = this.createQueryBuilder('fs')
        .leftJoinAndSelect('products', 'p', 'p.id = fs.product_id')
        .where('fs.id = :id', { id });

      const result = await queryBuilder.getRawOne();
      if (!result) return null;

      return this.mapRawToFlashSale(result);
    } catch (error) {
      this.logger.error(`Error finding flash sale with product by ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm flash sales theo user ID với phân trang
   */
  async findByUserId(
    userId: number,
    page: number = 1,
    limit: number = 10,
    status?: FlashSaleStatus
  ): Promise<PaginatedResult<FlashSale>> {
    try {
      const queryBuilder = this.createQueryBuilder('fs')
        .where('fs.user_id = :userId', { userId });

      if (status) {
        queryBuilder.andWhere('fs.status = :status', { status });
      }

      queryBuilder
        .orderBy('fs.created_at', 'DESC')
        .skip((page - 1) * limit)
        .take(limit);

      const [items, total] = await queryBuilder.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`Error finding flash sales by user ID ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm flash sales theo employee ID với phân trang
   */
  async findByEmployeeId(
    employeeId: number,
    page: number = 1,
    limit: number = 10,
    status?: FlashSaleStatus
  ): Promise<PaginatedResult<FlashSale>> {
    try {
      const queryBuilder = this.createQueryBuilder('fs')
        .where('fs.employee_id = :employeeId', { employeeId });

      if (status) {
        queryBuilder.andWhere('fs.status = :status', { status });
      }

      queryBuilder
        .orderBy('fs.created_at', 'DESC')
        .skip((page - 1) * limit)
        .take(limit);

      const [items, total] = await queryBuilder.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`Error finding flash sales by employee ID ${employeeId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra flash sale overlap cho sản phẩm
   */
  async checkOverlappingFlashSale(
    productId: number,
    startTime: number,
    endTime: number,
    excludeId?: number
  ): Promise<boolean> {
    try {
      const queryBuilder = this.createQueryBuilder('fs')
        .where('fs.product_id = :productId', { productId })
        .andWhere('fs.status IN (:...statuses)', { 
          statuses: [FlashSaleStatus.SCHEDULED, FlashSaleStatus.ACTIVE] 
        })
        .andWhere(
          '(fs.start_time <= :endTime AND fs.end_time >= :startTime)',
          { startTime, endTime }
        );

      if (excludeId) {
        queryBuilder.andWhere('fs.id != :excludeId', { excludeId });
      }

      const count = await queryBuilder.getCount();
      return count > 0;
    } catch (error) {
      this.logger.error(`Error checking overlapping flash sale: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm flash sale đang active cho sản phẩm
   */
  async findActiveFlashSaleByProductId(productId: number): Promise<FlashSale | null> {
    try {
      const now = Date.now();
      return await this.createQueryBuilder('fs')
        .where('fs.product_id = :productId', { productId })
        .andWhere('fs.status = :status', { status: FlashSaleStatus.ACTIVE })
        .andWhere('fs.start_time <= :now', { now })
        .andWhere('fs.end_time >= :now', { now })
        .andWhere('fs.is_active = :isActive', { isActive: true })
        .getOne();
    } catch (error) {
      this.logger.error(`Error finding active flash sale for product ${productId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy random flash sale products cho cart suggestions
   */
  async getRandomFlashSaleProducts(
    excludeProductIds: number[] = [],
    excludeUserId?: number,
    limit: number = 10
  ): Promise<FlashSale[]> {
    try {
      const now = Date.now();
      
      let queryBuilder = this.createQueryBuilder('fs')
        .leftJoinAndSelect('products', 'p', 'p.id = fs.product_id')
        .where('fs.status = :status', { status: FlashSaleStatus.ACTIVE })
        .andWhere('fs.start_time <= :now', { now })
        .andWhere('fs.end_time >= :now', { now })
        .andWhere('fs.is_active = true')
        .andWhere('p.status = :productStatus', { productStatus: ProductStatus.APPROVED });

      if (excludeProductIds.length > 0) {
        queryBuilder = queryBuilder.andWhere('fs.product_id NOT IN (:...excludeProductIds)', { excludeProductIds });
      }

      if (excludeUserId) {
        queryBuilder = queryBuilder.andWhere('p.user_id != :excludeUserId', { excludeUserId });
      }

      queryBuilder = queryBuilder
        .orderBy('RANDOM()')
        .limit(limit);

      const results = await queryBuilder.getRawMany();

      // ✅ Chỉ log khi có kết quả để debug suggestions
      if (results.length > 0) {
        this.logger.debug(`Flash sale suggestions query found ${results.length} results`);
      }

      return results.map(result => this.mapRawToFlashSale(result));
    } catch (error) {
      this.logger.error(`Error getting random flash sale products: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm flash sales cần chuyển status tự động
   */
  async findFlashSalesForStatusTransition(): Promise<FlashSale[]> {
    try {
      const now = Date.now();

      // ✅ SỬA: Sử dụng TypeORM LessThanOrEqual thay vì MongoDB syntax

      // Tìm SCHEDULED flash sales cần chuyển thành ACTIVE
      const scheduledToActive = await this.find({
        where: {
          status: FlashSaleStatus.SCHEDULED,
          startTime: LessThanOrEqual(now),
          isActive: true
        }
      });

      // Tìm ACTIVE flash sales cần chuyển thành EXPIRED
      const activeToExpired = await this.find({
        where: {
          status: FlashSaleStatus.ACTIVE,
          endTime: LessThanOrEqual(now)
        }
      });

      return [...scheduledToActive, ...activeToExpired];
    } catch (error) {
      this.logger.error(`Error finding flash sales for status transition: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Map raw query result to FlashSale entity
   */
  private mapRawToFlashSale(raw: any): FlashSale {
    const flashSale = new FlashSale();
    
    // Flash sale fields
    flashSale.id = raw.fs_id;
    flashSale.productId = raw.fs_product_id;
    flashSale.userId = raw.fs_user_id;
    flashSale.employeeId = raw.fs_employee_id;
    flashSale.discountPercentage = raw.fs_discount_percentage;
    flashSale.displayTime = raw.fs_display_time;
    // ✅ Safe timestamp mapping
    flashSale.startTime = this.safeParseTimestamp(raw.fs_start_time);
    flashSale.endTime = this.safeParseTimestamp(raw.fs_end_time);
    flashSale.maxConfiguration = raw.fs_max_configuration;
    flashSale.status = raw.fs_status;
    flashSale.isActive = raw.fs_is_active;
    flashSale.createdAt = raw.fs_created_at;
    flashSale.updatedAt = raw.fs_updated_at;

    // Product fields (if joined)
    if (raw.p_id) {
      flashSale.product = {
        id: raw.p_id,
        name: raw.p_name,
        description: raw.p_description,
        listedPrice: raw.p_listed_price,
        discountedPrice: raw.p_discounted_price,
        images: raw.p_images,
        category: raw.p_category,
        status: raw.p_status,
        userId: raw.p_user_id
      };

      // Calculate sale price
      const originalPrice = raw.p_discounted_price || raw.p_listed_price;
      flashSale.salePrice = Math.round(originalPrice * (1 - flashSale.discountPercentage / 100));

      // Calculate time remaining
      const now = Date.now();
      if (flashSale.status === FlashSaleStatus.ACTIVE && flashSale.endTime > now) {
        flashSale.timeRemaining = Math.floor((flashSale.endTime - now) / 1000);
      }
    }

    return flashSale;
  }

  /**
   * ✅ Helper method để safely parse timestamp từ database
   */
  private safeParseTimestamp(value: any): number {
    try {
      if (value === null || value === undefined) {
        this.logger.warn(`Null/undefined timestamp, using current time`);
        return Date.now();
      }

      let timestamp: number;

      if (typeof value === 'string') {
        timestamp = parseInt(value);
      } else if (typeof value === 'number') {
        timestamp = value;
      } else {
        this.logger.warn(`Invalid timestamp type: ${typeof value}, value: ${value}`);
        return Date.now();
      }

      // Validate timestamp range
      if (isNaN(timestamp) || timestamp < 0 || timestamp > 9999999999999) {
        this.logger.warn(`Invalid timestamp value: ${timestamp}, using current time`);
        return Date.now();
      }

      return timestamp;
    } catch (error) {
      this.logger.error(`Error parsing timestamp: ${error.message}`, error.stack);
      return Date.now();
    }
  }
}
