import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CampaignAudience, CampaignSegment, EmailCampaignContent } from '../../types/admin-email-campaign.types';

/**
 * DTO cho response admin email campaign item trong danh sách
 */
export class AdminEmailCampaignItemDto {
  @ApiProperty({
    description: 'ID của campaign',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Summer Sale Campaign 2024'
  })
  name: string;

  @ApiProperty({
    description: 'Tiêu đề email',
    example: '<PERSON><PERSON><PERSON>ến mãi mùa hè - Giảm giá 50%'
  })
  subject: string;

  @ApiProperty({
    description: 'Trạng thái campaign',
    example: 'COMPLETED'
  })
  status: string;

  @ApiProperty({
    description: 'Tổng số email dự kiến gửi',
    example: 1500
  })
  totalRecipients: number;

  @ApiPropertyOptional({
    description: 'Thông tin segment',
    example: {
      id: 1,
      name: 'VIP Customers',
      description: 'Khách hàng VIP với chi tiêu cao'
    }
  })
  segment?: CampaignSegment;

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: 1703980800
  })
  scheduledAt?: number;

  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu gửi (Unix timestamp)',
    example: 1703980800
  })
  startedAt?: number;

  @ApiPropertyOptional({
    description: 'Thời gian hoàn thành (Unix timestamp)',
    example: 1703982600
  })
  completedAt?: number;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1703894400
  })
  createdAt: number;

  @ApiProperty({
    description: 'ID của nhân viên tạo',
    example: 1
  })
  createdBy: number;

  @ApiPropertyOptional({
    description: 'Tên nhân viên tạo',
    example: 'Nguyễn Văn A'
  })
  createdByName?: string;

  @ApiPropertyOptional({
    description: 'Thống kê cơ bản',
    example: {
      sentCount: 1450,
      openCount: 580,
      clickCount: 145,
      openRate: 40.0,
      clickRate: 10.0
    }
  })
  stats?: {
    sentCount: number;
    openCount: number;
    clickCount: number;
    openRate: number;
    clickRate: number;
  };
}

/**
 * DTO cho response chi tiết admin email campaign
 */
export class AdminEmailCampaignDetailDto {
  @ApiProperty({
    description: 'ID của campaign',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên chiến dịch',
    example: 'Summer Sale Campaign 2024'
  })
  name: string;

  @ApiProperty({
    description: 'Tiêu đề email',
    example: 'Khuyến mãi mùa hè - Giảm giá 50%'
  })
  subject: string;

  @ApiPropertyOptional({
    description: 'Thông tin segment',
    example: {
      id: 1,
      name: 'VIP Customers',
      description: 'Khách hàng VIP với chi tiêu cao'
    }
  })
  segment?: CampaignSegment;

  @ApiPropertyOptional({
    description: 'Tên người gửi',
    example: 'RedAI Support'
  })
  senderName?: string;

  @ApiPropertyOptional({
    description: 'Email người gửi',
    example: '<EMAIL>'
  })
  senderEmail?: string;

  @ApiPropertyOptional({
    description: 'Email reply-to',
    example: '<EMAIL>'
  })
  replyTo?: string;

  @ApiPropertyOptional({
    description: 'Nội dung email (HTML và text)',
    example: {
      html: '<h1>Chào mừng!</h1><p>Nội dung email...</p>',
      text: 'Chào mừng! Nội dung email...'
    }
  })
  content?: EmailCampaignContent;

  @ApiPropertyOptional({
    description: 'Danh sách audience',
    example: [
      { name: 'Nguyễn Văn A', email: '<EMAIL>' },
      { name: 'Trần Thị B', email: '<EMAIL>' }
    ],
    type: [Object]
  })
  audiences?: CampaignAudience[];

  @ApiPropertyOptional({
    description: 'Danh sách email cụ thể',
    example: ['<EMAIL>', '<EMAIL>']
  })
  emailList?: string[];

  @ApiPropertyOptional({
    description: 'Biến template',
    example: {
      customerName: 'Nguyễn Văn A',
      discountPercent: '50'
    }
  })
  templateVariables?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Thời gian lên lịch gửi (Unix timestamp)',
    example: 1703980800
  })
  scheduledAt?: number;

  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu gửi (Unix timestamp)',
    example: 1703980800
  })
  startedAt?: number;

  @ApiPropertyOptional({
    description: 'Thời gian hoàn thành (Unix timestamp)',
    example: 1703982600
  })
  completedAt?: number;

  @ApiProperty({
    description: 'Trạng thái campaign',
    example: 'COMPLETED'
  })
  status: string;

  @ApiProperty({
    description: 'Tổng số email dự kiến gửi',
    example: 1500
  })
  totalRecipients: number;

  @ApiPropertyOptional({
    description: 'Danh sách job IDs',
    example: ['job_123', 'job_124']
  })
  jobIds?: string[];

  @ApiPropertyOptional({
    description: 'Cấu hình email server',
    example: {
      serverId: 1,
      serverName: 'SMTP Server 1'
    }
  })
  emailServerConfig?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Ghi chú',
    example: 'Campaign khuyến mãi cuối năm cho khách hàng VIP'
  })
  notes?: string;

  @ApiProperty({
    description: 'ID của nhân viên tạo',
    example: 1
  })
  createdBy: number;

  @ApiPropertyOptional({
    description: 'ID của nhân viên cập nhật cuối',
    example: 1
  })
  updatedBy?: number;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1703894400
  })
  createdAt: number;

  @ApiPropertyOptional({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1703894400
  })
  updatedAt?: number;
}

/**
 * DTO cho thông tin campaign được cập nhật trong sync status
 */
export class SyncCampaignUpdateDto {
  @ApiProperty({
    description: 'ID của campaign',
    example: 1
  })
  campaignId: number;

  @ApiProperty({
    description: 'Tên campaign',
    example: 'Welcome Email'
  })
  campaignName: string;

  @ApiProperty({
    description: 'Trạng thái trước khi cập nhật',
    example: 'SCHEDULED'
  })
  previousStatus: string;

  @ApiProperty({
    description: 'Trạng thái sau khi cập nhật',
    example: 'FAILED'
  })
  currentStatus: string;

  @ApiProperty({
    description: 'Lý do cập nhật',
    example: 'Quá thời gian lên lịch và không còn job trong queue'
  })
  reason: string;
}

/**
 * DTO cho tóm tắt kết quả sync status
 */
export class SyncStatusSummaryDto {
  @ApiProperty({
    description: 'Số campaign SCHEDULED chuyển thành FAILED',
    example: 1
  })
  scheduledToFailed: number;

  @ApiProperty({
    description: 'Số campaign SENDING chuyển thành COMPLETED',
    example: 1
  })
  sendingToCompleted: number;

  @ApiProperty({
    description: 'Số campaign SENDING chuyển thành FAILED',
    example: 0
  })
  sendingToFailed: number;
}

/**
 * Response DTO cho việc sync trạng thái campaign
 */
export class SyncCampaignStatusResponseDto {
  @ApiProperty({
    description: 'Tổng số campaign được kiểm tra',
    example: 25
  })
  totalCampaignsChecked: number;

  @ApiProperty({
    description: 'Danh sách campaign được cập nhật',
    type: [SyncCampaignUpdateDto]
  })
  updatedCampaigns: SyncCampaignUpdateDto[];

  @ApiProperty({
    description: 'Tóm tắt kết quả cập nhật',
    type: SyncStatusSummaryDto
  })
  summary: SyncStatusSummaryDto;
}