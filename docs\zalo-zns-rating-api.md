# API Lấy Thông Tin Đánh Giá Khách Hàng ZNS

## Tổng quan

API này cho phép lấy thông tin đánh giá khách hàng đã phản hồi qua template đánh giá dịch vụ ZNS của Zalo.

## Endpoint

```
GET /v1/marketing/zalo/zns/{integrationId}/ratings
```

## Tham số

### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `integrationId` | string | ✅ | ID của Integration (UUID) |

### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `template_id` | string | ✅ | ID của template cần lấy thông tin đánh giá |
| `from_time` | number | ✅ | Thời điểm bắt đầu (Unix timestamp milliseconds) |
| `to_time` | number | ✅ | Thời điểm kết thúc (Unix timestamp milliseconds) |
| `page` | number | ❌ | Số trang hiện tại (mặc định: 1) |
| `limit` | number | ❌ | Số lượng đánh giá tối đa (1-100, mặc định: 10) |
| `search` | string | ❌ | Từ khóa tìm kiếm |
| `sortBy` | string | ❌ | Trường sắp xếp |
| `sortDirection` | string | ❌ | Hướng sắp xếp (ASC/DESC) |

## Headers

```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

## Request Example

```bash
curl -X GET \
  "https://api.redai.vn/v1/marketing/zalo/zns/123e4567-e89b-12d3-a456-************/ratings?template_id=203972&from_time=1616673095659&to_time=1616673271320&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## Response

### Success Response (200)

```json
{
  "code": 200,
  "message": "Lấy thông tin đánh giá khách hàng thành công",
  "result": {
    "items": [
      {
        "note": "Tôi rất hài lòng.",
        "rate": 5,
        "submitDate": "1616673095659",
        "msgId": "7e4c33cfc20b05575c18",
        "feedbacks": [
          "Nhân viên vui vẻ",
          "Quy trình đơn giản, hiệu quả",
          "Xử lý nhanh nhẹn",
          "Tác phong chuyên nghiệp",
          "Hướng dẫn tận tình"
        ],
        "trackingId": "1956"
      },
      {
        "note": "Phục vụ chưa tốt",
        "rate": 4,
        "submitDate": "1616673164105",
        "msgId": "4da9fb7506b1c1ed98a2",
        "feedbacks": [
          "Cải thiện thái độ nhân viên",
          "Xử lý nhanh hơn"
        ],
        "trackingId": "729"
      }
    ],
    "meta": {
      "totalItems": 3,
      "itemCount": 2,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### Error Responses

#### 400 - Tham số không hợp lệ
```json
{
  "code": 9999,
  "message": "Template ID không hợp lệ"
}
```

#### 401 - Chưa có access token
```json
{
  "code": 401,
  "message": "Official Account chưa có access token"
}
```

#### 403 - Không có quyền truy cập
```json
{
  "code": 403,
  "message": "Không có quyền truy cập template này"
}
```

## Cấu trúc dữ liệu

### ZnsRatingDetailDto

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| `note` | string? | Phần ghi chú thêm của khách hàng |
| `rate` | number | Số sao được khách hàng đánh giá (1-5) |
| `submitDate` | string | Thời điểm khách hàng submit đánh giá (Unix timestamp milliseconds) |
| `msgId` | string | ID của thông tin đánh giá |
| `feedbacks` | string[]? | Phần nhận xét từ khách hàng |
| `trackingId` | string? | Tracking ID từ phía đối tác truyền vào khi gửi ZNS |

## Lưu ý quan trọng

1. **Quyền truy cập**: Ứng dụng chỉ có thể lấy thông tin đánh giá từ template được tạo bởi ứng dụng đó hoặc OA cấp quyền cho ứng dụng.

2. **Access token**: Access token truyền vào phải ứng với template ID được tạo bởi app và OA.

3. **Thời gian**: 
   - `from_time` và `to_time` phải là Unix timestamp tính bằng milliseconds
   - `submitDate` trong response cũng là Unix timestamp milliseconds

4. **Phân trang**: 
   - Tối đa 100 đánh giá mỗi lần gọi API
   - Sử dụng `offset` và `limit` để phân trang

5. **Template ID**: Phải là ID của template đánh giá dịch vụ hợp lệ

## Ví dụ sử dụng

### JavaScript/TypeScript

```typescript
const response = await fetch('/v1/marketing/zalo/zns/123e4567-e89b-12d3-a456-************/ratings?' + 
  new URLSearchParams({
    template_id: '203972',
    from_time: '1616673095659',
    to_time: '1616673271320',
    offset: '0',
    limit: '10'
  }), {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log('Đánh giá khách hàng:', data.data.data.data);
```

### Python

```python
import requests

url = "https://api.redai.vn/v1/marketing/zalo/zns/123e4567-e89b-12d3-a456-************/ratings"
params = {
    "template_id": "203972",
    "from_time": 1616673095659,
    "to_time": 1616673271320,
    "offset": 0,
    "limit": 10
}
headers = {
    "Authorization": f"Bearer {token}",
    "Content-Type": "application/json"
}

response = requests.get(url, params=params, headers=headers)
data = response.json()
print("Đánh giá khách hàng:", data["data"]["data"]["data"])
```

## Tích hợp với hệ thống

API này có thể được sử dụng để:

1. **Theo dõi chất lượng dịch vụ**: Phân tích đánh giá khách hàng để cải thiện dịch vụ
2. **Báo cáo định kỳ**: Tạo báo cáo về mức độ hài lòng của khách hàng
3. **Cảnh báo chất lượng**: Thiết lập cảnh báo khi có đánh giá thấp
4. **Phân tích xu hướng**: Theo dõi xu hướng đánh giá theo thời gian
