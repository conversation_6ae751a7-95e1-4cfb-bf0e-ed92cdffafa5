# API Lấy Audience với Filter theo Segment

## Tổng quan
Tài liệu này mô tả cách sử dụng các API audience đã được cải thiện để hỗ trợ filter theo segment, bao gồm cả API phân trang và API lấy tất cả.

## API 1: <PERSON><PERSON>y danh sách audience (có phân trang)

### Endpoint
```
GET /api/marketing/audiences
```

### Tham số mới cho segment filtering

#### Lọc theo segment (Include)
- `segmentId` (number): Lấy audience thuộc segment cụ thể
- `segmentIds` (array): Lấy audience thuộc ít nhất một trong các segment (OR logic)

#### Loại trừ segment (Exclude)
- `excludeSegmentId` (number): Loại trừ audience thuộc segment cụ thể
- `excludeSegmentIds` (array): Loại trừ audience thuộc ít nhất một trong các segment

### Ví dụ Request

#### 1. Lấy audience thuộc segment 123
```
GET /api/marketing/audiences?segmentId=123&page=1&limit=20
```

#### 2. Lấy audience thuộc segment 123 hoặc 456
```
GET /api/marketing/audiences?segmentIds=123,456&page=1&limit=20
```

#### 3. Lấy audience thuộc segment 123 nhưng loại trừ segment 456
```
GET /api/marketing/audiences?segmentId=123&excludeSegmentId=456&page=1&limit=20
```

#### 4. Lấy audience từ Zalo thuộc segment 123
```
GET /api/marketing/audiences?platform=ZALO&segmentId=123&page=1&limit=20
```

#### 5. Tìm kiếm trong segment với từ khóa
```
GET /api/marketing/audiences?segmentId=123&search=nguyen&page=1&limit=20
```

## API 2: Lấy tất cả audience (không phân trang)

### Endpoint
```
GET /api/marketing/audiences/all
```

### Tham số đặc biệt

#### Performance
- `limit` (number): Giới hạn số lượng kết quả (mặc định: 1000, tối đa: 10000)
- `basicInfo` (boolean): Chỉ trả về thông tin cơ bản (id, name, email, phone) để tăng tốc độ

#### Sorting
- `sortBy` (string): Trường sắp xếp (id, name, email, createdAt, updatedAt)
- `sortDirection` (string): Hướng sắp xếp (ASC, DESC)

### Ví dụ Request

#### 1. Lấy tất cả audience thuộc segment 123 (thông tin đầy đủ)
```
GET /api/marketing/audiences/all?segmentId=123&limit=5000
```

#### 2. Lấy thông tin cơ bản của audience thuộc segment 123
```
GET /api/marketing/audiences/all?segmentId=123&basicInfo=true&limit=10000
```

#### 3. Lấy audience từ nhiều segment, sắp xếp theo tên
```
GET /api/marketing/audiences/all?segmentIds=123,456,789&sortBy=name&sortDirection=ASC&limit=2000
```

#### 4. Lấy audience Zalo loại trừ segment VIP
```
GET /api/marketing/audiences/all?platform=ZALO&excludeSegmentId=999&basicInfo=true&limit=5000
```

#### 5. Tìm kiếm trong segment với giới hạn kết quả
```
GET /api/marketing/audiences/all?segmentId=123&search=gmail&limit=1000&basicInfo=true
```

## Response Examples

### API có phân trang
```json
{
  "success": true,
  "message": "Danh sách audience",
  "data": {
    "data": [
      {
        "id": 1234,
        "name": "Nguyễn Văn A",
        "email": "<EMAIL>",
        "phone": "+84912345678",
        "avatar": "https://cdn.redai.com/customer_avatars/avatar1.jpg",
        "tags": [
          { "id": 1, "name": "VIP Customer" }
        ],
        "customFields": [
          {
            "fieldId": 1,
            "fieldName": "Nghề nghiệp",
            "value": "Kỹ sư phần mềm"
          }
        ],
        "importResource": "ZALO",
        "integrationId": "550e8400-e29b-41d4-a716-446655440000",
        "zaloUserId": "1234567890123456789",
        "createdAt": "2024-06-24T15:30:00Z",
        "updatedAt": "2024-06-24T15:30:00Z"
      }
    ],
    "meta": {
      "total": 1250,
      "page": 1,
      "limit": 20,
      "totalPages": 63,
      "hasPreviousPage": false,
      "hasNextPage": true
    }
  }
}
```

### API không phân trang (basicInfo=true)
```json
{
  "success": true,
  "message": "Danh sách tất cả audience",
  "data": [
    {
      "id": 1234,
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "phone": "+84912345678",
      "createdAt": "2024-06-24T15:30:00Z",
      "updatedAt": "2024-06-24T15:30:00Z",
      "customFields": [],
      "tags": [],
      "avatar": null,
      "importResource": "ZALO",
      "integrationId": "550e8400-e29b-41d4-a716-446655440000",
      "zaloUserId": "1234567890123456789"
    }
  ]
}
```

## Logic Filter theo Segment

### Include Logic (OR)
- `segmentId=123`: Lấy audience thuộc segment 123
- `segmentIds=123,456`: Lấy audience thuộc segment 123 HOẶC 456

### Exclude Logic (OR)
- `excludeSegmentId=456`: Loại trừ audience thuộc segment 456
- `excludeSegmentIds=456,789`: Loại trừ audience thuộc segment 456 HOẶC 789

### Combined Logic
```
Kết quả = (Include Logic) - (Exclude Logic)
```

Ví dụ:
```
segmentIds=123,456&excludeSegmentId=789
= (audience thuộc segment 123 HOẶC 456) - (audience thuộc segment 789)
```

## Performance Tips

### 1. Sử dụng basicInfo=true
Khi chỉ cần thông tin cơ bản (id, name, email, phone), sử dụng `basicInfo=true` để:
- Giảm thời gian query database
- Giảm kích thước response
- Tăng tốc độ xử lý

### 2. Giới hạn kết quả hợp lý
- API phân trang: `limit=20-50` cho UI
- API không phân trang: `limit=1000-5000` cho export/processing

### 3. Kết hợp filter
Kết hợp segment filter với các filter khác để giảm số lượng kết quả:
```
?segmentId=123&platform=ZALO&search=gmail&limit=1000
```

## Use Cases

### 1. Export audience từ segment
```
GET /api/marketing/audiences/all?segmentId=123&basicInfo=true&limit=10000
```

### 2. Hiển thị audience trong segment (phân trang)
```
GET /api/marketing/audiences?segmentId=123&page=1&limit=20
```

### 3. Tìm audience không thuộc segment VIP
```
GET /api/marketing/audiences/all?excludeSegmentId=999&limit=5000
```

### 4. Lấy audience từ nhiều segment cho campaign
```
GET /api/marketing/audiences/all?segmentIds=123,456,789&platform=ZALO&basicInfo=true&limit=5000
```

### 5. Phân tích audience overlap
```
GET /api/marketing/audiences/all?segmentId=123&excludeSegmentIds=456,789&limit=10000
```

## Error Handling

### Segment không tồn tại
Nếu segment ID không tồn tại hoặc không thuộc về user, API sẽ trả về kết quả rỗng thay vì lỗi.

### Limit vượt quá giới hạn
API `/all` sẽ tự động giới hạn `limit` tối đa 10000 để tránh quá tải server.

### Performance timeout
Với segment có số lượng audience lớn (>100k), có thể xảy ra timeout. Khuyến nghị:
- Sử dụng `basicInfo=true`
- Giảm `limit`
- Thêm filter khác để thu hẹp kết quả
