import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsObject,
  ValidateNested,
  IsUUID,
  IsArray,
  IsEnum
} from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto';
import { StructuredContentInterface } from '@modules/agent/interfaces/agent-memory.interface';

/**
 * DTO cho structured content của agent memory
 */
export class AgentMemoryStructuredContentDto implements StructuredContentInterface {
  @ApiPropertyOptional({
    description: 'Tiêu đề của memory',
    example: 'JavaScript Programming',
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Lý do ghi nhớ thông tin này',
    example: 'Để hỗ trợ lập trình',
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiProperty({
    description: 'Nội dung chính của memory',
    example: '<PERSON><PERSON><PERSON> thức về JavaScript cơ bản và nâng cao',
  })
  @IsString()
  content: string;
}

/**
 * DTO cho metadata của agent memory
 */
export class AgentMemoryMetadataDto {
  @ApiPropertyOptional({
    description: 'Nguồn gốc của thông tin',
    example: 'training',
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({
    description: 'Thời gian cập nhật cuối (timestamp)',
    example: 1703123456789,
  })
  @IsOptional()
  lastUpdated?: number;

  @ApiPropertyOptional({
    description: 'Tags để phân loại',
    example: ['programming', 'javascript'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  /**
   * Thông tin bổ sung khác
   */
  [key: string]: any;
}

/**
 * DTO để tạo agent memory mới
 */
export class CreateAgentMemoryDto {
  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: AgentMemoryStructuredContentDto,
    example: {
      title: 'JavaScript Programming',
      content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
      reason: 'Để hỗ trợ lập trình web hiệu quả'
    }
  })
  @IsObject()
  @ValidateNested()
  @Type(() => AgentMemoryStructuredContentDto)
  structuredContent: AgentMemoryStructuredContentDto;
}

/**
 * DTO để cập nhật agent memory
 */
export class UpdateAgentMemoryDto {
  @ApiPropertyOptional({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: AgentMemoryStructuredContentDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AgentMemoryStructuredContentDto)
  structuredContent?: AgentMemoryStructuredContentDto;
}

/**
 * Enum cho các trường có thể sắp xếp trong agent memories
 */
export enum AgentMemorySortField {
  CREATED_AT = 'createdAt',
  TITLE = 'title',
  CONTENT = 'content',
}

/**
 * DTO cho query danh sách agent memories
 */
export class QueryAgentMemoryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Trường cần sắp xếp',
    enum: AgentMemorySortField,
    example: AgentMemorySortField.CREATED_AT,
    default: AgentMemorySortField.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(AgentMemorySortField)
  declare sortBy?: AgentMemorySortField;

  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  declare sortDirection?: SortDirection;

  constructor() {
    super();
    this.sortBy = AgentMemorySortField.CREATED_AT;
    this.sortDirection = SortDirection.DESC;
  }
}

/**
 * DTO response cho agent memory
 */
export class AgentMemoryResponseDto {
  @ApiProperty({
    description: 'UUID của memory',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'UUID của agent',
    example: '456e7890-e89b-12d3-a456-************',
  })
  agentId: string;

  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: AgentMemoryStructuredContentDto,
  })
  structuredContent: AgentMemoryStructuredContentDto;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1703120000000,
  })
  createdAt: number;
}

/**
 * DTO response cho danh sách agent memories
 */
export class AgentMemoryListResponseDto {
  @ApiProperty({
    description: 'Danh sách memories',
    type: [AgentMemoryResponseDto],
  })
  items: AgentMemoryResponseDto[];

  @ApiProperty({
    description: 'Metadata phân trang',
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };
}
