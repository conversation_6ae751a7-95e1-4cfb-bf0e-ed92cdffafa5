import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { FLASH_SALE_ERROR_CODES } from '../../exceptions/flash-sale.exception';
import { FlashSaleRepository } from '../../repositories/flash-sale.repository';
import { ProductRepository } from '../../repositories/product.repository';
import { FlashSaleValidationHelper } from '../../helpers/flash-sale-validation.helper';
import { FlashSale } from '../../entities/flash-sale.entity';
import { FlashSaleStatus } from '../../enums/flash-sale-status.enum';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Transactional } from 'typeorm-transactional';
import {
  CreateFlashSaleDto,
  UpdateFlashSaleDto,
  UpdateFlashSaleStatusDto,
  BulkDeleteFlashSaleDto,
  BulkDeleteResponseDto,
  FlashSaleResponseDto,
  QueryFlashSaleUserDto,
  PaginatedFlashSaleResponseDto,
  FlashSaleProductResponseDto,
  FlashSaleSuggestionsResponseDto,
  SellerInfoDto
} from '../dto';
import { DEFAULT_MAX_CONFIGURATION } from '../../interfaces/max-configuration.interface';

/**
 * Service xử lý logic nghiệp vụ Flash Sale cho User
 */
@Injectable()
export class FlashSaleUserService {
  private readonly logger = new Logger(FlashSaleUserService.name);

  constructor(
    private readonly flashSaleRepository: FlashSaleRepository,
    private readonly productRepository: ProductRepository,
    private readonly flashSaleValidationHelper: FlashSaleValidationHelper
  ) {}

  /**
   * Tạo flash sale mới (User)
   */
  @Transactional()
  async createFlashSale(userId: number, createDto: CreateFlashSaleDto): Promise<FlashSaleResponseDto> {
    try {
      const { productId, discountPercentage, displayTime, startTime, endTime, maxConfiguration, status } = createDto;

      // Validate discount percentage
      this.flashSaleValidationHelper.validateDiscountPercentage(discountPercentage);

      // Validate time sequence
      this.flashSaleValidationHelper.validateTimeSequence(displayTime, startTime, endTime);

      // Validate product existence
      const product = await this.productRepository.findById(productId);
      if (!product) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.PRODUCT_NOT_ELIGIBLE,
          `Sản phẩm với ID ${productId} không tồn tại`
        );
      }

      // Check if user owns the product
      if (product.userId !== userId) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
          `Bạn không có quyền tạo flash sale cho sản phẩm ID ${productId}. Chỉ có thể tạo flash sale cho sản phẩm của chính mình`
        );
      }

      // Validate product eligibility
      this.flashSaleValidationHelper.validateProductEligibility(product);

      // Check overlapping flash sale
      await this.flashSaleValidationHelper.checkOverlappingFlashSale(productId, startTime, endTime);

      // Check user flash sale limit (max 3 active/scheduled flash sales)
      await this.checkUserFlashSaleLimit(userId);

      // Validate max configuration
      const validatedMaxConfig = this.flashSaleValidationHelper.validateMaxConfiguration(
        maxConfiguration || DEFAULT_MAX_CONFIGURATION
      );

      // ✅ Determine final status (default to DRAFT if not provided)
      const finalStatus = status || FlashSaleStatus.DRAFT;

      // ✅ Additional validation if status is SCHEDULED
      if (finalStatus === FlashSaleStatus.SCHEDULED) {
        const now = Date.now();
        if (startTime <= now) {
          throw new AppException(
            FLASH_SALE_ERROR_CODES.INVALID_TIME_RANGE,
            'Không thể tạo flash sale với trạng thái SCHEDULED khi thời gian bắt đầu đã qua'
          );
        }
      }

      // Create flash sale entity
      const flashSale = this.flashSaleRepository.create({
        productId,
        userId,
        employeeId: null,
        discountPercentage,
        displayTime, // displayTime is already in seconds (1-60)
        startTime,
        endTime,
        maxConfiguration: validatedMaxConfig,
        status: finalStatus, // ✅ Use selected status
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });

      // Save to database
      const savedFlashSale = await this.flashSaleRepository.save(flashSale);

      this.logger.log(`User ${userId} created flash sale ${savedFlashSale.id} for product ${productId}`);

      return this.mapToResponseDto(savedFlashSale);
    } catch (error) {
      this.logger.error(`Failed to create flash sale: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException(
        FLASH_SALE_ERROR_CODES.CREATION_FAILED,
        `Tạo flash sale thất bại: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách flash sale của user với phân trang
   */
  async getUserFlashSales(userId: number, queryDto: QueryFlashSaleUserDto): Promise<PaginatedFlashSaleResponseDto> {
    try {
      const { page = 1, limit = 10, status, productId, isActive, sortBy = 'createdAt', sortOrder = 'DESC' } = queryDto;

      const result = await this.flashSaleRepository.findByUserId(userId, page, limit, status);

      const responseItems = result.items.map(item => this.mapToResponseDto(item));

      return {
        items: responseItems,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(`Failed to get user flash sales: ${error.message}`, error.stack);
      throw new AppException(
        FLASH_SALE_ERROR_CODES.RETRIEVAL_FAILED,
        `Lấy danh sách flash sale thất bại: ${error.message}`
      );
    }
  }

  /**
   * Lấy chi tiết flash sale của user
   */
  async getUserFlashSaleById(userId: number, id: number): Promise<FlashSaleResponseDto> {
    try {
      const flashSale = await this.flashSaleRepository.findByIdWithProduct(id);
      
      if (!flashSale) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
          'Flash sale không tồn tại'
        );
      }

      // Validate ownership
      this.flashSaleValidationHelper.validateOwnership(flashSale, userId);

      return this.mapToResponseDto(flashSale);
    } catch (error) {
      this.logger.error(`Failed to get user flash sale ${id}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException(
        FLASH_SALE_ERROR_CODES.RETRIEVAL_FAILED,
        `Lấy thông tin flash sale thất bại: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật flash sale của user
   */
  @Transactional()
  async updateUserFlashSale(userId: number, id: number, updateDto: UpdateFlashSaleDto): Promise<FlashSaleResponseDto> {
    try {
      const flashSale = await this.flashSaleRepository.findById(id);
      
      if (!flashSale) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
          'Flash sale không tồn tại'
        );
      }

      // Validate ownership
      this.flashSaleValidationHelper.validateOwnership(flashSale, userId);

      // ✅ Allow updates for DRAFT and SCHEDULED status
      if (flashSale.status !== FlashSaleStatus.DRAFT && flashSale.status !== FlashSaleStatus.SCHEDULED) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.INVALID_STATUS_TRANSITION,
          `Chỉ có thể cập nhật flash sale ở trạng thái DRAFT hoặc SCHEDULED. Flash sale hiện tại đang ở trạng thái ${flashSale.status}`
        );
      }

      // Validate updates
      if (updateDto.discountPercentage) {
        this.flashSaleValidationHelper.validateDiscountPercentage(updateDto.discountPercentage);
      }

      if (updateDto.displayTime || updateDto.startTime || updateDto.endTime) {
        const displayTime = updateDto.displayTime || flashSale.displayTime;
        const startTime = updateDto.startTime || flashSale.startTime;
        const endTime = updateDto.endTime || flashSale.endTime;
        
        this.flashSaleValidationHelper.validateTimeSequence(displayTime, startTime, endTime);

        // Check overlapping if time changed
        if (updateDto.startTime || updateDto.endTime) {
          await this.flashSaleValidationHelper.checkOverlappingFlashSale(
            flashSale.productId,
            startTime,
            endTime,
            id
          );
        }
      }

      if (updateDto.maxConfiguration) {
        updateDto.maxConfiguration = this.flashSaleValidationHelper.validateMaxConfiguration(updateDto.maxConfiguration);
      }

      // Update fields
      Object.assign(flashSale, {
        ...updateDto,
        updatedAt: Date.now()
      });

      const updatedFlashSale = await this.flashSaleRepository.save(flashSale);

      this.logger.log(`User ${userId} updated flash sale ${id}`);

      return this.mapToResponseDto(updatedFlashSale);
    } catch (error) {
      this.logger.error(`Failed to update user flash sale ${id}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException(
        FLASH_SALE_ERROR_CODES.UPDATE_FAILED,
        `Cập nhật flash sale thất bại: ${error.message}`
      );
    }
  }

  /**
   * ✅ THÊM MỚI: Cập nhật trạng thái flash sale (User)
   */
  @Transactional()
  async updateFlashSaleStatus(userId: number, id: number, statusDto: UpdateFlashSaleStatusDto): Promise<FlashSaleResponseDto> {
    try {
      const flashSale = await this.flashSaleRepository.findById(id);

      if (!flashSale) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
          'Flash sale không tồn tại'
        );
      }

      // ✅ Validate ownership (user chỉ update flash sale của mình)
      this.flashSaleValidationHelper.validateOwnership(flashSale, userId);

      // ✅ Validate status transition
      this.flashSaleValidationHelper.validateStatusTransition(flashSale.status, statusDto.status);

      // ✅ Nếu chuyển sang SCHEDULED, validate thời gian
      if (statusDto.status === FlashSaleStatus.SCHEDULED) {
        const now = Date.now();
        if (flashSale.startTime <= now) {
          throw new AppException(
            FLASH_SALE_ERROR_CODES.INVALID_TIME_RANGE,
            'Không thể lên lịch flash sale với thời gian bắt đầu đã qua'
          );
        }
      }

      // Update status
      flashSale.status = statusDto.status;
      flashSale.updatedAt = Date.now();

      const updatedFlashSale = await this.flashSaleRepository.save(flashSale);

      this.logger.log(`User ${userId} updated flash sale ${id} status to ${statusDto.status}`);

      return this.mapToResponseDto(updatedFlashSale);
    } catch (error) {
      this.logger.error(`Failed to update user flash sale status ${id}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        FLASH_SALE_ERROR_CODES.STATUS_UPDATE_FAILED,
        `Cập nhật trạng thái flash sale thất bại: ${error.message}`
      );
    }
  }

  // ✅ REMOVED: Single delete method - chỉ sử dụng bulk delete API

  /**
   * Lấy gợi ý flash sale products cho cart page
   * Note: displayTime logic sẽ được handle ở frontend
   * Frontend sẽ hiển thị sản phẩm trong displayTime giây rồi ẩn đi
   */
  async getFlashSaleSuggestions(
    userId: number,
    excludeProductIds: number[] = [],
    limit: number = 5
  ): Promise<FlashSaleSuggestionsResponseDto> {
    try {
      const flashSales = await this.flashSaleRepository.getRandomFlashSaleProducts(
        excludeProductIds,
        userId, // Exclude user's own products
        limit
      );

      if (flashSales.length === 0) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.NO_FLASH_SALE_PRODUCTS,
          'Không có sản phẩm flash sale nào khả dụng'
        );
      }

      const suggestions = flashSales.map(flashSale => {
        const mapped = this.mapToFlashSaleProductResponseDto(flashSale);
        // Add displayTime to flash sale info for frontend to handle visibility
        mapped.flashSale.displayTime = flashSale.displayTime;
        return mapped;
      });

      return {
        suggestions,
        count: suggestions.length,
        generatedAt: Date.now()
      };
    } catch (error) {
      this.logger.error(`Failed to get flash sale suggestions: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        FLASH_SALE_ERROR_CODES.FLASH_SALE_SUGGESTIONS_FAILED,
        `Lấy gợi ý flash sale thất bại: ${error.message}`
      );
    }
  }

  /**
   * Check user flash sale limit
   */
  private async checkUserFlashSaleLimit(userId: number): Promise<void> {
    const activeCount = await this.flashSaleRepository
      .createQueryBuilder('fs')
      .where('fs.user_id = :userId', { userId })
      .andWhere('fs.status IN (:...statuses)', {
        statuses: [FlashSaleStatus.SCHEDULED, FlashSaleStatus.ACTIVE]
      })
      .andWhere('fs.is_active = :isActive', { isActive: true })
      .getCount();

    if (activeCount >= 3) {
      throw new AppException(
        FLASH_SALE_ERROR_CODES.USER_FLASH_SALE_LIMIT_EXCEEDED,
        `Bạn đã có ${activeCount} flash sale đang hoạt động/lên lịch. Tối đa chỉ được 3 flash sale cùng lúc. Vui lòng hủy hoặc chờ flash sale cũ kết thúc`
      );
    }
  }

  /**
   * Transform images to include both key and URL with CDN prefix
   */
  private transformImages(images: any[]): Array<{ key: string; position: number; url: string }> {
    if (!images || !Array.isArray(images)) return [];

    return images.map((img, index) => {
      const position = typeof img.position === 'number' ? img.position : index;
      return {
        key: img.key,
        position: position,
        url: `https://cdn.redai.vn/${img.key}`
      };
    });
  }

  /**
   * Map FlashSale entity to ResponseDto
   */
  private mapToResponseDto(flashSale: FlashSale): FlashSaleResponseDto {
    const responseDto = {
      id: flashSale.id,
      productId: flashSale.productId,
      userId: flashSale.userId,
      discountPercentage: flashSale.discountPercentage,
      displayTime: flashSale.displayTime,
      startTime: flashSale.startTime,
      endTime: flashSale.endTime,
      maxConfiguration: flashSale.maxConfiguration,
      status: flashSale.status,
      isActive: flashSale.isActive,
      createdAt: flashSale.createdAt,
      updatedAt: flashSale.updatedAt,
      product: flashSale.product,
      salePrice: flashSale.salePrice,
      timeRemaining: flashSale.timeRemaining,
      soldCount: flashSale.soldCount
    };

    // Transform product images to include URL field if product exists
    if (responseDto.product && responseDto.product.images) {
      responseDto.product = {
        ...responseDto.product,
        images: this.transformImages(responseDto.product.images)
      };
    }

    return responseDto;
  }

  /**
   * Map FlashSale to FlashSaleProductResponseDto
   */
  private mapToFlashSaleProductResponseDto(flashSale: FlashSale): FlashSaleProductResponseDto {
    return {
      id: flashSale.product.id,
      name: flashSale.product.name,
      description: flashSale.product.description,
      listedPrice: flashSale.product.listedPrice,
      discountedPrice: flashSale.product.discountedPrice,
      images: this.transformImages(flashSale.product.images),
      category: flashSale.product.category,
      seller: {
        id: flashSale.product.userId,
        name: 'User', // TODO: Get actual user name from join
        avatar: null,
        email: null,
        phoneNumber: null,
        type: 'user'
      },
      createdAt: flashSale.product.createdAt || Date.now(),
      soldCount: flashSale.soldCount || 0,
      canPurchase: true,
      flashSale: {
        salePrice: flashSale.salePrice || flashSale.product.discountedPrice,
        startTime: this.safeToISOString(flashSale.startTime),
        endTime: this.safeToISOString(flashSale.endTime),
        timeRemaining: flashSale.timeRemaining,
        discountPercentage: flashSale.discountPercentage,
        totalInventory: flashSale.maxConfiguration.totalInventory,
        soldCount: flashSale.soldCount
      }
    };
  }

  /**
   * ✅ Helper method để safely convert timestamp to ISO string
   */
  private safeToISOString(timestamp: any): string {
    try {
      // Handle different timestamp formats
      let time: number;

      if (typeof timestamp === 'string') {
        time = parseInt(timestamp);
      } else if (typeof timestamp === 'number') {
        time = timestamp;
      } else {
        this.logger.warn(`Invalid timestamp format: ${timestamp}, using current time`);
        return new Date().toISOString();
      }

      // Validate timestamp range (reasonable dates)
      if (isNaN(time) || time < 0 || time > 9999999999999) {
        this.logger.warn(`Invalid timestamp value: ${time}, using current time`);
        return new Date().toISOString();
      }

      const date = new Date(time);
      if (isNaN(date.getTime())) {
        this.logger.warn(`Invalid date from timestamp: ${time}, using current time`);
        return new Date().toISOString();
      }

      return date.toISOString();
    } catch (error) {
      this.logger.error(`Error converting timestamp to ISO string: ${error.message}`, error.stack);
      return new Date().toISOString();
    }
  }

  /**
   * ✅ NEW: Bulk delete flash sales của user
   */
  @Transactional()
  async deleteUserFlashSales(userId: number, deleteDto: BulkDeleteFlashSaleDto): Promise<BulkDeleteResponseDto> {
    const { ids } = deleteDto;
    const result: BulkDeleteResponseDto = {
      successCount: 0,
      successIds: [],
      failureCount: 0,
      failures: [],
      totalProcessed: ids.length
    };

    this.logger.log(`User ${userId} attempting to delete ${ids.length} flash sales: [${ids.join(', ')}]`);

    for (const id of ids) {
      try {
        const flashSale = await this.flashSaleRepository.findById(id);

        if (!flashSale) {
          result.failures.push({
            id,
            reason: 'Flash sale không tồn tại'
          });
          result.failureCount++;
          continue;
        }

        // Validate ownership
        try {
          this.flashSaleValidationHelper.validateOwnership(flashSale, userId);
        } catch (error) {
          result.failures.push({
            id,
            reason: 'Không có quyền xóa flash sale này'
          });
          result.failureCount++;
          continue;
        }

        // ✅ REMOVED: User có thể xóa flash sale ở mọi trạng thái (miễn là của mình)

        await this.flashSaleRepository.remove(flashSale);

        result.successIds.push(id);
        result.successCount++;

        this.logger.log(`User ${userId} successfully deleted flash sale ${id}`);
      } catch (error) {
        this.logger.error(`Failed to delete flash sale ${id}: ${error.message}`, error.stack);
        result.failures.push({
          id,
          reason: `Lỗi xóa flash sale: ${error.message}`
        });
        result.failureCount++;
      }
    }

    this.logger.log(`User ${userId} bulk delete completed: ${result.successCount} success, ${result.failureCount} failed`);
    return result;
  }
}
