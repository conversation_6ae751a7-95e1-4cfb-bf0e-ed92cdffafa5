import { ApiProperty } from '@nestjs/swagger';
import { IsObject, IsEnum, IsOptional, IsNotEmpty } from 'class-validator';
import { ApiKeyLocationEnum } from '../../constants';

/**
 * DTO cho việc thêm/cập nhật API Key của tool custom
 */
export class ManageToolApiKeyDto {
  /**
   * Api keys cần thiết cho việc gọi API
   */
  @ApiProperty({
    description: 'Các API key cần thiết cho việc gọi API',
    example: {
      'X-API-KEY': 'api_key_123456',
      'Authorization': 'Bearer token_123'
    },
    required: true,
  })
  @IsNotEmpty({ message: 'API keys không được để trống' })
  @IsObject({ message: 'API keys phải là đối tượng' })
  apiKeys: Record<string, string>;

  /**
   * Vị trí gửi API Key
   */
  @ApiProperty({
    description: 'Vị trí gửi API Key',
    enum: ApiKeyLocationEnum,
    example: ApiKeyLocationEnum.HEADER,
    required: false,
  })
  @IsOptional()
  @IsEnum(ApiKeyLocationEnum)
  apiKeyLocation?: ApiKeyLocationEnum;
}

/**
 * DTO response cho API Key của tool custom
 */
export class ToolApiKeyResponseDto {
  /**
   * ID của API Key
   */
  @ApiProperty({
    description: 'ID của API Key',
    example: 'uuid-api-key-id',
  })
  id: string;

  /**
   * Các API key được ẩn (masked)
   */
  @ApiProperty({
    description: 'Các API key được ẩn',
    example: {
      'X-API-KEY': '****',
      'Authorization': '****'
    },
  })
  apiKeys: Record<string, string>;

  /**
   * Vị trí gửi API Key
   */
  @ApiProperty({
    description: 'Vị trí gửi API Key',
    enum: ApiKeyLocationEnum,
    example: ApiKeyLocationEnum.HEADER,
  })
  apiKeyLocation: ApiKeyLocationEnum;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1703123456789,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (timestamp)',
    example: 1703123456789,
  })
  updatedAt: number;

  /**
   * Tạo DTO từ entity ApiKey
   */
  static fromEntity(apiKey: any, decryptedApiKeys: Record<string, string>): ToolApiKeyResponseDto {
    const dto = new ToolApiKeyResponseDto();
    dto.id = apiKey.id;
    dto.apiKeyLocation = apiKey.apiKeyLocation;
    dto.createdAt = apiKey.createdAt;
    dto.updatedAt = apiKey.updatedAt;
    
    // Tạo masked API keys
    dto.apiKeys = {};
    Object.keys(decryptedApiKeys).forEach(key => {
      dto.apiKeys[key] = '****';
    });

    return dto;
  }
}
