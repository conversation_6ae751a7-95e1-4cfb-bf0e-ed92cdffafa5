# Process Blocks Implementation

## <PERSON><PERSON> `processBlocks` đư<PERSON><PERSON> triển khai để xử lý các blocks dựa trên TypeAgent capabilities và CreateAgentDto data khi tạo agent mới.

## 🎯 Chức năng chính

### Workflow
```typescript
await this.processBlocks(agentSave, createDto, typeAgent, userId);
```

### Input Parameters
- `agent`: Agent entity đã được lưu vào database
- `createDto`: CreateAgentDto chứa dữ liệu từ request
- `typeAgent`: TypeAgent entity với các capabilities
- `userId`: ID của user tạo agent

## 🔧 Blocks được xử lý

### 1. Resources Block
**Điều kiện**: `typeAgent.enableResources && createDto.resources`

**Xử lý**:
- **URLs**: Tạo liên kết trong bảng `agents_url`
- **Media**: Tạo liên kết trong bảng `agents_media`
- **Products**: Tạo liên kết trong bảng `agents_product`
- **Knowledge Files**: T<PERSON>o liên kết trong bảng `agents_knowledge_file`

```typescript
// URLs
const urlEntities = resources.urlIds.map(urlId => ({
  agentId,
  urlId,
}));
await this.agentUrlRepository.save(urlEntities);
```

### 2. Output Messenger Block
**Điều kiện**: `typeAgent.enableOutputMessenger && createDto.outputMessenger`

**Xử lý**:
- Validate Facebook Pages thuộc về user
- Cấu hình Facebook Pages cho agent

### 3. Output Website Block
**Điều kiện**: `typeAgent.enableOutputLivechat && createDto.outputWebsite`

**Xử lý**:
- Cập nhật `agent_id` cho User Websites
- Validate websites thuộc về user

```typescript
await this.userWebsiteRepository.update(
  {
    id: In(outputWebsite.userWebsiteIds),
    userId: userId,
  },
  { agentId }
);
```

### 4. Output Zalo Block
**Điều kiện**: `typeAgent.enableOutputZaloOa && createDto.outputZalo`

**Xử lý**:
- Cấu hình Zalo Official Accounts cho agent

### 5. Output Payment Block
**Điều kiện**: `typeAgent.enableOutputPayment && createDto.outputPayment`

**Xử lý**:
- Validate Payment Gateway thuộc về user
- Cấu hình Payment Gateway cho agent

```typescript
const paymentGateway = await this.paymentGatewayRepository.findOne({
  where: { id: outputPayment.paymentGatewayId }
});
```

### 6. Tools Block
**Điều kiện**: `typeAgent.enableTool && createDto.customToolIds`

**Xử lý**:
- Validate Custom Tools thuộc về user
- Tạo liên kết trong bảng `agent_user_tools`

```typescript
const validTools = await this.userToolsCustomRepository.find({
  where: { 
    id: In(customToolIds), 
    userId
  }
});

const toolEntities = customToolIds.map(customToolId => ({
  userAgentId: agentId,
  customToolId,
}));
await this.agentUserToolsRepository.save(toolEntities);
```

### 7. Multi Agent Block
**Điều kiện**: `typeAgent.enableMultiAgent && createDto.multiAgent`

**Xử lý**:
- Validate Child Agents thuộc về user
- Tạo liên kết parent-child trong bảng `user_multi_agents`

```typescript
const multiAgentEntities = childAgentIds.map(childAgentId => ({
  parentAgentId: agentId,
  childAgentId,
}));
await this.userMultiAgentRepository.save(multiAgentEntities);
```

### 8. Strategy Block
**Điều kiện**: `typeAgent.enableStrategy && createDto.strategy`

**Xử lý**:
- Cấu hình Strategy cho agent

### 9. Shipment Block
**Điều kiện**: `typeAgent.enableShipment && createDto.shipmentConfig`

**Xử lý**:
- Validate User Provider Shipment thuộc về user
- Cấu hình Shipment cho agent

### 10. MCP Block
**Điều kiện**: `createDto.mcpIds`

**Xử lý**:
- Validate MCP Servers thuộc về user
- Tạo liên kết trong bảng `agent_user_mcp`

```typescript
const validMcps = await this.userMcpRepository.find({
  where: { 
    id: In(mcpIds), 
    userId
  }
});

const mcpEntities = mcpIds.map(mcpId => ({
  agentId,
  mcpId,
}));
await this.agentUserMcpRepository.save(mcpEntities);
```

## 🏗️ Database Tables Affected

### Junction Tables
- `agents_url` - Agent ↔ URL Data
- `agents_media` - Agent ↔ Media Data  
- `agents_product` - Agent ↔ User Products
- `agents_knowledge_file` - Agent ↔ Knowledge Files
- `agent_user_tools` - Agent ↔ Custom Tools
- `user_multi_agents` - Parent Agent ↔ Child Agents
- `agent_user_mcp` - Agent ↔ MCP Servers

### Update Tables
- `user_websites` - Set agent_id field

## ✅ Validation Logic

### 1. Ownership Validation
Tất cả resources phải thuộc về user hiện tại:
- Custom Tools: `userId` match
- Child Agents: `userId` match và `deletedAt IS NULL`
- MCP Servers: `userId` match
- User Websites: `userId` match

### 2. Existence Validation
Kiểm tra tồn tại trước khi tạo liên kết:
- Payment Gateway existence
- Tool existence và active status
- Agent existence và không bị soft delete

### 3. Error Handling
```typescript
if (validTools.length !== customToolIds.length) {
  const validToolIds = validTools.map(tool => tool.id);
  const invalidToolIds = customToolIds.filter(id => !validToolIds.includes(id));
  throw new AppException(
    AGENT_ERROR_CODES.TOOL_NOT_FOUND,
    `Không tìm thấy tools với IDs: ${invalidToolIds.join(', ')}`
  );
}
```

## 🔒 Security Considerations

### 1. User Isolation
- Mọi validation đều kiểm tra `userId`
- Không cho phép liên kết với resources của user khác

### 2. Data Integrity
- Sử dụng transactions để đảm bảo consistency
- Rollback nếu có lỗi trong quá trình xử lý

### 3. Input Validation
- Validate array lengths
- Check for null/undefined values
- Validate UUID formats

## 📊 Performance Considerations

### 1. Batch Operations
- Sử dụng `save()` với array để insert multiple records
- Sử dụng `In()` operator cho bulk queries

### 2. Minimal Queries
- Chỉ query khi cần thiết (có data trong DTO)
- Combine validation và data retrieval

### 3. Error Fast
- Validate trước khi thực hiện database operations
- Fail fast nếu có lỗi validation

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('processBlocks', () => {
  it('should process resources block correctly', async () => {
    // Mock repositories
    // Test resources processing
  });
  
  it('should validate user ownership', async () => {
    // Test ownership validation
  });
  
  it('should handle errors gracefully', async () => {
    // Test error scenarios
  });
});
```

### Integration Tests
- Test với real database
- Test transaction rollback
- Test với multiple blocks cùng lúc

## 🚀 Future Enhancements

### 1. Async Processing
- Sử dụng queues cho heavy operations
- Background processing cho large datasets

### 2. Caching
- Cache validation results
- Cache user permissions

### 3. Audit Trail
- Log tất cả block processing activities
- Track changes cho compliance

### 4. Webhooks
- Notify external systems khi agent được tạo
- Integration với third-party services
