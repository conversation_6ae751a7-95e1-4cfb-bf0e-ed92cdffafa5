import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { WebhookController } from '../webhook.controller';
import { WorkflowTriggerService } from '../../services/workflow-trigger.service';
import {
  FacebookWebhookDto,
  ZaloWebhookDto,
  GoogleWebhookDto,
  GenericWebhookDto,
} from '../../dto/webhook/webhook-trigger.dto';

describe('WebhookController', () => {
  let controller: WebhookController;
  let workflowTriggerService: jest.Mocked<WorkflowTriggerService>;

  const mockTriggerResult = {
    executionId: '123e4567-e89b-12d3-a456-426614174000',
    jobId: 'job-123',
  };

  const mockFacebookWebhook: FacebookWebhookDto = {
    object: 'page',
    entry: [
      {
        id: '123456789',
        time: 1640995200,
        messaging: [
          {
            sender: { id: '987654321' },
            recipient: { id: '123456789' },
            timestamp: 1640995200,
            message: {
              mid: 'mid.123',
              text: 'Hello World',
            },
          },
        ],
      },
    ],
  };

  const mockZaloWebhook: ZaloWebhookDto = {
    event_name: 'user_send_text',
    app_id: '123456789',
    user_id: '987654321',
    timestamp: '1640995200',
    message: {
      text: 'Hello World',
    },
  };

  const mockGoogleWebhook: GoogleWebhookDto = {
    message: {
      data: Buffer.from(JSON.stringify({ event: 'test', data: 'value' })).toString('base64'),
      messageId: 'msg-123',
      publishTime: '2023-01-01T00:00:00Z',
      attributes: {
        key: 'value',
      },
    },
    subscription: 'projects/my-project/subscriptions/my-subscription',
  };

  const mockGenericWebhook: GenericWebhookDto = {
    event: 'user.created',
    data: {
      userId: '123',
      email: '<EMAIL>',
    },
    timestamp: 1640995200000,
    source: 'external-api',
  };

  beforeEach(async () => {
    const mockTriggerService = {
      triggerWorkflow: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [WebhookController],
      providers: [
        {
          provide: WorkflowTriggerService,
          useValue: mockTriggerService,
        },
      ],
    }).compile();

    controller = module.get<WebhookController>(WebhookController);
    workflowTriggerService = module.get(WorkflowTriggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handleFacebookWebhook', () => {
    it('should successfully handle Facebook webhook', async () => {
      // Arrange
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      workflowTriggerService.triggerWorkflow.mockResolvedValue(mockTriggerResult);

      // Act
      const result = await controller.handleFacebookWebhook(
        workflowId,
        mockFacebookWebhook,
        'sha256=signature',
      );

      // Assert
      expect(workflowTriggerService.triggerWorkflow).toHaveBeenCalledWith(
        workflowId,
        'webhook.facebook',
        mockFacebookWebhook,
      );
      expect(result.success).toBe(true);
      expect(result.data.executionId).toBe(mockTriggerResult.executionId);
      expect(result.data.jobId).toBe(mockTriggerResult.jobId);
      expect(result.data.message).toBe('Facebook webhook processed successfully');
    });

    it('should handle Facebook webhook without signature', async () => {
      // Arrange
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      workflowTriggerService.triggerWorkflow.mockResolvedValue(mockTriggerResult);

      // Act
      const result = await controller.handleFacebookWebhook(
        workflowId,
        mockFacebookWebhook,
      );

      // Assert
      expect(workflowTriggerService.triggerWorkflow).toHaveBeenCalledWith(
        workflowId,
        'webhook.facebook',
        mockFacebookWebhook,
      );
      expect(result.success).toBe(true);
    });

    it('should propagate errors from trigger service', async () => {
      // Arrange
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      const error = new Error('Workflow not found');
      workflowTriggerService.triggerWorkflow.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.handleFacebookWebhook(workflowId, mockFacebookWebhook),
      ).rejects.toThrow('Workflow not found');
    });
  });

  describe('handleZaloWebhook', () => {
    it('should successfully handle Zalo webhook', async () => {
      // Arrange
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      workflowTriggerService.triggerWorkflow.mockResolvedValue(mockTriggerResult);

      // Act
      const result = await controller.handleZaloWebhook(
        workflowId,
        mockZaloWebhook,
        'signature',
        '1640995200',
      );

      // Assert
      expect(workflowTriggerService.triggerWorkflow).toHaveBeenCalledWith(
        workflowId,
        'webhook.zalo',
        mockZaloWebhook,
      );
      expect(result.success).toBe(true);
      expect(result.data.message).toBe('Zalo webhook processed successfully');
    });
  });

  describe('handleGoogleWebhook', () => {
    it('should successfully handle Google webhook', async () => {
      // Arrange
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      workflowTriggerService.triggerWorkflow.mockResolvedValue(mockTriggerResult);

      // Act
      const result = await controller.handleGoogleWebhook(workflowId, mockGoogleWebhook);

      // Assert
      expect(workflowTriggerService.triggerWorkflow).toHaveBeenCalledWith(
        workflowId,
        'webhook.google',
        expect.objectContaining({
          event: 'test',
          data: 'value',
          messageId: 'msg-123',
          publishTime: '2023-01-01T00:00:00Z',
          attributes: { key: 'value' },
          subscription: 'projects/my-project/subscriptions/my-subscription',
        }),
      );
      expect(result.success).toBe(true);
      expect(result.data.message).toBe('Google webhook processed successfully');
    });

    it('should throw BadRequestException for invalid Google Pub/Sub message', async () => {
      // Arrange
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      const invalidWebhook = {
        ...mockGoogleWebhook,
        message: {
          ...mockGoogleWebhook.message,
          data: 'invalid-base64',
        },
      };

      // Act & Assert
      await expect(
        controller.handleGoogleWebhook(workflowId, invalidWebhook),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('handleGenericWebhook', () => {
    it('should successfully handle generic webhook', async () => {
      // Arrange
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      workflowTriggerService.triggerWorkflow.mockResolvedValue(mockTriggerResult);

      // Act
      const result = await controller.handleGenericWebhook(workflowId, mockGenericWebhook);

      // Assert
      expect(workflowTriggerService.triggerWorkflow).toHaveBeenCalledWith(
        workflowId,
        'webhook.generic',
        mockGenericWebhook,
      );
      expect(result.success).toBe(true);
      expect(result.data.message).toBe('Generic webhook processed successfully');
    });
  });

  describe('error handling', () => {
    it('should handle and log errors properly', async () => {
      // Arrange
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      const error = new Error('Database connection failed');
      workflowTriggerService.triggerWorkflow.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.handleFacebookWebhook(workflowId, mockFacebookWebhook),
      ).rejects.toThrow('Database connection failed');

      expect(workflowTriggerService.triggerWorkflow).toHaveBeenCalledWith(
        workflowId,
        'webhook.facebook',
        mockFacebookWebhook,
      );
    });
  });

  describe('webhook data validation', () => {
    it('should accept valid Facebook webhook data', async () => {
      // Arrange
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      workflowTriggerService.triggerWorkflow.mockResolvedValue(mockTriggerResult);

      // Act
      const result = await controller.handleFacebookWebhook(workflowId, mockFacebookWebhook);

      // Assert
      expect(result.success).toBe(true);
      expect(workflowTriggerService.triggerWorkflow).toHaveBeenCalledWith(
        workflowId,
        'webhook.facebook',
        mockFacebookWebhook,
      );
    });

    it('should accept valid Zalo webhook data', async () => {
      // Arrange
      const workflowId = '123e4567-e89b-12d3-a456-426614174000';
      workflowTriggerService.triggerWorkflow.mockResolvedValue(mockTriggerResult);

      // Act
      const result = await controller.handleZaloWebhook(workflowId, mockZaloWebhook);

      // Assert
      expect(result.success).toBe(true);
      expect(workflowTriggerService.triggerWorkflow).toHaveBeenCalledWith(
        workflowId,
        'webhook.zalo',
        mockZaloWebhook,
      );
    });
  });
});
