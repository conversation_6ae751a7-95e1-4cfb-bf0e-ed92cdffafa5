import { Injectable, Logger } from '@nestjs/common';
import { S3Service } from '@shared/services/s3.service';
import { TokenCounterService } from './token-counter.service';
import { ProviderLlmEnum } from '../constants/provider.enum';
import { AppException } from '@common/exceptions';
import { MODELS_ERROR_CODES } from '../exceptions/models.exception';
import * as readline from 'readline';
import { Readable } from 'stream';

/**
 * Interface cho kết quả validation file JSONL
 */
export interface JsonlValidationResult {
  isValid: boolean;
  totalTokens: number;
  totalLines: number;
  fileSizeBytes: number;
  errors?: string[];
}

/**
 * Service để validate và đếm token cho file .jsonl từ S3
 * Hỗ trợ validation theo provider (OpenAI, Gemini)
 */
@Injectable()
export class JsonlFileValidatorService {
  private readonly logger = new Logger(JsonlFileValidatorService.name);

  constructor(
    private readonly s3Service: S3Service,
    private readonly tokenCounterService: TokenCounterService,
  ) {}

  /**
   * Validate và đếm token cho file .jsonl từ S3
   * @param s3Key Key của file .jsonl trên S3
   * @param provider Provider để validate (OPENAI hoặc GEMINI)
   * @param modelId Model ID để đếm token chính xác
   * @param apiKey API key (bắt buộc cho GEMINI)
   * @returns Kết quả validation với isValid và totalTokens
   */
  async validateJsonlFile(
    s3Key: string,
    provider: ProviderLlmEnum,
    modelId: string,
    apiKey?: string,
  ): Promise<JsonlValidationResult> {
    try {
      this.logger.log(`Bắt đầu validate file JSONL: ${s3Key} với provider: ${provider}`);

      // Tải file từ S3 dưới dạng stream
      const fileStream = await this.s3Service.downloadFileAsStream(s3Key);
      
      // Lấy thông tin file size
      const fileSizeBytes = await this.getFileSize(s3Key);
      
      // Validate file size theo provider
      this.validateFileSize(fileSizeBytes, provider);

      // Đọc và validate từng dòng
      const validationResult = await this.processJsonlLines(
        fileStream,
        provider,
        modelId,
        apiKey,
      );

      // Validate số lượng dòng theo provider
      this.validateLineCount(validationResult.totalLines, provider);

      this.logger.log(
        `Hoàn thành validate file JSONL: ${s3Key}. ` +
        `Lines: ${validationResult.totalLines}, Tokens: ${validationResult.totalTokens}`,
      );

      return {
        isValid: true,
        totalTokens: validationResult.totalTokens,
        totalLines: validationResult.totalLines,
        fileSizeBytes,
      };

    } catch (error) {
      this.logger.error(`Lỗi khi validate file JSONL ${s3Key}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
        `Lỗi khi validate file JSONL: ${error.message}`,
        { s3Key, provider, error: error.message },
      );
    }
  }

  /**
   * Lấy kích thước file từ S3
   * @param s3Key Key của file trên S3
   * @returns Kích thước file tính bằng bytes
   */
  private async getFileSize(s3Key: string): Promise<number> {
    try {
      // Sử dụng HeadObject để lấy metadata của file thông qua S3Service
      // Tạm thời sử dụng downloadFileAsBytes để lấy size
      const fileBytes = await this.s3Service.downloadFileAsBytes(s3Key);
      return fileBytes.length;
    } catch (error) {
      this.logger.warn(`Không thể lấy kích thước file ${s3Key}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Validate kích thước file theo provider
   * @param fileSizeBytes Kích thước file tính bằng bytes
   * @param provider Provider để validate
   */
  private validateFileSize(fileSizeBytes: number, provider: ProviderLlmEnum): void {
    const fileSizeMB = fileSizeBytes / (1024 * 1024);

    switch (provider) {
      case ProviderLlmEnum.OPENAI:
        if (fileSizeMB > 250) {
          throw new AppException(
            MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
            `File quá lớn cho OpenAI. Kích thước tối đa: 250MB, kích thước hiện tại: ${fileSizeMB.toFixed(2)}MB`,
            { fileSizeMB, maxSizeMB: 250, provider },
          );
        }
        break;

      case ProviderLlmEnum.GEMINI:
        // Gemini không có giới hạn file size cụ thể trong yêu cầu
        // Có thể thêm giới hạn nếu cần
        break;

      default:
        this.logger.warn(`Provider ${provider} chưa được hỗ trợ validation file size`);
        break;
    }
  }

  /**
   * Validate số lượng dòng theo provider
   * @param lineCount Số lượng dòng trong file
   * @param provider Provider để validate
   */
  private validateLineCount(lineCount: number, provider: ProviderLlmEnum): void {
    switch (provider) {
      case ProviderLlmEnum.OPENAI:
        if (lineCount < 10) {
          throw new AppException(
            MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
            `File OpenAI cần tối thiểu 10 dòng. Số dòng hiện tại: ${lineCount}`,
            { lineCount, minLines: 10, provider },
          );
        }
        break;

      case ProviderLlmEnum.GEMINI:
        if (lineCount < 20) {
          throw new AppException(
            MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
            `File Gemini cần tối thiểu 20 dòng. Số dòng hiện tại: ${lineCount}`,
            { lineCount, minLines: 20, provider },
          );
        }
        if (lineCount > 5000) {
          throw new AppException(
            MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
            `File Gemini có tối đa 5000 dòng. Số dòng hiện tại: ${lineCount}`,
            { lineCount, maxLines: 5000, provider },
          );
        }
        break;

      default:
        this.logger.warn(`Provider ${provider} chưa được hỗ trợ validation line count`);
        break;
    }
  }

  /**
   * Xử lý từng dòng trong file JSONL và đếm token
   * @param fileStream Stream của file từ S3
   * @param provider Provider để đếm token
   * @param modelId Model ID để đếm token
   * @param apiKey API key (cho GEMINI)
   * @returns Kết quả xử lý với tổng token và số dòng
   */
  private async processJsonlLines(
    fileStream: NodeJS.ReadableStream,
    provider: ProviderLlmEnum,
    modelId: string,
    apiKey?: string,
  ): Promise<{ totalTokens: number; totalLines: number }> {
    return new Promise((resolve, reject) => {
      const rl = readline.createInterface({
        input: fileStream as Readable,
        crlfDelay: Infinity, // Xử lý cả \r\n và \n
      });

      let totalTokens = 0;
      let totalLines = 0;
      const errors: string[] = [];

      rl.on('line', async (line) => {
        try {
          totalLines++;
          
          // Bỏ qua dòng trống
          if (!line.trim()) {
            return;
          }

          // Validate JSON format
          let jsonData: any;
          try {
            jsonData = JSON.parse(line);
          } catch (parseError) {
            errors.push(`Dòng ${totalLines}: JSON không hợp lệ - ${parseError.message}`);
            return;
          }

          // Chuyển đổi JSON thành text để đếm token
          const textContent = JSON.stringify(jsonData);
          
          // Đếm token cho dòng này
          const lineTokens = await this.tokenCounterService.countTokens(
            textContent,
            provider,
            modelId,
            apiKey,
          );

          // Validate token count cho OpenAI
          if (provider === ProviderLlmEnum.OPENAI && lineTokens > 50000) {
            errors.push(
              `Dòng ${totalLines}: Quá nhiều token (${lineTokens}). Tối đa 50,000 token mỗi dòng cho OpenAI`,
            );
            return;
          }

          totalTokens += lineTokens;

        } catch (error) {
          errors.push(`Dòng ${totalLines}: Lỗi xử lý - ${error.message}`);
        }
      });

      rl.on('close', () => {
        if (errors.length > 0) {
          reject(new AppException(
            MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
            `File JSONL có ${errors.length} lỗi validation`,
            { errors: errors.slice(0, 10) }, // Chỉ trả về 10 lỗi đầu tiên
          ));
          return;
        }

        resolve({ totalTokens, totalLines });
      });

      rl.on('error', (error) => {
        reject(new AppException(
          MODELS_ERROR_CODES.USER_DATA_FINE_TUNE_INVALID_TRAINING_DATA,
          `Lỗi khi đọc file JSONL: ${error.message}`,
          { error: error.message },
        ));
      });
    });
  }
}
