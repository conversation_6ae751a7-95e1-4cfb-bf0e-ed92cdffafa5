import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';

/**
 * DTO cho response của test connection
 */
@Exclude()
export class TestConnectionResponseDto {
  /**
   * Kết quả test connection
   */
  @ApiProperty({
    description: 'Kết quả test connection',
    example: true,
  })
  @Expose()
  success: boolean;

  /**
   * Thời gian phản hồi (ms)
   */
  @ApiPropertyOptional({
    description: 'Thời gian phản hồi (ms)',
    example: 250,
  })
  @Expose()
  responseTime?: number;

  /**
   * Thông báo lỗi (nếu có)
   */
  @ApiPropertyOptional({
    description: 'Thông báo lỗi (nếu có)',
    example: 'Invalid API key',
  })
  @Expose()
  error?: string;
}
