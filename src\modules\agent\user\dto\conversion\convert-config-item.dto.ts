import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsString,
  IsIn,
  IsOptional,
  IsBoolean,
  IsObject,
  IsArray,
  IsNumber,
  IsNotEmpty,
  Matches,
  MaxLength
} from 'class-validator';
import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';

/**
 * DTO cho một item trong conversion config - implement ConvertConfig interface
 */
export class ConvertConfigItemDto implements ConvertConfig {
  /**
   * Tên của field trong schema JSON
   */
  @ApiProperty({
    description: 'Tên của field trong schema JSON (chỉ chứa chữ cái, số, dấu gạch dưới, bắt đầu bằng chữ cái)',
    example: 'customer_name',
    maxLength: 100,
    pattern: '^[a-zA-Z][a-zA-Z0-9_]*$'
  })
  @IsString({ message: 'Tên field phải là chuỗi' })
  @IsNotEmpty({ message: 'Tên field không được rỗng' })
  @MaxLength(100, { message: 'Tên field không được vượt quá 100 ký tự' })
  @Matches(/^[a-zA-Z][a-zA-Z0-9_]*$/, {
    message: 'Tên field chỉ được chứa chữ cái, số và dấu gạch dưới, phải bắt đầu bằng chữ cái'
  })
  name: string;

  /**
   * Kiểu dữ liệu của field theo chuẩn JSON Schema
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu của field theo chuẩn JSON Schema',
    example: 'string',
    enum: ['string', 'number', 'boolean', 'array', 'object'],
  })
  @IsIn(['string', 'number', 'boolean', 'array', 'object'], {
    message: 'Kiểu dữ liệu phải là một trong các giá trị: string, number, boolean, array, object'
  })
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';

  /**
   * Mô tả (nội dung) của field
   */
  @ApiProperty({
    description: 'Mô tả hoặc nội dung của field',
    example: 'Tên đầy đủ của khách hàng',
    maxLength: 500,
  })
  @IsString({ message: 'Mô tả phải là chuỗi' })
  @MaxLength(500, { message: 'Mô tả không được vượt quá 500 ký tự' })
  description: string;

  /**
   * Trường này có bắt buộc không?
   */
  @ApiProperty({
    description: 'Trường này có bắt buộc không?',
    example: true,
    default: true,
  })
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'Required phải là boolean' })
  @Type(() => Boolean)
  required: boolean;

  /**
   * Giá trị mặc định
   */
  @ApiPropertyOptional({
    description: 'Giá trị mặc định cho field',
    example: '',
  })
  @IsOptional()
  default?: any;

  /**
   * Định nghĩa kiểu dữ liệu cho array (nếu type là array)
   */
  @ApiPropertyOptional({
    description: 'Định nghĩa kiểu dữ liệu cho array (nếu type là array)',
    example: { type: 'string', description: 'Danh sách chuỗi' },
  })
  @IsOptional()
  @IsObject()
  items?: {
    type: 'string' | 'number' | 'boolean' | 'object';
    description?: string;
  };

  /**
   * Enum values (nếu cần giới hạn giá trị)
   */
  @ApiPropertyOptional({
    description: 'Enum values (nếu cần giới hạn giá trị)',
    example: ['option1', 'option2', 'option3'],
  })
  @IsOptional()
  @IsArray()
  enum?: any[];

  /**
   * Định nghĩa properties cho object (nếu type là object)
   */
  @ApiPropertyOptional({
    description: 'Định nghĩa properties cho object (nếu type là object)',
    example: {
      'street': {
        name: 'street',
        type: 'string',
        description: 'Tên đường',
        required: true,
        default: '',
        deletable: true
      },
      'city': {
        name: 'city',
        type: 'string',
        description: 'Thành phố',
        required: true,
        default: '',
        deletable: true
      }
    },
  })
  @IsOptional()
  @IsObject()
  properties?: Record<string, ConvertConfig>;

  /**
   * Trường này có thể xóa không? (email và phone luôn là false)
   */
  @ApiPropertyOptional({
    description: 'Trường này có thể xóa không? (email và phone luôn là false)',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  deletable?: boolean;
}
