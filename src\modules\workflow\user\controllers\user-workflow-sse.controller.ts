import {
  Controller,
  ExecutionContext,
  Param,
  Query,
  Req,
  Sse,
  UseGuards,
  Injectable,
  CanActivate,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import {
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
  ApiExtraModels,
  ApiHeader,
} from '@nestjs/swagger';
import { JwtUtilService } from '@/modules/auth/guards/jwt.util';
import { AppException, ErrorCode } from '@/common';
import { WorkflowSSEService } from '../../services/workflow-sse.service';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { WorkflowSseMessage } from '../../constants/workflow-sse-events';
import { SSEApiErrorResponse } from '../../decorators';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';

/**
 * Controller xử lý SSE cho workflow events
 */
@ApiTags(SWAGGER_API_TAGS.USER_WORKFLOW)
@Controller('user/workflow-sse')
@UseGuards(JwtUserGuard)
// @ApiExtraModels(WorkflowSseMessage) // Commented out as WorkflowSseMessage is a type, not a class
export class UserWorkflowSSEController {
  private readonly logger = new Logger(UserWorkflowSSEController.name);

  constructor(private readonly workflowSSEService: WorkflowSSEService) {}

  /**
   * SSE endpoint để stream workflow execution events
   * @param executionId ID của execution (optional)
   * @param auth JWT token từ query parameter
   * @returns Observable stream của execution events
   */
  @Sse('executions/stream')
  @ApiOperation({
    summary: 'Stream workflow execution events qua SSE',
    description:
      'API này tạo kết nối SSE để stream workflow execution events real-time. ' +
      'Client sẽ nhận được thông báo khi execution status thay đổi. ' +
      'Token JWT được truyền qua query parameter "auth".',
  })
  @ApiQuery({
    name: 'auth',
    description: 'JWT token để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: true,
  })
  @ApiQuery({
    name: 'executionId',
    description: 'ID của execution để filter (optional)',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'SSE stream được thiết lập thành công',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      Connection: { description: 'keep-alive' },
      'Access-Control-Allow-Origin': { description: '*' },
      'Access-Control-Allow-Headers': { description: 'Cache-Control' },
    },
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"id":"exec_123","event":"execution_started","data":"{\\"type\\":\\"execution_started\\",\\"executionId\\":\\"123\\"}"}\\n\\n',
        },
        examples: {
          executionStarted: {
            summary: 'Execution Started Event',
            value: 'data: {"id":"exec_123","event":"execution_started","data":"{\\"type\\":\\"execution_started\\",\\"executionId\\":\\"123\\",\\"status\\":\\"running\\"}"}\\n\\n',
          },
          executionCompleted: {
            summary: 'Execution Completed Event',
            value: 'data: {"id":"exec_123","event":"execution_completed","data":"{\\"type\\":\\"execution_completed\\",\\"executionId\\":\\"123\\",\\"status\\":\\"completed\\"}"}\\n\\n',
          },
          heartbeat: {
            summary: 'Heartbeat Event',
            value: 'data: {"id":"heartbeat_123","event":"heartbeat","data":"{\\"type\\":\\"heartbeat\\",\\"timestamp\\":1640995200000}"}\\n\\n',
          },
        },
      },
    },
  })
  @SSEApiErrorResponse('INVALID_AUTH_TOKEN', 'CONNECTION_TIMEOUT')
  streamExecutionEvents(
    @Query('executionId') executionId: string,
    @Req() request: any,
  ): Observable<WorkflowSseMessage> {
    const user = request.user;
    if (!user) {
      throw new AppException(ErrorCode.TOKEN_NOT_FOUND, 'User information not found');
    }

    this.logger.log(
      `Starting execution events stream for user ${user.id}${executionId ? `, execution ${executionId}` : ''}`,
    );

    return this.workflowSSEService.streamExecutionEvents(Number(user.id), executionId);
  }

  /**
   * SSE endpoint để stream workflow node events
   * @param executionId ID của execution để filter (optional)
   * @param auth JWT token từ query parameter
   * @returns Observable stream của node events
   */
  @Sse('nodes/stream')
  @ApiOperation({
    summary: 'Stream workflow node events qua SSE',
    description:
      'API này tạo kết nối SSE để stream workflow node events real-time. ' +
      'Client sẽ nhận được thông báo khi node status thay đổi trong quá trình execution. ' +
      'Token JWT được truyền qua query parameter "auth".',
  })
  @ApiQuery({
    name: 'auth',
    description: 'JWT token để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: true,
  })
  @ApiQuery({
    name: 'executionId',
    description: 'ID của execution để filter (optional)',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'SSE stream được thiết lập thành công',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      Connection: { description: 'keep-alive' },
    },
  })
  @ApiResponse({ status: 401, description: 'Chưa xác thực' })
  streamNodeEvents(
    @Query('executionId') executionId: string,
    @Query('auth') auth: string,
    @Req() request: any,
  ): Observable<WorkflowSseMessage> {
    const user = request.user;
    if (!user) {
      throw new AppException(ErrorCode.TOKEN_NOT_FOUND, 'User information not found');
    }

    this.logger.log(
      `Starting node events stream for user ${user.id}${executionId ? `, execution ${executionId}` : ''}`,
    );

    return this.workflowSSEService.streamNodeEvents(Number(user.id), executionId);
  }

  /**
   * SSE endpoint để stream workflow test events
   * @param testId ID của test để filter (optional)
   * @param auth JWT token từ query parameter
   * @returns Observable stream của test events
   */
  @Sse('tests/stream')
  @ApiOperation({
    summary: 'Stream workflow test events qua SSE',
    description:
      'API này tạo kết nối SSE để stream workflow test events real-time. ' +
      'Client sẽ nhận được thông báo khi node test status thay đổi. ' +
      'Token JWT được truyền qua query parameter "auth".',
  })
  @ApiQuery({
    name: 'auth',
    description: 'JWT token để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: true,
  })
  @ApiQuery({
    name: 'testId',
    description: 'ID của test để filter (optional)',
    example: 'test_123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'SSE stream được thiết lập thành công',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      Connection: { description: 'keep-alive' },
    },
  })
  @ApiResponse({ status: 401, description: 'Chưa xác thực' })
  streamTestEvents(
    @Query('testId') testId: string,
    @Query('auth') auth: string,
    @Req() request: any,
  ): Observable<WorkflowSseMessage> {
    const user = request.user;
    if (!user) {
      throw new AppException(ErrorCode.TOKEN_NOT_FOUND, 'User information not found');
    }

    this.logger.log(
      `Starting test events stream for user ${user.id}${testId ? `, test ${testId}` : ''}`,
    );

    return this.workflowSSEService.streamTestEvents(Number(user.id), testId);
  }

  /**
   * SSE endpoint để stream combined workflow events (execution + node)
   * @param executionId ID của execution để filter (optional)
   * @param auth JWT token từ query parameter
   * @returns Observable stream của combined events
   */
  @Sse('combined/stream')
  @ApiOperation({
    summary: 'Stream combined workflow events qua SSE',
    description:
      'API này tạo kết nối SSE để stream cả execution và node events real-time. ' +
      'Client sẽ nhận được tất cả workflow events trong một stream duy nhất. ' +
      'Token JWT được truyền qua query parameter "auth".',
  })
  @ApiQuery({
    name: 'auth',
    description: 'JWT token để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: true,
  })
  @ApiQuery({
    name: 'executionId',
    description: 'ID của execution để filter (optional)',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'SSE stream được thiết lập thành công',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      Connection: { description: 'keep-alive' },
    },
  })
  @ApiResponse({ status: 401, description: 'Chưa xác thực' })
  streamCombinedEvents(
    @Query('executionId') executionId: string,
    @Query('auth') auth: string,
    @Req() request: any,
  ): Observable<WorkflowSseMessage> {
    const user = request.user;
    if (!user) {
      throw new AppException(ErrorCode.TOKEN_NOT_FOUND, 'User information not found');
    }

    this.logger.log(
      `Starting combined events stream for user ${user.id}${executionId ? `, execution ${executionId}` : ''}`,
    );

    // Merge execution and node events streams
    const executionStream = this.workflowSSEService.streamExecutionEvents(Number(user.id), executionId);
    const nodeStream = this.workflowSSEService.streamNodeEvents(Number(user.id), executionId);

    return new Observable<WorkflowSseMessage>((observer) => {
      const executionSub = executionStream.subscribe({
        next: (event) => observer.next(event),
        error: (error) => observer.error(error),
      });

      const nodeSub = nodeStream.subscribe({
        next: (event) => observer.next(event),
        error: (error) => observer.error(error),
      });

      // Cleanup subscriptions when observer is unsubscribed
      return () => {
        executionSub.unsubscribe();
        nodeSub.unsubscribe();
      };
    });
  }
}
