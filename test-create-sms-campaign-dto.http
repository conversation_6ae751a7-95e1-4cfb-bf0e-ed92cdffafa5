### Test CreateSmsCampaignDto với QueryDto inheritance
### API: POST /marketing/sms-campaigns

### Test 1: Basic SMS Campaign Creation (Existing functionality)
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Chiến dịch SMS khuyến mãi Black Friday",
  "campaignType": "ADS",
  "description": "Chiến dịch SMS marketing cho sự kiện Black Friday",
  "templateId": 1,
  "smsIntegrationConfig": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "integrationName": "FPT SMS - Brand Name",
    "typeId": 1,
    "metadata": {
      "brandName": "REDAI",
      "apiUrl": "https://api01.sms.fpt.net"
    }
  },
  "segmentConfig": {
    "id": 5,
    "name": "<PERSON><PERSON><PERSON>ch hàng VIP",
    "description": "Segment khách hàng VIP có giá trị đơn hàng cao",
    "audienceCount": 150
  },
  "templateVariables": {
    "customerName": "<PERSON>uyễn Văn A",
    "discountPercent": "50",
    "validUntil": "31/12/2024"
  },
  "scheduledAt": 1703980800
}

### Test 2: SMS Campaign với QueryDto parameters (New functionality)
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "Chiến dịch SMS với QueryDto params",
  "campaignType": "ADS",
  "templateId": 1,
  "smsIntegrationConfig": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "integrationName": "FPT SMS",
    "typeId": 1
  },
  "phoneNumbers": ["0921843966", "+84901234567"],
  "scheduledAt": 1703980800,
  "page": 1,
  "limit": 10,
  "search": "test",
  "sortBy": "createdAt",
  "sortDirection": "DESC"
}

### Test 3: OTP Campaign (No scheduledAt required)
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "OTP Campaign Test",
  "campaignType": "OTP",
  "templateId": 2,
  "smsIntegrationConfig": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "integrationName": "FPT SMS",
    "typeId": 1
  },
  "audiences": [
    {
      "name": "Nguyễn Văn A",
      "phoneNumber": "0901234567",
      "countryCode": 84
    }
  ],
  "templateVariables": {
    "otpCode": "123456"
  }
}

### Test 4: Validation Error - Missing required fields
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "campaignType": "ADS",
  "templateId": 1
}

### Expected Response: 400 Bad Request with validation errors

### Test 5: ADS Campaign without scheduledAt (Should fail)
POST https://v2.redai.vn/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "name": "ADS Campaign without scheduledAt",
  "campaignType": "ADS",
  "templateId": 1,
  "smsIntegrationConfig": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "integrationName": "FPT SMS",
    "typeId": 1
  },
  "phoneNumbers": ["0921843966"]
}

### Expected Response: 400 Bad Request - scheduledAt is required for ADS campaign
