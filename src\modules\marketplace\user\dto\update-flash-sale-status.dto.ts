import { ApiProperty } from '@nestjs/swagger';
import { IsEnum } from 'class-validator';
import { FlashSaleStatus } from '../../enums/flash-sale-status.enum';

/**
 * DTO cho update status flash sale (User)
 */
export class UpdateFlashSaleStatusDto {
  @ApiProperty({
    description: 'Trạng thái mới của flash sale. User có thể chuyển giữa DRAFT, SCHEDULED, và CANCELLED',
    enum: FlashSaleStatus,
    example: FlashSaleStatus.SCHEDULED,
    examples: {
      draft: {
        value: FlashSaleStatus.DRAFT,
        description: 'Lưu flash sale ở trạng thái nháp'
      },
      scheduled: {
        value: FlashSaleStatus.SCHEDULED,
        description: 'Lên lịch flash sale để tự động kích hoạt'
      },
      cancelled: {
        value: FlashSaleStatus.CANCELLED,
        description: 'Hủy flash sale'
      }
    }
  })
  @IsEnum(FlashSaleStatus, {
    message: 'Tr<PERSON>ng thái phải là DRAFT, SCHEDULED hoặc CANCELLED'
  })
  status: FlashSaleStatus;
}
