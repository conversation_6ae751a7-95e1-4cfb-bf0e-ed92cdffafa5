import { ModelData } from '../../repositories/models.repository';
import { UserModelsResponseDto } from '../dto/user-models/user-models-response.dto';

/**
 * Mapper cho User Models
 */
export class UserModelsMapper {
  /**
   * <PERSON>yển đổi ModelsWithJoinedData sang UserModelsResponseDto
   * @param model ModelsWithJoinedData entity
   * @returns UserModelsResponseDto
   */
  static toResponseDto(model: ModelData): UserModelsResponseDto {
    return {
      id: model.id,
      modelId: model.modelName,
      inputModalities: model.inputModalities || [],
      outputModalities: model.outputModalities || [],
      samplingParameters: model.samplingParameters || [],
      features: model.features || [],
      basePricing: model.basePricing,
      fineTunePricing: model.fineTunePricing,
      trainingPricing: model.trainingPricing,
      maxTokens: model.maxTokens,
      contextWindow: model.contextWindow,
    };
  }

  /**
   * <PERSON>yển đổi mảng ModelsWithJoinedData sang mảng UserModelsResponseDto
   * @param models Mảng ModelsWithJoinedData entities
   * @returns Mảng UserModelsResponseDto
   */
  static toResponseDtoList(models: ModelData[]): UserModelsResponseDto[] {
    return models.map(model => this.toResponseDto(model));
  }
}
