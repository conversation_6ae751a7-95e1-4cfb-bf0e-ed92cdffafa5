import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBearerAuth,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { ApiResponseDto } from '@/common/response';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { WorkflowExecutionService } from '../../services/workflow-execution.service';
import {
  WorkflowExecutionDto,
  CreateWorkflowExecutionDto,
  UpdateExecutionStatusDto,
  QueryExecutionsDto,
  ExecutionStatisticsDto,
  CancelExecutionDto,
} from '../../dto/execution';
import {
  ExecutionApiErrorResponse,
  CommonWorkflowErrorResponses,
} from '../../decorators';

/**
 * Controller xử lý workflow execution management cho user
 */
@ApiTags(SWAGGER_API_TAGS.USER_WORKFLOW)
@Controller('user/workflow-executions')
@UseGuards(JwtUserGuard)
@ApiBearerAuth()
@ApiExtraModels(
  WorkflowExecutionDto,
  CreateWorkflowExecutionDto,
  UpdateExecutionStatusDto,
  QueryExecutionsDto,
  ExecutionStatisticsDto,
  CancelExecutionDto,
)
export class UserWorkflowExecutionController {
  constructor(
    private readonly workflowExecutionService: WorkflowExecutionService,
  ) {}

  /**
   * Tạo mới workflow execution
   */
  @Post()
  @ApiOperation({
    summary: 'Create new workflow execution',
    description: 'Tạo mới workflow execution với trigger data. Execution sẽ được queue và thực thi bởi worker.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Workflow execution created successfully',
    schema: ApiResponseDto.getSchema(WorkflowExecutionDto),
  })
  @ExecutionApiErrorResponse('WORKFLOW_NOT_FOUND', 'INVALID_TRIGGER_DATA')
  async createExecution(
    @Body() createDto: CreateWorkflowExecutionDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowExecutionDto>> {
    const execution = await this.workflowExecutionService.create({
      ...createDto,
      metadata: {
        ...createDto.metadata,
        userId,
        source: 'manual',
      },
    });

    return ApiResponseDto.created(execution, 'Workflow execution created successfully');
  }

  /**
   * Lấy danh sách workflow executions
   */
  @Get()
  @ApiOperation({
    summary: 'Get workflow executions',
    description: 'Lấy danh sách workflow executions với pagination và filter. Chỉ trả về executions của user hiện tại.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow executions retrieved successfully',
    schema: ApiResponseDto.getPaginatedSchema(WorkflowExecutionDto),
  })
  @CommonWorkflowErrorResponses.AUTH_ERRORS
  async getExecutions(
    @Query() queryDto: QueryExecutionsDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<{
    executions: WorkflowExecutionDto[];
    total: number;
    page: number;
    limit: number;
  }>> {
    const { page = 1, limit = 20, sortBy = 'startedAt', sortOrder = 'DESC', ...filters } = queryDto;
    const skip = (page - 1) * limit;

    const { executions, total } = await this.workflowExecutionService.findMany({
      where: {
        ...filters,
        metadata: {
          userId,
        },
      },
      take: limit,
      skip,
      order: {
        [sortBy]: sortOrder,
      },
    });

    return ApiResponseDto.success({
      executions,
      total,
      page,
      limit,
    }, 'Workflow executions retrieved successfully');
  }

  /**
   * Lấy workflow execution theo ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get workflow execution by ID',
    description: 'Lấy thông tin chi tiết workflow execution theo ID. Chỉ có thể truy cập executions của user hiện tại.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của workflow execution',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow execution retrieved successfully',
    schema: ApiResponseDto.getSchema(WorkflowExecutionDto),
  })
  @ExecutionApiErrorResponse('EXECUTION_NOT_FOUND', 'ACCESS_DENIED')
  async getExecutionById(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowExecutionDto>> {
    const execution = await this.workflowExecutionService.findById(id);

    // Kiểm tra quyền truy cập
    if (execution.metadata?.userId !== userId) {
      throw new Error('Access denied');
    }

    return ApiResponseDto.success(execution, 'Workflow execution retrieved successfully');
  }

  /**
   * Cập nhật execution status
   */
  @Put(':id/status')
  @ApiOperation({
    summary: 'Update execution status',
    description: 'Cập nhật trạng thái của workflow execution',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của workflow execution',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Execution status updated successfully',
    schema: ApiResponseDto.getSchema(WorkflowExecutionDto),
  })
  async updateExecutionStatus(
    @Param('id') id: string,
    @Body() updateDto: UpdateExecutionStatusDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowExecutionDto>> {
    // Kiểm tra quyền truy cập
    const execution = await this.workflowExecutionService.findById(id);
    if (execution.metadata?.userId !== userId) {
      throw new Error('Access denied');
    }

    const updatedExecution = await this.workflowExecutionService.updateStatus(
      id,
      updateDto.status,
      {
        result: updateDto.result,
        error: updateDto.error,
        metadata: updateDto.metadata,
      },
    );

    return ApiResponseDto.success(updatedExecution, 'Execution status updated successfully');
  }

  /**
   * Bắt đầu execution
   */
  @Post(':id/start')
  @ApiOperation({
    summary: 'Start workflow execution',
    description: 'Bắt đầu thực thi workflow (chuyển từ QUEUED sang RUNNING)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của workflow execution',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow execution started successfully',
    schema: ApiResponseDto.getSchema(WorkflowExecutionDto),
  })
  async startExecution(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowExecutionDto>> {
    // Kiểm tra quyền truy cập
    const execution = await this.workflowExecutionService.findById(id);
    if (execution.metadata?.userId !== userId) {
      throw new Error('Access denied');
    }

    const startedExecution = await this.workflowExecutionService.start(id);

    return ApiResponseDto.success(startedExecution, 'Workflow execution started successfully');
  }

  /**
   * Hủy execution
   */
  @Post(':id/cancel')
  @ApiOperation({
    summary: 'Cancel workflow execution',
    description: 'Hủy thực thi workflow',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của workflow execution',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow execution cancelled successfully',
    schema: ApiResponseDto.getSchema(WorkflowExecutionDto),
  })
  async cancelExecution(
    @Param('id') id: string,
    @Body() cancelDto: CancelExecutionDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowExecutionDto>> {
    // Kiểm tra quyền truy cập
    const execution = await this.workflowExecutionService.findById(id);
    if (execution.metadata?.userId !== userId) {
      throw new Error('Access denied');
    }

    const cancelledExecution = await this.workflowExecutionService.cancel(id, cancelDto.reason);

    return ApiResponseDto.success(cancelledExecution, 'Workflow execution cancelled successfully');
  }

  /**
   * Pause execution
   */
  @Post(':id/pause')
  @ApiOperation({
    summary: 'Pause workflow execution',
    description: 'Tạm dừng thực thi workflow',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của workflow execution',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow execution paused successfully',
    schema: ApiResponseDto.getSchema(WorkflowExecutionDto),
  })
  async pauseExecution(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowExecutionDto>> {
    // Kiểm tra quyền truy cập
    const execution = await this.workflowExecutionService.findById(id);
    if (execution.metadata?.userId !== userId) {
      throw new Error('Access denied');
    }

    const pausedExecution = await this.workflowExecutionService.pause(id);

    return ApiResponseDto.success(pausedExecution, 'Workflow execution paused successfully');
  }

  /**
   * Resume execution
   */
  @Post(':id/resume')
  @ApiOperation({
    summary: 'Resume workflow execution',
    description: 'Tiếp tục thực thi workflow từ trạng thái PAUSED',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của workflow execution',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Workflow execution resumed successfully',
    schema: ApiResponseDto.getSchema(WorkflowExecutionDto),
  })
  async resumeExecution(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowExecutionDto>> {
    // Kiểm tra quyền truy cập
    const execution = await this.workflowExecutionService.findById(id);
    if (execution.metadata?.userId !== userId) {
      throw new Error('Access denied');
    }

    const resumedExecution = await this.workflowExecutionService.resume(id);

    return ApiResponseDto.success(resumedExecution, 'Workflow execution resumed successfully');
  }

  /**
   * Retry execution
   */
  @Post(':id/retry')
  @ApiOperation({
    summary: 'Retry workflow execution',
    description: 'Thử lại thực thi workflow đã failed',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của workflow execution',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Workflow execution retry created successfully',
    schema: ApiResponseDto.getSchema(WorkflowExecutionDto),
  })
  async retryExecution(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<WorkflowExecutionDto>> {
    // Kiểm tra quyền truy cập
    const execution = await this.workflowExecutionService.findById(id);
    if (execution.metadata?.userId !== userId) {
      throw new Error('Access denied');
    }

    const retryExecution = await this.workflowExecutionService.retry(id);

    return ApiResponseDto.created(retryExecution, 'Workflow execution retry created successfully');
  }

  /**
   * Lấy execution statistics
   */
  @Get('statistics/overview')
  @ApiOperation({
    summary: 'Get execution statistics',
    description: 'Lấy thống kê về workflow executions của user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Execution statistics retrieved successfully',
    schema: ApiResponseDto.getSchema(ExecutionStatisticsDto),
  })
  async getExecutionStatistics(
    @CurrentUser('id') userId: number,
    @Query('workflowId') workflowId?: string,
  ): Promise<ApiResponseDto<ExecutionStatisticsDto>> {
    // TODO: Filter by userId in statistics
    const statistics = await this.workflowExecutionService.getStatistics(workflowId);

    return ApiResponseDto.success(statistics, 'Execution statistics retrieved successfully');
  }
}
