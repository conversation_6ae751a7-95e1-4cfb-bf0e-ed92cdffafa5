# Admin Template Email - <PERSON><PERSON><PERSON> nhật từ PaginatedResponseDto sang PaginatedResult

## 🎯 <PERSON><PERSON><PERSON> đích
Cập nhật API `findAll` của Admin Template Email để sử dụng `PaginatedResult` thay vì `PaginatedResponseDto` để đồng nhất với các API khác trong hệ thống.

## 📝 Thay đổi đã thực hiện

### 1. Controller (`admin-template-email.controller.ts`)

#### Import Changes
```typescript
// Loại bỏ
import { PaginatedResponseDto } from '../dto/common';

// Giữ lại
import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
```

#### Return Type Changes
```typescript
// Trước
async findAll(@Query() query: TemplateEmailQueryDto): Promise<AppApiResponse<PaginatedResponseDto<TemplateEmailListResponseDto>>>

// Sau  
async findAll(@Query() query: TemplateEmailQueryDto): Promise<AppApiResponse<PaginatedResult<TemplateEmailListResponseDto>>>
```

#### API Documentation Changes
```typescript
// Trước
@ApiResponse({
  status: 200,
  description: 'Danh sách template email với phân trang',
  type: PaginatedResponseDto,
  schema: {
    allOf: [
      { $ref: '#/components/schemas/PaginatedResponseDto' },
      // ...
    ]
  }
})

// Sau
@ApiResponse({
  status: 200,
  description: 'Danh sách template email với phân trang',
  schema: {
    allOf: [
      { $ref: '#/components/schemas/ApiResponseDto' },
      {
        properties: {
          data: {
            allOf: [
              { $ref: '#/components/schemas/PaginatedResult' },
              {
                properties: {
                  items: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/TemplateEmailListResponseDto' }
                  }
                }
              }
            ]
          }
        }
      }
    ]
  }
})
```

### 2. Service (`admin-template-email.service.ts`)

#### Import Changes
```typescript
// Loại bỏ
import { PaginatedResponseDto, PaginationMetaDto } from '../dto/common';

// Thêm
import { PaginatedResult } from '@/common/response';
```

#### Return Type Changes
```typescript
// Trước
async findAll(query: TemplateEmailQueryDto): Promise<PaginatedResponseDto<TemplateEmailListResponseDto>>

// Sau
async findAll(query: TemplateEmailQueryDto): Promise<PaginatedResult<TemplateEmailListResponseDto>>
```

#### Return Data Structure Changes
```typescript
// Trước
const data = templates.map(template => this.mapToListDto(template));
const meta: PaginationMetaDto = {
  total,
  page,
  limit,
  totalPages,
  hasPreviousPage: page > 1,
  hasNextPage: page < totalPages,
};

return {
  data,
  meta,
};

// Sau
const items = templates.map(template => this.mapToListDto(template));
const totalPages = Math.ceil(total / limit);

return {
  items,
  total,
  page,
  limit,
  totalPages,
  hasPreviousPage: page > 1,
  hasNextPage: page < totalPages,
};
```

### 3. Code Cleanup
- Loại bỏ unused `employee` parameters trong các method không cần thiết:
  - `getOverview()`
  - `findById()`
  - `bulkDelete()`

## 📊 So sánh Response Format

### Trước (PaginatedResponseDto)
```json
{
  "success": true,
  "message": "Danh sách template email",
  "data": {
    "data": [
      {
        "id": 1,
        "subject": "Welcome Email",
        "category": "WELCOME"
      }
    ],
    "meta": {
      "total": 100,
      "page": 1,
      "limit": 10,
      "totalPages": 10,
      "hasPreviousPage": false,
      "hasNextPage": true
    }
  }
}
```

### Sau (PaginatedResult)
```json
{
  "success": true,
  "message": "Danh sách template email",
  "data": {
    "items": [
      {
        "id": 1,
        "subject": "Welcome Email",
        "category": "WELCOME"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10,
    "hasPreviousPage": false,
    "hasNextPage": true
  }
}
```

## ✅ Lợi ích

1. **Đồng nhất**: Sử dụng cùng format với các API khác trong hệ thống
2. **Đơn giản hóa**: Loại bỏ nested structure không cần thiết
3. **Consistency**: Frontend có thể sử dụng cùng một interface cho tất cả paginated APIs
4. **Type Safety**: TypeScript types được chuẩn hóa

## 🧪 Testing

API endpoint vẫn giữ nguyên:
```
GET /admin/template-email?page=1&limit=10&category=WELCOME&sortBy=createdAt&sortDirection=DESC
```

Response structure đã thay đổi như mô tả ở trên.

## 📁 Files đã thay đổi

1. `src/modules/marketing/admin/controllers/admin-template-email.controller.ts`
2. `src/modules/marketing/admin/services/admin-template-email.service.ts`

**✨ Cập nhật hoàn tất! API đã sử dụng PaginatedResult thay vì PaginatedResponseDto.**
