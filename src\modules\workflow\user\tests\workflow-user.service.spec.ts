import { Test, TestingModule } from '@nestjs/testing';
import { AppException } from '@common/exceptions';
import { WorkflowUserService } from '../services/workflow-user.service';
import { WorkflowRepository } from '../../repositories/workflow.repository';
import { WORKFLOW_ERROR_CODES } from '../../exceptions/workflow.exception';
import { Workflow } from '../../entities';
import { CreateWorkflowDto, UpdateWorkflowDto, WorkflowQueryDto } from '../dto';
import { WorkflowSortBy } from '../dto/workflow-query.dto';
import { SortDirection } from '@common/dto/query.dto';

describe('WorkflowUserService', () => {
  let service: WorkflowUserService;
  let workflowRepository: jest.Mocked<WorkflowRepository>;

  const mockWorkflow: Workflow = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    userId: 1,
    employeeId: null,
    name: 'Test Workflow',
    isActive: true,
    definition: { nodes: [], edges: [] },
    createdAt: Date.now(),
    updatedAt: Date.now(),
    updateTimestamp: jest.fn(),
  } as any;

  beforeEach(async () => {
    const mockWorkflowRepository = {
      findAndCount: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      remove: jest.fn(),
      find: jest.fn(),
      count: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkflowUserService,
        {
          provide: WorkflowRepository,
          useValue: mockWorkflowRepository,
        },
      ],
    }).compile();

    service = module.get<WorkflowUserService>(WorkflowUserService);
    workflowRepository = module.get(WorkflowRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getWorkflows', () => {
    it('should return paginated workflows', async () => {
      const queryDto: WorkflowQueryDto = {
        page: 1,
        limit: 10,
        sortBy: WorkflowSortBy.UPDATED_AT,
        sortDirection: SortDirection.DESC,
      };

      const mockResult = [[mockWorkflow], 1];
      workflowRepository.findAndCount.mockResolvedValue(mockResult as any);

      const result = await service.getWorkflows(1, queryDto);

      expect(result).toEqual({
        items: [
          {
            id: mockWorkflow.id,
            name: mockWorkflow.name,
            isActive: mockWorkflow.isActive,
            createdAt: mockWorkflow.createdAt,
            updatedAt: mockWorkflow.updatedAt,
            nodeCount: 0,
            edgeCount: 0,
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });

      expect(workflowRepository.findAndCount).toHaveBeenCalledWith({
        where: { userId: 1 },
        order: { updatedAt: 'DESC' },
        skip: 0,
        take: 10,
      });
    });

    it('should handle search and filter parameters', async () => {
      const queryDto: WorkflowQueryDto = {
        page: 1,
        limit: 10,
        search: 'test',
        isActive: true,
        sortBy: WorkflowSortBy.NAME,
        sortDirection: SortDirection.ASC,
      };

      workflowRepository.findAndCount.mockResolvedValue([[], 0]);

      await service.getWorkflows(1, queryDto);

      expect(workflowRepository.findAndCount).toHaveBeenCalledWith({
        where: {
          userId: 1,
          name: expect.objectContaining({ _type: 'ilike', _value: '%test%' }),
          isActive: true,
        },
        order: { name: 'ASC' },
        skip: 0,
        take: 10,
      });
    });

    it('should throw exception on database error', async () => {
      const queryDto: WorkflowQueryDto = {
        page: 1,
        limit: 10,
        sortBy: WorkflowSortBy.UPDATED_AT,
        sortDirection: SortDirection.DESC,
      };

      workflowRepository.findAndCount.mockRejectedValue(new Error('Database error'));

      await expect(service.getWorkflows(1, queryDto)).rejects.toThrow(
        new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_LIST_FAILED),
      );
    });
  });

  describe('getWorkflowById', () => {
    it('should return workflow detail', async () => {
      workflowRepository.findOne.mockResolvedValue(mockWorkflow);

      const result = await service.getWorkflowById(1, mockWorkflow.id);

      expect(result).toEqual({
        id: mockWorkflow.id,
        userId: mockWorkflow.userId,
        employeeId: mockWorkflow.employeeId,
        name: mockWorkflow.name,
        isActive: mockWorkflow.isActive,
        definition: mockWorkflow.definition,
        createdAt: mockWorkflow.createdAt,
        updatedAt: mockWorkflow.updatedAt,
      });

      expect(workflowRepository.findOne).toHaveBeenCalledWith({
        where: { id: mockWorkflow.id, userId: 1 },
      });
    });

    it('should throw not found exception when workflow does not exist', async () => {
      workflowRepository.findOne.mockResolvedValue(null);

      await expect(service.getWorkflowById(1, 'non-existent-id')).rejects.toThrow(
        new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND),
      );
    });

    it('should throw exception on database error', async () => {
      workflowRepository.findOne.mockRejectedValue(new Error('Database error'));

      await expect(service.getWorkflowById(1, mockWorkflow.id)).rejects.toThrow(
        new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_GET_FAILED),
      );
    });
  });

  describe('createWorkflow', () => {
    const createDto: CreateWorkflowDto = {
      name: 'New Workflow',
      isActive: false,
      definition: { nodes: [], edges: [] },
    };

    it('should create workflow successfully', async () => {
      workflowRepository.findOne.mockResolvedValue(null); // No existing workflow
      workflowRepository.create.mockReturnValue(mockWorkflow);
      workflowRepository.save.mockResolvedValue(mockWorkflow);

      const result = await service.createWorkflow(1, createDto);

      expect(result).toEqual({
        id: mockWorkflow.id,
        userId: mockWorkflow.userId,
        employeeId: mockWorkflow.employeeId,
        name: mockWorkflow.name,
        isActive: mockWorkflow.isActive,
        definition: mockWorkflow.definition,
        createdAt: mockWorkflow.createdAt,
        updatedAt: mockWorkflow.updatedAt,
      });

      expect(workflowRepository.findOne).toHaveBeenCalledWith({
        where: { userId: 1, name: createDto.name },
      });
      expect(workflowRepository.create).toHaveBeenCalled();
      expect(workflowRepository.save).toHaveBeenCalled();
    });

    it('should throw exception when workflow name already exists', async () => {
      workflowRepository.findOne.mockResolvedValue(mockWorkflow);

      await expect(service.createWorkflow(1, createDto)).rejects.toThrow(
        new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NAME_EXISTS),
      );
    });

    it('should throw exception on database error', async () => {
      workflowRepository.findOne.mockResolvedValue(null);
      workflowRepository.create.mockReturnValue(mockWorkflow);
      workflowRepository.save.mockRejectedValue(new Error('Database error'));

      await expect(service.createWorkflow(1, createDto)).rejects.toThrow(
        new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_CREATE_FAILED),
      );
    });
  });

  describe('updateWorkflow', () => {
    const updateDto: UpdateWorkflowDto = {
      name: 'Updated Workflow',
      isActive: true,
    };

    it('should update workflow successfully', async () => {
      const updatedWorkflow = { ...mockWorkflow, ...updateDto };
      workflowRepository.findOne
        .mockResolvedValueOnce(mockWorkflow) // First call for finding workflow
        .mockResolvedValueOnce(null); // Second call for checking name uniqueness
      workflowRepository.save.mockResolvedValue({
        ...updatedWorkflow,
        updateTimestamp: jest.fn()
      } as any);

      const result = await service.updateWorkflow(1, mockWorkflow.id, updateDto);

      expect(result.name).toBe(updateDto.name);
      expect(result.isActive).toBe(updateDto.isActive);
      expect(workflowRepository.save).toHaveBeenCalled();
    });

    it('should throw not found exception when workflow does not exist', async () => {
      workflowRepository.findOne.mockResolvedValue(null);

      await expect(service.updateWorkflow(1, 'non-existent-id', updateDto)).rejects.toThrow(
        new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND),
      );
    });

    it('should throw exception when new name already exists', async () => {
      const existingWorkflow = { ...mockWorkflow, id: 'different-id' };
      workflowRepository.findOne
        .mockResolvedValueOnce(mockWorkflow) // First call for finding workflow
        .mockResolvedValueOnce({
          ...existingWorkflow,
          updateTimestamp: jest.fn()
        } as any); // Second call for checking name uniqueness

      await expect(service.updateWorkflow(1, mockWorkflow.id, updateDto)).rejects.toThrow(
        new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NAME_EXISTS),
      );
    });
  });

  describe('deleteWorkflow', () => {
    it('should delete workflow successfully', async () => {
      workflowRepository.findOne.mockResolvedValue(mockWorkflow);
      workflowRepository.remove.mockResolvedValue(mockWorkflow);

      await service.deleteWorkflow(1, mockWorkflow.id);

      expect(workflowRepository.findOne).toHaveBeenCalledWith({
        where: { id: mockWorkflow.id, userId: 1 },
      });
      expect(workflowRepository.remove).toHaveBeenCalledWith(mockWorkflow);
    });

    it('should throw not found exception when workflow does not exist', async () => {
      workflowRepository.findOne.mockResolvedValue(null);

      await expect(service.deleteWorkflow(1, 'non-existent-id')).rejects.toThrow(
        new AppException(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND),
      );
    });
  });

  describe('toggleWorkflowStatus', () => {
    it('should toggle workflow status successfully', async () => {
      const updatedWorkflow = { ...mockWorkflow, isActive: false };
      workflowRepository.findOne.mockResolvedValue(mockWorkflow);
      workflowRepository.save.mockResolvedValue({
        ...updatedWorkflow,
        updateTimestamp: jest.fn()
      } as any);

      const result = await service.toggleWorkflowStatus(1, mockWorkflow.id, false);

      expect(result.isActive).toBe(false);
      expect(workflowRepository.save).toHaveBeenCalled();
    });
  });

  describe('searchWorkflows', () => {
    it('should search workflows by name', async () => {
      workflowRepository.find.mockResolvedValue([mockWorkflow]);

      const result = await service.searchWorkflows(1, 'test', 5);

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe(mockWorkflow.name);
      expect(workflowRepository.find).toHaveBeenCalledWith({
        where: {
          userId: 1,
          name: expect.objectContaining({ _type: 'ilike', _value: '%test%' }),
        },
        order: { updatedAt: 'DESC' },
        take: 5,
      });
    });
  });

  describe('getWorkflowStatistics', () => {
    it('should return workflow statistics', async () => {
      workflowRepository.count
        .mockResolvedValueOnce(10) // Total workflows
        .mockResolvedValueOnce(7); // Active workflows

      const result = await service.getWorkflowStatistics(1);

      expect(result).toEqual({
        totalWorkflows: 10,
        activeWorkflows: 7,
        inactiveWorkflows: 3,
      });
    });
  });
});
