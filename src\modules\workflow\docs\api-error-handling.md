# Workflow API Error Handling Documentation

## 📋 Overview

This document provides comprehensive information about error handling in the Workflow API module. All errors follow the standardized `ApiResponseDto` format for consistency across the application.

## 🔧 Error Response Format

All error responses follow this standard format:

```json
{
  "code": 15001,
  "message": "Workflow not found",
  "result": null,
  "detail": {
    "field": "workflowId",
    "value": "invalid-uuid",
    "constraint": "must be a valid UUID"
  },
  "timestamp": "2023-01-01T00:00:00.000Z",
  "path": "/api/v1/user/workflows/invalid-uuid"
}
```

### Response Fields

- **`code`** (number): Unique error code for programmatic handling
- **`message`** (string): Human-readable error message
- **`result`** (null): Always null for error responses
- **`detail`** (object, optional): Additional error context and validation details
- **`timestamp`** (string): ISO timestamp when error occurred
- **`path`** (string): Request path where error occurred

## 🚨 Error Categories

### 1. Authentication & Authorization Errors

| HTTP Status | Error Code | Message | Description |
|-------------|------------|---------|-------------|
| 401 | 10001 | Unauthorized | Missing or invalid authentication token |
| 401 | 10002 | Token expired | JWT token has expired |
| 403 | 10003 | Access denied | User doesn't have permission for this resource |
| 403 | 15002 | Workflow access denied | User cannot access this specific workflow |

### 2. Validation Errors

| HTTP Status | Error Code | Message | Description |
|-------------|------------|---------|-------------|
| 400 | 11001 | Validation failed | Request data validation failed |
| 400 | 11002 | Missing required fields | Required fields are missing from request |
| 400 | 11003 | Invalid data format | Data format doesn't match expected schema |
| 400 | 15003 | Invalid workflow definition | Workflow definition structure is invalid |
| 400 | 15004 | Invalid node configuration | Node configuration is invalid |

### 3. Resource Not Found Errors

| HTTP Status | Error Code | Message | Description |
|-------------|------------|---------|-------------|
| 404 | 12001 | Resource not found | Generic resource not found |
| 404 | 15005 | Workflow not found | Specified workflow doesn't exist |
| 404 | 15006 | Node definition not found | Specified node definition doesn't exist |
| 404 | 15007 | Execution not found | Specified workflow execution doesn't exist |
| 404 | 15008 | Node test not found | Specified node test doesn't exist |

### 4. Workflow Execution Errors

| HTTP Status | Error Code | Message | Description |
|-------------|------------|---------|-------------|
| 400 | 15010 | Invalid execution status | Cannot perform action with current execution status |
| 400 | 15011 | Execution already running | Workflow execution is already in progress |
| 400 | 15012 | Execution failed | Workflow execution failed during processing |
| 408 | 15013 | Execution timeout | Workflow execution exceeded timeout limit |
| 409 | 15014 | Execution cancelled | Workflow execution was cancelled |

### 5. Node Testing Errors

| HTTP Status | Error Code | Message | Description |
|-------------|------------|---------|-------------|
| 400 | 15020 | Node test failed | Node test execution failed |
| 400 | 15021 | Invalid node input | Node input data is invalid |
| 400 | 15022 | Node execution error | Error during node simulation |
| 400 | 15023 | Mock data generation failed | Failed to generate mock data |

### 6. Webhook Errors

| HTTP Status | Error Code | Message | Description |
|-------------|------------|---------|-------------|
| 400 | 15030 | Invalid webhook payload | Webhook payload format is invalid |
| 400 | 15031 | Webhook validation failed | Webhook signature validation failed |
| 500 | 15032 | Webhook processing failed | Internal error processing webhook |

### 7. SSE Connection Errors

| HTTP Status | Error Code | Message | Description |
|-------------|------------|---------|-------------|
| 400 | 15040 | Invalid auth token | SSE authentication token is invalid |
| 408 | 15041 | Connection timeout | SSE connection exceeded timeout |
| 500 | 15042 | Connection failed | Failed to establish SSE connection |

## 🔍 Error Handling Examples

### Example 1: Workflow Not Found

**Request:**
```http
GET /api/v1/user/workflows/invalid-uuid
Authorization: Bearer <token>
```

**Response:**
```json
{
  "code": 15005,
  "message": "Workflow not found",
  "result": null,
  "detail": {
    "workflowId": "invalid-uuid",
    "reason": "Workflow does not exist or user doesn't have access"
  },
  "timestamp": "2023-01-01T00:00:00.000Z",
  "path": "/api/v1/user/workflows/invalid-uuid"
}
```

### Example 2: Validation Error

**Request:**
```http
POST /api/v1/user/workflows
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "",
  "definition": "invalid-json"
}
```

**Response:**
```json
{
  "code": 11001,
  "message": "Validation failed",
  "result": null,
  "detail": {
    "errors": [
      {
        "field": "name",
        "value": "",
        "constraint": "name should not be empty"
      },
      {
        "field": "definition",
        "value": "invalid-json",
        "constraint": "definition must be a valid JSON object"
      }
    ]
  },
  "timestamp": "2023-01-01T00:00:00.000Z",
  "path": "/api/v1/user/workflows"
}
```

### Example 3: Execution Status Error

**Request:**
```http
POST /api/v1/user/workflow-executions/123/start
Authorization: Bearer <token>
```

**Response:**
```json
{
  "code": 15010,
  "message": "Invalid execution status",
  "result": null,
  "detail": {
    "executionId": "123",
    "currentStatus": "completed",
    "requiredStatus": "queued",
    "reason": "Cannot start execution that is already completed"
  },
  "timestamp": "2023-01-01T00:00:00.000Z",
  "path": "/api/v1/user/workflow-executions/123/start"
}
```

## 🛠️ Client Error Handling

### JavaScript/TypeScript Example

```typescript
interface ApiError {
  code: number;
  message: string;
  result: null;
  detail?: any;
  timestamp: string;
  path: string;
}

async function handleWorkflowRequest() {
  try {
    const response = await fetch('/api/v1/user/workflows', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(workflowData),
    });

    const data = await response.json();

    if (!response.ok) {
      const error = data as ApiError;
      
      switch (error.code) {
        case 15005:
          console.error('Workflow not found:', error.detail);
          break;
        case 11001:
          console.error('Validation errors:', error.detail.errors);
          break;
        case 10001:
          console.error('Authentication required');
          // Redirect to login
          break;
        default:
          console.error('Unknown error:', error.message);
      }
      
      throw new Error(error.message);
    }

    return data.result;
  } catch (error) {
    console.error('Request failed:', error);
    throw error;
  }
}
```

## 📚 Best Practices

### For API Consumers

1. **Always check HTTP status codes** before processing response
2. **Use error codes for programmatic handling** instead of parsing messages
3. **Display user-friendly messages** based on error codes
4. **Log detailed error information** for debugging
5. **Implement retry logic** for transient errors (5xx codes)

### For API Developers

1. **Use consistent error codes** across related endpoints
2. **Provide meaningful error messages** that help users understand the issue
3. **Include relevant context** in the `detail` field
4. **Document all possible error responses** in Swagger
5. **Test error scenarios** thoroughly

## 🔄 Error Recovery Strategies

### Retry Logic

```typescript
async function retryableRequest(url: string, options: RequestInit, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, options);
      
      if (response.ok) {
        return response.json();
      }
      
      // Don't retry client errors (4xx)
      if (response.status >= 400 && response.status < 500) {
        throw new Error(`Client error: ${response.status}`);
      }
      
      // Retry server errors (5xx)
      if (attempt === maxRetries) {
        throw new Error(`Server error after ${maxRetries} attempts`);
      }
      
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
    }
  }
}
```

## 📞 Support

For additional support with error handling:

1. Check the [API Documentation](http://localhost:3000/api/docs)
2. Review error logs in the application
3. Contact the development team with error codes and request details
