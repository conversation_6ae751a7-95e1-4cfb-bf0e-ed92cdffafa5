import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { NodeDefinition, NodeCategory } from '../entities';

/**
 * Repository cho NodeDefinition entity
 * Xử lý các thao tác database liên quan đến node definitions
 */
@Injectable()
export class NodeDefinitionRepository extends Repository<NodeDefinition> {
  constructor(private dataSource: DataSource) {
    super(NodeDefinition, dataSource.createEntityManager());
  }

  /**
   * Tạo base query cho NodeDefinition
   */
  private createBaseQuery() {
    return this.createQueryBuilder('node_def');
  }

  /**
   * L<PERSON>y tất cả node definitions theo category
   * @param category - Category của node
   * @returns Promise<NodeDefinition[]>
   */
  async findByCategory(category: NodeCategory): Promise<NodeDefinition[]> {
    return this.createBaseQuery()
      .where('node_def.category = :category', { category })
      .orderBy('node_def.name', 'ASC')
      .getMany();
  }

  /**
   * Lấy node definition theo type
   * @param type - Type của node
   * @returns Promise<NodeDefinition | null>
   */
  async findByType(type: string): Promise<NodeDefinition | null> {
    return this.createBaseQuery()
      .where('node_def.type = :type', { type })
      .getOne();
  }

  /**
   * Lấy tất cả node definitions với phân trang
   * @param page - Số trang
   * @param limit - Số lượng item per page
   * @returns Promise<[NodeDefinition[], number]>
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 20,
  ): Promise<[NodeDefinition[], number]> {
    const offset = (page - 1) * limit;

    return this.createBaseQuery()
      .orderBy('node_def.category', 'ASC')
      .addOrderBy('node_def.name', 'ASC')
      .skip(offset)
      .take(limit)
      .getManyAndCount();
  }

  /**
   * Tìm kiếm node definitions theo tên hoặc mô tả
   * @param searchTerm - Từ khóa tìm kiếm
   * @returns Promise<NodeDefinition[]>
   */
  async searchByNameOrDescription(searchTerm: string): Promise<NodeDefinition[]> {
    return this.createBaseQuery()
      .where(
        '(LOWER(node_def.name) LIKE LOWER(:searchTerm) OR LOWER(node_def.description) LIKE LOWER(:searchTerm))',
        { searchTerm: `%${searchTerm}%` }
      )
      .orderBy('node_def.name', 'ASC')
      .getMany();
  }

  /**
   * Kiểm tra node definition có tồn tại theo type
   * @param type - Type của node
   * @returns Promise<boolean>
   */
  async existsByType(type: string): Promise<boolean> {
    const count = await this.createBaseQuery()
      .where('node_def.type = :type', { type })
      .getCount();

    return count > 0;
  }

  /**
   * Lấy tất cả categories có sẵn
   * @returns Promise<NodeCategory[]>
   */
  async getAvailableCategories(): Promise<NodeCategory[]> {
    const result = await this.createBaseQuery()
      .select('DISTINCT node_def.category', 'category')
      .getRawMany();

    return result.map(row => row.category);
  }

  /**
   * Cập nhật version của node definition
   * @param type - Type của node
   * @param version - Version mới
   * @returns Promise<void>
   */
  async updateVersion(type: string, version: string): Promise<void> {
    await this.createBaseQuery()
      .update()
      .set({ version })
      .where('type = :type', { type })
      .execute();
  }

  /**
   * Tìm kiếm node definitions theo tags và category
   * @param tags - Danh sách tags
   * @param category - Category (optional)
   * @returns Promise<NodeDefinition[]>
   */
  async findByTagsAndCategory(
    tags: string[],
    category?: NodeCategory
  ): Promise<NodeDefinition[]> {
    let query = this.createBaseQuery()
      .where('node_def.tags && :tags', { tags });

    if (category) {
      query = query.andWhere('node_def.category = :category', { category });
    }

    return query
      .orderBy('node_def.name', 'ASC')
      .getMany();
  }

  /**
   * Full-text search trong name, description, và documentation
   * @param searchTerm - Từ khóa tìm kiếm
   * @returns Promise<NodeDefinition[]>
   */
  async fullTextSearch(searchTerm: string): Promise<NodeDefinition[]> {
    return this.createBaseQuery()
      .where(
        `to_tsvector('english', COALESCE(node_def.name, '') || ' ' || COALESCE(node_def.description, '') || ' ' || COALESCE(node_def.documentation, '')) @@ plainto_tsquery('english', :searchTerm)`,
        { searchTerm }
      )
      .orderBy('node_def.name', 'ASC')
      .getMany();
  }

  /**
   * Lấy thống kê registry
   * @returns Promise<object>
   */
  async getStatistics(): Promise<{
    total: number;
    byCategory: Record<string, number>;
    versions: number;
  }> {
    const total = await this.count();

    const byCategory = await this.createBaseQuery()
      .select('node_def.category', 'category')
      .addSelect('COUNT(*)', 'count')
      .groupBy('node_def.category')
      .getRawMany();

    // Deprecated and UI-related statistics removed - simplified stats

    const versions = await this.createBaseQuery()
      .select('DISTINCT node_def.version')
      .getRawMany();

    const categoryStats = byCategory.reduce((acc, item) => {
      acc[item.category] = parseInt(item.count);
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      byCategory: categoryStats,
      versions: versions.length
    };
  }

  /**
   * Lấy tất cả node definitions bị deprecated
   * @returns Promise<NodeDefinition[]>
   */
  async findDeprecated(): Promise<NodeDefinition[]> {
    return this.createBaseQuery()
      .where('node_def.is_deprecated = :isDeprecated', { isDeprecated: true })
      .orderBy('node_def.updated_at', 'DESC')
      .getMany();
  }

  /**
   * Tìm node definitions theo version range
   * @param nodeType - Type của node
   * @param minVersion - Version tối thiểu
   * @param maxVersion - Version tối đa
   * @returns Promise<NodeDefinition[]>
   */
  async findByVersionRange(
    nodeType: string,
    minVersion: string,
    maxVersion: string
  ): Promise<NodeDefinition[]> {
    return this.createBaseQuery()
      .where('node_def.type = :nodeType', { nodeType })
      .andWhere('node_def.version >= :minVersion', { minVersion })
      .andWhere('node_def.version <= :maxVersion', { maxVersion })
      .orderBy('node_def.version', 'DESC')
      .getMany();
  }

  /**
   * Advanced search với multiple filters
   * @param filters - Object chứa các filter options
   * @returns Promise<NodeDefinition[]>
   */
  async advancedSearch(filters: {
    query?: string;
    category?: NodeCategory;
    tags?: string[];
    isDeprecated?: boolean;
    hasDocumentation?: boolean;
    hasExamples?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<[NodeDefinition[], number]> {
    let query = this.createBaseQuery();

    // Text search
    if (filters.query) {
      query = query.where(
        `(LOWER(node_def.name) LIKE LOWER(:query) OR LOWER(node_def.description) LIKE LOWER(:query) OR LOWER(node_def.documentation) LIKE LOWER(:query))`,
        { query: `%${filters.query}%` }
      );
    }

    // Category filter
    if (filters.category) {
      query = query.andWhere('node_def.category = :category', { category: filters.category });
    }

    // Tags filter
    if (filters.tags && filters.tags.length > 0) {
      query = query.andWhere('node_def.tags && :tags', { tags: filters.tags });
    }

    // Deprecated filter
    if (filters.isDeprecated !== undefined) {
      query = query.andWhere('node_def.is_deprecated = :isDeprecated', {
        isDeprecated: filters.isDeprecated
      });
    }

    // Documentation filter
    if (filters.hasDocumentation !== undefined) {
      if (filters.hasDocumentation) {
        query = query.andWhere('node_def.documentation IS NOT NULL');
      } else {
        query = query.andWhere('node_def.documentation IS NULL');
      }
    }

    // Examples filter
    if (filters.hasExamples !== undefined) {
      if (filters.hasExamples) {
        query = query.andWhere('node_def.examples IS NOT NULL');
      } else {
        query = query.andWhere('node_def.examples IS NULL');
      }
    }

    // Pagination
    if (filters.offset) {
      query = query.skip(filters.offset);
    }

    if (filters.limit) {
      query = query.take(filters.limit);
    }

    return query
      .orderBy('node_def.category', 'ASC')
      .addOrderBy('node_def.name', 'ASC')
      .getManyAndCount();
  }
}
