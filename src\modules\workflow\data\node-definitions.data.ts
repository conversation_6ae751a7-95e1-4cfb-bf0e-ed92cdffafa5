import { NodeCategory } from '../entities';
import { CreateNodeDefinitionDto } from '../dto/node-definition';

/**
 * Core node definitions for system initialization
 * UI metadata (icons, colors, documentation) handled by FE
 */
export const NODE_DEFINITIONS: CreateNodeDefinitionDto[] = [
  {
    type: 'system.start',
    name: 'Start Node',
    description: 'Workflow start point',
    category: NodeCategory.SYSTEM,
    inputSchema: {
      type: 'object',
      properties: {
        triggerData: { type: 'object' }
      }
    },
    outputSchema: {
      type: 'object',
      properties: {
        output: { type: 'object' }
      }
    },
    version: '1.0.0'
  },
  {
    type: 'system.end',
    name: 'End Node',
    description: 'Workflow end point',
    category: NodeCategory.SYSTEM,
    inputSchema: {
      type: 'object',
      properties: {
        input: { type: 'object' }
      }
    },
    outputSchema: {
      type: 'object',
      properties: {}
    },
    version: '1.0.0'
  },
  {
    type: 'system.condition',
    name: 'Condition Node',
    description: 'Conditional branching',
    category: NodeCategory.SYSTEM,
    inputSchema: {
      type: 'object',
      properties: {
        condition: { type: 'string' },
        input: { type: 'object' }
      },
      required: ['condition']
    },
    outputSchema: {
      type: 'object',
      properties: {
        true: { type: 'object' },
        false: { type: 'object' }
      }
    },
    version: '1.0.0'
  },
  {
    type: 'system.loop',
    name: 'Loop Node',
    description: 'Loop through items',
    category: NodeCategory.SYSTEM,
    inputSchema: {
      type: 'object',
      properties: {
        items: { type: 'array' },
        maxIterations: { type: 'number' }
      },
      required: ['items']
    },
    outputSchema: {
      type: 'object',
      properties: {
        results: { type: 'array' }
      }
    },
    version: '1.0.0'
  },
  {
    type: 'system.delay',
    name: 'Delay Node',
    description: 'Pause workflow execution',
    category: NodeCategory.SYSTEM,
    inputSchema: {
      type: 'object',
      properties: {
        duration: { type: 'number' },
        unit: { type: 'string', enum: ['seconds', 'minutes', 'hours'] }
      },
      required: ['duration', 'unit']
    },
    outputSchema: {
      type: 'object',
      properties: {
        delayed: { type: 'boolean' }
      }
    },
    version: '1.0.0'
  },
  {
    type: 'google.sheet.getRows',
    name: 'Get Google Sheets Rows',
    description: 'Retrieve data from Google Sheets',
    category: NodeCategory.GOOGLE_SHEETS,
    inputSchema: {
      type: 'object',
      properties: {
        spreadsheetId: { type: 'string' },
        range: { type: 'string' }
      },
      required: ['spreadsheetId', 'range']
    },
    outputSchema: {
      type: 'object',
      properties: {
        rows: { type: 'array' }
      }
    },
    version: '1.0.0'
  },
  {
    type: 'facebook.page.post',
    name: 'Facebook Page Post',
    description: 'Post to Facebook page',
    category: NodeCategory.FACEBOOK_PAGE,
    inputSchema: {
      type: 'object',
      properties: {
        pageId: { type: 'string' },
        message: { type: 'string' }
      },
      required: ['pageId', 'message']
    },
    outputSchema: {
      type: 'object',
      properties: {
        postId: { type: 'string' }
      }
    },
    version: '1.0.0'
  },
  {
    type: 'zalo.oa.sendMessage',
    name: 'Zalo OA Send Message',
    description: 'Send message via Zalo OA',
    category: NodeCategory.ZALO_OA,
    inputSchema: {
      type: 'object',
      properties: {
        userId: { type: 'string' },
        message: { type: 'string' }
      },
      required: ['userId', 'message']
    },
    outputSchema: {
      type: 'object',
      properties: {
        messageId: { type: 'string' }
      }
    },
    version: '1.0.0'
  }
];
