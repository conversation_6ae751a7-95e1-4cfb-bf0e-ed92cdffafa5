import { Module } from '@nestjs/common';
import { WorkflowUserModule } from './user/workflow-user.module';
import { WorkflowAdminModule } from './admin/workflow-admin.module';
import { WorkflowSharedModule } from './shared/workflow-shared.module';
import { WorkflowWebhookModule } from './webhook/webhook.module';

/**
 * Module chính cho Workflow Management System
 */
@Module({
  imports: [
    WorkflowSharedModule,
    WorkflowUserModule,
    WorkflowAdminModule,
    WorkflowWebhookModule,
  ],
  exports: [
    WorkflowUserModule,
    WorkflowAdminModule,
    WorkflowSharedModule,
  ],
})
export class WorkflowModule {}
