import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { FLASH_SALE_ERROR_CODES } from '../../exceptions/flash-sale.exception';
import { FlashSaleRepository } from '../../repositories/flash-sale.repository';
import { ProductRepository } from '../../repositories/product.repository';
import { FlashSaleValidationHelper } from '../../helpers/flash-sale-validation.helper';
import { FlashSale } from '../../entities/flash-sale.entity';
import { FlashSaleStatus } from '../../enums/flash-sale-status.enum';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Transactional } from 'typeorm-transactional';
import {
  CreateFlashSaleDto,
  UpdateFlashSaleDto,
  BulkDeleteFlashSaleDto,
  BulkDeleteResponseDto,
  FlashSaleResponseDto,
  QueryFlashSaleAdminDto,
  UpdateFlashSaleStatusDto,
  PaginatedFlashSaleResponseDto
} from '../dto';
import { DEFAULT_MAX_CONFIGURATION } from '../../interfaces/max-configuration.interface';

/**
 * Service xử lý logic nghiệp vụ Flash Sale cho Admin
 */
@Injectable()
export class FlashSaleAdminService {
  private readonly logger = new Logger(FlashSaleAdminService.name);

  constructor(
    private readonly flashSaleRepository: FlashSaleRepository,
    private readonly productRepository: ProductRepository,
    private readonly flashSaleValidationHelper: FlashSaleValidationHelper
  ) {}

  /**
   * Tạo flash sale mới (Admin)
   */
  @Transactional()
  async createFlashSale(employeeId: number, createDto: CreateFlashSaleDto): Promise<FlashSaleResponseDto> {
    try {
      const { productId, discountPercentage, displayTime, startTime, endTime, maxConfiguration, status } = createDto;

      // Validate discount percentage
      this.flashSaleValidationHelper.validateDiscountPercentage(discountPercentage);

      // Validate time sequence
      this.flashSaleValidationHelper.validateTimeSequence(displayTime, startTime, endTime);

      // Validate product existence
      const product = await this.productRepository.findById(productId);
      if (!product) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.PRODUCT_NOT_ELIGIBLE,
          `Sản phẩm với ID ${productId} không tồn tại`
        );
      }

      // Validate product eligibility
      this.flashSaleValidationHelper.validateProductEligibility(product);

      // Check overlapping flash sale
      await this.flashSaleValidationHelper.checkOverlappingFlashSale(productId, startTime, endTime);

      // Validate max configuration
      const validatedMaxConfig = this.flashSaleValidationHelper.validateMaxConfiguration(
        maxConfiguration || DEFAULT_MAX_CONFIGURATION
      );

      // ✅ Determine final status (default to DRAFT if not provided)
      const finalStatus = status || FlashSaleStatus.DRAFT;

      // ✅ Additional validation if status is SCHEDULED
      if (finalStatus === FlashSaleStatus.SCHEDULED) {
        const now = Date.now();
        if (startTime <= now) {
          throw new AppException(
            FLASH_SALE_ERROR_CODES.INVALID_TIME_RANGE,
            'Không thể tạo flash sale với trạng thái SCHEDULED khi thời gian bắt đầu đã qua'
          );
        }
      }

      // Create flash sale entity
      const flashSale = this.flashSaleRepository.create({
        productId,
        employeeId,
        userId: null,
        discountPercentage,
        displayTime, // displayTime is already in seconds (1-60)
        startTime,
        endTime,
        maxConfiguration: validatedMaxConfig,
        status: finalStatus, // ✅ Use selected status
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });

      // Save to database
      const savedFlashSale = await this.flashSaleRepository.save(flashSale);

      this.logger.log(`Admin ${employeeId} created flash sale ${savedFlashSale.id} for product ${productId}`);

      return this.mapToResponseDto(savedFlashSale);
    } catch (error) {
      this.logger.error(`Failed to create flash sale: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException(
        FLASH_SALE_ERROR_CODES.CREATION_FAILED,
        `Tạo flash sale thất bại: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách flash sale với phân trang (Admin)
   */
  async getFlashSales(queryDto: QueryFlashSaleAdminDto): Promise<PaginatedFlashSaleResponseDto> {
    try {
      const { page = 1, limit = 10, status, productId, employeeId, isActive, sortBy = 'createdAt', sortOrder = 'DESC' } = queryDto;

      // Build query conditions
      const queryBuilder = this.flashSaleRepository.createQueryBuilder('fs');

      if (status) {
        queryBuilder.andWhere('fs.status = :status', { status });
      }

      if (productId) {
        queryBuilder.andWhere('fs.product_id = :productId', { productId });
      }

      if (employeeId) {
        queryBuilder.andWhere('fs.employee_id = :employeeId', { employeeId });
      }

      if (isActive !== undefined) {
        queryBuilder.andWhere('fs.is_active = :isActive', { isActive });
      }

      // Add sorting
      const validSortFields = ['createdAt', 'startTime', 'endTime', 'updatedAt'];
      const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
      queryBuilder.orderBy(`fs.${this.camelToSnake(sortField)}`, sortOrder);

      // Add pagination
      queryBuilder.skip((page - 1) * limit).take(limit);

      const [items, total] = await queryBuilder.getManyAndCount();

      const responseItems = items.map(item => this.mapToResponseDto(item));

      return {
        items: responseItems,
        meta: {
          totalItems: total,
          itemCount: responseItems.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`Failed to get flash sales: ${error.message}`, error.stack);
      throw new AppException(
        FLASH_SALE_ERROR_CODES.RETRIEVAL_FAILED,
        `Lấy danh sách flash sale thất bại: ${error.message}`
      );
    }
  }

  /**
   * Lấy chi tiết flash sale (Admin)
   */
  async getFlashSaleById(id: number): Promise<FlashSaleResponseDto> {
    try {
      const flashSale = await this.flashSaleRepository.findByIdWithProduct(id);
      
      if (!flashSale) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
          'Flash sale không tồn tại'
        );
      }

      return this.mapToResponseDto(flashSale);
    } catch (error) {
      this.logger.error(`Failed to get flash sale ${id}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException(
        FLASH_SALE_ERROR_CODES.RETRIEVAL_FAILED,
        `Lấy thông tin flash sale thất bại: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật flash sale (Admin)
   */
  @Transactional()
  async updateFlashSale(id: number, employeeId: number, updateDto: UpdateFlashSaleDto): Promise<FlashSaleResponseDto> {
    try {
      const flashSale = await this.flashSaleRepository.findById(id);
      
      if (!flashSale) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
          'Flash sale không tồn tại'
        );
      }

      // ✅ Debug logging chỉ khi cần thiết (có thể comment out trong production)

      // ✅ Validate ownership - Admin chỉ có thể update flash sale của mình
      if (flashSale.employeeId && Number(flashSale.employeeId) !== Number(employeeId)) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
          `Không có quyền cập nhật flash sale này. Flash sale thuộc về employee ${flashSale.employeeId}, bạn là employee ${employeeId}`
        );
      }

      // ✅ Nếu flash sale được tạo bởi user, admin không thể update
      if (flashSale.userId && !flashSale.employeeId) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
          `Không thể cập nhật flash sale này. Flash sale được tạo bởi user (ID: ${flashSale.userId}), không phải admin`
        );
      }

      // Validate updates
      if (updateDto.discountPercentage) {
        this.flashSaleValidationHelper.validateDiscountPercentage(updateDto.discountPercentage);
      }

      if (updateDto.displayTime || updateDto.startTime || updateDto.endTime) {
        const displayTime = updateDto.displayTime || flashSale.displayTime;
        const startTime = updateDto.startTime || flashSale.startTime;
        const endTime = updateDto.endTime || flashSale.endTime;
        
        this.flashSaleValidationHelper.validateTimeSequence(displayTime, startTime, endTime);

        // Check overlapping if time changed
        if (updateDto.startTime || updateDto.endTime) {
          await this.flashSaleValidationHelper.checkOverlappingFlashSale(
            flashSale.productId,
            startTime,
            endTime,
            id
          );
        }
      }

      if (updateDto.maxConfiguration) {
        updateDto.maxConfiguration = this.flashSaleValidationHelper.validateMaxConfiguration(updateDto.maxConfiguration);
      }

      // Update fields
      Object.assign(flashSale, {
        ...updateDto,
        updatedAt: Date.now()
      });

      const updatedFlashSale = await this.flashSaleRepository.save(flashSale);

      this.logger.log(`Admin ${employeeId} updated flash sale ${id}`);

      return this.mapToResponseDto(updatedFlashSale);
    } catch (error) {
      this.logger.error(`Failed to update flash sale ${id}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException(
        FLASH_SALE_ERROR_CODES.UPDATE_FAILED,
        `Cập nhật flash sale thất bại: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật trạng thái flash sale (Admin)
   */
  @Transactional()
  async updateFlashSaleStatus(id: number, employeeId: number, statusDto: UpdateFlashSaleStatusDto): Promise<FlashSaleResponseDto> {
    try {
      const flashSale = await this.flashSaleRepository.findById(id);
      
      if (!flashSale) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.FLASH_SALE_NOT_FOUND,
          'Flash sale không tồn tại'
        );
      }

      // Validate ownership
      if (flashSale.employeeId && flashSale.employeeId !== employeeId) {
        throw new AppException(
          FLASH_SALE_ERROR_CODES.UNAUTHORIZED,
          'Không có quyền cập nhật flash sale này'
        );
      }

      // Validate status transition
      this.flashSaleValidationHelper.validateStatusTransition(flashSale.status, statusDto.status);

      // ✅ Nếu chuyển sang SCHEDULED, validate thời gian
      if (statusDto.status === FlashSaleStatus.SCHEDULED) {
        const now = Date.now();
        if (flashSale.startTime <= now) {
          throw new AppException(
            FLASH_SALE_ERROR_CODES.INVALID_TIME_RANGE,
            'Không thể lên lịch flash sale với thời gian bắt đầu đã qua'
          );
        }
      }

      // Update status
      flashSale.status = statusDto.status;
      flashSale.updatedAt = Date.now();

      const updatedFlashSale = await this.flashSaleRepository.save(flashSale);

      this.logger.log(`Admin ${employeeId} updated flash sale ${id} status to ${statusDto.status}`);

      return this.mapToResponseDto(updatedFlashSale);
    } catch (error) {
      this.logger.error(`Failed to update flash sale status ${id}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException(
        FLASH_SALE_ERROR_CODES.STATUS_UPDATE_FAILED,
        `Cập nhật trạng thái flash sale thất bại: ${error.message}`
      );
    }
  }

  // ✅ REMOVED: Single delete method - chỉ sử dụng bulk delete API

  /**
   * Transform images to include both key and URL with CDN prefix
   */
  private transformImages(images: any[]): Array<{ key: string; position: number; url: string }> {
    if (!images || !Array.isArray(images)) return [];

    return images.map((img, index) => {
      const position = typeof img.position === 'number' ? img.position : index;
      return {
        key: img.key,
        position: position,
        url: `https://cdn.redai.vn/${img.key}`
      };
    });
  }

  /**
   * Map FlashSale entity to ResponseDto
   */
  private mapToResponseDto(flashSale: FlashSale): FlashSaleResponseDto {
    const responseDto = {
      id: flashSale.id,
      productId: flashSale.productId,
      employeeId: flashSale.employeeId,
      discountPercentage: flashSale.discountPercentage,
      displayTime: flashSale.displayTime,
      startTime: flashSale.startTime,
      endTime: flashSale.endTime,
      maxConfiguration: flashSale.maxConfiguration,
      status: flashSale.status,
      isActive: flashSale.isActive,
      createdAt: flashSale.createdAt,
      updatedAt: flashSale.updatedAt,
      product: flashSale.product,
      salePrice: flashSale.salePrice,
      timeRemaining: flashSale.timeRemaining,
      soldCount: flashSale.soldCount
    };

    // Transform product images to include URL field if product exists
    if (responseDto.product && responseDto.product.images) {
      responseDto.product = {
        ...responseDto.product,
        images: this.transformImages(responseDto.product.images)
      };
    }

    return responseDto;
  }

  /**
   * Convert camelCase to snake_case
   */
  private camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * ✅ NEW: Bulk delete flash sales (Admin)
   */
  @Transactional()
  async deleteFlashSales(employeeId: number, deleteDto: BulkDeleteFlashSaleDto): Promise<BulkDeleteResponseDto> {
    const { ids } = deleteDto;
    const result: BulkDeleteResponseDto = {
      successCount: 0,
      successIds: [],
      failureCount: 0,
      failures: [],
      totalProcessed: ids.length
    };

    this.logger.log(`Admin ${employeeId} attempting to delete ${ids.length} flash sales: [${ids.join(', ')}]`);

    for (const id of ids) {
      try {
        const flashSale = await this.flashSaleRepository.findById(id);

        if (!flashSale) {
          result.failures.push({
            id,
            reason: 'Flash sale không tồn tại'
          });
          result.failureCount++;
          continue;
        }

        // ✅ Debug logging chỉ khi cần thiết (có thể comment out trong production)

        // ✅ Validate ownership - Admin chỉ có thể delete flash sale của mình
        if (flashSale.employeeId && Number(flashSale.employeeId) !== Number(employeeId)) {
          result.failures.push({
            id,
            reason: `Không có quyền xóa flash sale này. Flash sale thuộc về employee ${flashSale.employeeId}, bạn là employee ${employeeId}`
          });
          result.failureCount++;
          continue;
        }

        // ✅ Nếu flash sale được tạo bởi user, admin không thể delete
        if (flashSale.userId && !flashSale.employeeId) {
          result.failures.push({
            id,
            reason: `Không thể xóa flash sale này. Flash sale được tạo bởi user (ID: ${flashSale.userId}), không phải admin`
          });
          result.failureCount++;
          continue;
        }

        // ✅ REMOVED: Admin có thể xóa flash sale ở mọi trạng thái (miễn là của mình)

        await this.flashSaleRepository.remove(flashSale);

        result.successIds.push(id);
        result.successCount++;

        this.logger.log(`Admin ${employeeId} successfully deleted flash sale ${id}`);
      } catch (error) {
        this.logger.error(`Failed to delete flash sale ${id}: ${error.message}`, error.stack);
        result.failures.push({
          id,
          reason: `Lỗi xóa flash sale: ${error.message}`
        });
        result.failureCount++;
      }
    }

    this.logger.log(`Admin ${employeeId} bulk delete completed: ${result.successCount} success, ${result.failureCount} failed`);
    return result;
  }
}
