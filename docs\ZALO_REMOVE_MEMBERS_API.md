# Zalo Remove Members API Documentation

## Tổng quan

API này cho phép xóa các thành viên khỏi nhóm chat Zalo GMF.

## Điề<PERSON> kiện sử dụng

- **Quyền cần thiết**: O<PERSON> phải được cấp quyền quản lý thông tin nhóm
- **Vai trò**: OA phải là admin của nhóm
- **Thành viên**: Các user ID phải là thành viên hiện tại của nhóm
- **Giới hạn**: Tối đa 100 thành viên mỗi lần xóa
- **Ràng buộc**: 
  - Không thể xóa admin khác (phải xóa quyền admin trước)
  - Không thể xóa chính mình
  - Không thể xóa OA khỏi nhóm

## Endpoint

### X<PERSON><PERSON> thành viên khỏi nhóm

```
POST /v1/zalo-group-management/{integrationId}/{groupId}/members/remove
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| integrationId | string | Yes | ID của Integration Zalo OA (UUID format) |
| groupId | string | Yes | ID của nhóm chat Zalo |

#### Request Body

```json
{
  "memberUserIds": [
    "8756287263669629130",
    "1234567890123456789"
  ]
}
```

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| memberUserIds | string[] | Yes | Danh sách user ID cần xóa (1-100 items) |

#### Headers

```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

#### Response

**Success Response (200 OK):**

```json
{
  "success": true,
  "message": "Xóa thành viên khỏi nhóm thành công",
  "data": {
    "error": 0,
    "message": "Success",
    "removedCount": 2
  }
}
```

**Error Response (400 Bad Request):**

```json
{
  "success": false,
  "message": "Danh sách member user IDs không được để trống",
  "error": "VALIDATION_ERROR"
}
```

**Error Response (403 Forbidden):**

```json
{
  "success": false,
  "message": "OA không có quyền quản lý thông tin nhóm",
  "error": "PERMISSION_DENIED"
}
```

**Error Response (404 Not Found):**

```json
{
  "success": false,
  "message": "Nhóm không tồn tại hoặc OA không phải admin",
  "error": "GROUP_NOT_FOUND"
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| error | integer | Mã lỗi từ Zalo API (0 = thành công) |
| message | string | Thông báo kết quả từ Zalo API |
| removedCount | integer | Số lượng thành viên đã gửi request xóa |

## Sử dụng

### 1. Xóa một thành viên

```bash
curl -X POST \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4/members/remove' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "memberUserIds": ["8756287263669629130"]
  }'
```

### 2. Xóa nhiều thành viên

```bash
curl -X POST \
  'https://api.example.com/v1/zalo-group-management/123e4567-e89b-12d3-a456-426614174000/f414c8f76fa586fbdfb4/members/remove' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "memberUserIds": [
      "8756287263669629130",
      "1234567890123456789",
      "9876543210987654321"
    ]
  }'
```

## Error Handling

### Lỗi thường gặp

1. **Không có quyền admin**
```json
{
  "success": false,
  "message": "OA không có quyền quản lý thông tin nhóm",
  "error": "PERMISSION_DENIED"
}
```
**Giải pháp**: Đảm bảo OA là admin của nhóm

2. **User không phải thành viên**
```json
{
  "success": false,
  "message": "Một số user ID không phải thành viên của nhóm",
  "error": "INVALID_USER_IDS"
}
```
**Giải pháp**: Kiểm tra danh sách thành viên trước khi xóa

3. **Cố gắng xóa admin**
```json
{
  "success": false,
  "message": "Không thể xóa admin khỏi nhóm. Vui lòng xóa quyền admin trước",
  "error": "CANNOT_REMOVE_ADMIN"
}
```
**Giải pháp**: Xóa quyền admin trước khi xóa thành viên

4. **Vượt quá giới hạn**
```json
{
  "success": false,
  "message": "Không thể xóa quá 100 thành viên cùng lúc",
  "error": "VALIDATION_ERROR"
}
```
**Giải pháp**: Chia nhỏ danh sách thành các batch < 100 items

## Code Example (JavaScript)

```javascript
class ZaloMembersManager {
  constructor(apiBaseUrl, authToken) {
    this.apiBaseUrl = apiBaseUrl;
    this.authToken = authToken;
  }

  async removeMembers(integrationId, groupId, memberUserIds) {
    const response = await fetch(
      `${this.apiBaseUrl}/v1/zalo-group-management/${integrationId}/${groupId}/members/remove`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          memberUserIds: memberUserIds,
        }),
      }
    );
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data;
  }

  async removeMembersBatch(integrationId, groupId, memberUserIds) {
    // Chia nhỏ nếu quá 100 thành viên
    const batchSize = 100;
    let totalRemoved = 0;
    
    for (let i = 0; i < memberUserIds.length; i += batchSize) {
      const batch = memberUserIds.slice(i, i + batchSize);
      const result = await this.removeMembers(integrationId, groupId, batch);
      totalRemoved += result.removedCount;
      
      // Delay giữa các batch để tránh rate limit
      if (i + batchSize < memberUserIds.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return {
      removedCount: totalRemoved,
      message: `Đã xóa ${totalRemoved} thành viên`,
    };
  }

  async removeInactiveMembers(integrationId, groupId, inactiveUserIds) {
    try {
      // Lấy danh sách thành viên hiện tại
      const membersResponse = await fetch(
        `${this.apiBaseUrl}/v1/zalo-group-management/${integrationId}/${groupId}/members`,
        {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
          },
        }
      );
      
      const membersData = await membersResponse.json();
      
      if (!membersData.success) {
        throw new Error('Không thể lấy danh sách thành viên');
      }
      
      // Lọc chỉ những user ID thực sự là thành viên
      const currentMemberIds = membersData.data.items.map(member => member.userId);
      const validUserIds = inactiveUserIds.filter(id => currentMemberIds.includes(id));
      
      if (validUserIds.length === 0) {
        return { removedCount: 0, message: 'Không có thành viên nào cần xóa' };
      }
      
      // Xóa thành viên
      return await this.removeMembersBatch(integrationId, groupId, validUserIds);
    } catch (error) {
      return {
        removedCount: 0,
        message: `Lỗi xóa thành viên: ${error.message}`,
        success: false,
      };
    }
  }
}

// Sử dụng
const membersManager = new ZaloMembersManager(
  'https://api.example.com',
  'your-jwt-token'
);

// Xóa thành viên cụ thể
membersManager.removeMembers(
  'integration-id',
  'group-id',
  ['8756287263669629130', '1234567890123456789']
).then(result => {
  console.log(`Đã xóa ${result.removedCount} thành viên`);
}).catch(error => {
  console.error('Lỗi:', error.message);
});

// Xóa thành viên không hoạt động
const inactiveUsers = ['user1', 'user2', 'user3'];
membersManager.removeInactiveMembers('integration-id', 'group-id', inactiveUsers)
  .then(result => {
    console.log(result.message);
  });
```

## Workflow tích hợp

### 1. Workflow cơ bản
```
1. Lấy danh sách thành viên hiện tại
   GET /members

2. Xác định thành viên cần xóa
   (User selection hoặc auto-remove logic)

3. Kiểm tra quyền admin của các thành viên
   (Không thể xóa admin)

4. Xóa thành viên
   POST /members/remove

5. Kiểm tra kết quả
   (Success/Error handling)
```

### 2. Auto-cleanup workflow
```javascript
async function autoCleanupInactiveMembers(integrationId, groupId, inactiveDays = 30) {
  try {
    // Logic xác định thành viên không hoạt động
    const inactiveMembers = await getInactiveMembers(groupId, inactiveDays);
    
    if (inactiveMembers.length === 0) {
      return { message: 'Không có thành viên không hoạt động' };
    }
    
    // Lọc bỏ admin
    const nonAdminMembers = inactiveMembers.filter(member => !member.isAdmin);
    
    if (nonAdminMembers.length === 0) {
      return { message: 'Không có thành viên không hoạt động nào có thể xóa' };
    }
    
    // Xóa thành viên
    const userIds = nonAdminMembers.map(m => m.userId);
    const result = await removeMembers(integrationId, groupId, userIds);
    
    return {
      message: `Đã tự động xóa ${result.removedCount}/${inactiveMembers.length} thành viên không hoạt động`,
      success: true,
    };
  } catch (error) {
    return {
      message: `Lỗi auto-cleanup: ${error.message}`,
      success: false,
    };
  }
}
```

## Best Practices

1. **Kiểm tra trước khi xóa**: Luôn lấy danh sách thành viên hiện tại trước
2. **Batch processing**: Chia nhỏ nếu > 100 thành viên
3. **Admin check**: Kiểm tra quyền admin trước khi xóa
4. **Confirmation**: Yêu cầu xác nhận từ user trước khi xóa
5. **Logging**: Log các hoạt động xóa thành viên
6. **Notification**: Thông báo cho admin khi có lỗi
7. **Rate limiting**: Tránh gọi API quá nhanh
8. **Rollback plan**: Có kế hoạch khôi phục nếu xóa nhầm
