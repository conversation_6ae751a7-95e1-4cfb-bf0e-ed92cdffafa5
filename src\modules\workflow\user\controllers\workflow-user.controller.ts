import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { CurrentUser } from '@/modules/auth/decorators';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { 
  Body, 
  Controller, 
  Delete, 
  Get, 
  Param, 
  ParseUUIDPipe, 
  Patch, 
  Post, 
  Query, 
  UseGuards 
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { WORKFLOW_ERROR_CODES } from '@modules/workflow/exceptions/workflow.exception';
import { WorkflowUserService } from '../services';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  ToggleWorkflowStatusDto,
  WorkflowQueryDto,
  WorkflowListItemDto,
  WorkflowDetailDto,
  WorkflowStatisticsDto,
} from '../dto';

/**
 * Controller xử lý các API endpoint cho Workflow của người dùng
 * Following agent-user.controller patterns
 */
@ApiTags(SWAGGER_API_TAGS.USER_WORKFLOW)
@Controller('user/workflows')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto)
export class WorkflowUserController {
  constructor(private readonly workflowUserService: WorkflowUserService) {}

  /**
   * Lấy danh sách workflows của user với pagination và filtering
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách workflows',
    description: 'Lấy danh sách workflows của user với pagination, search và filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách workflows thành công',
    type: ApiResponseDto<PaginatedResult<WorkflowListItemDto>>,
  })
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_LIST_FAILED)
  async getWorkflows(
    @Query() queryDto: WorkflowQueryDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<WorkflowListItemDto>>> {
    const result = await this.workflowUserService.getWorkflows(user.id, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách workflows thành công');
  }

  /**
   * Lấy chi tiết workflow theo ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết workflow',
    description: 'Lấy thông tin chi tiết của một workflow theo ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết workflow thành công',
    type: ApiResponseDto<WorkflowDetailDto>,
  })
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND)
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_GET_FAILED)
  async getWorkflowById(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<WorkflowDetailDto>> {
    const result = await this.workflowUserService.getWorkflowById(user.id, id);
    return ApiResponseDto.success(result, 'Lấy chi tiết workflow thành công');
  }

  /**
   * Tạo workflow mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo workflow mới',
    description: 'Tạo một workflow mới cho user',
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo workflow thành công',
    type: ApiResponseDto<WorkflowDetailDto>,
  })
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_NAME_EXISTS)
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_CREATE_FAILED)
  async createWorkflow(
    @Body() createDto: CreateWorkflowDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<WorkflowDetailDto>> {
    const result = await this.workflowUserService.createWorkflow(user.id, createDto);
    return ApiResponseDto.success(result, 'Tạo workflow thành công');
  }

  /**
   * Cập nhật workflow
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật workflow',
    description: 'Cập nhật thông tin của một workflow',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật workflow thành công',
    type: ApiResponseDto<WorkflowDetailDto>,
  })
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND)
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_NAME_EXISTS)
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_UPDATE_FAILED)
  async updateWorkflow(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateWorkflowDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<WorkflowDetailDto>> {
    const result = await this.workflowUserService.updateWorkflow(user.id, id, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật workflow thành công');
  }

  /**
   * Xóa workflow
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa workflow',
    description: 'Xóa một workflow theo ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa workflow thành công',
    type: ApiResponseDto<{ success: boolean }>,
  })
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND)
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_DELETE_FAILED)
  async deleteWorkflow(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    await this.workflowUserService.deleteWorkflow(user.id, id);
    return ApiResponseDto.success({ success: true }, 'Xóa workflow thành công');
  }

  /**
   * Toggle trạng thái kích hoạt workflow
   */
  @Patch(':id/status')
  @ApiOperation({
    summary: 'Toggle trạng thái workflow',
    description: 'Bật/tắt trạng thái kích hoạt của workflow',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái workflow thành công',
    type: ApiResponseDto<WorkflowDetailDto>,
  })
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_NOT_FOUND)
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_UPDATE_FAILED)
  async toggleWorkflowStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() toggleDto: ToggleWorkflowStatusDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<WorkflowDetailDto>> {
    const result = await this.workflowUserService.toggleWorkflowStatus(
      user.id, 
      id, 
      toggleDto.isActive
    );
    return ApiResponseDto.success(result, 'Cập nhật trạng thái workflow thành công');
  }

  /**
   * Tìm kiếm workflows
   */
  @Get('search/:query')
  @ApiOperation({
    summary: 'Tìm kiếm workflows',
    description: 'Tìm kiếm workflows theo tên',
  })
  @ApiResponse({
    status: 200,
    description: 'Tìm kiếm workflows thành công',
    type: ApiResponseDto<WorkflowListItemDto[]>,
  })
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_SEARCH_FAILED)
  async searchWorkflows(
    @Param('query') query: string,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<WorkflowListItemDto[]>> {
    const result = await this.workflowUserService.searchWorkflows(user.id, query);
    return ApiResponseDto.success(result, 'Tìm kiếm workflows thành công');
  }

  /**
   * Lấy thống kê workflows
   */
  @Get('statistics/overview')
  @ApiOperation({
    summary: 'Lấy thống kê workflows',
    description: 'Lấy thống kê tổng quan về workflows của user',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê workflows thành công',
    type: ApiResponseDto<WorkflowStatisticsDto>,
  })
  @ApiErrorResponse(WORKFLOW_ERROR_CODES.WORKFLOW_STATISTICS_FAILED)
  async getWorkflowStatistics(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<WorkflowStatisticsDto>> {
    const result = await this.workflowUserService.getWorkflowStatistics(user.id);
    return ApiResponseDto.success(result, 'Lấy thống kê workflows thành công');
  }
}
