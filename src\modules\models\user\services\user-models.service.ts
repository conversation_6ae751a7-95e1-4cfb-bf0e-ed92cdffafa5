import { AppException, ErrorCode } from '@common/exceptions';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { Injectable, Logger } from '@nestjs/common';

// DTOs
import {
  UserModelsResponseDto
} from '../dto/user-models';

// Mappers
import { UserModelsMapper } from '../mappers';

// Services
import { ModelsRepository } from '../../repositories/models.repository';
import { UserModelQueryDto } from '../dto/user-models/model-query.dto';

// Services

/**
 * Service xử lý business logic cho User Models APIs
 */
@Injectable()
export class UserModelsService {
  private readonly logger = new Logger(UserModelsService.name);

  constructor(
    private readonly modelsRepository: ModelsRepository,
  ) { }

  /**
   * Lấy danh sách models theo user keys với pagination
   * @param userId ID của user
   * @param keyllmId ID của user_key_llm
   * @param queryDto Query parameters
   * @returns Kết quả phân trang với user models
   */
  async getUserModelsByKeys(
    userId: number,
    queryDto: UserModelQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<UserModelsResponseDto>>> {
    try {
      // Lấy dữ liệu từ repository
      const result = await this.modelsRepository.findAll(userId, queryDto);

      // Convert entities sang DTOs
      const items = UserModelsMapper.toResponseDtoList(result.items);

      // Thêm hasItems field
      const hasItems = result.meta.totalItems > 0;

      return ApiResponseDto.paginated({
        items,
        meta: {
          ...result.meta,
          hasItems,
        }
      }, 'Lấy danh sách models theo keys thành công');

    } catch (error) {
      this.logger.error(`Failed to get user models by keys for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể lấy danh sách models theo keys'
      );
    }
  }
}
