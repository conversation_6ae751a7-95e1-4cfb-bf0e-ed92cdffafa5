import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  Min,
  Max,
  ValidateNested,
  IsObject,
  IsEnum
} from 'class-validator';
import { FlashSaleStatus } from '../../enums/flash-sale-status.enum';
import { MaxConfiguration } from '../../interfaces/max-configuration.interface';

/**
 * DTO cho time window limit configuration
 */
export class TimeWindowLimitDto {
  @ApiProperty({
    description: 'Số lượng cho phép trong khung thời gian',
    example: 1,
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  qty: number;

  @ApiProperty({
    description: 'Khung thời gian tính bằng phút',
    example: 60,
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  windowMinutes: number;
}

/**
 * DTO cho max configuration
 */
export class MaxConfigurationDto implements MaxConfiguration {
  @ApiPropertyOptional({
    description: 'Số lượng tối đa mỗi user có thể mua (null = không giới hạn)',
    example: 3,
    nullable: true
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxPerUser: number | null;

  @ApiPropertyOptional({
    description: 'Tổng số lượng inventory cho flash sale (null = không giới hạn)',
    example: 1000,
    nullable: true
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  totalInventory: number | null;

  @ApiPropertyOptional({
    description: 'Giới hạn số lượng mỗi đơn hàng (null = không giới hạn)',
    example: 2,
    nullable: true
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  purchaseLimitPerOrder: number | null;

  @ApiPropertyOptional({
    description: 'Giới hạn mua lặp trong khung thời gian (null = không giới hạn)',
    type: TimeWindowLimitDto,
    nullable: true
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => TimeWindowLimitDto)
  timeWindowLimit: TimeWindowLimitDto | null;
}

/**
 * DTO để tạo flash sale mới (Admin)
 */
export class CreateFlashSaleDto {
  @ApiProperty({
    description: 'ID sản phẩm áp dụng flash sale',
    example: 123
  })
  @IsNotEmpty()
  @IsNumber()
  productId: number;

  @ApiProperty({
    description: 'Phần trăm giảm giá (1-99%)',
    example: 20,
    minimum: 1,
    maximum: 99
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(99)
  discountPercentage: number;

  @ApiProperty({
    description: 'Thời gian hiển thị sản phẩm flash sale cho user (giây, 1-60). Tạo cảm giác khan hiếm - sản phẩm chỉ hiển thị trong khoảng thời gian này',
    example: 30,
    minimum: 1,
    maximum: 60
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(60)
  displayTime: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu flash sale (timestamp milliseconds)',
    example: 1641081600000
  })
  @IsNotEmpty()
  @IsNumber()
  startTime: number;

  @ApiProperty({
    description: 'Thời gian kết thúc flash sale (timestamp milliseconds)',
    example: 1641168000000
  })
  @IsNotEmpty()
  @IsNumber()
  endTime: number;

  @ApiPropertyOptional({
    description: 'Trạng thái flash sale (mặc định: DRAFT). Admin có thể chọn DRAFT để lưu nháp hoặc SCHEDULED để lên lịch ngay',
    enum: FlashSaleStatus,
    example: FlashSaleStatus.DRAFT,
    default: FlashSaleStatus.DRAFT
  })
  @IsOptional()
  @IsEnum(FlashSaleStatus, {
    message: 'Trạng thái phải là DRAFT, SCHEDULED hoặc CANCELLED'
  })
  status?: FlashSaleStatus;

  @ApiPropertyOptional({
    description: 'Cấu hình giới hạn flash sale',
    type: MaxConfigurationDto
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => MaxConfigurationDto)
  maxConfiguration?: MaxConfigurationDto;
}
