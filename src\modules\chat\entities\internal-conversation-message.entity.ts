import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Message role enum for internal conversations
 */
export enum InternalConversationMessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
}

/**
 * Internal Conversation Messages entity
 * Stores actual messages with dual ownership support and AI conversation roles
 */
@Entity('internal_conversation_messages')
export class InternalConversationMessage {
  /**
   * UUID primary key for the message
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Reference to the conversation thread
   */
  @Column({ name: 'thread_id', type: 'uuid', nullable: false })
  threadId: string;

  /**
   * ID of the user who sent this message (nullable for employee messages)
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId?: number;

  /**
   * ID of the employee who sent this message (nullable for user messages)
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: true })
  employeeId?: number;

  /**
   * Message text content
   */
  @Column({ type: 'text', nullable: false })
  text: string;

  /**
   * Flag indicating if message has attachments
   */
  @Column({ name: 'has_attachments', type: 'boolean', default: false })
  hasAttachments: boolean;

  /**
   * Creation timestamp in milliseconds
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  createdAt: string;

  /**
   * Role of the message in the conversation (user or assistant)
   */
  @Column({
    name: 'role',
    type: 'enum',
    enum: InternalConversationMessageRole,
    nullable: false
  })
  role: InternalConversationMessageRole;

  /**
   * Flag indicating if this message is a tool call confirmation
   */
  @Column({
    name: 'is_tool_call_confirm',
    type: 'boolean',
    default: false,
    nullable: false
  })
  isToolCallConfirm: boolean;

  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId?: string;

  /**
   * Flag indicating if this message has been processed by the worker
   * Used to track which messages need processing vs already processed
   */
  @Column({
    name: 'processed',
    type: 'boolean',
    default: false,
    nullable: false
  })
  processed: boolean;

  @Column({
    name: 'replying_to_message_id',
    type: 'uuid',
    nullable: true
  })
  replyingToMessageId?: string;
}
