import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppException, ErrorCode } from '@/common';
import { WORKFLOW_ERROR_CODES } from '../constants/workflow-error-codes';
import { Workflow, WorkflowExecution } from '../entities';
import { WorkflowQueueService } from './workflow-queue.service';
import { WorkflowExecutionJobData } from '@/shared/queue/queue.types';
import { ExecutionStatus } from '../constants';

/**
 * Service quản lý việc trigger workflow từ webhook và các nguồn khác
 * Handles workflow triggering logic và tạo execution jobs
 */
@Injectable()
export class WorkflowTriggerService {
  private readonly logger = new Logger(WorkflowTriggerService.name);

  constructor(
    @InjectRepository(Workflow)
    private readonly workflowRepository: Repository<Workflow>,
    @InjectRepository(WorkflowExecution)
    private readonly workflowExecutionRepository: Repository<WorkflowExecution>,
    private readonly workflowQueueService: WorkflowQueueService,
  ) {}

  /**
   * Trigger workflow từ webhook
   * @param workflowId ID của workflow
   * @param triggerType Loại trigger (webhook.facebook, webhook.zalo, etc.)
   * @param webhookData Dữ liệu webhook
   * @param userId ID của user (optional, sẽ lấy từ workflow nếu không có)
   * @returns Promise với execution ID
   */
  async triggerWorkflow(
    workflowId: string,
    triggerType: string,
    webhookData: any,
    userId?: number,
  ): Promise<{ executionId: string; jobId?: string }> {
    this.logger.log(`Triggering workflow ${workflowId} with trigger type: ${triggerType}`);

    try {
      // 1. Validate workflow exists and is active
      const workflow = await this.validateWorkflow(workflowId, userId);

      // 2. Create WorkflowExecution record
      const execution = await this.createWorkflowExecution(
        workflow,
        triggerType,
        webhookData,
      );

      // 3. Create job payload with webhook data
      const jobData: WorkflowExecutionJobData = {
        executionId: execution.id,
        workflowId: workflow.id,
        userId: workflow.userId || workflow.employeeId || 0,
        triggerData: webhookData,
        triggerType: this.mapTriggerType(triggerType),
        metadata: {
          source: this.extractSourceFromTriggerType(triggerType),
          webhookId: webhookData?.id || webhookData?.event_id,
          priority: this.determinePriority(triggerType),
        },
        options: {
          enableSSE: true, // Enable real-time updates
          timeout: 300000, // 5 minutes timeout
        },
      };

      // 4. Push to Redis queue
      const jobId = await this.workflowQueueService.addWorkflowExecutionJob(jobData);

      this.logger.log(
        `Workflow ${workflowId} triggered successfully. Execution: ${execution.id}, Job: ${jobId}`,
      );

      return {
        executionId: execution.id,
        jobId,
      };
    } catch (error) {
      this.logger.error(
        `Failed to trigger workflow ${workflowId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Trigger workflow với priority cao (cho webhook quan trọng)
   * @param workflowId ID của workflow
   * @param triggerType Loại trigger
   * @param webhookData Dữ liệu webhook
   * @param userId ID của user
   * @returns Promise với execution ID
   */
  async triggerHighPriorityWorkflow(
    workflowId: string,
    triggerType: string,
    webhookData: any,
    userId?: number,
  ): Promise<{ executionId: string; jobId?: string }> {
    this.logger.log(`Triggering high priority workflow ${workflowId}`);

    const workflow = await this.validateWorkflow(workflowId, userId);
    const execution = await this.createWorkflowExecution(workflow, triggerType, webhookData);

    const jobData: WorkflowExecutionJobData = {
      executionId: execution.id,
      workflowId: workflow.id,
      userId: workflow.userId || workflow.employeeId || 0,
      triggerData: webhookData,
      triggerType: this.mapTriggerType(triggerType),
      metadata: {
        source: this.extractSourceFromTriggerType(triggerType),
        webhookId: webhookData?.id || webhookData?.event_id,
        priority: 10, // Highest priority
      },
      options: {
        enableSSE: true,
        timeout: 300000,
      },
    };

    const jobId = await this.workflowQueueService.addHighPriorityWorkflowExecutionJob(jobData);

    return {
      executionId: execution.id,
      jobId,
    };
  }

  /**
   * Trigger workflow với delay
   * @param workflowId ID của workflow
   * @param triggerType Loại trigger
   * @param webhookData Dữ liệu webhook
   * @param delayMs Thời gian delay (milliseconds)
   * @param userId ID của user
   * @returns Promise với execution ID
   */
  async triggerDelayedWorkflow(
    workflowId: string,
    triggerType: string,
    webhookData: any,
    delayMs: number,
    userId?: number,
  ): Promise<{ executionId: string; jobId?: string }> {
    this.logger.log(`Triggering delayed workflow ${workflowId} with delay: ${delayMs}ms`);

    const workflow = await this.validateWorkflow(workflowId, userId);
    const execution = await this.createWorkflowExecution(workflow, triggerType, webhookData);

    const jobData: WorkflowExecutionJobData = {
      executionId: execution.id,
      workflowId: workflow.id,
      userId: workflow.userId || workflow.employeeId || 0,
      triggerData: webhookData,
      triggerType: this.mapTriggerType(triggerType),
      metadata: {
        source: this.extractSourceFromTriggerType(triggerType),
        webhookId: webhookData?.id || webhookData?.event_id,
        priority: 5, // Medium priority for delayed workflows
      },
      options: {
        enableSSE: true,
        timeout: 300000,
      },
    };

    const jobId = await this.workflowQueueService.addDelayedWorkflowExecutionJob(jobData, delayMs);

    return {
      executionId: execution.id,
      jobId,
    };
  }

  /**
   * Validate workflow exists và is active
   * @private
   */
  private async validateWorkflow(workflowId: string, userId?: number): Promise<Workflow> {
    const queryBuilder = this.workflowRepository.createQueryBuilder('workflow')
      .where('workflow.id = :workflowId', { workflowId });

    // Nếu có userId, filter theo user
    if (userId) {
      queryBuilder.andWhere(
        '(workflow.userId = :userId OR workflow.employeeId = :userId)',
        { userId },
      );
    }

    const workflow = await queryBuilder.getOne();

    if (!workflow) {
      throw new NotFoundException(`Workflow ${workflowId} not found`);
    }

    if (!workflow.isActive) {
      throw new AppException(
        WORKFLOW_ERROR_CODES.WORKFLOW_INACTIVE,
        `Workflow ${workflowId} is not active`,
      );
    }

    return workflow;
  }

  /**
   * Tạo WorkflowExecution record
   * @private
   */
  private async createWorkflowExecution(
    workflow: Workflow,
    triggerType: string,
    webhookData: any,
  ): Promise<WorkflowExecution> {
    const execution = this.workflowExecutionRepository.create({
      workflowId: workflow.id,
      status: ExecutionStatus.QUEUED,
      triggerEvent: {
        type: triggerType,
        data: webhookData,
        timestamp: Date.now(),
      },
      startedAt: Date.now(),
    });

    return await this.workflowExecutionRepository.save(execution);
  }

  /**
   * Map trigger type từ webhook type sang queue type
   * @private
   */
  private mapTriggerType(triggerType: string): 'manual' | 'webhook' | 'schedule' {
    if (triggerType.startsWith('webhook.')) {
      return 'webhook';
    }
    if (triggerType === 'manual') {
      return 'manual';
    }
    if (triggerType.startsWith('schedule.')) {
      return 'schedule';
    }
    return 'webhook'; // Default to webhook
  }

  /**
   * Extract source từ trigger type
   * @private
   */
  private extractSourceFromTriggerType(triggerType: string): string {
    if (triggerType.includes('.')) {
      return triggerType.split('.')[1] || 'unknown';
    }
    return triggerType;
  }

  /**
   * Determine priority dựa trên trigger type
   * @private
   */
  private determinePriority(triggerType: string): number {
    // High priority cho webhook từ payment systems
    if (triggerType.includes('payment') || triggerType.includes('order')) {
      return 9;
    }
    // Medium priority cho social media webhooks
    if (triggerType.includes('facebook') || triggerType.includes('zalo')) {
      return 7;
    }
    // Normal priority cho các webhook khác
    return 5;
  }
}
