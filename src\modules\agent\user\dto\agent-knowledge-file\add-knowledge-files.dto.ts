import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc thêm knowledge files vào agent
 */
export class AddKnowledgeFilesDto {
  /**
   * <PERSON>h sách ID của knowledge files cần thêm
   */
  @ApiProperty({
    description: 'Danh sách ID của knowledge files cần thêm',
    example: ['uuid-knowledge-file-1', 'uuid-knowledge-file-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  fileIds: string[];
}
