### Test SMS Campaign Sorting với sortBy và sortDirection
### API: GET /marketing/sms-campaigns

### Test 1: Sắ<PERSON> xếp theo createdAt DESC (mặc định)
GET http://localhost:3004/api/v1/marketing/sms-campaigns
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test 2: Sắp xếp theo createdAt ASC
GET http://localhost:3004/api/v1/marketing/sms-campaigns?sortBy=createdAt&sortDirection=ASC
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test 3: Sắp xếp theo name ASC
GET http://localhost:3004/api/v1/marketing/sms-campaigns?sortBy=name&sortDirection=ASC
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test 4: Sắp xếp theo name DESC
GET http://localhost:3004/api/v1/marketing/sms-campaigns?sortBy=name&sortDirection=DESC
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test 5: Sắp xếp theo status ASC
GET http://localhost:3004/api/v1/marketing/sms-campaigns?sortBy=status&sortDirection=ASC
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test 6: Sắp xếp theo totalRecipients DESC
GET http://localhost:3004/api/v1/marketing/sms-campaigns?sortBy=totalRecipients&sortDirection=DESC
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test 7: Sắp xếp theo scheduledAt ASC
GET http://localhost:3004/api/v1/marketing/sms-campaigns?sortBy=scheduledAt&sortDirection=ASC
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test 8: Sắp xếp theo updatedAt DESC
GET http://localhost:3004/api/v1/marketing/sms-campaigns?sortBy=updatedAt&sortDirection=DESC
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test 9: Sắp xếp với invalid sortBy (should fallback to createdAt)
GET http://localhost:3004/api/v1/marketing/sms-campaigns?sortBy=invalidField&sortDirection=ASC
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test 10: Kết hợp sorting với filter và search
GET http://localhost:3004/api/v1/marketing/sms-campaigns?sortBy=name&sortDirection=ASC&status=SENT&search=khuyến mãi&page=1&limit=10
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test 11: Pagination với sorting
GET http://localhost:3004/api/v1/marketing/sms-campaigns?sortBy=createdAt&sortDirection=DESC&page=2&limit=5
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Expected Response Format:
### {
###   "code": 200,
###   "message": "Danh sách chiến dịch SMS",
###   "result": {
###     "items": [
###       {
###         "id": 123,
###         "name": "Chiến dịch SMS khuyến mãi Black Friday",
###         "description": "Chiến dịch SMS marketing cho sự kiện Black Friday",
###         "campaignType": "ADS",
###         "status": "SENT",
###         "totalRecipients": 150,
###         "sentCount": 145,
###         "failedCount": 5,
###         "successRate": 96.7,
###         "scheduledAt": 1703980800,
###         "startedAt": 1703980800,
###         "completedAt": 1703981200,
###         "createdAt": 1703980000,
###         "updatedAt": 1703981200
###       }
###     ],
###     "meta": {
###       "totalItems": 25,
###       "itemCount": 10,
###       "itemsPerPage": 10,
###       "totalPages": 3,
###       "currentPage": 1
###     }
###   }
### }

### Lưu ý: 
### - Các trường sortBy hợp lệ: createdAt, updatedAt, name, status, scheduledAt, totalRecipients
### - sortDirection: ASC hoặc DESC (mặc định DESC)
### - Nếu sortBy không hợp lệ, sẽ fallback về createdAt
### - Có thể kết hợp với search, status filter, và pagination
