import { AppException, ErrorCode } from '@common/exceptions';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { Injectable, Logger } from '@nestjs/common';
import { ModelType } from './../../user/dto/user-models/model-query.dto';
import {
  AdminModelQueryDto,
  AdminModelsResponseDto
} from '../dto/admin-models';
import { AdminModelsMapper } from '../mappers/admin-models.mapper';
import { ModelsRepository } from '../../repositories/models.repository';

/**
 * Service xử lý logic nghiệp vụ cho Admin Models
 */
@Injectable()
export class AdminModelsService {
  private readonly logger = new Logger(AdminModelsService.name);

  constructor(
    private readonly modelsRepository: ModelsRepository,
  ) { }

  /**
   * Lấy danh sách models cho admin với pagination
   * @param employeeId ID của employee
   * @param queryDto Query parameters
   * @returns Kết quả phân trang với admin models
   */
  async getAdminModels(
    employeeId: number,
    queryDto: AdminModelQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<AdminModelsResponseDto>>> {
    try {
      // Lấy dữ liệu từ repository
      const result = await this.modelsRepository.findAll(employeeId, {
        ...queryDto,
        type: ModelType.SYSTEM
      });

      // Convert entities sang DTOs
      const items = AdminModelsMapper.toResponseDtoList(result.items);

      // Thêm hasItems field
      const hasItems = result.meta.totalItems > 0;

      return ApiResponseDto.paginated({
        items,
        meta: {
          ...result.meta,
          hasItems,
        }
      }, 'Lấy danh sách models cho admin thành công');

    } catch (error) {
      this.logger.error(`Failed to get admin models for employee ${employeeId}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể lấy danh sách models theo keys'
      );
    }
  }
}
