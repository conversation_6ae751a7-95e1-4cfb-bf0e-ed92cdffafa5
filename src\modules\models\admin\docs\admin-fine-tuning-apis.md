# Admin Fine-Tuning APIs Documentation

## Tổng quan

Admin Fine-Tuning APIs cung cấp khả năng fine-tuning models cho admin với các đặc điểm:
- **Không tính phí**: <PERSON><PERSON> không bị trừ R-Points
- **System Models**: Models được tạo thuộc về system (userId = null)
- **Token Tracking**: Chỉ lưu lại thông tin token usage để tham khảo
- **System Integration**: Sử dụng system integration keys

## 🔗 **API Endpoints**

### **1. Validate Dataset API**
```
POST /admin/fine-tuning/validate
```

#### **Request Body**
```typescript
{
  "datasetId": "550e8400-e29b-41d4-a716-************",
  "modelId": "123e4567-e89b-12d3-a456-************", 
  "provider": "OPENAI"
}
```

#### **Response**
```typescript
{
  "success": true,
  "message": "Dataset validation thành công",
  "data": {
    "status": "success",
    "estimatedTokens": 15000,
    "estimatedCostUSD": 45.50,
    "datasetInfo": {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Admin Customer Service Dataset",
      "totalExamples": 2000,
      "provider": "OPENAI"
    },
    "baseModelInfo": {
      "id": "123e4567-e89b-12d3-a456-************",
      "modelId": "gpt-4o-mini-2024-07-18",
      "provider": "OPENAI"
    },
    "hyperparameters": {
      "epochs": 3,
      "batchSize": "auto",
      "learningRateMultiplier": "auto"
    },
    "estimatedDurationMinutes": 60,
    "validationDetails": {
      "trainFileValid": true,
      "validFileValid": true,
      "formatValid": true,
      "tokenCountValid": true,
      "warnings": []
    }
  }
}
```

### **2. Execute Fine-Tuning API**
```
POST /admin/fine-tuning/execute
```

#### **Request Body**
```typescript
{
  "datasetId": "550e8400-e29b-41d4-a716-************",
  "modelId": "123e4567-e89b-12d3-a456-************",
  "provider": "OPENAI",
  "suffix": "admin-model-v1",
  "hyperparameters": {
    "epochs": 3,
    "batchSize": "auto",
    "learningRate": 0.0001
  }
}
```

#### **Response**
```typescript
{
  "success": true,
  "message": "Fine-tuning job đã được tạo thành công",
  "data": {
    "jobId": "ft-abc123def456",
    "modelId": "789e0123-e89b-12d3-a456-************",
    "status": "validating_files",
    "tokensUsed": 15000,
    "estimatedCostUSD": 45.50,
    "datasetInfo": {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Admin Customer Service Dataset",
      "totalExamples": 2000
    },
    "baseModelInfo": {
      "id": "123e4567-e89b-12d3-a456-************",
      "modelId": "gpt-4o-mini-2024-07-18",
      "provider": "OPENAI"
    },
    "hyperparameters": {
      "epochs": 3,
      "batchSize": "auto",
      "learningRateMultiplier": "auto"
    },
    "estimatedDurationMinutes": 60,
    "providerJobInfo": {
      "id": "ft-abc123def456",
      "object": "fine_tuning.job",
      "model": "gpt-4o-mini-2024-07-18",
      "created_at": **********,
      "status": "validating_files"
    },
    "message": "Fine-tuning job đã được tạo thành công. Job sẽ được monitor tự động."
  }
}
```

## 🔄 **Workflow Comparison**

### **User vs Admin Fine-Tuning**

| Aspect | User API | Admin API |
|--------|----------|-----------|
| **Authentication** | JwtUserGuard | JwtEmployeeGuard |
| **Cost** | Trừ R-Points | Không tính phí |
| **Model Ownership** | userId = user.id | userId = null (system) |
| **Integration Keys** | User keys hoặc system keys | System keys only |
| **Token Usage** | Tính phí theo token | Chỉ lưu để tham khảo |
| **Monitoring** | Redis queue với userId | Redis queue với employeeId |

### **Database Records**

#### **Models Table**
```sql
-- User Model
INSERT INTO models (id, model_id, user_id, is_fine_tune, active)
VALUES ('uuid', 'ft-user123', 123, true, false);

-- Admin Model  
INSERT INTO models (id, model_id, user_id, is_fine_tune, active)
VALUES ('uuid', 'ft-admin123', NULL, true, false);
```

#### **Model Integration Mapping**
```sql
-- Cả user và admin đều sử dụng system integration
INSERT INTO model_integration (model_id, integration_id)
VALUES ('model-uuid', 'system-integration-uuid');
```

## 🔧 **Implementation Details**

### **Service Layer**
```typescript
@Injectable()
export class AdminFineTuningService {
  // Không có logic trừ points
  // Sử dụng employeeId thay vì userId
  // Models được tạo với userId = null
}
```

### **Controller Layer**
```typescript
@Controller('admin/fine-tuning')
@UseGuards(JwtEmployeeGuard)
export class AdminFineTuningController {
  // Sử dụng req.user.id (employeeId)
  // Swagger documentation cho admin
}
```

### **Queue Integration**
```typescript
// Admin monitoring job payload
{
  historyId: "model-uuid",
  providerJobId: "ft-abc123",
  provider: "OPENAI", 
  employeeId: 456  // Thay vì userId
}
```

## 🚀 **Usage Examples**

### **1. Admin Validate Dataset**
```bash
curl -X POST "https://api.example.com/admin/fine-tuning/validate" \
  -H "Authorization: Bearer admin-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "datasetId": "550e8400-e29b-41d4-a716-************",
    "modelId": "123e4567-e89b-12d3-a456-************",
    "provider": "OPENAI"
  }'
```

### **2. Admin Execute Fine-Tuning**
```bash
curl -X POST "https://api.example.com/admin/fine-tuning/execute" \
  -H "Authorization: Bearer admin-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "datasetId": "550e8400-e29b-41d4-a716-************",
    "modelId": "123e4567-e89b-12d3-a456-************",
    "provider": "OPENAI",
    "suffix": "admin-customer-service-v1",
    "hyperparameters": {
      "epochs": 5,
      "batchSize": 16,
      "learningRate": 0.0001
    }
  }'
```

## 📊 **Monitoring & Tracking**

### **Token Usage Tracking**
- Admin APIs lưu token usage vào `model_detail.metadata`
- Không trừ R-Points từ admin account
- Cost estimate chỉ để tham khảo và reporting

### **Job Monitoring**
- Sử dụng cùng Redis queue system như user
- Worker xử lý với `employeeId` thay vì `userId`
- Model activation tự động khi job hoàn thành

### **Audit Trail**
- Log tất cả admin fine-tuning activities
- Track employee ID và timestamp
- Monitor system resource usage

## 🔒 **Security Considerations**

### **Access Control**
- Chỉ admin employees có thể access
- Validate employee permissions
- Audit log cho tất cả operations

### **Resource Management**
- Monitor system integration key usage
- Rate limiting cho admin operations
- Resource quota management

## 📈 **Benefits**

1. **Cost Efficiency**: Admin không bị tính phí
2. **System Models**: Tạo models cho toàn hệ thống
3. **Centralized Management**: Admin control over fine-tuning
4. **Resource Tracking**: Monitor token usage và costs
5. **Scalability**: Sử dụng system integration keys

Admin Fine-Tuning APIs cung cấp powerful tools cho admin để manage và tạo system models một cách hiệu quả! 🎯
