import { AppException } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { Validator } from 'jsonschema';
import { TOOLS_ERROR_CODES } from '../exceptions';

// Define interfaces for jsonschema types
interface Schema {
  id?: string;
  type?: string | string[];
  properties?: Record<string, Schema>;
  required?: string[];
  additionalProperties?: boolean | Schema;
  items?: Schema;
  enum?: any[];
  minimum?: number;
  maximum?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  description?: string;
  default?: any;
  $ref?: string;
}

interface ValidationError {
  property: string;
  message: string;
  schema: Schema;
  instance: any;
  name: string;
  argument: any;
  stack: string;
}

interface ValidatorResult {
  valid: boolean;
  errors: ValidationError[];
}

/**
 * Service để validate tham số của tool sử dụng JSON Schema
 */
@Injectable()
export class ToolParameterValidatorService {
  private readonly logger = new Logger(ToolParameterValidatorService.name);
  private readonly validator = new Validator();

  constructor() {
    // Đăng ký các schema cơ bản
    this.registerBaseSchemas();
  }

  /**
   * Đăng ký các schema cơ bản cho validation
   */
  private registerBaseSchemas(): void {
    // Schema cho property cơ bản
    const propertySchema: Schema = {
      id: '/Property',
      type: 'object',
      properties: {
        type: {
          type: 'string',
          enum: ['string', 'number', 'integer', 'boolean', 'array', 'object']
        },
        description: {
          type: 'string'
        },
        default: {},
        enum: {
          type: 'array'
        },
        minimum: {
          type: 'number'
        },
        maximum: {
          type: 'number'
        },
        minLength: {
          type: 'integer',
          minimum: 0
        },
        maxLength: {
          type: 'integer',
          minimum: 0
        },
        pattern: {
          type: 'string'
        },
        items: {
          $ref: '/Property'
        },
        properties: {
          type: 'object',
          additionalProperties: {
            $ref: '/Property'
          }
        },
        required: {
          type: 'array',
          items: {
            type: 'string'
          }
        },
        additionalProperties: {
          type: 'boolean'
        }
      },
      required: ['type'],
      additionalProperties: false
    };

    this.validator.addSchema(propertySchema, '/Property');
  }

  /**
   * Schema chính cho tool parameters theo chuẩn JSON Schema
   */
  private getToolParametersSchema(): Schema {
    return {
      id: '/ToolParameters',
      type: 'object',
      properties: {
        type: {
          type: 'string',
          enum: ['object']
        },
        properties: {
          type: 'object',
          additionalProperties: {
            $ref: '/Property'
          }
        },
        required: {
          type: 'array',
          items: {
            type: 'string'
          }
        },
        additionalProperties: {
          type: 'boolean'
        }
      },
      required: ['type', 'properties'],
      additionalProperties: false
    };
  }

  /**
   * Validate tham số của tool
   * @param parameters Đối tượng parameters cần validate
   * @returns true nếu hợp lệ
   * @throws AppException nếu không hợp lệ
   */
  validateToolParameters(parameters: Record<string, unknown>): boolean {
    try {
      const schema = this.getToolParametersSchema();
      const result: ValidatorResult = this.validator.validate(parameters, schema);

      if (!result.valid) {
        const errors = result.errors.map(error =>
          `${error.property}: ${error.message}`
        ).join('; ');

        this.logger.error(`Tool parameters validation failed: ${errors}`);
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID,
          `Tham số tool không hợp lệ: ${errors}`
        );
      }

      // Validate bổ sung cho các trường hợp đặc biệt
      this.validateAdditionalRules(parameters);

      return true;
    } catch (error) {
      this.logger.error(`Error validating tool parameters: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID,
        `Lỗi khi validate tham số tool: ${error.message}`
      );
    }
  }

  /**
   * Validate các quy tắc bổ sung
   * @param parameters Đối tượng parameters
   */
  private validateAdditionalRules(parameters: Record<string, unknown>): void {
    // Kiểm tra type phải là 'object'
    if (parameters.type !== 'object') {
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID,
        'Tham số tool phải có type là "object"'
      );
    }

    // Kiểm tra properties phải tồn tại và không rỗng
    const properties = parameters.properties as Record<string, unknown>;
    if (!properties || Object.keys(properties).length === 0) {
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID,
        'Tool phải có ít nhất một tham số trong properties'
      );
    }

    // Validate từng property
    // Object.entries(properties).forEach(([key, property]) => {
    //   this.validateProperty(key, property as Record<string, unknown>);
    // });

    // Kiểm tra required array nếu có
    const required = parameters.required as string[];
    if (required && Array.isArray(required)) {
      required.forEach(requiredField => {
        if (!properties[requiredField]) {
          throw new AppException(
            TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID,
            `Trường required "${requiredField}" không tồn tại trong properties`
          );
        }
      });
    }
  }

  /**
   * Validate một property cụ thể
   * @param key Tên property
   * @param property Đối tượng property
   */
  private validateProperty(key: string, property: Record<string, unknown>): void {
    // Kiểm tra tên property
    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(key)) {
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID,
        `Tên property "${key}" không hợp lệ. Phải bắt đầu bằng chữ cái và chỉ chứa chữ cái, số, dấu gạch dưới`
      );
    }

    // Kiểm tra type
    const validTypes = ['string', 'number', 'integer', 'boolean', 'array', 'object'];
    if (!validTypes.includes(property.type as string)) {
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID,
        `Property "${key}" có type không hợp lệ: ${property.type}`
      );
    }

    // Validate description
    if (property.description && typeof property.description !== 'string') {
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID,
        `Property "${key}" có description không hợp lệ`
      );
    }

    // Validate cho array type
    if (property.type === 'array' && property.items) {
      this.validateProperty(`${key}.items`, property.items as Record<string, unknown>);
    }

    // Validate cho object type
    if (property.type === 'object' && property.properties) {
      const nestedProperties = property.properties as Record<string, unknown>;
      Object.entries(nestedProperties).forEach(([nestedKey, nestedProperty]) => {
        this.validateProperty(`${key}.${nestedKey}`, nestedProperty as Record<string, unknown>);
      });
    }
  }

  /**
   * Tạo một schema mẫu cho tool parameters
   * @returns Schema mẫu
   */
  createSampleSchema(): Record<string, unknown> {
    return {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Câu truy vấn tìm kiếm',
          minLength: 1,
          maxLength: 1000
        },
        limit: {
          type: 'integer',
          description: 'Số lượng kết quả tối đa',
          minimum: 1,
          maximum: 100,
          default: 10
        },
        includeMetadata: {
          type: 'boolean',
          description: 'Có bao gồm metadata trong kết quả',
          default: false
        }
      },
      required: ['query'],
      additionalProperties: false
    };
  }
}
