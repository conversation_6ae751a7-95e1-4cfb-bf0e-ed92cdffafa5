import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO cho thông tin file đã tạo và URL tải lên (Admin)
 */
export class AdminFileCreationInfoDto {
  @ApiProperty({
    description: 'ID của file tri thức đã tạo',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Tên file',
    example: 'Tài liệu hướng dẫn của sơn sói.pdf',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'URL ký sẵn để tải file lên',
    example: 'https://storage.example.com/presigned-url?token=abc123',
  })
  @Expose()
  uploadUrl: string;

  @ApiProperty({
    description: 'Khóa lưu trữ của file trên hệ thống',
    example: 'knowledge_files/123/document-123456-abcdef.pdf',
  })
  @Expose()
  storageKey: string;
}

/**
 * DTO cho kết quả tạo nhiều file tri thức (Admin)
 */
export class AdminBatchCreateFilesResponseDto {
  @ApiProperty({
    description: 'Danh sách thông tin các file đã tạo',
    type: [AdminFileCreationInfoDto],
    example: [
      {
        id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
        name: 'Tài liệu hướng dẫn của sơn sói.pdf',
        uploadUrl: 'https://storage.example.com/presigned-url?token=abc123',
        storageKey: 'knowledge_files/123/document-123456-abcdef.pdf'
      }
    ]
  })
  @Expose()
  @Type(() => AdminFileCreationInfoDto)
  files: AdminFileCreationInfoDto[];
}
