import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { WorkflowExecution } from '../entities';

/**
 * Repository cho WorkflowExecution entity
 * <PERSON><PERSON> lý các thao tác database liên quan đến workflow executions
 */
@Injectable()
export class WorkflowExecutionRepository extends Repository<WorkflowExecution> {
  constructor(private dataSource: DataSource) {
    super(WorkflowExecution, dataSource.createEntityManager());
  }

  /**
   * Tạo base query cho WorkflowExecution
   */
  private createBaseQuery() {
    return this.createQueryBuilder('workflow_execution');
  }

  /**
   * <PERSON><PERSON>y tất cả executions của một workflow
   * @param workflowId - ID của workflow
   * @returns Promise<WorkflowExecution[]>
   */
  async findByWorkflowId(workflowId: string): Promise<WorkflowExecution[]> {
    return this.createBaseQuery()
      .where('workflow_execution.workflowId = :workflowId', { workflowId })
      .orderBy('workflow_execution.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Lấy execution theo ID và workflow ID
   * @param id - ID của execution
   * @param workflowId - ID của workflow
   * @returns Promise<WorkflowExecution | null>
   */
  async findByIdAndWorkflowId(id: string, workflowId: string): Promise<WorkflowExecution | null> {
    return this.createBaseQuery()
      .where('workflow_execution.id = :id', { id })
      .andWhere('workflow_execution.workflowId = :workflowId', { workflowId })
      .getOne();
  }

  /**
   * Lấy executions theo status
   * @param status - Status của execution
   * @returns Promise<WorkflowExecution[]>
   */
  async findByStatus(status: string): Promise<WorkflowExecution[]> {
    return this.createBaseQuery()
      .where('workflow_execution.status = :status', { status })
      .orderBy('workflow_execution.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Lấy executions theo user ID
   * @param userId - ID của user
   * @returns Promise<WorkflowExecution[]>
   */
  async findByUserId(userId: number): Promise<WorkflowExecution[]> {
    return this.createBaseQuery()
      .where('workflow_execution.userId = :userId', { userId })
      .orderBy('workflow_execution.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Đếm số lượng executions theo status
   * @param status - Status của execution
   * @returns Promise<number>
   */
  async countByStatus(status: string): Promise<number> {
    return this.createBaseQuery()
      .where('workflow_execution.status = :status', { status })
      .getCount();
  }

  /**
   * Lấy executions với pagination
   * @param params - Query parameters
   * @returns Promise với paginated result
   */
  async findWithPagination(params: {
    page: number;
    limit: number;
    workflowId?: string;
    userId?: number;
    status?: string;
  }): Promise<{
    data: WorkflowExecution[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const query = this.createBaseQuery();

    if (params.workflowId) {
      query.andWhere('workflow_execution.workflowId = :workflowId', { workflowId: params.workflowId });
    }

    if (params.userId) {
      query.andWhere('workflow_execution.userId = :userId', { userId: params.userId });
    }

    if (params.status) {
      query.andWhere('workflow_execution.status = :status', { status: params.status });
    }

    const [data, total] = await query
      .skip((params.page - 1) * params.limit)
      .take(params.limit)
      .orderBy('workflow_execution.createdAt', 'DESC')
      .getManyAndCount();

    return {
      data,
      total,
      page: params.page,
      limit: params.limit,
      totalPages: Math.ceil(total / params.limit)
    };
  }

  /**
   * Cập nhật status của execution
   * @param id - ID của execution
   * @param status - Status mới
   * @param result - Kết quả execution (optional)
   * @returns Promise với update result
   */
  async updateStatus(id: string, status: string, result?: any): Promise<any> {
    const updateData: any = { status };
    if (result !== undefined) {
      updateData.result = result;
    }
    if (status === 'completed' || status === 'failed') {
      updateData.endedAt = new Date();
    }

    return this.update(id, updateData);
  }

  /**
   * Lấy statistics về executions
   * @param timeRange - Time range for statistics
   * @returns Promise với statistics
   */
  async getStatistics(timeRange?: string): Promise<any> {
    const query = this.createBaseQuery();

    if (timeRange) {
      // Add time range filter based on timeRange parameter
      // This is a placeholder implementation
    }

    const [
      total,
      running,
      completed,
      failed
    ] = await Promise.all([
      query.getCount(),
      this.createBaseQuery().where('workflow_execution.status = :status', { status: 'running' }).getCount(),
      this.createBaseQuery().where('workflow_execution.status = :status', { status: 'completed' }).getCount(),
      this.createBaseQuery().where('workflow_execution.status = :status', { status: 'failed' }).getCount()
    ]);

    return {
      total,
      running,
      completed,
      failed,
      pending: total - running - completed - failed
    };
  }

  /**
   * Xóa executions cũ
   * @param olderThanDays - Xóa executions cũ hơn số ngày này
   * @returns Promise với delete result
   */
  async deleteOldExecutions(olderThanDays: number): Promise<any> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    return this.createBaseQuery()
      .delete()
      .where('workflow_execution.createdAt < :cutoffDate', { cutoffDate })
      .execute();
  }
}
