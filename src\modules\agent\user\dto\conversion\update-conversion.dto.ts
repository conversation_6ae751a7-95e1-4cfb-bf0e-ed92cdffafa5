import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  ValidateNested,
  ArrayMaxSize
} from 'class-validator';
import { ConvertConfigItemDto } from './convert-config-item.dto';
import { UniqueFieldNames } from './validators/unique-field-names.validator';
import { HasRequiredFields } from './validators/required-fields.validator';

/**
 * DTO cho việc cập nhật conversion config của agent
 */
export class UpdateConversionDto {
  /**
   * Danh sách cấu hình chuyển đổi
   */
  @ApiProperty({
    description: 'Danh sách cấu hình chuyển đổi (tối đa 20 fields). Email và phone là bắt buộc, không thể xóa, chỉ có thể cập nhật description.',
    type: [ConvertConfigItemDto],
    example: [
      {
        name: 'customer_email',
        type: 'string',
        description: '<PERSON>ail của khách hàng (chỉ có thể sửa description)',
        required: true,
        deletable: false
      },
      {
        name: 'customer_phone',
        type: 'string',
        description: 'Số điện thoại của khách hàng (chỉ có thể sửa description)',
        required: true,
        deletable: false
      },
      {
        name: 'customer_name',
        type: 'string',
        description: 'Tên đầy đủ của khách hàng',
        required: true,
        deletable: true
      },
      {
        name: 'order_amount',
        type: 'number',
        description: 'Số tiền đơn hàng',
        required: false,
        deletable: true
      },
      {
        name: 'product_categories',
        type: 'array',
        description: 'Danh mục sản phẩm quan tâm',
        required: false,
        items: {
          type: 'string',
          description: 'Tên danh mục sản phẩm'
        },
        deletable: true
      },
      {
        name: 'customer_address',
        type: 'object',
        description: 'Địa chỉ khách hàng',
        required: false,
        properties: {
          'street': {
            name: 'street',
            type: 'string',
            description: 'Tên đường',
            required: true,
            deletable: true
          },
          'city': {
            name: 'city',
            type: 'string',
            description: 'Thành phố',
            required: true,
            deletable: true
          }
        },
        deletable: true
      },
      {
        name: 'product_categories',
        type: 'array',
        description: 'Danh mục sản phẩm quan tâm',
        required: false,
        items: {
          type: 'string',
          description: 'Tên danh mục'
        },
        deletable: true
      }
    ],
    maxItems: 20,
  })
  @IsArray({ message: 'Conversion config phải là mảng' })
  @ArrayMaxSize(20, { message: 'Không được vượt quá 20 fields' })
  @UniqueFieldNames({ message: 'Tên field không được trùng lặp' })
  @HasRequiredFields({ message: 'Phải có đầy đủ field customer_email và customer_phone' })
  @ValidateNested({ each: true })
  @Type(() => ConvertConfigItemDto)
  convertConfig: ConvertConfigItemDto[];
}
