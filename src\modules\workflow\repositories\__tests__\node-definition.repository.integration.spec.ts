import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { NodeDefinitionRepository } from '../node-definition.repository';
import { NodeDefinition, NodeCategory } from '../../entities';
import { ALL_NODE_TYPES, NODE_TYPE_CATEGORIES } from '../../test-data/node-types.data';

describe('NodeDefinitionRepository Integration Tests', () => {
  let repository: NodeDefinitionRepository;
  let dataSource: DataSource;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [NodeDefinition],
          synchronize: true,
          logging: false,
        }),
        TypeOrmModule.forFeature([NodeDefinition]),
      ],
      providers: [NodeDefinitionRepository],
    }).compile();

    repository = module.get<NodeDefinitionRepository>(NodeDefinitionRepository);
    dataSource = module.get<DataSource>(DataSource);
  });

  afterAll(async () => {
    await dataSource.destroy();
    await module.close();
  });

  beforeEach(async () => {
    // Clear database before each test
    await repository.clear();
  });

  describe('Basic CRUD Operations', () => {
    it('should create and find node definition', async () => {
      const nodeData = {
        type: 'system.webhook.trigger',
        name: 'Webhook Trigger',
        description: 'Triggers workflow execution when webhook is called',
        category: NodeCategory.SYSTEM,
        inputSchema: {
          type: 'object',
          properties: {
            url: { type: 'string' },
            method: { type: 'string', enum: ['GET', 'POST', 'PUT', 'DELETE'] }
          }
        },
        outputSchema: {
          type: 'object',
          properties: {
            data: { type: 'object' },
            headers: { type: 'object' }
          }
        },
        version: '1.0.0',
        documentation: '# Webhook Trigger Node\n\nThis node triggers workflow execution.',
        examples: { basic: { url: '/webhook/test', method: 'POST' } },
        tags: ['trigger', 'webhook', 'http'],
        iconUrl: '/icons/webhook.svg',
        colorScheme: '#4CAF50',
      };

      const created = repository.create(nodeData);
      const saved = await repository.save(created);

      expect(saved.type).toBe(nodeData.type);
      expect(saved.name).toBe(nodeData.name);
      expect(saved.category).toBe(nodeData.category);
      expect(saved.tags).toEqual(nodeData.tags);
      expect(saved.isDeprecated).toBe(false);

      const found = await repository.findByType(nodeData.type);
      expect(found).toBeTruthy();
      expect(found!.type).toBe(nodeData.type);
    });

    it('should update node definition', async () => {
      const nodeData = {
        type: 'system.manual.trigger',
        name: 'Manual Trigger',
        description: 'Manually triggers workflow execution',
        category: NodeCategory.SYSTEM,
        inputSchema: { type: 'object' },
        outputSchema: { type: 'object' },
        version: '1.0.0',
      };

      const created = repository.create(nodeData);
      const saved = await repository.save(created);

      // Update
      saved.name = 'Updated Manual Trigger';
      saved.description = 'Updated description';
      saved.tags = ['trigger', 'manual', 'updated'];

      const updated = await repository.save(saved);

      expect(updated.name).toBe('Updated Manual Trigger');
      expect(updated.description).toBe('Updated description');
      expect(updated.tags).toEqual(['trigger', 'manual', 'updated']);
    });
  });

  describe('Advanced Search and Filtering', () => {
    beforeEach(async () => {
      // Create test data
      const testNodes = [
        {
          type: 'system.webhook.trigger',
          name: 'Webhook Trigger',
          description: 'Triggers workflow execution when webhook is called',
          category: NodeCategory.SYSTEM,
          inputSchema: { type: 'object' },
          outputSchema: { type: 'object' },
          version: '1.0.0',
          tags: ['trigger', 'webhook', 'http'],
          documentation: 'Webhook trigger documentation',
        },
        {
          type: 'google.sheets.getRows',
          name: 'Get Rows',
          description: 'Get rows from Google Sheets',
          category: NodeCategory.GOOGLE_SHEETS,
          inputSchema: { type: 'object' },
          outputSchema: { type: 'object' },
          version: '1.0.0',
          tags: ['google', 'sheets', 'read'],
          documentation: 'Google Sheets get rows documentation',
        },
        {
          type: 'facebook.pages.createPost',
          name: 'Create Post',
          description: 'Create a post on Facebook page',
          category: NodeCategory.FACEBOOK_PAGES,
          inputSchema: { type: 'object' },
          outputSchema: { type: 'object' },
          version: '1.0.0',
          tags: ['facebook', 'post', 'social'],
          isDeprecated: true,
          deprecationMessage: 'Use facebook.pages.createPost.v2 instead',
        },
      ];

      for (const nodeData of testNodes) {
        const node = repository.create(nodeData);
        await repository.save(node);
      }
    });

    it('should find nodes by category', async () => {
      const systemNodes = await repository.findByCategory(NodeCategory.SYSTEM);
      expect(systemNodes).toHaveLength(1);
      expect(systemNodes[0].type).toBe('system.webhook.trigger');

      const googleNodes = await repository.findByCategory(NodeCategory.GOOGLE_SHEETS);
      expect(googleNodes).toHaveLength(1);
      expect(googleNodes[0].type).toBe('google.sheets.getRows');
    });

    it('should find nodes by tags and category', async () => {
      const triggerNodes = await repository.findByTagsAndCategory(['trigger']);
      expect(triggerNodes).toHaveLength(1);
      expect(triggerNodes[0].type).toBe('system.webhook.trigger');

      const googleSheetsNodes = await repository.findByTagsAndCategory(
        ['google'], 
        NodeCategory.GOOGLE_SHEETS
      );
      expect(googleSheetsNodes).toHaveLength(1);
      expect(googleSheetsNodes[0].type).toBe('google.sheets.getRows');
    });

    it('should perform full-text search', async () => {
      // Note: SQLite doesn't support PostgreSQL's full-text search
      // This test would work with PostgreSQL
      const searchResults = await repository.fullTextSearch('webhook');
      // In real PostgreSQL environment, this would find the webhook trigger node
      expect(Array.isArray(searchResults)).toBe(true);
    });

    it('should get statistics', async () => {
      const stats = await repository.getStatistics();

      expect(stats.total).toBe(3);
      expect(stats.byCategory[NodeCategory.SYSTEM]).toBe(1);
      expect(stats.byCategory[NodeCategory.GOOGLE_SHEETS]).toBe(1);
      expect(stats.byCategory[NodeCategory.FACEBOOK_PAGES]).toBe(1);
      expect(stats.deprecated).toBe(1);
    });

    it('should find deprecated nodes', async () => {
      const deprecatedNodes = await repository.findDeprecated();
      expect(deprecatedNodes).toHaveLength(1);
      expect(deprecatedNodes[0].type).toBe('facebook.pages.createPost');
      expect(deprecatedNodes[0].isDeprecated).toBe(true);
    });

    it('should perform advanced search with multiple filters', async () => {
      const [results, count] = await repository.advancedSearch({
        category: NodeCategory.SYSTEM,
        tags: ['trigger'],
        isDeprecated: false,
        hasDocumentation: true,
        limit: 10,
        offset: 0,
      });

      expect(count).toBe(1);
      expect(results).toHaveLength(1);
      expect(results[0].type).toBe('system.webhook.trigger');
    });

    it('should filter by documentation and examples', async () => {
      const [withDocs] = await repository.advancedSearch({
        hasDocumentation: true,
      });

      expect(withDocs.length).toBeGreaterThan(0);
      withDocs.forEach(node => {
        expect(node.documentation).toBeTruthy();
      });

      const [withoutDocs] = await repository.advancedSearch({
        hasDocumentation: false,
      });

      withoutDocs.forEach(node => {
        expect(node.documentation).toBeFalsy();
      });
    });
  });

  describe('Performance Tests', () => {
    it('should handle large number of node definitions', async () => {
      // Create a subset of the 192 node types for testing
      const testNodeTypes = ALL_NODE_TYPES.slice(0, 50); // Test with 50 nodes

      const startTime = Date.now();

      // Bulk insert
      const nodes = testNodeTypes.map(type => {
        const category = NODE_TYPE_CATEGORIES[type];
        return repository.create({
          type,
          name: type.split('.').pop()?.replace(/([A-Z])/g, ' $1').trim() || type,
          description: `Description for ${type}`,
          category,
          inputSchema: { type: 'object', properties: {} },
          outputSchema: { type: 'object', properties: {} },
          version: '1.0.0',
          tags: type.split('.'),
        });
      });

      await repository.save(nodes);

      const insertTime = Date.now() - startTime;
      console.log(`Inserted ${testNodeTypes.length} nodes in ${insertTime}ms`);

      // Test search performance
      const searchStartTime = Date.now();
      const [searchResults] = await repository.advancedSearch({
        limit: 20,
      });
      const searchTime = Date.now() - searchStartTime;

      console.log(`Search completed in ${searchTime}ms, found ${searchResults.length} results`);

      expect(searchResults.length).toBeLessThanOrEqual(20);
      expect(insertTime).toBeLessThan(5000); // Should complete within 5 seconds
      expect(searchTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle concurrent operations', async () => {
      const concurrentOperations = Array.from({ length: 10 }, (_, i) => {
        const nodeData = {
          type: `test.concurrent.${i}`,
          name: `Concurrent Test ${i}`,
          description: `Test node ${i}`,
          category: NodeCategory.SYSTEM,
          inputSchema: { type: 'object' },
          outputSchema: { type: 'object' },
          version: '1.0.0',
        };

        return repository.save(repository.create(nodeData));
      });

      const results = await Promise.all(concurrentOperations);

      expect(results).toHaveLength(10);
      results.forEach((result, i) => {
        expect(result.type).toBe(`test.concurrent.${i}`);
      });

      // Verify all nodes were created
      const count = await repository.count();
      expect(count).toBe(10);
    });
  });

  describe('Data Integrity Tests', () => {
    it('should enforce unique constraint on type', async () => {
      const nodeData = {
        type: 'system.unique.test',
        name: 'Unique Test',
        description: 'Test unique constraint',
        category: NodeCategory.SYSTEM,
        inputSchema: { type: 'object' },
        outputSchema: { type: 'object' },
        version: '1.0.0',
      };

      // Create first node
      const first = repository.create(nodeData);
      await repository.save(first);

      // Try to create duplicate
      const duplicate = repository.create(nodeData);
      
      await expect(repository.save(duplicate)).rejects.toThrow();
    });

    it('should handle JSON fields correctly', async () => {
      const complexSchema = {
        type: 'object',
        properties: {
          user: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              age: { type: 'number' },
              preferences: {
                type: 'array',
                items: { type: 'string' }
              }
            }
          }
        }
      };

      const nodeData = {
        type: 'test.json.fields',
        name: 'JSON Test',
        description: 'Test JSON fields',
        category: NodeCategory.SYSTEM,
        inputSchema: complexSchema,
        outputSchema: complexSchema,
        version: '1.0.0',
        examples: {
          basic: { user: { name: 'John', age: 30, preferences: ['email', 'sms'] } },
          advanced: { user: { name: 'Jane', age: 25, preferences: ['push'] } }
        },
        tags: ['test', 'json', 'complex'],
      };

      const created = repository.create(nodeData);
      const saved = await repository.save(created);

      expect(saved.inputSchema).toEqual(complexSchema);
      expect(saved.outputSchema).toEqual(complexSchema);
      expect(saved.examples).toEqual(nodeData.examples);
      expect(saved.tags).toEqual(nodeData.tags);

      // Retrieve and verify
      const found = await repository.findByType('test.json.fields');
      expect(found!.inputSchema).toEqual(complexSchema);
      expect(found!.examples).toEqual(nodeData.examples);
    });
  });
});
