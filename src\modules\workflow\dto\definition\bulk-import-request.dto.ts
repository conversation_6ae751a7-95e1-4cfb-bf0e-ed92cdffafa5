import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsBoolean, IsN<PERSON>ber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Import Options DTO
 */
export class ImportOptionsDto {
  @ApiPropertyOptional({ description: 'Overwrite existing workflows' })
  @IsOptional()
  @IsBoolean()
  overwriteExisting?: boolean;

  @ApiPropertyOptional({ description: 'Validate only without importing' })
  @IsOptional()
  @IsBoolean()
  validateOnly?: boolean;

  @ApiPropertyOptional({ description: 'Preserve original IDs' })
  @IsOptional()
  @IsBoolean()
  preserveIds?: boolean;

  @ApiPropertyOptional({ description: 'Name prefix for imported workflows' })
  @IsOptional()
  namePrefix?: string;

  @ApiPropertyOptional({ description: 'Name suffix for imported workflows' })
  @IsOptional()
  nameSuffix?: string;

  @ApiPropertyOptional({ description: 'Admin import flag' })
  @IsOptional()
  @IsBoolean()
  adminImport?: boolean;
}

/**
 * Workflow Export Data DTO
 */
export class WorkflowExportDataDto {
  @ApiProperty({ description: 'Workflow data' })
  workflow: {
    name: string;
    description?: string;
    definition: any;
    metadata: any;
  };
}

/**
 * Bulk Import Request DTO
 */
export class BulkImportRequestDto {
  @ApiProperty({ description: 'Array of workflows to import' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowExportDataDto)
  workflows: WorkflowExportDataDto[];

  @ApiPropertyOptional({ description: 'Import options' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ImportOptionsDto)
  options?: ImportOptionsDto;

  @ApiPropertyOptional({ description: 'Continue on error' })
  @IsOptional()
  @IsBoolean()
  continueOnError?: boolean;

  @ApiPropertyOptional({ description: 'Batch size' })
  @IsOptional()
  @IsNumber()
  batchSize?: number;
}
