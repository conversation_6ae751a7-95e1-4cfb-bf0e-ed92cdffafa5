import { ApiProperty } from '@nestjs/swagger';
import { ModelPricingInterface } from '@/modules/models/interfaces/pricing.interface';

/**
 * DTO cho response danh sách models của admin
 */
export class AdminModelsResponseDto {
    @ApiProperty({
        description: 'ID của model',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    id: string;

    @ApiProperty({
        description: 'Tên model',
        example: 'gpt-4-turbo'
    })
    modelName: string;

    @ApiProperty({
        description: 'Input modalities được hỗ trợ',
        example: ['text', 'image'],
        type: [String]
    })
    inputModalities: string[];

    @ApiProperty({
        description: 'Output modalities được hỗ trợ',
        example: ['text'],
        type: [String]
    })
    outputModalities: string[];

    @ApiProperty({
        description: 'Sampling parameters',
        example: ['temperature', 'top_p'],
        type: [String]
    })
    samplingParameters: string[];

    @ApiProperty({
        description: 'Features được hỗ trợ',
        example: ['function_calling', 'streaming'],
        type: [String]
    })
    features: string[];

    @ApiProperty({
        description: 'Giá cơ bản',
        type: Object,
        example: { inputRate: 0.01, outputRate: 0.03 }
    })
    basePricing: ModelPricingInterface;

    @ApiProperty({
        description: 'Giá fine-tune',
        type: Object,
        example: { inputRate: 0.02, outputRate: 0.06 }
    })
    fineTunePricing: ModelPricingInterface;

    @ApiProperty({
        description: 'Giá training',
        example: 0.008
    })
    trainingPricing: number;

    @ApiProperty({
        description: 'Số token tối đa',
        example: 128000
    })
    maxTokens: number;

    @ApiProperty({
        description: 'Context window',
        example: 128000
    })
    contextWindow: number;
}
