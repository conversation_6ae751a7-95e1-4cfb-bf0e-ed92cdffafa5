import { plainToClass } from 'class-transformer';
import { Workflow } from '../../entities/workflow.entity';
import { ValidationResult } from '../../services/workflow-validation.service';
import {
  WorkflowDefinitionUpdateResponseDto,
  WorkflowNodeOperationResponseDto,
  WorkflowEdgeOperationResponseDto,
  WorkflowValidationResponseDto
} from '../../dto/definition/workflow-definition-response.dto';

/**
 * Mapper utility for converting Workflow entities to DTOs
 * Ensures only necessary fields are exposed in API responses
 */
export class WorkflowDefinitionMapper {
  /**
   * Convert Workflow entity to WorkflowDefinitionUpdateResponseDto
   * @param workflow - Workflow entity
   * @param validation - Validation result
   * @returns WorkflowDefinitionUpdateResponseDto
   */
  static toDefinitionUpdateResponse(
    workflow: Workflow,
    validation: ValidationResult
  ): WorkflowDefinitionUpdateResponseDto {
    const dto = plainToClass(WorkflowDefinitionUpdateResponseDto, {
      id: workflow.id,
      name: workflow.name,
      isActive: workflow.isActive,
      definition: workflow.definition,
      updatedAt: workflow.updatedAt,
      validation: {
        isValid: validation.isValid,
        errors: validation.errors || [],
        warnings: validation.warnings || []
      }
    });

    return dto;
  }

  /**
   * Convert Workflow entity to WorkflowNodeOperationResponseDto
   * @param workflow - Workflow entity
   * @returns WorkflowNodeOperationResponseDto
   */
  static toNodeOperationResponse(workflow: Workflow): WorkflowNodeOperationResponseDto {
    const nodes = workflow.definition?.nodes || [];
    const edges = workflow.definition?.edges || [];

    const dto = plainToClass(WorkflowNodeOperationResponseDto, {
      id: workflow.id,
      name: workflow.name,
      definition: workflow.definition,
      updatedAt: workflow.updatedAt,
      nodeCount: nodes.length,
      edgeCount: edges.length
    });

    return dto;
  }

  /**
   * Convert Workflow entity to WorkflowEdgeOperationResponseDto
   * @param workflow - Workflow entity
   * @returns WorkflowEdgeOperationResponseDto
   */
  static toEdgeOperationResponse(workflow: Workflow): WorkflowEdgeOperationResponseDto {
    const edges = workflow.definition?.edges || [];

    const dto = plainToClass(WorkflowEdgeOperationResponseDto, {
      id: workflow.id,
      name: workflow.name,
      definition: workflow.definition,
      updatedAt: workflow.updatedAt,
      edgeCount: edges.length
    });

    return dto;
  }

  /**
   * Convert ValidationResult to WorkflowValidationResponseDto
   * @param validation - Validation result
   * @param metadata - Additional metadata
   * @returns WorkflowValidationResponseDto
   */
  static toValidationResponse(
    validation: ValidationResult,
    metadata?: Record<string, any>
  ): WorkflowValidationResponseDto {
    const dto = plainToClass(WorkflowValidationResponseDto, {
      isValid: validation.isValid,
      errors: validation.errors || [],
      warnings: validation.warnings || [],
      metadata: metadata || {}
    });

    return dto;
  }

  /**
   * Sanitize workflow definition for response
   * Removes sensitive or internal fields
   * @param definition - Raw workflow definition
   * @returns Sanitized definition
   */
  static sanitizeDefinition(definition: Record<string, any>): Record<string, any> {
    const sanitized = { ...definition };

    // Remove internal fields if they exist
    if (sanitized.metadata) {
      // Keep only safe metadata fields
      const safeMetadata = {
        version: sanitized.metadata.version,
        lastModified: sanitized.metadata.lastModified,
        description: sanitized.metadata.description,
        tags: sanitized.metadata.tags
      };
      sanitized.metadata = safeMetadata;
    }

    // Ensure nodes and edges are arrays
    if (!Array.isArray(sanitized.nodes)) {
      sanitized.nodes = [];
    }
    if (!Array.isArray(sanitized.edges)) {
      sanitized.edges = [];
    }

    return sanitized;
  }

  /**
   * Calculate workflow statistics for metadata
   * @param workflow - Workflow entity
   * @returns Workflow statistics
   */
  static calculateWorkflowStats(workflow: Workflow): Record<string, any> {
    const nodes = workflow.definition?.nodes || [];
    const edges = workflow.definition?.edges || [];

    // Count node types
    const nodeTypeCount = nodes.reduce((acc: Record<string, number>, node: any) => {
      const type = node.type || 'unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    // Check for cycles (basic check)
    const nodeIds = new Set(nodes.map((node: any) => node.id));
    const hasOrphanNodes = nodes.some((node: any) => {
      const hasIncoming = edges.some((edge: any) => edge.targetNodeId === node.id);
      const hasOutgoing = edges.some((edge: any) => edge.sourceNodeId === node.id);
      return !hasIncoming && !hasOutgoing && nodes.length > 1;
    });

    return {
      nodeCount: nodes.length,
      edgeCount: edges.length,
      nodeTypeCount,
      hasOrphanNodes,
      complexity: this.calculateComplexity(nodes, edges)
    };
  }

  /**
   * Calculate workflow complexity score
   * @param nodes - Workflow nodes
   * @param edges - Workflow edges
   * @returns Complexity score (1-10)
   */
  private static calculateComplexity(nodes: any[], edges: any[]): number {
    const nodeCount = nodes.length;
    const edgeCount = edges.length;
    
    // Basic complexity calculation
    let complexity = 1;
    
    if (nodeCount > 10) complexity += 2;
    else if (nodeCount > 5) complexity += 1;
    
    if (edgeCount > nodeCount * 1.5) complexity += 2;
    else if (edgeCount > nodeCount) complexity += 1;
    
    // Check for conditional edges
    const conditionalEdges = edges.filter((edge: any) => edge.condition);
    if (conditionalEdges.length > 0) {
      complexity += Math.min(conditionalEdges.length, 3);
    }
    
    return Math.min(complexity, 10);
  }
}
