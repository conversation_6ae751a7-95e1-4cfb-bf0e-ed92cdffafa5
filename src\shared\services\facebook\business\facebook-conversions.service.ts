import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@config/config.service';
import { ConfigType } from '@/config';
import { FacebookConfig } from '@config/interfaces';
import { firstValueFrom } from 'rxjs';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  FacebookPixel,
  CustomConversion,
  OfflineEventSet,
  CreatePixelRequest,
  CreateCustomConversionRequest,
  CreateOfflineEventSetRequest,
  UploadOfflineEventsRequest,
  UploadOfflineEventsResponse,
  GetPixelsResponse,
  GetCustomConversionsResponse,
  GetOfflineEventSetsResponse,
  GetConversionsResponse,
} from '../interfaces/facebook-conversions.interface';

/**
 * Service để quản lý Conversion Tracking trong Facebook Business API
 */
@Injectable()
export class FacebookConversionsService {
  private readonly logger = new Logger(FacebookConversionsService.name);
  private readonly facebookConfig: FacebookConfig;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.facebookConfig = this.configService.getConfig<FacebookConfig>(
      ConfigType.Facebook,
    );
  }

  /**
   * Lấy danh sách Facebook Pixels
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param fields Các trường cần lấy
   * @param limit Số lượng kết quả
   * @returns Danh sách Facebook Pixels
   */
  async getPixels(
    adAccountId: string,
    accessToken: string,
    fields?: string,
    limit?: number,
  ): Promise<GetPixelsResponse> {
    try {
      this.logger.log(`Lấy danh sách Facebook Pixels cho Ad Account ${adAccountId}`);

      const params: any = {
        access_token: accessToken,
      };

      if (fields) {
        params.fields = fields;
      } else {
        params.fields = 'id,name,creation_time,last_fired_time,code,is_created_by_business,owner_ad_account,owner_business,pixel_domain_control_rule,is_unavailable,data_use_setting,enable_automatic_matching,first_party_cookie_status,is_consolidated_container,consolidated_tracking,restricted_use,valid_domains,microdata,automatic_matching_fields,can_proxy,config,domain_control_rule,enable_auto_assign_to_accounts,has_1p_pixel_event,is_eligible_for_deletion,is_restricted_use,last_used_time,matched_system_users,owner_pixelable_object,sort_by_type';
      }

      if (limit) {
        params.limit = limit;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetPixelsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/act_${adAccountId}/adspixels`,
          { params },
        ),
      );

      this.logger.log(
        `Đã lấy ${response.data.data.length} Facebook Pixels cho Ad Account ${adAccountId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách Facebook Pixels: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách Facebook Pixels',
        { adAccountId },
      );
    }
  }

  /**
   * Tạo Facebook Pixel mới
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param pixelData Dữ liệu pixel
   * @returns Facebook Pixel đã tạo
   */
  async createPixel(
    adAccountId: string,
    accessToken: string,
    pixelData: CreatePixelRequest,
  ): Promise<FacebookPixel> {
    try {
      this.logger.log(`Tạo Facebook Pixel mới cho Ad Account ${adAccountId}`);

      const requestData = {
        ...pixelData,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post<FacebookPixel>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/act_${adAccountId}/adspixels`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(
        `Đã tạo Facebook Pixel ${response.data.id} cho Ad Account ${adAccountId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo Facebook Pixel cho Ad Account ${adAccountId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo Facebook Pixel',
        { adAccountId, pixelData },
      );
    }
  }

  /**
   * Lấy dữ liệu conversions
   * @param pixelId ID của Facebook Pixel
   * @param accessToken Access token
   * @param startDate Ngày bắt đầu (YYYY-MM-DD)
   * @param endDate Ngày kết thúc (YYYY-MM-DD)
   * @returns Dữ liệu conversions
   */
  async getConversions(
    pixelId: string,
    accessToken: string,
    startDate?: string,
    endDate?: string,
  ): Promise<GetConversionsResponse> {
    try {
      this.logger.log(`Lấy dữ liệu conversions cho Pixel ${pixelId}`);

      const params: any = {
        access_token: accessToken,
      };

      if (startDate) {
        params.start_date = startDate;
      }
      if (endDate) {
        params.end_date = endDate;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetConversionsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${pixelId}/stats`,
          { params },
        ),
      );

      this.logger.log(`Đã lấy dữ liệu conversions cho Pixel ${pixelId}`);

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy dữ liệu conversions cho Pixel ${pixelId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy dữ liệu conversions',
        { pixelId },
      );
    }
  }

  /**
   * Tạo Custom Conversion
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param conversionData Dữ liệu custom conversion
   * @returns Custom Conversion đã tạo
   */
  async createCustomConversion(
    adAccountId: string,
    accessToken: string,
    conversionData: CreateCustomConversionRequest,
  ): Promise<CustomConversion> {
    try {
      this.logger.log(`Tạo Custom Conversion mới cho Ad Account ${adAccountId}`);

      const requestData = {
        ...conversionData,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post<CustomConversion>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/act_${adAccountId}/customconversions`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(
        `Đã tạo Custom Conversion ${response.data.id} cho Ad Account ${adAccountId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo Custom Conversion cho Ad Account ${adAccountId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo Custom Conversion',
        { adAccountId, conversionData },
      );
    }
  }

  /**
   * Lấy danh sách Custom Conversions
   * @param adAccountId ID của Ad Account
   * @param accessToken Access token
   * @param fields Các trường cần lấy
   * @param limit Số lượng kết quả
   * @returns Danh sách Custom Conversions
   */
  async getCustomConversions(
    adAccountId: string,
    accessToken: string,
    fields?: string,
    limit?: number,
  ): Promise<GetCustomConversionsResponse> {
    try {
      this.logger.log(`Lấy danh sách Custom Conversions cho Ad Account ${adAccountId}`);

      const params: any = {
        access_token: accessToken,
      };

      if (fields) {
        params.fields = fields;
      } else {
        params.fields = 'id,name,description,account_id,pixel_id,pixel_rule,creation_time,last_fired_time,is_archived,default_conversion_value,custom_event_type,data_sources,event_source_type,first_fired_time,is_unavailable,aggregation_rule,business,advanced_rule_json';
      }

      if (limit) {
        params.limit = limit;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetCustomConversionsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/act_${adAccountId}/customconversions`,
          { params },
        ),
      );

      this.logger.log(
        `Đã lấy ${response.data.data.length} Custom Conversions cho Ad Account ${adAccountId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách Custom Conversions: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách Custom Conversions',
        { adAccountId },
      );
    }
  }

  /**
   * Lấy danh sách Offline Event Sets
   * @param businessId ID của Business
   * @param accessToken Access token
   * @param fields Các trường cần lấy
   * @param limit Số lượng kết quả
   * @returns Danh sách Offline Event Sets
   */
  async getOfflineEventSets(
    businessId: string,
    accessToken: string,
    fields?: string,
    limit?: number,
  ): Promise<GetOfflineEventSetsResponse> {
    try {
      this.logger.log(`Lấy danh sách Offline Event Sets cho Business ${businessId}`);

      const params: any = {
        access_token: accessToken,
      };

      if (fields) {
        params.fields = fields;
      } else {
        params.fields = 'id,name,business_id,is_mta_use,is_restricted_use,is_unavailable,last_upload_app,last_upload_app_changed_time,match_rate_approx,usage,owner_business,config,data_use_setting,description,duplicate_entries,enable_auto_assign_to_accounts,event_stats,event_time_max,event_time_min,first_party_cookie_status,has_bapi_domains,is_consolidated_container,is_created_by_business,last_fired_time,last_upload_time,matched_entries,owner_ad_account,upload_tag,valid_entries';
      }

      if (limit) {
        params.limit = limit;
      }

      const response = await firstValueFrom(
        this.httpService.get<GetOfflineEventSetsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${businessId}/offline_event_sets`,
          { params },
        ),
      );

      this.logger.log(
        `Đã lấy ${response.data.data.length} Offline Event Sets cho Business ${businessId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách Offline Event Sets: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách Offline Event Sets',
        { businessId },
      );
    }
  }

  /**
   * Tạo Offline Event Set mới
   * @param businessId ID của Business
   * @param accessToken Access token
   * @param eventSetData Dữ liệu offline event set
   * @returns Offline Event Set đã tạo
   */
  async createOfflineEventSet(
    businessId: string,
    accessToken: string,
    eventSetData: CreateOfflineEventSetRequest,
  ): Promise<OfflineEventSet> {
    try {
      this.logger.log(`Tạo Offline Event Set mới cho Business ${businessId}`);

      const requestData = {
        ...eventSetData,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post<OfflineEventSet>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${businessId}/offline_event_sets`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(
        `Đã tạo Offline Event Set ${response.data.id} cho Business ${businessId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo Offline Event Set cho Business ${businessId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo Offline Event Set',
        { businessId, eventSetData },
      );
    }
  }

  /**
   * Upload Offline Events
   * @param offlineEventSetId ID của Offline Event Set
   * @param accessToken Access token
   * @param eventsData Dữ liệu offline events
   * @returns Kết quả upload
   */
  async uploadOfflineEvents(
    offlineEventSetId: string,
    accessToken: string,
    eventsData: UploadOfflineEventsRequest,
  ): Promise<UploadOfflineEventsResponse> {
    try {
      this.logger.log(`Upload Offline Events cho Event Set ${offlineEventSetId}`);

      const requestData = {
        ...eventsData,
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.post<UploadOfflineEventsResponse>(
          `https://graph.facebook.com/${this.facebookConfig.graphApiVersion}/${offlineEventSetId}/events`,
          requestData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(
        `Đã upload ${response.data.events_received} Offline Events cho Event Set ${offlineEventSetId}`,
      );

      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi upload Offline Events cho Event Set ${offlineEventSetId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi upload Offline Events',
        { offlineEventSetId, eventsData },
      );
    }
  }
}
