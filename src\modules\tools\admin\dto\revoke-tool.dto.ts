import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc thu hồi tool nâng cao
 */
export class RevokeToolDto {
  @ApiProperty({
    description: 'Thông báo lý do thu hồi tool',
    example: 'Tool có lỗi bảo mật nghiêm trọng và cần được thu hồi ngay lập tức',
  })
  @IsString()
  @MaxLength(1000)
  message: string;

  @ApiProperty({
    description: 'Thời hạn gỡ tool (ISO 8601 format)',
    example: '2024-12-31T23:59:59.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  revokeDeadline?: string;

  @ApiProperty({
    description: '<PERSON><PERSON> chú bổ sung cho việc thu hồi',
    example: 'Người dùng cần cập nhật lên phiên bản mới nhất',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  notes?: string;
}
