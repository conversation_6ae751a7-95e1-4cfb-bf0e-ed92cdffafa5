import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { EncryptionService } from '@/shared/services/encryption/encryption.service';
import { GoogleOAuthService } from '@/shared/services/google/auth/google-oauth.service';
import { AppException } from '@/common/exceptions/app.exception';
import { INTEGRATION_ERROR_CODES } from '../../exceptions/integration-error.code';
import { Integration, IntegrationProvider } from '../../entities';
import { OwnedTypeEnum } from '../../enums';
import { ProviderEnum } from '../../constants/provider.enum';
import {
  GoogleDocsAuthUrlDto,
  GoogleDocsAuthUrlResponseDto,
  GoogleDocsOAuthCallbackDto,
  CreateGoogleDocsIntegrationDto,
  UpdateGoogleDocsIntegrationDto,
  GoogleDocsIntegrationResponseDto,
} from '../dto/google-docs';
import { GoogleDocsConfig, GoogleDocsMetadata, GoogleDocsPayload } from '../../interfaces';

/**
 * Service xử lý Google Docs integration
 */
@Injectable()
export class GoogleDocsIntegrationService {
  private readonly logger = new Logger(GoogleDocsIntegrationService.name);
  private readonly secretKeyPrivate: string;

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    @InjectRepository(IntegrationProvider)
    private readonly integrationProviderRepository: Repository<IntegrationProvider>,
    private readonly encryptionService: EncryptionService,
    private readonly googleOAuthService: GoogleOAuthService,
    private readonly configService: ConfigService,
  ) {
    this.secretKeyPrivate = this.configService.get<string>('ENCRYPTION_PRIVATE_KEY') || 'default-private-key';
  }

  /**
   * Tạo URL xác thực OAuth cho Google Docs
   */
  async generateAuthUrl(userId: number, authDto: GoogleDocsAuthUrlDto): Promise<GoogleDocsAuthUrlResponseDto> {
    try {
      this.logger.log(`Generating Google Docs auth URL for user ${userId}`);

      const scopes = [
        'https://www.googleapis.com/auth/documents',
        'https://www.googleapis.com/auth/drive.file',
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/userinfo.profile',
      ];

      const authUrl = this.googleOAuthService.generateAuthUrl(
        scopes,
        authDto.state,
        authDto.redirectUri,
      );

      return {
        authUrl,
        state: authDto.state || '',
      };
    } catch (error) {
      this.logger.error(`Error generating auth URL: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.GOOGLE_DOCS_AUTH_FAILED,
        `Lỗi khi tạo URL xác thực: ${error.message}`,
      );
    }
  }

  /**
   * Xử lý OAuth callback và tạo integration
   */
  async handleOAuthCallback(
    userId: number,
    callbackDto: GoogleDocsOAuthCallbackDto,
  ): Promise<GoogleDocsIntegrationResponseDto> {
    try {
      this.logger.log(`Handling Google Docs OAuth callback for user ${userId}`);

      // Exchange authorization code for tokens
      const tokens = await this.googleOAuthService.getToken(
        callbackDto.code,
        callbackDto.redirectUri,
      );

      // Set credentials and get user info
      this.googleOAuthService.setCredentials(tokens);
      const userInfo = await this.googleOAuthService.getUserInfo();

      // Create integration
      const createDto: CreateGoogleDocsIntegrationDto = {
        integrationName: `Google Docs - ${userInfo.email}`,
        accessToken: tokens.access_token || '',
        refreshToken: tokens.refresh_token || '',
        expiresAt: tokens.expiry_date || undefined,
        scope: tokens.scope,
      };

      return await this.createIntegration(userId, createDto, userInfo);
    } catch (error) {
      this.logger.error(`Error handling OAuth callback: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.GOOGLE_DOCS_AUTH_FAILED,
        `Lỗi khi xử lý OAuth callback: ${error.message}`,
      );
    }
  }

  /**
   * Tạo Google Docs integration
   */
  async createIntegration(
    userId: number,
    createDto: CreateGoogleDocsIntegrationDto,
    userInfo?: any,
  ): Promise<GoogleDocsIntegrationResponseDto> {
    try {
      this.logger.log(`Creating Google Docs integration for user ${userId}`);

      // Tìm provider
      const provider = await this.integrationProviderRepository.findOne({
        where: { type: ProviderEnum.GOOGLE_DOCS },
      });

      if (!provider) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.PROVIDER_NOT_FOUND,
          'Không tìm thấy Google Docs provider',
        );
      }

      // Tạo config object
      const config: GoogleDocsConfig = {
        accessToken: createDto.accessToken,
        refreshToken: createDto.refreshToken,
        expiresAt: createDto.expiresAt,
        scope: createDto.scope,
      };

      // Tạo metadata
      const metadata: GoogleDocsMetadata = {
        userInfo: userInfo ? {
          email: userInfo.email,
          name: userInfo.name,
          picture: userInfo.picture,
        } : undefined,
        usage: {
          documentsCreated: 0,
          documentsRead: 0,
          documentsUpdated: 0,
          lastUsedAt: Date.now(),
        },
        settings: {
          autoBackup: false,
          syncEnabled: true,
        },
        tokenInfo: {
          issuedAt: Date.now(),
          refreshCount: 0,
        },
      };

      // Encrypt config
      const secretKey = this.encryptionService.generateSecretKey();
      const encryptedConfig = this.encryptionService.encrypt(secretKey, this.secretKeyPrivate, config);

      // Create integration
      const newIntegration = this.integrationRepository.create({
        integrationName: createDto.integrationName,
        typeId: provider.id,
        userId,
        ownedType: OwnedTypeEnum.USER,
        encryptedConfig,
        secretKey,
        metadata: metadata as any,
      });

      const integration = await this.integrationRepository.save(newIntegration);

      this.logger.log(`Created Google Docs integration ${integration.id} for user ${userId}`);
      return this.toResponseDto(integration, config, metadata);
    } catch (error) {
      this.logger.error(`Error creating integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật Google Docs integration
   */
  async updateIntegration(
    userId: number,
    integrationId: string,
    updateDto: UpdateGoogleDocsIntegrationDto,
  ): Promise<GoogleDocsIntegrationResponseDto> {
    try {
      this.logger.log(`Updating Google Docs integration ${integrationId} for user ${userId}`);

      const integration = await this.integrationRepository.findOne({
        where: { id: integrationId, userId },
      });

      if (!integration) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND,
          'Không tìm thấy integration',
        );
      }

      // Decrypt current config
      const currentConfig = this.decryptConfig(integration);

      // Update config
      const updatedConfig: GoogleDocsConfig = {
        ...currentConfig,
        ...(updateDto.accessToken && { accessToken: updateDto.accessToken }),
        ...(updateDto.refreshToken && { refreshToken: updateDto.refreshToken }),
        ...(updateDto.expiresAt && { expiresAt: updateDto.expiresAt }),
      };

      // Encrypt updated config
      const encryptedConfig = this.encryptionService.encrypt(
        integration.secretKey!,
        this.secretKeyPrivate,
        updatedConfig,
      );

      // Update integration
      await this.integrationRepository.update(integrationId, {
        ...(updateDto.integrationName && { integrationName: updateDto.integrationName }),
        encryptedConfig,
      });

      const updatedIntegration = await this.integrationRepository.findOne({
        where: { id: integrationId },
      });

      return this.toResponseDto(updatedIntegration!, updatedConfig, (integration.metadata || {}) as GoogleDocsMetadata);
    } catch (error) {
      this.logger.error(`Error updating integration: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Làm mới access token
   */
  async refreshToken(userId: number, integrationId: string): Promise<{ accessToken: string; expiresAt?: number }> {
    try {
      this.logger.log(`Refreshing token for integration ${integrationId}`);

      const integration = await this.integrationRepository.findOne({
        where: { id: integrationId, userId },
      });

      if (!integration) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND,
          'Không tìm thấy integration',
        );
      }

      const config = this.decryptConfig(integration);

      // Refresh token
      const newTokens = await this.googleOAuthService.refreshAccessToken(config.refreshToken);

      // Update config
      const updatedConfig: GoogleDocsConfig = {
        ...config,
        accessToken: newTokens.access_token || '',
        expiresAt: newTokens.expiry_date || undefined,
      };

      // Update metadata
      const metadata = integration.metadata as GoogleDocsMetadata;
      if (metadata?.tokenInfo) {
        metadata.tokenInfo.lastRefreshedAt = Date.now();
        metadata.tokenInfo.refreshCount += 1;
      }

      // Encrypt and save
      const encryptedConfig = this.encryptionService.encrypt(
        integration.secretKey!,
        this.secretKeyPrivate,
        updatedConfig,
      );

      await this.integrationRepository.update(integrationId, {
        encryptedConfig,
        metadata: metadata as any,
      });

      return {
        accessToken: newTokens.access_token || '',
        expiresAt: newTokens.expiry_date || undefined,
      };
    } catch (error) {
      this.logger.error(`Error refreshing token: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.GOOGLE_DOCS_TOKEN_REFRESH_FAILED,
        `Lỗi khi làm mới token: ${error.message}`,
      );
    }
  }

  /**
   * Thu hồi quyền truy cập
   */
  async revokeAccess(userId: number, integrationId: string): Promise<boolean> {
    try {
      this.logger.log(`Revoking access for integration ${integrationId}`);

      const integration = await this.integrationRepository.findOne({
        where: { id: integrationId, userId },
      });

      if (!integration) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND,
          'Không tìm thấy integration',
        );
      }

      const config = this.decryptConfig(integration);

      // Revoke token with Google (manual implementation)
      try {
        const revokeUrl = `https://oauth2.googleapis.com/revoke?token=${config.accessToken}`;
        await fetch(revokeUrl, { method: 'POST' });
      } catch (revokeError) {
        this.logger.warn(`Failed to revoke token: ${revokeError.message}`);
        // Continue with deletion even if revoke fails
      }

      // Delete integration
      await this.integrationRepository.delete(integrationId);

      return true;
    } catch (error) {
      this.logger.error(`Error revoking access: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.GOOGLE_DOCS_REVOKE_FAILED,
        `Lỗi khi thu hồi quyền truy cập: ${error.message}`,
      );
    }
  }

  /**
   * Lấy access token hợp lệ (tự động refresh nếu cần)
   */
  async getValidAccessToken(userId: number, integrationId: string): Promise<string> {
    try {
      const integration = await this.integrationRepository.findOne({
        where: { id: integrationId, userId },
      });

      if (!integration) {
        throw new AppException(
          INTEGRATION_ERROR_CODES.INTEGRATION_NOT_FOUND,
          'Không tìm thấy integration',
        );
      }

      const config = this.decryptConfig(integration);

      // Check if token is expired
      if (config.expiresAt && Date.now() >= config.expiresAt) {
        this.logger.log(`Token expired, refreshing for integration ${integrationId}`);
        const refreshResult = await this.refreshToken(userId, integrationId);
        return refreshResult.accessToken;
      }

      return config.accessToken;
    } catch (error) {
      this.logger.error(`Error getting valid access token: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật thống kê sử dụng
   */
  async updateUsageStats(
    userId: number,
    integrationId: string,
    action: 'create' | 'read' | 'update',
    count: number = 1,
  ): Promise<void> {
    try {
      const integration = await this.integrationRepository.findOne({
        where: { id: integrationId, userId },
      });

      if (!integration) return;

      const metadata = integration.metadata as GoogleDocsMetadata;
      if (!metadata?.usage) return;

      // Update usage stats
      switch (action) {
        case 'create':
          metadata.usage.documentsCreated += count;
          break;
        case 'read':
          metadata.usage.documentsRead += count;
          break;
        case 'update':
          metadata.usage.documentsUpdated += count;
          break;
      }

      metadata.usage.lastUsedAt = Date.now();

      await this.integrationRepository.update(integrationId, {
        metadata: metadata as any,
      });
    } catch (error) {
      this.logger.warn(`Could not update usage stats: ${error.message}`);
      // Don't throw error as this is not critical
    }
  }

  /**
   * Decrypt config từ integration
   */
  private decryptConfig(integration: Integration): GoogleDocsConfig {
    try {
      if (!integration.encryptedConfig || !integration.secretKey) {
        throw new Error('Missing encrypted config or secret key');
      }

      return this.encryptionService.decrypt(
        integration.secretKey,
        this.secretKeyPrivate,
        integration.encryptedConfig,
      ) as GoogleDocsConfig;
    } catch (error) {
      this.logger.error(`Error decrypting config: ${error.message}`, error.stack);
      throw new AppException(
        INTEGRATION_ERROR_CODES.DECRYPTION_FAILED,
        'Lỗi khi giải mã cấu hình integration',
      );
    }
  }

  /**
   * Convert integration entity to response DTO
   */
  private toResponseDto(
    integration: Integration,
    config: GoogleDocsConfig,
    metadata: GoogleDocsMetadata,
  ): GoogleDocsIntegrationResponseDto {
    const status = this.getConnectionStatus(config);

    return {
      id: integration.id,
      integrationName: integration.integrationName,
      typeId: integration.typeId,
      userId: integration.userId!,
      status,
      expiresAt: config.expiresAt,
      scope: config.scope,
      createdAt: integration.createdAt,
      metadata,
    };
  }

  /**
   * Xác định trạng thái kết nối
   */
  private getConnectionStatus(config: GoogleDocsConfig): 'connected' | 'disconnected' | 'expired' {
    if (!config.accessToken) {
      return 'disconnected';
    }

    if (config.expiresAt && Date.now() >= config.expiresAt) {
      return 'expired';
    }

    return 'connected';
  }
}
